# Matomo Tracking Implementation Guide

This document describes how to use Matomo analytics tracking in the Account Portal application.

## Overview

Matomo has been integrated into the application to track:

- Page views 
- Navigation events
- Custom events (button clicks, form submissions, etc.)
- Site searches
- Goal conversions
- URL parameter changes (tab switches, filters, etc.)

## How It Works

The Matomo tracking is implemented in several components:

1. **Matomo Component**: A core component that injects the Matomo tracking code
2. **MatomoContext**: A React context that provides tracking functionality throughout the application
3. **MatomoPageTracker**: Component that automatically tracks page views and URL parameter changes
4. **useRouteTracking Hook**: A custom hook for tracking route and parameter changes
5. **Tracking HOC**: Higher-order component that adds tracking to any component using a simple trackingId prop
6. **Tracking Utilities**: Helper functions to track custom events and actions

## Matomo Configuration

The current configuration is defined in `config/matomo.ts`:
- Site ID: 3
- Tracking URL: https://analysic-uat.fundpark.com/
- Script URL: https://analysic-uat.fundpark.com/matomo.js
- Debug mode: enabled
- Development mode tracking: enabled

To change these configurations, modify the values in the `config/matomo.ts` file.

## Architecture

### Component Structure

- `<Matomo />`: Initializes the tracking script with configuration options
- `<MatomoProvider />`: React context provider that makes tracking methods available throughout the app
- `<MatomoPageTracker />`: Component that should be placed in layout components for automatic tracking
- `<TrackingProvider />`: Provider component that centralizes tracking configuration options
- Shared components with built-in tracking support: `Button`, `Input`, `Password`, `MobileInput`, etc.

### Hook APIs

- `useMatomo()`: Basic hook for accessing tracking methods directly
- `useMatomoContext()`: Hook to access the complete Matomo context including enabled state
- `useRouteTracking()`: Advanced hook for tracking route changes and URL parameter changes
- `useTrackingConfig()`: Hook to access global tracking configuration

## Simplified Tracking with `trackingId`

All shared components now support a simple `trackingId` prop that can be used to identify interactions in Matomo. This is the preferred way to add tracking to components:

```tsx
<PrimaryButton 
  label="Submit" 
  trackingId="form_submit_button" 
  onClick={() => console.log("Submit clicked")}
/>
```

### What Gets Tracked

When using the `trackingId` prop:

1. The `trackingId` value is used as the event name in Matomo
2. User ID is tracked automatically if enabled
3. The current URL is captured as a custom dimension if URL tracking is enabled
4. The current timestamp is recorded if time tracking is enabled
5. Additional custom dimensions can be added as needed

### Configuring Tracking Globally

You can configure tracking globally using the `TrackingProvider`:

```tsx
<TrackingProvider
  eventNamePrefix="MyApp_"
  trackUserId={true}
  trackUrl={true}
  trackTime={true}
  disableTracking={false}
>
  <App />
</TrackingProvider>
```

### Available Tracking Options

The following options can be configured in the `TrackingProvider` or on individual components:

- `eventNamePrefix`: Prefix added to all event names (e.g., "MyApp_button_click")
- `trackUserId`: Whether to include user ID in tracking (default: true)
- `trackUrl`: Whether to include current URL in tracking (default: true)
- `trackTime`: Whether to include timestamp in tracking (default: true)
- `disableTracking`: Whether to disable tracking for this component or section (default: false)
- `customDimensions`: Object mapping dimension IDs to values

## Tracking Different UI Elements

This section provides examples for tracking various UI elements and interactions in your application.

### 1. Tracking Page Views

Page views are automatically tracked if you've added the `MatomoPageTracker` component to your layouts. Here's how this works:

```tsx
// This is already added to your layout components
import MatomoPageTracker from "@/components/MatomoPageTracker";

const SomeLayout: React.FC = ({ children }) => {
  return (
    <Layout>
      <MatomoPageTracker />
      {children}
    </Layout>
  );
};
```

**For manual page view tracking:**

```tsx
import { useMatomoContext } from '@/contexts/MatomoContext';

const SomePage = () => {
  const { trackPageView } = useMatomoContext();
  
  useEffect(() => {
    // Track with custom title and URL
    trackPageView('Custom Page Title', '/virtual/page/url');
  }, []);
  
  return <div>Page content</div>;
};
```

### 2. Tracking Button Clicks

**Simplified Method: Using trackingId prop (Recommended)**

```tsx
import { Button } from '@/components/shared/Button';
import PrimaryButton from '@/components/shared/PrimaryButton';
import { SecondaryButton } from '@/components/shared/SecondaryButton';

const MyComponent = () => {
  return (
    <div>
      <Button 
        label="Default Button" 
        trackingId="default_button"
        onClick={() => console.log("Default clicked")} 
      />
      
      <PrimaryButton 
        label="Submit" 
        trackingId="form_submit_button"
        onClick={() => console.log("Submit clicked")} 
      />
      
      <SecondaryButton 
        label="Cancel" 
        trackingId="form_cancel_button"
        onClick={() => console.log("Cancel clicked")} 
      />
    </div>
  );
};
```

**Advanced Usage: With Custom Dimensions**

```tsx
<Button 
  label="Track with Custom Dimensions" 
  onClick={() => console.log("Custom dimensions tracked")}
  trackingId="dimensions_button"
  customDimensions={{
    4: "Button Dimension 1",
    5: "Button Dimension 2"
  }}
/>
```

**Legacy Method: Using the hook directly with Ant Design Button**

```tsx
import { Button } from 'antd';
import { useMatomoContext } from '@/contexts/MatomoContext';

const MyComponent = () => {
  const { trackEvent } = useMatomoContext();
  
  const handleClick = () => {
    // Track the button click
    trackEvent('User Interface', 'Button Click', 'Submit Application');
    
    // Your regular button click handler
    submitApplication();
  };
  
  return (
    <Button type="primary" onClick={handleClick}>
      Submit Application
    </Button>
  );
};
```

### 3. Tracking Form Inputs

**Simplified Method: Using trackingId prop (Recommended)**

```tsx
import { Input, Password } from '@/components/shared/Input';
import MobileInput from '@/components/shared/MobileInput';
import { Form } from 'antd';

const FormComponent = () => {
  const [form] = Form.useForm();
  
  return (
    <Form form={form} layout="vertical">
      <Form.Item label="Username" name="username">
        <Input 
          placeholder="Enter username" 
          trackingId="username_field"
        />
      </Form.Item>
      
      <Form.Item label="Password" name="password">
        <Password 
          placeholder="Enter password" 
          showVerificationTips 
          trackingId="password_field"
        />
      </Form.Item>
      
      <Form.Item label="Phone Number" name="phoneNumber">
        <MobileInput 
          placeholder="Enter mobile number" 
          trackingId="mobile_input"
        />
      </Form.Item>
    </Form>
  );
};
```

**Legacy Method: Track Input Changes Manually**

```tsx
import { Input } from 'antd';
import { useMatomoContext } from '@/contexts/MatomoContext';

const FormComponent = () => {
  const { trackEvent } = useMatomoContext();
  
  const handleInputChange = (e) => {
    // Avoid tracking the actual value for privacy
    trackEvent('Form', 'Input Change', 'Username Field');
  };
  
  const handleInputBlur = (e) => {
    // Track when user finishes editing a field
    trackEvent('Form', 'Input Blur', 'Username Field');
  };
  
  return (
    <Input
      placeholder="Username"
      onChange={handleInputChange}
      onBlur={handleInputBlur}
    />
  );
};
```

**Track Form Submission:**

```tsx
import { Form, Button } from 'antd';
import { Input } from '@/components/shared/Input';
import { useMatomoContext } from '@/contexts/MatomoContext';

const ContactForm = () => {
  const { trackEvent } = useMatomoContext();
  const [form] = Form.useForm();
  
  const handleSubmit = (values) => {
    // Track the form submission
    trackEvent('Form', 'Submit', 'Contact Form', Object.keys(values).length);
    
    // Submit form data
    submitFormToAPI(values);
  };
  
  return (
    <Form form={form} onFinish={handleSubmit}>
      <Form.Item name="name" label="Name">
        <Input trackingId="name_field" />
      </Form.Item>
      <Form.Item name="email" label="Email">
        <Input trackingId="email_field" />
      </Form.Item>
      <Form.Item name="message" label="Message">
        <Input.TextArea rows={4} trackingId="message_field" />
      </Form.Item>
      <Button type="primary" htmlType="submit" trackingId="submit_contact_form">
        Submit
      </Button>
    </Form>
  );
};
```

## Using the TrackingProvider

The `TrackingProvider` allows you to configure tracking options for all components within its scope:

```tsx
import { TrackingProvider } from '@/components/shared/tracking';

const App = () => {
  return (
    <TrackingProvider
      eventNamePrefix="MyApp_"
      trackUserId={true}
      trackUrl={true}
      trackTime={true}
    >
      <Layout>
        <Header />
        <Content />
        <Footer />
      </Layout>
    </TrackingProvider>
  );
};
```

You can also use nested providers with different configurations:

```tsx
<TrackingProvider eventNamePrefix="MyApp_">
  <Layout>
    {/* These components will use the "MyApp_" prefix */}
    <Header />
    
    <TrackingProvider eventNamePrefix="Admin_">
      {/* These components will use the "Admin_" prefix */}
      <AdminPanel />
    </TrackingProvider>
    
    <Footer />
  </Layout>
</TrackingProvider>
```

## Using the Matomo Context

You can access the Matomo context in any component:

```tsx
import { useEffect } from 'react';
import { useMatomoContext } from '@/contexts/MatomoContext';

const MyComponent = () => {
  const { trackEvent, trackPageView, setUserId, isEnabled } = useMatomoContext();
  
  // Track login with enabled check
  const handleLogin = (userId) => {
    if (!isEnabled) return;
    
    setUserId(userId);
    trackEvent('User', 'Login');
  };
  
  // Track custom page view
  useEffect(() => {
    if (isEnabled) {
      trackPageView('My Custom Page');
    }
  }, [trackPageView, isEnabled]);
  
  return (
    <button onClick={() => handleLogin('user123')}>Login</button>
  );
};
```

## Available Tracking Functions

The following tracking functions are available via `useMatomoContext()`:

- `trackEvent(category, action, name?, value?)`: Track custom events
- `trackPageView(customTitle?, customUrl?)`: Track page views
- `trackSiteSearch(keyword, category?, resultsCount?)`: Track searches
- `trackGoal(goalId, conversionValue?)`: Track goal conversions
- `setCustomDimension(dimensionId, dimensionValue)`: Set custom dimensions
- `setUserId(userId)`: Set user ID for cross-device tracking
- `resetUserId()`: Reset user ID (e.g., on logout)
- `isEnabled`: Boolean indicating if tracking is currently enabled

## Best Practices for Tracking

1. **Use the trackingId prop when possible**:
   - This is the simplest and most consistent way to add tracking
   - Use descriptive IDs that indicate both component type and purpose (e.g., "login_submit_button")

2. **Be consistent with naming conventions**:
   - Use consistent categories, actions, and names across your application
   - Create a tracking taxonomy document if necessary

3. **Don't track personal information**:
   - Avoid tracking actual user input values
   - Track field names instead of field values
   - Use the form field's label or ID rather than its content

4. **Track important user actions**:
   - Focus on business-critical interactions
   - Track key conversion points in user journeys
   - Track form abandonment and error states

5. **Check isEnabled before tracking**:
   - Always check `isEnabled` before tracking to respect user privacy settings
   - Wrap tracking calls in conditional blocks

6. **Group related events**:
   - Use consistent categories for related UI elements
   - For example, all navigation events should use "Navigation" category
   - Use the eventNamePrefix for logical grouping

7. **Include contextual information**:
   - Enable URL tracking to see where interactions occurred
   - Use timestamp tracking to analyze user behavior over time
   - Add custom dimensions for additional context when needed

## Advanced Configuration

### Alternative Matomo Configurations

The Matomo component can load alternative configurations from localStorage:

```js
// Store alternative configuration in localStorage
localStorage.setItem('matomoAltConfig', JSON.stringify({
  siteId: '5',
  baseUrl: 'https://analytics-prod.example.com/',
  scriptUrl: 'https://analytics-prod.example.com/matomo.js'
}));

// Remove alternative configuration
localStorage.removeItem('matomoAltConfig');
```

### Debugging

The Matomo component has built-in error handling and debug logging. To enable:

```tsx
// In your App.tsx or configuration
<Matomo 
  enableDebug={true}
  // other props
/>
```

## Testing Matomo Tracking

To verify tracking is working correctly:

1. Open your browser's developer tools
2. Check the Network tab
3. Filter requests to "matomo.php"
4. Perform actions in the application
5. Verify tracking requests are being sent

You can also check the Matomo dashboard at https://analysic-uat.fundpark.com/ to see incoming data.

## Troubleshooting

If tracking isn't working:

1. Check browser console for any Matomo-related errors
2. Verify the Matomo server is accessible (the component will attempt to verify connectivity)
3. Check if tracking is disabled via configuration or environment settings
4. Use the debug mode to see detailed logs of tracking attempts
5. Test a direct tracking call: `window._paq.push(['trackPageView'])`

## User ID Tracking Implementation

The application includes comprehensive user ID tracking for cross-device user identification and advanced analytics.

### Automatic User ID Management

**Automatic Login Tracking**: User ID is automatically set when users log in successfully:
```tsx
// In login component - automatically handled
const { setUserId } = useMatomoContext();
const res = await LoginHandler(values);
if (res.message === "success") {
  setUserId(String(res.data.user.id)); // Automatic
}
```

**Automatic Logout Tracking**: User ID is automatically reset when users log out:
```tsx
// In store/common.ts - automatically handled
logout: () => {
  useMatomoStore.getState().resetMatomoUserId(); // Automatic
  // ... other logout logic
}
```

**Session Persistence**: User ID is automatically restored on page refresh if user is still logged in.

### Manual User ID Management

**Using the Dedicated Hook** (Recommended):
```tsx
import { useMatomoUserTracking } from '@/hooks/useMatomoUserTracking';

const MyComponent = () => {
  const { 
    currentUserId, 
    isUserTracked, 
    setUserId, 
    resetUserId,
    refreshUserTracking 
  } = useMatomoUserTracking();
  
  // Current user ID and tracking status are automatically available
  console.log('Current user ID:', currentUserId);
  console.log('Is user being tracked:', isUserTracked);
  
  // Manual methods available if needed
  const handleManualSet = () => setUserId('12345');
  const handleManualReset = () => resetUserId();
  const handleRefresh = () => refreshUserTracking();
};
```

**Using Context Methods**:
```tsx
import { useMatomoContext } from '@/contexts/MatomoContext';

const MyComponent = () => {
  const { setUserId, resetUserId } = useMatomoContext();
  
  const handleSetUser = () => setUserId('user123');
  const handleResetUser = () => resetUserId();
};
```

**Using Utility Functions Directly**:
```tsx
import { setUserId, resetUserId, getCurrentUserId, initializeUserId } from '@/utils/matomoTracking';

// Set user ID
setUserId('user123');

// Reset user ID
resetUserId();

// Get current user ID from localStorage
const currentId = getCurrentUserId();

// Initialize user ID from localStorage (useful on app startup)
initializeUserId();
```

### Automatic Component-Level Tracking

Components can automatically include user ID in their tracking:

```tsx
import { TrackingProvider } from '@/components/shared/tracking';

// Enable automatic user ID tracking for all components in this section
<TrackingProvider trackUserId={true}>
  <Button trackingId="my_button">Click me</Button>
  <Input trackingId="my_input" />
</TrackingProvider>
```

### Best Practices for User ID Tracking

1. **Let Automatic Systems Handle It**: The login/logout flow automatically manages user IDs
2. **Use the Dedicated Hook**: `useMatomoUserTracking` provides the most comprehensive interface
3. **Check Tracking Status**: Use `isUserTracked` to verify if user tracking is active
4. **Handle Edge Cases**: The system includes error handling and validation
5. **Respect Privacy**: User IDs are automatically reset on logout
6. **Debug When Needed**: Enhanced logging helps troubleshoot tracking issues

### Data Flow

1. **Login**: API returns user object → `setUserId(user.id)` → Matomo receives user ID
2. **Session**: User ID persists in Matomo across page loads
3. **Refresh**: App checks localStorage → automatically restores user ID if logged in
4. **Logout**: Store triggers reset → `resetUserId()` → Matomo clears user ID
5. **Components**: Automatic tracking includes user ID when `trackUserId` is enabled

### Error Handling and Validation

The improved implementation includes:
- **Input Validation**: Ensures user ID is a valid non-empty string
- **Error Logging**: Console warnings for debugging tracking issues
- **Graceful Degradation**: Continues working even if Matomo script fails to load
- **Type Safety**: TypeScript interfaces prevent common errors

### Troubleshooting User ID Tracking

**Check if user ID is set**:
```tsx
import { getCurrentUserId } from '@/utils/matomoTracking';
console.log('Current Matomo user ID:', getCurrentUserId());
```

**Verify tracking status**:
```tsx
import { useMatomoUserTracking } from '@/hooks/useMatomoUserTracking';
const { currentUserId, isUserTracked } = useMatomoUserTracking();
console.log('User ID:', currentUserId, 'Tracked:', isUserTracked);
```

**Force refresh tracking**:
```tsx
const { refreshUserTracking } = useMatomoUserTracking();
refreshUserTracking(); // Manually sync user ID with current login state
```

**Test logout behavior**:
```tsx
import { testMatomoLogout, debugMatomoState } from '@/utils/testMatomoLogout';

// Test the complete logout flow
testMatomoLogout();

// Debug current state
debugMatomoState();
```

### Common Issues and Solutions

**Issue: User ID still tracked after logout**
- **Cause**: Components using `trackUserId={true}` were reading user ID from localStorage without checking if user is actually logged in
- **Solution**: Fixed in v2.1 - `getCurrentUserId()` and tracking functions now verify access token exists before returning user ID
- **Verification**: Use `testMatomoLogout()` to verify the fix works correctly

**Issue: Events tracked with user ID even when logged out**
- **Cause**: The `withTracking` HOC was calling `getUserId()` which didn't check login status
- **Solution**: Updated `getUserId()` function to check for valid access token before returning user ID
- **Prevention**: Always use the provided tracking utilities rather than directly accessing localStorage 