# Matomo Configuration Guide

This guide explains how to configure Matomo analytics for different environments in the Account Portal application.

## Overview

The Matomo configuration automatically adapts based on the current environment (development, staging, production) and can be overridden using environment variables.

## Environment Detection

The configuration uses the following priority order to determine the environment:

1. `VITE_NODE_ENV` environment variable
2. Vite's `MODE` (set via `--mode` flag or automatically by Vite)
3. Defaults to `development` if neither is found

## Default Configurations

### Development
- **Site ID**: `3`
- **Base URL**: `https://analysic-dev.fundpark.com/`
- **Script URL**: `https://analysic-dev.fundpark.com/matomo.js`
- **Debug**: `true`
- **Disable in Development**: `true` (tracking disabled)

### Staging
- **Site ID**: `3`
- **Base URL**: `https://analysic-uat.fundpark.com/`
- **Script URL**: `https://analysic-uat.fundpark.com/matomo.js`
- **Debug**: `true`
- **Disable in Development**: `false`

### Production
- **Site ID**: `1`
- **Base URL**: `https://analysic.fundpark.com/`
- **Script URL**: `https://analysic.fundpark.com/matomo.js`
- **Debug**: `false`
- **Disable in Development**: `false`

## Environment Variables

You can override any configuration using environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_NODE_ENV` | Force specific environment | `development`, `staging`, `production` |
| `VITE_MATOMO_SITE_ID` | Matomo site ID | `1`, `2`, `3` |
| `VITE_MATOMO_BASE_URL` | Matomo installation URL | `https://analytics.example.com/` |
| `VITE_MATOMO_SCRIPT_URL` | Matomo script URL | `https://analytics.example.com/matomo.js` |
| `VITE_MATOMO_DEBUG` | Enable debug logging | `true`, `false` |
| `VITE_MATOMO_DISABLE_IN_DEV` | Disable tracking in development | `true`, `false` |

## Usage Examples

### Running with Different Environments

Using the package.json scripts (recommended):

```bash
# Development (default)
npm run dev

# Staging environment  
npm run dev:staging

# Production environment (for testing)
npm run dev:production

# Building for different environments
npm run build:development
npm run build:staging
npm run build:production

# Legacy SIT build (maps to staging)
npm run build:sit
```

Or using Vite directly with mode flags:

```bash
# Development (default)
npm run dev

# Staging
npm run dev -- --mode staging

# Production
npm run build -- --mode production
```

### Using Environment Files

Create environment-specific files:

**`.env.development`**
```env
VITE_MATOMO_DEBUG=true
VITE_MATOMO_DISABLE_IN_DEV=true
```

**`.env.staging`**
```env
VITE_MATOMO_SITE_ID=3
VITE_MATOMO_BASE_URL=https://analysic-uat.fundpark.com/
VITE_MATOMO_DEBUG=true
```

**`.env.production`**
```env
VITE_MATOMO_SITE_ID=1
VITE_MATOMO_BASE_URL=https://analysic.fundpark.com/
VITE_MATOMO_DEBUG=false
```

### Override for Local Development

Create `.env.local` to override settings for your local environment:

```env
# Use a different Matomo instance for local testing
VITE_MATOMO_BASE_URL=https://my-test-analytics.com/
VITE_MATOMO_SITE_ID=999
VITE_MATOMO_DEBUG=true
```

## Package.json Integration

The Matomo configuration is fully integrated with your existing package.json scripts. The environment is automatically detected from the Vite `--mode` flag used in your scripts.

### Root package.json scripts:
```json
{
  "scripts": {
    "dev": "turbo run dev --filter=@fundpark/account-portal-ui",
    "dev:staging": "turbo run dev:staging --filter=@fundpark/account-portal-ui",
    "dev:production": "turbo run dev:production --filter=@fundpark/account-portal-ui",
    "build:development": "turbo run build:development --filter=@fundpark/account-portal-ui",
    "build:staging": "turbo run build:staging --filter=@fundpark/account-portal-ui",
    "build:production": "turbo run build:production --filter=@fundpark/account-portal-ui"
  }
}
```

### Account Portal package.json scripts:
```json
{
  "scripts": {
    "dev": "vite --mode development",
    "dev:staging": "vite --mode staging",
    "dev:production": "vite --mode production",
    "build:development": "vite build --mode development",
    "build:sit": "vite build --mode staging",
    "build:staging": "vite build --mode staging",
    "build:production": "vite build --mode production"
  }
}
```

**Note:** The `build:sit` script is kept for backward compatibility and maps to the staging environment.

## Debugging

When debug mode is enabled, the configuration will log the current settings to the browser console:

```
[Matomo] Using configuration for environment: staging
{
  siteId: "3",
  baseUrl: "https://analysic-uat.fundpark.com/",
  scriptUrl: "https://analysic-uat.fundpark.com/matomo.js",
  enableDebug: true,
  disableInDevelopment: false
}
```

## Security Notes

- Environment variables starting with `VITE_` are exposed to the client-side code
- Never put sensitive information in these variables
- The Matomo URLs and site IDs are safe to expose as they're public analytics endpoints

## Troubleshooting

### Configuration Not Loading
1. Check that environment variables are prefixed with `VITE_`
2. Restart the development server after changing environment variables
3. Check the browser console for debug logs when `VITE_MATOMO_DEBUG=true`

### Wrong Environment Detected
1. Verify the `MODE` or `VITE_NODE_ENV` values
2. Check that the environment name matches exactly: `development`, `staging`, or `production`
3. Use debug mode to see which environment is being detected

### Tracking Not Working
1. Check if `disableInDevelopment` is set to `true` in development mode
2. Verify the Matomo URLs are accessible
3. Check browser network tab for failed requests to Matomo endpoints 