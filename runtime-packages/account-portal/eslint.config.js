import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tailwindcss from 'eslint-plugin-tailwindcss'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [
		js.configs.recommended,
		...tseslint.configs.recommended,
		"plugin:tailwindcss/recommended"
	],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
	  'tailwindcss': tailwindcss,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
	  "tailwindcss/classnames-order": "warn",
	  "tailwindcss/no-custom-classname": "warn",
      "tailwindcss/no-contradicting-classname": "error",
	  "@typescript-eslint/no-explicit-any": "off"
    },
  },
)
