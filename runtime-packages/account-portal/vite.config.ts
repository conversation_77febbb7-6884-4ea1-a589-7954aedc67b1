import { fileURLToPath } from "node:url";
import { dirname, resolve } from "node:path";
import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import svgr from "vite-plugin-svgr";
// import viteCompression from "vite-plugin-compression";
import visualizer from "rollup-plugin-visualizer";
import tsconfigPaths from "vite-tsconfig-paths";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
    const env = loadEnv(mode, process.cwd(), "");
    const isBuild = command === "build";

    const plugins = [
        react(),
        svgr({
            svgrOptions: {
                svgo: true,
                plugins: ["@svgr/plugin-svgo", "@svgr/plugin-jsx"],
                svgoConfig: {
                    floatPrecision: 2,
                    plugins: [
                        {
                            name: "cleanupIds",
                            params: {
                                minify: false
                            }
                        }
                    ]
                },
                memo: true
            }
        }),
        tsconfigPaths(),
        ...[]
        // ...(isBuild && mode === "staging" ? [visualizer()] : [])
    ].filter(Boolean);

    return {
        base: "/",
        plugins,
        resolve: {
            alias: {
                "@": resolve(__dirname, "./src")
            }
        },
        css: {
            modules: {
                generateScopedName: "[local]___[hash:base64:5]",
                localsConvention: "camelCaseOnly"
            },
        },
        server: {
            proxy: {
                "^/": {
                    target: "https://account-uat.fundpark.com",
                    changeOrigin: true,
                }
            }
        },
        build: {
            // sourcemap: true,
            rollupOptions: {
                input: {
                    index: resolve(__dirname, "index.html")
                },
                output: {
                    chunkFileNames: "financing/static/js/[name]-[hash].js",
                    entryFileNames: "financing/static/js/[name]-[hash].js",
                    assetFileNames: "financing/static/[ext]/[name]-[hash].[ext]",
                    manualChunks(id) {
                        if (id.includes("node_modules")) {
                            return "common-chunk";
                        }
                    }
                }
            }
        }
    };
});
