# Environment Configuration
# Copy this file to .env.local, .env.development, .env.staging, or .env.production
# and update the values according to your environment

# Environment type (optional - will use Vite's MODE if not specified)
# Options: development, staging, production
# VITE_NODE_ENV=development

# Matomo Configuration (optional - will use defaults from config if not specified)
# VITE_MATOMO_SITE_ID=3
# VITE_MATOMO_BASE_URL=https://analysic-dev.fundpark.com/
# VITE_MATOMO_SCRIPT_URL=https://analysic-dev.fundpark.com/matomo.js
# VITE_MATOMO_DEBUG=true
# VITE_MATOMO_DISABLE_IN_DEV=true 