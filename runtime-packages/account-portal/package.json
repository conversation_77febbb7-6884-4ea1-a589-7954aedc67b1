{"name": "@fundpark/account-portal-ui", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --mode development", "dev:staging": "vite --mode staging", "dev:production": "vite --mode production", "build:development": "vite build --mode development", "build:developmentAlt": "vite build --mode developmentAlt", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "preview": "vite preview", "geni18n:excel": "i18n-utils generate ./src -f i18n_translations.xlsx", "geni18n:json": "i18n-utils parse ./src -f i18n_translations.xlsx"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@fundpark/base-types": "workspace:*", "@fundpark/fp-api": "workspace:*", "@fundpark/ui-utils": "workspace:*", "antd": "~5.23.0", "blueimp-md5": "^2.19.0", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "es-toolkit": "^1.31.0", "i18next": "^24.2.0", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "rc-motion": "^2.9.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.28.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@fundpark/i18n-utils": "workspace:*", "@svgr/plugin-jsx": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "@types/blueimp-md5": "^2.18.2", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.6", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "eslint-plugin-tailwindcss": "^3.17.5", "globals": "^15.14.0", "postcss": "^8", "rollup-plugin-visualizer": "^5.13.1", "sass": "^1.85.1", "tailwindcss": "^3.4.1", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "typescript-plugin-css-modules": "^5.1.0", "vite": "^6.0.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}}