// src/store/limitStore.ts
import {create} from "zustand";
import {getSignerInfo, getUserCreditApplication, getUserLimits} from "@fundpark/fp-api";
import type {GetCreditApplicationRes, GetLimitRes} from "@fundpark/fp-api/types/limits.ts";

interface LimitState {
    creditApplication: GetCreditApplicationRes | null | undefined;
    creditLimit: GetLimitRes | null;
    isFinishDocSign: boolean;
    isCalculatingLimit: boolean;
    isCalculatingLimitAfterFirstDrawdown: boolean;
    hasLimitIncrease: boolean;

    isLoadingLimits: boolean;
    isLoadingApplication: boolean;
    error: string | null;

    hasFetchedLimits: boolean;
    hasFetchedApplication: boolean;
    hasFetchedIsFinishDocSign: boolean;

    fetchUserLimits: (force?: boolean) => Promise<void>;
    fetchUserCreditApplication: (force?: boolean) => Promise<void>;
    fetchDocSignStatus: (force?: boolean) => Promise<void>;

    clear: () => void;
}

export const useLimitStore = create<LimitState>((set, get) => ({
    creditLimit: null,
    creditApplication: undefined,
    isFinishDocSign: false,
    isCalculatingLimit: false,
    isCalculatingLimitAfterFirstDrawdown: false,
    hasLimitIncrease: false,

    isLoadingLimits: false,
    isLoadingApplication: false,
    error: null,

    hasFetchedLimits: false,
    hasFetchedApplication: false,
    hasFetchedIsFinishDocSign: false,


    fetchUserLimits: async (force = false) => {
        const { hasFetchedLimits, isLoadingLimits } = get();
        if (!force && (hasFetchedLimits || isLoadingLimits)) {
            return;
        }

        set({ isLoadingLimits: true, error: null });
        try {
            const response = await getUserLimits(null);
            if (response.code !== 0 ) return;
            const data = response.data;
            set({
                creditLimit: data,
                hasFetchedLimits: true,
            });
        } catch (err: any) {
            // set({ error: err.message || "Failed to load limits" });
        } finally {
            set({ hasFetchedLimits: true, isLoadingLimits: false });
        }
    },

    fetchUserCreditApplication: async (force = false) => {
        const {hasFetchedApplication, isLoadingApplication} = get();
        if (!force && (hasFetchedApplication || isLoadingApplication)) {
            return;
        }

        set({isLoadingApplication: true, error: null});
        try {
            const response = await getUserCreditApplication(null);
            const res = response.data;
            set({
                creditApplication: res,
                hasFetchedApplication: true,
                isCalculatingLimit: res?.status === 'submitted' && res?.pre_limit == null,
                isCalculatingLimitAfterFirstDrawdown: res?.status === 'submitted' && res?.pre_limit !== null,
                hasLimitIncrease: res?.type === "limit_increase" && !["active", "failed"].includes(res?.status),
            });
        } catch (err: any) {
            set({
                error: err.message || "Failed to load application",
                isCalculatingLimit: false,
                isCalculatingLimitAfterFirstDrawdown: false,
            });
        } finally {
            set({
                isLoadingApplication: false,
                hasFetchedApplication: true,
            });
        }
    },

    fetchDocSignStatus: async (force = false) => {
        const {hasFetchedIsFinishDocSign, isFinishDocSign} = get();
        if (!force && (hasFetchedIsFinishDocSign || isFinishDocSign)) {
            return;
        }

        try {
            const res = await getSignerInfo();
            // @ts-ignore
            const allSigned = res.data.every(person => person.sign_status === 2);
            // @ts-ignore
            if (res.code === 0 && res.data.length > 0 && allSigned) {
                set({
                    isFinishDocSign: true,
                });
            }else {
                set({
                    isFinishDocSign: false,
                });
            }
        } catch (err: any) {
            set({error: err.message || "Failed to load application"});
        } finally {
            set({isLoadingApplication: false});
        }

    },

    clear: () => set({
        creditLimit: null,
        creditApplication: undefined,
        isFinishDocSign: false,
        isCalculatingLimit: false,
        isCalculatingLimitAfterFirstDrawdown: false,
        hasLimitIncrease: false,

        isLoadingLimits: false,
        isLoadingApplication: false,
        error: null,

        hasFetchedLimits: false,
        hasFetchedApplication: false,
        hasFetchedIsFinishDocSign: false,
    })
}));
