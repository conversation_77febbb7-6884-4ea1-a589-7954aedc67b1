export interface ScenarioConfig {
    id: number;
    type: "xdj" | "automation" | "limit_increase" | null;
    status: "active" | "pending" | "submitted" | "failed" | "deprecated" | null;
    stage:
        | "calculating"
        | "account_info"
        | "info_confirm"
        | "info_processing"
        | "signing"
        | "waiting_result"
        | "loaning"
        | "finished"
        | null;
    has_pre_limit?: boolean | "any";
    showAddButton: boolean;
    showBackButton: boolean;
    showNextButton: boolean;
    showSubmitButton: boolean;
}

export const SCENARIO_CONFIGS: ScenarioConfig[] = [
    {
        id: 9,
        type: "automation",
        status: "submitted",
        stage: "waiting_result",
        has_pre_limit: true,
        showAddButton: false,
        showBackButton: true,
        showNextButton: false,
        showSubmitButton: false,
    },
    {
        id: 10,
        type: "automation",
        status: "active",
        stage: "loaning",
        has_pre_limit: true,
        showAddButton: true,
        showBackButton: true,
        showNextButton: false,
        showSubmitButton: true,
    },
    {
        id: 11,
        type: "automation",
        status: "failed",
        stage: "info_processing",
        has_pre_limit: 'any',
        showAddButton: false,
        showBackButton: false,
        showNextButton: false,
        showSubmitButton: false,
    },
    {
        id: 12,
        type: "automation",
        status: "failed",
        stage: "finished",
        has_pre_limit: 'any',
        showAddButton: false,
        showBackButton: false,
        showNextButton: false,
        showSubmitButton: false,
    },
    {
        id: 13,
        type: "limit_increase",
        status: "pending",
        stage: "calculating",
        has_pre_limit: false,
        showAddButton: false,
        showBackButton: true,
        showNextButton: false,
        showSubmitButton: false,
    },
    {
        id: 14,
        type: "limit_increase",
        status: "pending",
        stage: "calculating",
        has_pre_limit: true,
        showAddButton: false,
        showBackButton: true,
        showNextButton: false,
        showSubmitButton: false,
    },
    {
        id: 15,
        type: "limit_increase",
        status: "pending",
        stage: "calculating",
        has_pre_limit: true,
        showAddButton: false,
        showBackButton: false,
        showNextButton: true,
        showSubmitButton: false,
    },
    {
        id: 16,
        type: "limit_increase",
        status: "submitted",
        stage: "waiting_result",
        has_pre_limit: true,
        showAddButton: false,
        showBackButton: false,
        showNextButton: true,
        showSubmitButton: false,
    },
    {
        id: 17,
        type: "limit_increase",
        status: "failed",
        stage: null,
        has_pre_limit: "any",
        showAddButton: true,
        showBackButton: true,
        showNextButton: false,
        showSubmitButton: true,
    },
    {
        id: 18,
        type: "limit_increase",
        status: "active",
        stage: "finished",
        has_pre_limit: "any",
        showAddButton: true,
        showBackButton: true,
        showNextButton: false,
        showSubmitButton: true,
    },
]; 