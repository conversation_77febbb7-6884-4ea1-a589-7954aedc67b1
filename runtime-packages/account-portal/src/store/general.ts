import { create } from "zustand";
import type { LoginMethod } from "@/types/common/LoginMethodSwitcher/loginMethodSwitcher.types.ts";

interface GeneralState {
  authModalOpen: boolean;
  loginMethod: LoginMethod;
  setAuthModalOpen: (open: boolean) => void;
  setLoginMethod: (method: LoginMethod) => void;
}

export const useGeneralStore = create<GeneralState>(set => ({
  authModalOpen: false,
  loginMethod: "login",
  setAuthModalOpen: (open: boolean) => set({ authModalOpen: open }),
  setLoginMethod: (loginMethod: LoginMethod) => set({ loginMethod }),
}));
