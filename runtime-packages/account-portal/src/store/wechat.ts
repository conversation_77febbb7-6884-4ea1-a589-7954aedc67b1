// src/store/limitStore.ts
import {create} from "zustand";
import {getCpidInfo} from "@fundpark/fp-api";

interface WechatState {
    wechatBase64: string | null;
    cpid: string | null;

    isLoadingWechatSrc: boolean;

    hasFetchedWechatSrc: boolean;

    fetchWechatSrc: () => Promise<void>;
    setCpid: (cpid: string | null) => void;

    clear: () => void;
}

export const useWechatStore = create<WechatState>((set, get) => ({
    wechatBase64: null,
    isLoadingWechatSrc: false,
    hasFetchedWechatSrc: false,
    cpid: null,


    fetchWechatSrc: async () => {
        const { hasFetchedWechatSrc, isLoadingWechatSrc, cpid } = get();
        if (hasFetchedWechatSrc || isLoadingWechatSrc || !cpid) {
            return;
        }

        set({ isLoadingWechatSrc: true });
        try {
            const response = await getCpidInfo({id: cpid});
            if (response.code !== 0 ) return;
            const data = response.data;
            set({
                wechatBase64: data.cs_qrcode,
                hasFetchedWechatSrc: true,
            });
        } catch (err: any) {

        } finally {
            set({ hasFetchedWechatSrc: true, isLoadingWechatSrc: false });
        }
    },

    setCpid: (cpid: string | null) => {
        set({ cpid });
    },

    clear: () => set({
        wechatBase64: null,
        isLoadingWechatSrc: false,
        hasFetchedWechatSrc: false,
    })
}));
