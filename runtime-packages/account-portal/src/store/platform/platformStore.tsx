import {create} from "zustand";
import {IPlatform} from "@/types/platforms/platforms.ts";
import {ICON_MAPPING} from "@/constants/platforms.ts";
import {getPlatformList} from "@fundpark/fp-api";

interface PlatformStoreState {
    platforms: IPlatform[];
    isFetched: boolean;
    fetchPlatforms: () => Promise<void>;

    clear: () => void;
    isLoading: boolean;
}

export const usePlatformStore = create<PlatformStoreState>((set, get) => ({
    platforms: [],
    isFetched: false,
    isLoading: false,

    fetchPlatforms: async () => {
        if (get().isFetched || get().isLoading) return;
        set({isLoading: true});

        try {
            const res = await getPlatformList({});
            if (res.code === 0) {
                const mappedPlatforms = res.data.map((item: any) => ({
                    id: item.id,
                    platform: item.platform,
                    partner_abbr: item.partner_abbr,
                    action: item.action,
                    is_callback: item.is_callback,
                    icon: ICON_MAPPING[item.platform] || "",
                    sites: item?.sites || null,
                }));
                set({platforms: mappedPlatforms, isFetched: true});
            }
        } catch (error) {
            console.error("Error fetching platforms", error);
        } finally {
            set({isLoading: false});
        }
    },

    clear: () => set({
        platforms: [],
        isFetched: false
    })
}));
