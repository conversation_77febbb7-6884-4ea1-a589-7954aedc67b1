import { create } from "zustand";
import { IPsp } from "@/types/platforms/psp.ts";
import { SHORT_ICON_MAPPING as ICON_MAPPING } from "@/constants/psp.ts";
import { getPaymentGatewayList } from "@fundpark/fp-api";

interface PspStoreState {
    payment_gateway: IPsp[];
    isFetched: boolean;
    fetchPaymentGateway: (forceRefresh?: boolean) => Promise<void>;

    isLoading: boolean;
    clear: () => void;
}

export const usePaymentGatewayStore = create<PspStoreState>((set, get) => ({
    payment_gateway: [],
    isFetched: false,
    isLoading: false,

    fetchPaymentGateway: async (forceRefresh = false) => {
        if (!forceRefresh && (get().isFetched || get().isLoading)) return;
        set({ isLoading: true });
        if (forceRefresh) {
            set({ isFetched: false });
        }

        try {
            const res = await getPaymentGatewayList({});
            if (res.code === 0) {
                const mappedPSPs = res.data.map((item: any) => ({
                    id: item.id,
                    platform: item.platform,
                    partner_abbr: item.partner_abbr,
                    action: item.action,
                    icon: ICON_MAPPING[item.platform] || "",
                    is_callback: item.is_callback
                }));
                set({ payment_gateway: mappedPSPs, isFetched: true });
            }
        } catch (error) {
            console.error("Error fetching PSPs", error);
        } finally {
            set({ isLoading: false });
        }
    },

    clear: () =>
        set({
            payment_gateway: [],
            isFetched: false,
            isLoading: false
        })
}));
