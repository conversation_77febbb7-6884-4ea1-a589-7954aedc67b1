import { create } from "zustand";

interface secondDrawdownFormData {
  drawdownAmount: number;
  drawdownCurrency: string;
  bankId: number;
  refNumber: string;
}

interface secondDrawdownFormStore {
  formData: secondDrawdownFormData; 
  setFormData: (data: secondDrawdownFormData) => void; 
}

export const usesecondDrawdownFormStore = create<secondDrawdownFormStore>((set) => ({
  formData: {
    drawdownAmount: 0,
    drawdownCurrency: "",
    bankId: 0,
    refNumber: "",
  },
  setFormData: (data) => set({ formData: data }),
}));