import {create} from "zustand";
import type {AccessInfo} from "@/types/common/accessInfo";
import {UserInfo} from '@fundpark/fp-api/types/login.ts'
import {CompanyNotification} from '@fundpark/fp-api/types/notifications.ts'
import {getOauthShopList, GetUserInfo} from "@fundpark/fp-api";
import type { GetOauthShopListRes } from "@fundpark/fp-api";
import {SHORT_ICON_MAPPING} from "@/constants/psp.ts";
import {useMatomoStore} from "@/hooks/useMatomoStore";


interface CommonStore {
    userInfo: UserInfo | null;
    accessToken: string | null;
    isLogin: boolean;
    notificationList: CompanyNotification[] | null;

    setUserInfo: (user: UserInfo | null) => void;
    setNotifications: (notifications: CompanyNotification[] | null) => void;
    setAccessToken: (token: AccessInfo | null) => void;
    logout: () => void;
    refreshUserInfo: () => void;
    refreshNotifications: () => void;

    authedPlatforms: GetOauthShopListRes | null;
    fetchAuthedList: (forceUpdate?: boolean) => Promise<void>;
    isFetchedAuthedList: boolean;
    isAnyShopAuthed: boolean;

    isLoadingAuthedList: boolean;
}

// @ts-ignore
export const useCommonStore = create<CommonStore>((set, get) => {
    const storedUser = localStorage.getItem("userInfo");
    const userInfo: UserInfo | null = storedUser ? JSON.parse(storedUser) : null;
    const token = localStorage.getItem("accessToken");
    const isLogin = Boolean(token);
    const notificationList: CompanyNotification[] | null = null;

    return {
        userInfo: userInfo,
        accessToken: token,
        isLogin,
        notificationList: notificationList,
        isLoadingAuthedList: false,

        setUserInfo: (user) => {
            if (user) {
                localStorage.setItem("userInfo", JSON.stringify(user));
                // Automatically set Matomo user ID when user info is set
                // Only set if there's also a valid access token (user is actually logged in)
                const accessToken = localStorage.getItem("accessToken");
                if (user.id && window._paq && accessToken) {
                    window._paq.push(['setUserId', String(user.id)]);
                }
            } else {
                localStorage.removeItem("userInfo");
                // Reset Matomo user ID when user info is cleared
                if (window._paq) {
                    window._paq.push(['resetUserId']);
                }
            }
            set({userInfo: user});
        },

        setAccessToken: (tokenValue) => {
            if (tokenValue) {
                localStorage.setItem("accessToken", tokenValue.accessToken);
            } else {
                localStorage.removeItem("accessToken");
            }
            set({accessToken: tokenValue?.accessToken, isLogin: Boolean(tokenValue)});
        },

        logout: () => {
            // Reset Matomo user ID
            useMatomoStore.getState().resetMatomoUserId();

            localStorage.removeItem("accessToken");
            localStorage.removeItem("userInfo");
            localStorage.removeItem("hasSeenAuthModal")

            set({
                userInfo: null,
                accessToken: null,
                isLogin: false,
                isFetchedAuthedList: false,
                authedPlatforms: null,
                isAnyShopAuthed: undefined,
            });
        },

        isFetchedAuthedList: false,
        authedPlatforms: null,

        fetchAuthedList: async (forceUpdate = false) => {
            if (get().isLoadingAuthedList) {
                return;
            }

            if (get().isFetchedAuthedList && !forceUpdate) {
                return;
            }

            if (!get().isLogin) {
                return;
            }

            set({isLoadingAuthedList: true});

            try {
                console.log("doing fetch")
                const res = await getOauthShopList({});

                if (res.code === 0) {
                    // @ts-ignore
                    res.data.psp.forEach(item => {
                        item.icon = SHORT_ICON_MAPPING[item.platform] || "";
                    });

                    // @ts-ignore
                    set({authedPlatforms: res.data});
                    set({isFetchedAuthedList: true});

                    // @ts-ignore
                    const hasNoPlatforms = res.data.psp.length === 0 && res.data.shop.length === 0;
                    if (hasNoPlatforms) {
                        set({isAnyShopAuthed: false});
                    } else {
                        set({isAnyShopAuthed: true});
                    }

                } else {
                    console.error("Oauth list error:", res.message);
                }
            } catch (error) {
                console.error("Error fetching authorized platforms", error);
            } finally {
                set({isLoadingAuthedList: false});
            }
        },

        refreshUserInfo: async () => {
            const res = await GetUserInfo(null);
            if (res.code === 0) {
                set({userInfo: res.data.user});
                localStorage.setItem("userInfo", JSON.stringify(res.data.user));
            } else {
                console.error("Error fetching user info:", res.message);
            }
        },

        setNotifications: (notifications) => {
            set({notificationList: notifications});
        },
    };
});