import { create } from 'zustand';
import { getBusinessInfo, submitBusinessInfo } from '@fundpark/fp-api';
import { mapBusinessInfoApiToUi, mapBusinessInfoFormToApi } from '@/mappers/businessInfo';
import { BusinessInfoFormData } from '@/components/DrawdownForm/BusinessInfo/types';

interface CAABusinessInfoState {
    formData: BusinessInfoFormData;
    isLoading: boolean;
    error: string | null;
    hasFetchedData: boolean;
    isConfirmed: boolean;
    currentSubStep: number; // 0 for AuthShop, 1 for BusinessInfo

    // Actions
    setFormData: (data: Partial<BusinessInfoFormData>) => void;
    setIsConfirmed: (confirmed: boolean) => void;
    setCurrentSubStep: (step: number) => void;
    fetchBusinessInfo: (force?: boolean) => Promise<void>;
    submitBusinessInfo: () => Promise<{
        success: boolean;
        error?: string;
        status?: number;
    }>;
    resetStore: () => void;
}

const initialFormData: BusinessInfoFormData = {
    topBuyerCountries: [],
    topSupplierCountries: [],
    fundSourceCountries: '',
    mainProducts: '',
    industry: '',
    initialWealthSources: [],
    ongoingIncomeSources: [],
    fundingSources: [],
};

export const useCAABusinessInfoStore = create<CAABusinessInfoState>((set, get) => ({
    formData: initialFormData,
    isLoading: false,
    error: null,
    hasFetchedData: false,
    isConfirmed: false,
    currentSubStep: 0,

    setFormData: (data) => {
        set((state) => ({
            formData: {
                ...state.formData,
                ...data
            }
        }));
    },

    setIsConfirmed: (confirmed) => {
        set({ isConfirmed: confirmed });
    },

    setCurrentSubStep: (step) => {
        set({ currentSubStep: step });
    },

    fetchBusinessInfo: async (force = false) => {
        const { hasFetchedData, isLoading } = get();
        if (!force && (hasFetchedData || isLoading)) {
            return;
        }

        set({ isLoading: true, error: null });

        try {
            const response = await getBusinessInfo();
            
            if (response.status === 0 && response.data) {
                const uiData = mapBusinessInfoApiToUi(response);
                set({
                    formData: {
                        ...get().formData,
                        ...uiData
                    },
                    hasFetchedData: true,
                });
            } else {
                throw new Error(response.message || 'Failed to fetch business info data');
            }
        } catch (err: any) {
            set({ error: err.message || 'Failed to fetch business info data' });
        } finally {
            set({ isLoading: false });
        }
    },

    submitBusinessInfo: async () => {
        const { formData } = get();
        set({ isLoading: true, error: null });

        try {
            const apiData = mapBusinessInfoFormToApi(formData);
            const response = await submitBusinessInfo(apiData);

            if (response.status === 0) {
                return { success: true, status: response.status };
            } else {
                return {
                    success: false,
                    error: response.message || 'Failed to submit business info',
                    status: response.status
                };
            }
        } catch (err: any) {
            set({ error: err.message || 'Failed to submit business info' });
            return {
                success: false,
                error: err.message || 'Failed to submit business info'
            };
        } finally {
            set({ isLoading: false });
        }
    },

    resetStore: () => {
        set({
            formData: initialFormData,
            isLoading: false,
            error: null,
            hasFetchedData: false,
            isConfirmed: false,
            currentSubStep: 0,
        });
    },
})); 