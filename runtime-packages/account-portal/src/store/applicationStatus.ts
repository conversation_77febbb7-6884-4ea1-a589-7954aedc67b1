import { create } from "zustand";
import { getUserCreditApplication, getUserLimits } from "@fundpark/fp-api";
import type { GetCreditApplicationRes, GetLimitRes } from "@fundpark/fp-api/types/limits.ts";
import { SCENARIO_CONFIGS, type ScenarioConfig } from "./scenarioConfigs";

interface ApplicationStatusState {
    // Data state
    creditApplication: GetCreditApplicationRes | null | undefined;
    creditLimit: GetLimitRes | null;
    type: "xdj" | "automation" | "limit_increase" | null;
    status: "active" | "pending" | "submitted" | "failed" | "deprecated" | null;
    stage:
        | "calculating"
        | "account_info"
        | "info_confirm"
        | "info_processing"
        | "signing"
        | "waiting_result"
        | "loaning"
        | "finished"
        | null;
    pre_limit: number | null;

    // Output state - Button visibility
    showAddButton: boolean;
    showBackButton: boolean;
    showNextButton: boolean;
    showSubmitButton: boolean;
    matchedScenarioId: number | null; // For debugging which scenario was matched

    // Loading states
    isLoadingApplication: boolean;
    isLoadingLimits: boolean;
    isLoading: boolean;

    // Error states
    error: string | null;

    // Fetch flags
    hasFetchedApplication: boolean;
    hasFetchedLimits: boolean;

    // API methods
    fetchCreditApplication: (force?: boolean) => Promise<void>;
    fetchCreditLimit: (force?: boolean) => Promise<void>;
    fetchAllData: (force?: boolean) => Promise<void>;

    // Output computation
    computeOutputs: () => void;

    // Utility methods
    clear: () => void;
}

export const useApplicationStatusStore = create<ApplicationStatusState>((set, get) => ({
    // Initial data state
    creditApplication: undefined,
    creditLimit: null,
    type: null,
    status: null,
    stage: null,
    pre_limit: null,

    // Initial output state - Button visibility
    showAddButton: false,
    showBackButton: false,
    showNextButton: false,
    showSubmitButton: false,
    matchedScenarioId: null,

    // Initial loading states
    isLoadingApplication: false,
    isLoadingLimits: false,
    isLoading: false,

    // Initial error state
    error: null,

    // Initial fetch flags
    hasFetchedApplication: false,
    hasFetchedLimits: false,

    fetchCreditApplication: async (force = false) => {
        const { hasFetchedApplication, isLoadingApplication } = get();
        if (!force && (hasFetchedApplication || isLoadingApplication)) {
            return;
        }

        set({ isLoadingApplication: true, error: null });
        try {
            const response = await getUserCreditApplication(null);
            const creditApplication = response.data;

            set({
                creditApplication,
                type: creditApplication?.type || null,
                status: creditApplication?.status || null,
                stage: creditApplication?.stage || null,
                pre_limit: creditApplication?.pre_limit || null,
                hasFetchedApplication: true
            });

            // Recompute outputs after data update
            get().computeOutputs();
        } catch (err: any) {
            set({ error: err.message || "Failed to load credit application" });
        } finally {
            set({ isLoadingApplication: false });
        }
    },

    fetchCreditLimit: async (force = false) => {
        const { hasFetchedLimits, isLoadingLimits } = get();
        if (!force && (hasFetchedLimits || isLoadingLimits)) {
            return;
        }

        set({ isLoadingLimits: true, error: null });
        try {
            const response = await getUserLimits(null);
            if (response.code !== 0) return;

            const creditLimit = response.data;
            set({
                creditLimit,
                hasFetchedLimits: true
            });

            // Recompute outputs after data update
            get().computeOutputs();
        } catch (err: any) {
            set({ error: err.message || "Failed to load credit limits" });
        } finally {
            set({ isLoadingLimits: false });
        }
    },

    fetchAllData: async (force = false) => {
        set({ isLoading: true });
        try {
            await Promise.all([get().fetchCreditApplication(force), get().fetchCreditLimit(force)]);
        } finally {
            set({ isLoading: false });
        }
    },

    computeOutputs: () => {
        const { type, status, stage, pre_limit } = get();

        // Find matching scenario using configuration-driven approach
        const matchedScenario = findMatchingScenario(type, status, stage, pre_limit);

        if (matchedScenario) {
            set({
                showAddButton: matchedScenario.showAddButton,
                showBackButton: matchedScenario.showBackButton,
                showNextButton: matchedScenario.showNextButton,
                showSubmitButton: matchedScenario.showSubmitButton,
                matchedScenarioId: matchedScenario.id
            });
        } else {
            // Fallback for unmatched scenarios
            set({
                showAddButton: false,
                showBackButton: false,
                showNextButton: false,
                showSubmitButton: false,
                matchedScenarioId: null
            });
        }
    },

    clear: () =>
        set({
            // Clear data
            creditApplication: undefined,
            creditLimit: null,
            type: null,
            status: null,
            stage: null,
            pre_limit: null,

            // Clear output state - Button visibility
            showAddButton: false,
            showBackButton: false,
            showNextButton: false,
            showSubmitButton: false,
            matchedScenarioId: null,

            // Clear loading states
            isLoadingApplication: false,
            isLoadingLimits: false,
            isLoading: false,

            // Clear error
            error: null,

            // Clear fetch flags
            hasFetchedApplication: false,
            hasFetchedLimits: false
        })
}));

// SCENARIO MATCHING LOGIC
export function findMatchingScenario(
    type: "xdj" | "automation" | "limit_increase" | null,
    status: "active" | "pending" | "submitted" | "failed" | "deprecated" | null,
    stage:
        | "calculating"
        | "account_info"
        | "info_confirm"
        | "info_processing"
        | "signing"
        | "waiting_result"
        | "loaning"
        | "finished"
        | null,
    pre_limit: number | null
): ScenarioConfig | null {
    const has_pre_limit = pre_limit != null;

    // Sort by id for consistent ordering
    const sortedScenarios = [...SCENARIO_CONFIGS].sort((a, b) => a.id - b.id);

    // Find first matching scenario
    for (const scenario of sortedScenarios) {
        if (
            (scenario.type === type || scenario.type === null) &&
            (scenario.status === status || scenario.status === null) &&
            (scenario.stage === stage || scenario.stage === null) &&
            (scenario.has_pre_limit === "any" ||
                scenario.has_pre_limit === has_pre_limit ||
                scenario.has_pre_limit === undefined)
        ) {
            return scenario;
        }
    }

    return null;
}
