import {create} from 'zustand';
import {claimRedPocket, getRedPocketById, getRedPocketList,} from "@fundpark/fp-api";
import {RedPocket} from "@fundpark/fp-api/types/redPocket.ts";
import {RED_POCKET_TYPE} from "@/constants/general.ts"

interface RedPocketState {
    redPockets: RedPocket[];
    loading: boolean;
    rpLoading: boolean;
    fetchRedPocketList: () => Promise<void>;
    collect: (redPocketId: number) => Promise<any>;
    getRedPocketInfoById: (redPocketId: number) => Promise<any>;
    getRedPocketByType: (type: string) => RedPocket | null;
    getRedPocketByStatus: (status?: string[]) => RedPocket[] | null;

    isRedPocketModalOpen: boolean;
    setIsRedPocketModalOpen: (open: boolean) => void;

    clear: () => void;
}

export const useRedPocketStore = create<RedPocketState>((set, get) => ({
    loading: false,
    rpLoading: false,
    redPockets: [],

    isRedPocketModalOpen: false,
    setIsRedPocketModalOpen: (open) => set({ isRedPocketModalOpen: open }),

    fetchRedPocketList: async () => {
        set({rpLoading: true});
        try {
            const resp = await getRedPocketList({status: 'active,claimed,used,expired,invalid'});

            const pockets = resp.data || [];

            const enriched = await Promise.all(
              pockets.map(async (p) => {
                // @ts-ignore
                  const typeMeta = RED_POCKET_TYPE[p.type];
                if (typeMeta) {
                  const detail = await getRedPocketById({ id: typeMeta.id });
                  return {
                    ...p,
                    remaining_quantity: detail.data?.remaining_quantity ?? 0,
                  };
                }
                return {
                  ...p,
                  remaining_quantity: undefined,
                };
              })
            );


            set({ redPockets: enriched });
        } catch {
            set({redPockets: []});
        } finally {
            set({rpLoading: false});
        }
    },

    collect: async (redPocketId) => {
        set({loading: true});
        try {
            const resp = await claimRedPocket({id: redPocketId});
            await get().fetchRedPocketList();
            return resp;
        } catch (err) {
            console.error("collect() failed:", err);
            throw err;
        } finally {
            set({loading: false});
        }
    },

    getRedPocketInfoById: async (redPocketId) => {
        set({loading: true});
        try {
            const resp = await getRedPocketById({id: redPocketId});
            return resp.data;
        } catch {
            return null;
        } finally {
            set({loading: false});
        }
    },

    getRedPocketByType: (type) => {
        const updatedList = get().redPockets;
        return updatedList.find(p => p.type === type) ?? null;
    },

    getRedPocketByStatus: (
        status?: string[]
    ): RedPocket[] | null => {
        const updatedList = get().redPockets;
        if (Array.isArray(status)) {
            return updatedList.filter(p => status.includes(p.status));
        }
        return null;
    },

    clear: () => set({
        redPockets: [],
        loading: false,
        isRedPocketModalOpen: false,
    })

}));
