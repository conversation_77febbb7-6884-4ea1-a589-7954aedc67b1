import {create} from 'zustand';
import {getDebitAccount, submitDebitAccount} from '@fundpark/fp-api';
import {mapDebitAccountApiToUi, mapDebitAccountFormToApi} from '@/mappers/drawdownForm';
import {DrawdownAccountFormData} from '@/components/DrawdownForm/DrawdownAccount/types';

interface DrawdownAccountState {
    formData: DrawdownAccountFormData;
    isLoading: boolean;
    error: string | null;
    hasFetchedData: boolean;

    // Actions
    setFormData: (data: Partial<DrawdownAccountFormData>) => void;
    fetchDebitAccount: (force?: boolean) => Promise<void>;
    submitDebitAccount: (formData: DrawdownAccountFormData) => Promise<{
        success: boolean;
        error?: string;
        status?: number;
    }>;
    resetStore: () => void;
}

const initialFormData: DrawdownAccountFormData = {
    countryRegion: '',
    companyNameEn: '',
    companyNameCn: '',
    businessRegistrationNumber: '',
    drawdownCurrency: 'USD',
    drawdownAmount: '',
    bankAccountName: '',
    bankAccountNumber: '',
    bank: '',
    swiftCode: '',
    bankAddress: '',
};

export const useDrawdownAccountStore = create<DrawdownAccountState>((set, get) => ({
    formData: initialFormData,
    isLoading: false,
    error: null,
    hasFetchedData: false,

    setFormData: (data) => {
        set((state) => ({
            formData: {
                ...state.formData,
                ...data
            }
        }));
    },

    fetchDebitAccount: async (force = false) => {
        const {hasFetchedData, isLoading} = get();
        if (!force && (hasFetchedData || isLoading)) {
            return;
        }

        set({isLoading: true, error: null});

        try {
            const response = await getDebitAccount();

            if (response.status === 0 && response.data) {
                const uiData = mapDebitAccountApiToUi(response.data);
                set({
                    formData: {
                        ...get().formData,
                        ...uiData
                    },
                    hasFetchedData: true,
                });
            } else {
                throw new Error(response.message || 'Failed to fetch debit account data');
            }
        } catch (err: any) {
            set({error: err.message || 'Failed to fetch debit account data'});
        } finally {
            set({isLoading: false});
        }
    },

    submitDebitAccount: async (formData) => {
        // const {formData} = get();
        set({isLoading: true, error: null});

        try {
            const finalFormData = {
                ...formData,
                bank: formData.other_bank_name?.trim() || formData.bank,
            };
            const apiData = mapDebitAccountFormToApi(finalFormData);
            const response = await submitDebitAccount(apiData);

            if (response.status === 0) {
                return {success: true, status: response.status};
            } else {
                return {
                    success: false,
                    error: response.message || 'Failed to submit debit account',
                    status: response.status
                };
            }
        } catch (err: any) {
            set({error: err.message || 'Failed to submit debit account'});
            return {
                success: false,
                error: err.message || 'Failed to submit debit account'
            };
        } finally {
            set({isLoading: false});
        }
    },

    resetStore: () => {
        set({
            formData: initialFormData,
            isLoading: false,
            error: null,
            hasFetchedData: false,
        });
    },
})); 