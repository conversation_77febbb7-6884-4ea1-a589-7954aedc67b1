import { create } from "zustand";
import type { ThemeConfig } from "antd";
import type { Locale } from "@fundpark/ui-utils";
import antdTheme1 from "@/themes/antd-theme1";

interface SettingsStore {
    locale: Locale;
    antdTheme: ThemeConfig;
    setLocale: (value: Locale) => void;
    update: (data: Partial<SettingsStore>) => void;
}

export const useSettingsStore = create<SettingsStore>(set => {
    const locale = (localStorage.getItem("locale") || "en_US") as Locale;

    return {
        locale,
        setLocale: (value: Locale) => {
            localStorage.setItem("locale", value);
            set({ locale: value });
        },
        antdTheme: antdTheme1,
        update: set
    };
});
