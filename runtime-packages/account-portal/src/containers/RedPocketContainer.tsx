import React, {useEffect, useState} from "react";
// import {useRedPocket} from "@/hooks/useRedPocket";
import {useCommonStore} from "@/store/common.ts";
import {useSelectedProduct} from "@/hooks/useSelectedProduct";
import RedPocket from "@/components/RedPocket";
// import ClockIcon from "@/assets-new/icons/common/clock.svg";
import RedPocketModal7Days from "@/components/RedPocketModal7Days";
import {useNavigate} from "react-router-dom";
import RedPocketModal500 from "@/components/RedPocketModal500";
import {useRedPocketStore} from "@/store/redpocket.ts";
import OverlayContainer from "@/components/Container/overlayContainer.tsx";
import {useLimitStore} from "@/store/limits.ts";
import {POSITION_STATUS} from "@/constants/position-status";
import {usePositionTracking} from "@/hooks/usePositionTracking";
import {useMatomoContext} from "@/contexts/MatomoContext";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";


interface RedPocketContainerProps {
    clickEvent?: () => void;
}

const RedPocketContainer: React.FC<RedPocketContainerProps> = ({clickEvent}) => {
    // const {getRedPocketsAbleToClaim, getClaimedRedPockets} = useRedPocket();
    const product = useSelectedProduct();
    const navigate = useNavigate();
    const isAnyShopAuthed = useCommonStore(s => s.isAnyShopAuthed);
    const {
        redPockets,
        getRedPocketByType,
        isRedPocketModalOpen,
        setIsRedPocketModalOpen,
        rpLoading
    } = useRedPocketStore();

    const [redPocketActionTitle, setRedPocketActionTitle] = useState<string | null>("激活领取");
    const [isShowRedPocket, setIsShowRedPocket] = useState(false);
    const {creditApplication, isLoadingApplication} = useLimitStore();
    const {trackPosition} = usePositionTracking();
    const {trackEvent} = useMatomoContext();
    const [rpMain, setRpMain] = useState<React.ReactNode | null>(null);

    useEffect(() => {
        if (creditApplication === undefined || rpLoading) return;

        if (creditApplication?.status === 'failed' || getRedPocketByType("deduction")?.remaining_quantity === 0 || isLoadingApplication) {
            setIsShowRedPocket(false);
            return;
        }
        if (!getRedPocketByType("deduction")) {
            setRedPocketActionTitle("激活领取");
            setIsShowRedPocket(true);

            return;
        }
        if (getRedPocketByType("deduction")?.status === "claimed") {
            if (creditApplication?.type === 'limit_increase'){
                setRedPocketActionTitle(null);
            }else{
                setRedPocketActionTitle(`待激活使用`);
            }
            setIsShowRedPocket(true);
        } else {
            setIsShowRedPocket(false);
        }
    }, [redPockets, getRedPocketByType, creditApplication, rpLoading, isLoadingApplication, isAnyShopAuthed]);

    const handleRedPocketOpen = async (isUse0: boolean) => {
        if (isUse0) {
            trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.USE_FREE_XDJ); 
        }

        if (isAnyShopAuthed === undefined || creditApplication?.type === 'xdj' && creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result'){
            return;
        }

        if (!isAnyShopAuthed) {
            clickEvent?.();
            return;
        }
        setIsRedPocketModalOpen(true);
    
    };

    useEffect(() => {
        if (isLoadingApplication) {
            setIsRedPocketModalOpen(false);
            return
        }
        if (isAnyShopAuthed && product === "xdj" && creditApplication === null) {
            setIsRedPocketModalOpen(true);
        } else {
            setIsRedPocketModalOpen(false);
        }
    }, [isAnyShopAuthed, product, creditApplication, isLoadingApplication]);

    useEffect(() => {
        if (isLoadingApplication || rpLoading || isAnyShopAuthed===undefined) {
            setRpMain(null);
            return
        } else if (creditApplication?.status === "failed") {
            setRpMain(null);
        } else if (creditApplication === null && product === "xdj") {
            setRpMain(
                <>
                    <OverlayContainer>
                        <RedPocketModal7Days
                            open={isRedPocketModalOpen}
                            onClose={() => setIsRedPocketModalOpen(false)}
                            actionEvent={async () => {
                                trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.GET_7_DAY_RED_PACKET); 
                                await trackPosition({
                                    path: "/credit/hook/register/drawdown-account",
                                    status: POSITION_STATUS.XDJ_DRAWDOWN_ACCOUNT_DRAFT
                                });
                                navigate("/credit/hook/register/drawdown-account", {replace: true});
                                setIsRedPocketModalOpen(false);
                            }}
                        />
                        <RedPocket
                            type="default"
                            redPocketTitle="7天免息金，有店就能用"
                            actionTitle="立即0元用"
                            // isShowHelpingText
                            // helpingText={
                            //     <div>
                            //         <img src={ClockIcon} alt="Clock" width={20} height={20} /> 限时免费领取品类报告
                            //     </div>
                            // }
                            actionClick={() => {
                                handleRedPocketOpen(true);
                            }}
                        />
                    </OverlayContainer>
                </>
            );
        } else if (isShowRedPocket) {
            setRpMain(
                <>
                    <OverlayContainer>
                        <RedPocketModal500 open={isRedPocketModalOpen} onClose={() => setIsRedPocketModalOpen(false)}/>
                        <RedPocket
                            actionTitle={redPocketActionTitle}
                            type="doubleLine"
                            titleLineOne="激活额度领红包"
                            titleLineTwo="500 美元"
                            actionClick={() => {
                                handleRedPocketOpen(false);
                            }}
                        />
                    </OverlayContainer>
                </>
            );
        }
    }, [isLoadingApplication, rpLoading, creditApplication, isShowRedPocket, isRedPocketModalOpen, isAnyShopAuthed]);


    return <> {rpMain} </>;
};

export default RedPocketContainer;
