import React, {useEffect} from "react";
import {useLimitStore} from "@/store/limits";
import LongButton from "@/components/LongButtton";
import Text from "@/components/shared/Text";
import {formatNumberToFraction} from "@/utils/math";
import {OurWechatModal} from "@/components/shared/OurWechatModal";
import {useCommonStore} from "@/store/common.ts";
import {useRedPocketStore} from "@/store/redpocket.ts";
import {useNavigate} from "react-router-dom";
import {useSelectedProduct} from "@/hooks/useSelectedProduct.ts";
import TwoButtonModal from "@/components/shared/GenericModal";

import {Grid, Spin} from "antd";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants";

const LimitActionsContainer: React.FC = () => {
    const screens = Grid.useBreakpoint();
    const {
        fetchUserLimits,
        fetchUserCreditApplication,
        creditApplication,
        creditLimit,
        isLoadingLimits,
        isLoadingApplication,
        isCalculatingLimitAfterFirstDrawdown
    } = useLimitStore();

    const {userInfo} = useCommonStore();
    const {redPockets, getRedPocketByType, setIsRedPocketModalOpen, loading} = useRedPocketStore();
    const navigate = useNavigate();
    const product = useSelectedProduct();
    const {trackEvent, isEnabled} = useMatomoContext();

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits();
            await fetchUserCreditApplication();
        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);

    useEffect(() => {
        if (loading || creditApplication === null || getRedPocketByType("deduction")?.remaining_quantity === 0 || creditApplication?.type === 'limit_increase') {
            setHelpingText(null)
            return;
        }
        const able = !getRedPocketByType("deduction") && creditApplication;
        if (able) {
            setHelpingText("500美元红包可领取");
            return;
        }

        if (getRedPocketByType("deduction")?.status === "claimed" && getRedPocketByType("deduction")?.can_use) {
            setHelpingText("500美元红包待激活使用");
        }
    }, [redPockets, userInfo, getRedPocketByType, creditApplication, loading]);

    const [wechatModal, setWechatModal] = React.useState<boolean>(false);
    const [twoButtonModal, setTwoButtonModal] = React.useState<boolean>(false);
    const [helpingText, setHelpingText] = React.useState<string | null>(null);

    if (isLoadingLimits || isLoadingApplication) {
        return (
            <div className="flex justify-center py-8">
                <Spin/>
            </div>
        );
    }

    const availableLimit = creditLimit?.available_limit ?? 0;
    const approvalLimit = creditLimit?.approval_limit ?? 0;
    const status = creditApplication?.status;
    const preWml = creditApplication?.pre_wml ?? 0;

    const routeToPage = (url: string) => {
        const baseUrl = product === "xdj" ? "credit/hook" : "credit/underwriting";
        navigate(`/${baseUrl}/${url}`);
    };

    const getActivateButtonTitle = () => {
        if (
            isCalculatingLimitAfterFirstDrawdown
            || (creditApplication?.type === 'limit_increase' && creditApplication?.status === 'pending' && !creditApplication?.pre_wml && !creditLimit?.approval_limit)
            || (creditApplication?.type === 'xdj' && creditApplication?.status === 'active' && creditApplication.pre_limit === null)
            || (status === "submitted" && creditApplication?.pre_limit == null && creditApplication?.stage === 'waiting_result')
        ) {
            return "激活中...";
        }
        if (creditApplication?.pre_limit == null || creditApplication?.pre_limit == 0 || creditApplication?.status == 'failed') {
            return "涨额度";
        }
        if (preWml <= approvalLimit) {
            return "涨额度";
        }
        return `立即激活${formatNumberToFraction(preWml)}额度`;
    };

    const getActivateButtonType = () => {
        if (creditApplication?.type === 'xdj' && creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result') {
            return "disabled";
        } else if (creditApplication?.status === 'failed' && creditApplication?.type === 'xdj') {
            return "disabled";
        }
        return 'light';
    };

    const getLongButtonType = () => {
        if (creditApplication?.type === 'xdj' && creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result') {
            return "disabled";
        }

        if (availableLimit > 0) {
            return "dark";
        }

        return "disabled";
    }

    const renderActions = () => {
        if (creditApplication === null) {
            return;
        }

        if (status === "failed" && creditApplication?.type === "xdj") {
            return (
                <div className="flex items-center justify-center gap-8 mt-8 mb-3">
                    <LongButton
                        buttonTitle="试试联系客服"
                        type="dark"
                        onClick={() => {
                            setWechatModal(true);
                        }}
                    />
                </div>
            );
        }

        if (status === "submitted" && creditApplication?.pre_limit == null && creditApplication?.stage !== 'waiting_result') {
            return null;
        }

        return (
            <div
                className={screens.md ? "flex items-center justify-center gap-8 mt-8 mb-3" : "items-center justify-center gap-8 mt-8 mb-3"}>
                <div className="flex flex-col items-center gap-2">
                    <LongButton
                        buttonTitle="现在用"
                        type={getLongButtonType()}
                        onClick={() => {
                            if (getLongButtonType() === "disabled") {
                                return;
                            }
                            routeToPage("second-drawdown");
                        }}
                    />
                    <Text className="mt-3">现有额度：{formatNumberToFraction(approvalLimit)} 美元</Text>
                </div>
                <div className="flex flex-col items-center gap-2">
                    <LongButton
                        buttonTitle={getActivateButtonTitle()}
                        type={getActivateButtonType()}
                        isShowHelpingText={helpingText !== null}
                        helpingText={helpingText}
                        onClick={() => {
                            if (getActivateButtonType() === "disabled") {
                                return;
                            }

                            if (helpingText !== null && !isCalculatingLimitAfterFirstDrawdown) {
                                setIsRedPocketModalOpen(true);
                                return;
                            }
                            
                            if (isEnabled) {
                                trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.INCREASE_LIMIT_HOOK_TO_LITE);
                            }

                            if (creditApplication?.type === 'limit_increase' && creditApplication?.status === 'pending' && creditApplication?.pre_limit !== null && creditApplication?.stage === 'signing') {
                                routeToPage("activate-shop/signing");
                                return;
                            }
                            routeToPage("activate-shop");
                        }}
                    />
                    <Text className="mt-3">提额高达 10,000,000 美元</Text>
                </div>
            </div>
        );
    };

    return (
        <>
            <TwoButtonModal
                open={twoButtonModal}
                onClose={() => {
                    setTwoButtonModal(false);
                }}
                onComplete={() => {
                    setTwoButtonModal(false);
                    setWechatModal(true);
                }}
                content={
                    <>
                        尊敬的客户，您的专属客户经理将在30分钟内联系您，请注意接听来电。
                        <br/>
                        <br/>
                        其他问题请点击【联系客服】
                    </>
                }
            />
            <OurWechatModal
                open={wechatModal}
                onClose={() => {
                    setWechatModal(false);
                }}
                hasAlertIcon
                textAlign={"center"}
            />

            {renderActions()}
        </>
    );
};

export default LimitActionsContainer;
