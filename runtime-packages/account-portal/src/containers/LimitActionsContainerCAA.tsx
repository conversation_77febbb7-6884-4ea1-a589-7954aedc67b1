import React, {useEffect} from "react";
import {useLimitStore} from "@/store/limits";
import LongButton from "@/components/LongButtton";
import Text from "@/components/shared/Text";
import {formatNumberToFraction} from "@/utils/math";
import {OurWechatModal} from "@/components/shared/OurWechatModal";
import {useCommonStore} from "@/store/common.ts";
import {useRedPocketStore} from "@/store/redpocket.ts";
import {useNavigate} from "react-router-dom";
import {useSelectedProduct} from "@/hooks/useSelectedProduct.ts";
import TwoButtonModal from "@/components/shared/GenericModal";
import { Grid, Spin } from "antd";

type Props = {
  openAuthShopModalDialog: () => void;
};

const LimitActionsContainerCAA: React.FC<Props> = ({ openAuthShopModalDialog }) => {
    const screens = Grid.useBreakpoint();
    const {
        fetchUserLimits,
        fetchUserCreditApplication,
        creditApplication,
        creditLimit,
        isLoadingLimits,
        isLoadingApplication,
        isCalculatingLimitAfterFirstDrawdown
    } = useLimitStore();

    const {userInfo} = useCommonStore();
    const {redPockets, getRedPocketByType, loading, getRedPocketByStatus} = useRedPocketStore();
    const navigate = useNavigate();
    const product = useSelectedProduct();
    // const isAnyShopAuthed = useCommonStore((s) => s.isAnyShopAuthed);

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits();
            await fetchUserCreditApplication();
        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);

    useEffect(() => {
        const hasUsedOrExpired = getRedPocketByStatus(['used', 'expired', 'invalid']);
        if (loading || hasUsedOrExpired && hasUsedOrExpired.length > 0) {
            setHelpingText(null);
            return;
        }
        if (creditApplication === null || getRedPocketByType("deduction")?.remaining_quantity === 0 || !(creditApplication?.type === 'automation' && creditApplication?.status === 'pending')) {
            setHelpingText(null);
            return;
        }
        const able = !getRedPocketByType("deduction") && creditApplication;
        if (able) {
            setHelpingText("500美元红包可领取");
            return;
        }

        if (getRedPocketByType("deduction")?.status === "claimed" && getRedPocketByType("deduction")?.can_use) {
            setHelpingText("500美元红包待激活使用");
        }
    }, [redPockets, userInfo, getRedPocketByType, loading, creditApplication, getRedPocketByStatus]);

    const [wechatModal, setWechatModal] = React.useState<boolean>(false);
    const [twoButtonModal, setTwoButtonModal] = React.useState<boolean>(false);
    const [helpingText, setHelpingText] = React.useState<string | null>(null);

    if (isLoadingLimits || isLoadingApplication) {
        return (
            <div className="flex justify-center py-8">
                <Spin/>
            </div>
        );
    }

    // const availableLimit = creditLimit?.available_limit ?? 0;
    const approvalLimit = creditLimit?.approval_limit ?? 0;
    // const preLimit = creditApplication?.pre_limit ?? 0;
    const status = creditApplication?.status;
    const isFirstDrawdown = creditApplication?.status === 'active' || creditApplication?.type === 'limit_increase';
    // const isExceeded10k = approvalLimit >= 100000
    const preWml = creditApplication?.pre_wml ?? 0;

    const routeToPage = (url: string) => {
        const baseUrl = product === "xdj" ? "credit/hook" : "credit/underwriting";
        navigate(`/${baseUrl}/${url}`);
    };

    const getLongButtonType = () => {
        if (creditApplication?.type === 'limit_increase' && creditApplication?.pre_limit && creditLimit )
            return "dark";

        if (creditApplication?.type === 'automation' && (creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result') || creditApplication?.status === 'failed') {
            return "disabled";
        }

        if (creditLimit && creditLimit?.available_limit <= 0) {
            return "disabled";
        }

        if (creditApplication?.type === 'limit_increase' && !creditApplication.pre_limit && !creditLimit) {
            return "disabled";
        }

        return "dark";
    }

    const getActivateButtonType = () => {
        if (creditApplication?.type === 'limit_increase' && creditApplication?.pre_limit && creditLimit )
            return "light";

        if (creditApplication?.status === 'failed' && creditApplication?.type !== 'automation') {
            return "disabled";
        }
        return 'light';
    };

    const getActivateButtonTitle = () => {
        if (creditApplication?.type === 'limit_increase' && (creditApplication.status === 'active' || creditApplication.status === 'failed')) {
            if (preWml > approvalLimit) {
                return `立即激活${formatNumberToFraction(preWml)}额度`;
            }
            return "涨额度";
        }

        if (creditApplication?.type === 'limit_increase' && !(creditApplication.status === 'active' || creditApplication.status === 'failed') && !creditApplication?.pre_limit && !creditApplication?.pre_wml) {
            return "激活中...";
        }

        if (creditApplication?.type === 'limit_increase' && !(creditApplication.status === 'active' || creditApplication.status === 'failed') && creditApplication?.pre_limit && creditLimit?.approval_limit && preWml < 100000) {
            return "激活中...";
        }


        if (creditApplication?.type === 'limit_increase' && !(creditApplication.status === 'active' || creditApplication.status === 'failed') && creditApplication?.pre_limit && creditLimit?.approval_limit) {
            return `立即激活${formatNumberToFraction(preWml)}额度`;
        }

        if (isCalculatingLimitAfterFirstDrawdown || (creditApplication?.type === 'automation' && creditApplication?.status === 'failed')) {
            return "激活中...";
        }

        if (creditApplication?.type === 'automation' && creditApplication?.status === 'active' && preWml > approvalLimit && preWml > 100000){
            return `立即激活${formatNumberToFraction(preWml)}额度`;
        }

        if (creditApplication?.type === 'limit_increase' && creditApplication?.status === 'active' && preWml > approvalLimit && preWml > 100000){
            return `立即激活${formatNumberToFraction(preWml)}额度`;
        }

        return `涨额度`;
    };

    const renderActions = () => {
        if (creditApplication === null || creditApplication === undefined) {
            return;
        }

        if (creditApplication && creditApplication?.pre_limit == 0) {
            return (
                <div className="flex items-center justify-center gap-8 mt-8 mb-3">
                    <LongButton
                        buttonTitle="去处理"
                        type="dark"
                        onClick={openAuthShopModalDialog}
                    />
                </div>
            );
        }

        // type=limit_increase，永远是 还可以用
        if (
            ((creditApplication?.type !== 'automation' && status === "failed")
                || (creditApplication?.type === 'automation' && creditApplication?.status === "failed" && creditApplication?.stage === 'info_processing'))
            && !(creditApplication?.type === 'limit_increase' && creditApplication?.pre_limit && creditLimit )
        ) {
            return (
                <div className="flex items-center justify-center gap-8 mt-8 mb-3">
                    <LongButton
                        buttonTitle="试试联系客服"
                        type="dark"
                        onClick={() => {
                            setWechatModal(true);
                        }}
                    />
                </div>
            );
        }

        if (
            ["submitted", "pending"].includes(creditApplication?.status ?? "")
            && (creditApplication?.pre_limit == null
                && (!creditLimit || Array.isArray(creditLimit)))
            && !(creditApplication?.type === 'limit_increase' && !creditApplication?.pre_limit && !creditLimit)
        ) {
            return null;
        }

        return (
            <div className={screens.md ? "flex items-center justify-center gap-8 mt-8 mb-3" : "items-center justify-center gap-8 mt-8 mb-3"}>
                <div className="flex flex-col items-center gap-2">
                    <LongButton
                        isShowHelpingText={helpingText !== null}
                        helpingText={helpingText}
                        helpingTextStyle={"left"}
                        buttonTitle="现在用"
                        // type={availableLimit > 0 ? "dark" : "disabled"}
                        type={getLongButtonType()}
                        onClick={() => {
                            if (getLongButtonType() === "disabled") {
                                return;
                            }

                            if (creditApplication?.type === 'automation' && (creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result' || creditApplication?.status === 'failed')) {
                                return;
                            }

                            // if (preLimit > 100000) {
                            //     setTwoButtonModal(true);
                            //     return;
                            // }
                            if (!isFirstDrawdown) {
                                routeToPage("register/drawdown-account");
                            } else {
                                if (creditLimit && creditLimit?.available_limit <= 0) {
                                    return;
                                } else {
                                    routeToPage("second-drawdown");
                                }
                            }
                        }}
                    />
                    {getLongButtonType() === 'dark' && creditLimit ?
                        <Text className="mt-3">现有额度：{formatNumberToFraction(approvalLimit)} 美元</Text> :
                        <Text className="mt-3 invisible">现有额度：{formatNumberToFraction(approvalLimit)} 美元</Text>}
                </div>
                <div className="flex flex-col items-center gap-2">
                    <LongButton
                        buttonTitle={getActivateButtonTitle()}
                        type={getActivateButtonType()}
                        onClick={() => {
                            if (getActivateButtonType() === "disabled") {
                                return;
                            }

                            if (creditApplication?.type === 'automation' && creditApplication?.status === 'pending') {
                                routeToPage('increase-limit/authorize')
                                return
                            } else {
                                routeToPage("activate-shop");
                                return;
                            }

                            // if (creditApplication?.type === 'automation' && creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result'){
                            //     routeToPage("activate-shop");
                            //     return;
                            // }


                            // if ((creditApplication?.type === 'limit_increase' && creditApplication?.status !== 'failed') ||
                            //     (creditApplication?.type ==='automation' && creditApplication?.status === 'failed')
                            // ){
                            //     routeToPage("activate-shop");
                            //     return
                            // }


                            // if (creditApplication?.type === 'automation' && creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result'){
                            //     return;
                            // }
                            // routeToPage("activate-shop");
                        }}
                    />
                    <Text className="mt-3">提额高达 10,000,000 美元</Text>
                </div>
            </div>
        );
    };

    return (
        <>
            <TwoButtonModal
                open={twoButtonModal}
                onClose={() => {
                    setTwoButtonModal(false);
                }}
                onComplete={() => {
                    setTwoButtonModal(false);
                    setWechatModal(true);
                }}
                content={
                    <>
                        尊敬的客户，您的专属客户经理将在30分钟内联系您，请注意接听来电。
                        <br/>
                        <br/>
                        其他问题请点击【联系客服】
                    </>
                }
            />
            <OurWechatModal
                open={wechatModal}
                onClose={() => {
                    setWechatModal(false);
                }}
                hasAlertIcon
                textAlign={"center"}
            />

            {renderActions()}
        </>
    );
};

export default LimitActionsContainerCAA;
