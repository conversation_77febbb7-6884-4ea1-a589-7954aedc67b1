import React, {useEffect, useState} from "react";
// import {useRedPocket} from "@/hooks/useRedPocket";
import {useCommonStore} from "@/store/common.ts";
import {useSelectedProduct} from "@/hooks/useSelectedProduct";
import RedPocket from "@/components/RedPocket";
import OverlayContainer from "@/components/Container/overlayContainer.tsx";
import RedPocketModal500 from "@/components/RedPocketModal500";
import {useRedPocketStore} from "@/store/redpocket.ts";
import {useLimitStore} from "@/store/limits.ts";
import {useNavigate} from "react-router-dom";
// import { trackEvent } from "@/utils/matomoTracking";
// import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import TwoButtonModal from "@/components/shared/GenericModal";
import {OurWechatModal} from "@/components/shared/OurWechatModal";

interface RedPocketContainerProps {
    clickEvent?: () => void;
}

const RedPocketContainerCAA: React.FC<RedPocketContainerProps> = (
    {clickEvent}
) => {

    const {getRedPocketByStatus, loading} = useRedPocketStore();
    const product = useSelectedProduct();
    const isAnyShopAuthed = useCommonStore((s) => s.isAnyShopAuthed);

    const [isRedPocketModalOpen, setIsRedPocketModalOpen] = useState(false);
    const [wechatModal, setWechatModal] = React.useState<boolean>(false);
    // const [isShowHelpingText, setIsShowHelpingText] = useState(false);
    const [isShowRedPocket, setIsShowRedPocket] = useState(false);
    const {creditApplication, isLoadingApplication} = useLimitStore();
    const [redPocketActionTitle, setRedPocketActionTitle] = useState<string>("激活领取");
    const {redPockets, getRedPocketByType} = useRedPocketStore();
    const [twoButtonModal, setTwoButtonModal] = React.useState<boolean>(false);
    const navigate = useNavigate();

    useEffect(() => {
        if (creditApplication === undefined) return;
        if (isLoadingApplication || loading) return;

        if (creditApplication?.status === 'failed' || getRedPocketByType("deduction")?.remaining_quantity === 0) {
            setIsShowRedPocket(false);
            return;
        }

        if (
            creditApplication?.status && ["submitted", "pending"].includes(creditApplication?.status) &&
            creditApplication?.pre_limit == null
        ) {
            setIsShowRedPocket(false);
            return;
        }

        if (!getRedPocketByType('deduction')) {
            setRedPocketActionTitle("激活领取");
            setIsShowRedPocket(true);
            return;
        }
        if (getRedPocketByType('deduction')?.status === "claimed") {
            setRedPocketActionTitle("待激活使用");
            setIsShowRedPocket(true);
        } else {
            setIsShowRedPocket(false);
        }
    }, [redPockets, getRedPocketByType, creditApplication, isLoadingApplication, loading]);

    useEffect(() => {
        if (isLoadingApplication || loading) return;

        if (isLoadingApplication) {
            setIsRedPocketModalOpen(false);
            return
        }

        if (creditApplication === null && !isAnyShopAuthed) {
            // setIsShowHelpingText(true);
            setIsShowRedPocket(true);
            return;
        }

        if (creditApplication === null && isAnyShopAuthed) {
            setIsRedPocketModalOpen(true);
            setIsShowRedPocket(true);
            return;
        }else {
            setIsRedPocketModalOpen(false);
        }

        const hasUsedOrExpired = getRedPocketByStatus(['used', 'expired', 'invalid']);
        if (hasUsedOrExpired && hasUsedOrExpired.length > 0) {
            setIsShowRedPocket(false);
            return;
        }

    }, [creditApplication, product, isAnyShopAuthed, getRedPocketByStatus, isLoadingApplication, loading]);

    const handleRedPocketOpen = async () => {
        if (!isAnyShopAuthed) {
            clickEvent?.();
            return
        }
        if (creditApplication?.type === 'automation' && creditApplication?.status === 'submitted' && creditApplication?.stage === 'waiting_result'){
            return;
        }

        if (isAnyShopAuthed && creditApplication === null){
            setIsRedPocketModalOpen(true)
        }else{
            // if (creditApplication && creditApplication?.pre_limit > 100000) {
            //     setTwoButtonModal(true);
            //     return;
            // }
            const baseUrl = product === "xdj" ? "credit/hook" : "credit/underwriting";
            navigate(`/${baseUrl}/register/drawdown-account`);
        }
    }

    return (
        <>
            {isShowRedPocket && (<OverlayContainer>
                <RedPocketModal500
                    open={isRedPocketModalOpen}
                    onClose={() => setIsRedPocketModalOpen(false)}
                />
                <RedPocket
                    actionTitle={redPocketActionTitle}
                    type="doubleLine"
                    titleLineOne="激活额度领红包"
                    titleLineTwo="500 美元"
                    actionClick={handleRedPocketOpen}
                />
            </OverlayContainer>)}
            <TwoButtonModal
                open={twoButtonModal}
                onClose={() => {
                    setTwoButtonModal(false);
                }}
                onComplete={() => {
                    setTwoButtonModal(false);
                    setWechatModal(true);
                }}
                content={
                    <>
                        尊敬的客户，您的专属客户经理将在30分钟内联系您，请注意接听来电。
                        <br/>
                        <br/>
                        其他问题请点击【联系客服】
                    </>
                }
            />
            <OurWechatModal
                open={wechatModal}
                onClose={() => {
                    setWechatModal(false);
                }}
                hasAlertIcon
                textAlign={"center"}
            />
        </>
    );

};
export default RedPocketContainerCAA;
