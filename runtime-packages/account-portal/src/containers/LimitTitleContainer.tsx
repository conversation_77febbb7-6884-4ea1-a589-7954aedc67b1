import AmountWithTitle from "@/components/shared/AmountWithTitle";
import React, {useEffect, useState} from "react";
import {useLimitStore} from "@/store/limits.ts";
import {useCommonStore} from "@/store/common.ts";
import {formatNumberToFraction} from "@/utils/math.ts";

const LimitTitleContainer: React.FC = () => {
    const {
        fetchUserLimits,
        fetchUserCreditApplication,
        creditApplication,
        creditLimit,
        isLoadingLimits,
        isLoadingApplication,
    } = useLimitStore();

    const { fetchAuthedList, isLoadingAuthedList } = useCommonStore();
    const [helperText, setHelperText] = useState<string>();

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits();
            await fetchUserCreditApplication();
            await fetchAuthedList();
        }
        fetchData().catch((err) => {
            console.error("Error fetching data:", err);
        });
    }, []);

    const [title, setTitle] = useState<string>();
    const [amount, setAmount] = useState<number | string>();
    const isAnyShopAuthed = useCommonStore((s) => s.isAnyShopAuthed);
    // const isIndexPage = !(window.location.href.includes("increase-limit") || window.location.href.includes('activate-shop'))
    // const isHomePage = window.location.href.includes('home')
    const approvalLimit = creditLimit?.approval_limit ?? 0;
    const preWml = creditApplication?.pre_wml ?? 0;
    // const isExceeded10K = approvalLimit >= 100000;

    useEffect(() => {
        // || ((window.location.href).includes('activate-shop') && creditApplication?.status === 'active')  removed
        if (!isAnyShopAuthed || creditApplication===null) {
            setTitle("最低可得(美元)");
            setAmount(2000);
            return;
        }
        if (creditApplication?.type === "xdj" && creditApplication?.status === 'failed') {
            setTitle(undefined);
            setAmount("暂时未有额度");
        } else if (
            (creditApplication?.status === 'submitted' && creditApplication?.pre_limit == null && creditApplication?.stage !== 'waiting_result')
            || (window.location.href.includes("activate-shop") && creditApplication?.status === "active" && creditApplication?.type === 'xdj' && !creditApplication?.pre_limit )
            || (window.location.href.includes("activate-shop") && creditApplication?.status === "pending" && creditApplication?.type === 'limit_increase' && !creditApplication?.pre_limit )
        ) {
            setTitle("正在激活额度(美元)");
            setAmount("额度计算中");
        } else if (
            ((creditApplication?.status === 'active' && creditApplication?.type === 'xdj' && creditApplication?.pre_limit && window.location.href.includes('activate-shop'))
            || creditApplication?.status === "pending" && creditApplication?.type === 'limit_increase' && creditApplication?.pre_limit
            || preWml > approvalLimit
            || creditApplication?.status === 'active' && creditApplication?.type === 'limit_increase'
            || creditApplication?.status === 'pending' && creditApplication?.type === 'limit_increase')
            && (window.location.href.includes("increase-limit") || window.location.href.includes('activate-shop'))
            && (creditApplication?.status !== 'failed')
        ) {
            // Never show 正在激活额度(美元) + pre_limit in index.
            setTitle("正在激活额度(美元)");
            const DISPLAY_LIMIT_THRESHOLD = 100000;
            const adjusted_pre_limit = Math.min(
              creditApplication?.pre_limit ?? Infinity,
              creditApplication?.pre_wml   ?? Infinity
            );
            const shouldCapDisplayLimit = approvalLimit >= DISPLAY_LIMIT_THRESHOLD;
            if (shouldCapDisplayLimit){
                setAmount(preWml);
                setHelperText('')
                return;
            }
            setAmount(Math.min(adjusted_pre_limit,DISPLAY_LIMIT_THRESHOLD));
            if (preWml > DISPLAY_LIMIT_THRESHOLD){
                setHelperText(`（总额度$ ${formatNumberToFraction(preWml)}）`);
            }else {
                setHelperText('');
            }
        }else {
            setTitle("还可以用（美元）");
            setAmount(creditLimit?.available_limit);
        }
    }, [creditApplication, creditLimit, isAnyShopAuthed]);

    if (isLoadingLimits || isLoadingApplication) return

    return (
        <AmountWithTitle
            title={title}
            amount={amount}
            loading={isLoadingLimits || isLoadingApplication || isLoadingAuthedList}
            helperText={helperText}
        />
    );
};


export default LimitTitleContainer;
