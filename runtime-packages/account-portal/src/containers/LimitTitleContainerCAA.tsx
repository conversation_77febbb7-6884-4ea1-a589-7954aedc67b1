import AmountWithTitle from "@/components/shared/AmountWithTitle";
import React, { useEffect, useState } from "react";
import { useLimitStore } from "@/store/limits.ts";
import { useCommonStore } from "@/store/common.ts";
import {formatNumberToFraction} from "@/utils/math";

const LimitTitleContainerCAA: React.FC = () => {
    const {
        fetchUserLimits,
        fetchUserCreditApplication,
        creditApplication,
        creditLimit,
        isLoadingLimits,
        isLoadingApplication
    } = useLimitStore();

    const { fetchAuthedList, isLoadingAuthedList } = useCommonStore();

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits();
            await fetchUserCreditApplication();
            await fetchAuthedList();
        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);

    const [title, setTitle] = useState<string>();
    const [amount, setAmount] = useState<number | string>();
    const [helperText, setHelperText] = useState<string>();
    const isAnyShopAuthed = useCommonStore(s => s.isAnyShopAuthed);
    const authedPlatforms = useCommonStore(s => s.authedPlatforms);
    const approvalLimit = creditLimit?.approval_limit ?? 0;
    const preWml = creditApplication?.pre_wml ?? 0;

    const is_exceeded_10 =  (creditApplication?.type === 'automation' && creditApplication?.status === 'active' && preWml > approvalLimit && preWml > 100000)
        || (creditApplication?.type === 'limit_increase' && creditApplication?.status === 'active' && preWml > approvalLimit && preWml > 100000)

    // const isIndexPage = !(window.location.href.includes("increase-limit") || window.location.href.includes('activate-shop'))
    // const isHomePage = window.location.href.includes('home')


    useEffect(() => {
        if (isLoadingLimits || isLoadingApplication || isLoadingAuthedList) {
            setTitle("加载中...");
            setAmount("加载中...");
            return;
        }

        if(
            is_exceeded_10
            && (window.location.href.includes("increase-limit") || window.location.href.includes('activate-shop'))
        ){
            setTitle("正在激活额度(美元)");
            setAmount(preWml);
            return;
        }

        if (creditApplication && creditApplication?.pre_limit == 0) {
            setTitle(undefined);
            setAmount("暂时未有额度");
            return;
        }

        // type=limit_increase，永远是 还可以用
        if(
            !(window.location.href.includes("increase-limit") || window.location.href.includes('activate-shop'))
            && (creditApplication?.type === 'limit_increase' && creditApplication?.pre_limit && creditLimit )
        ) {
            setTitle("还可以用（美元）");
            setAmount(creditLimit?.available_limit);
            return;
        }

        if(window.location.href.includes('activate-shop/signing') && creditApplication?.type === 'xdj'){
            setTitle("免费额度(美元)");
            setAmount(2000);
            return;
        }

        if (
            !isAnyShopAuthed
            || creditApplication === null
            || (creditApplication?.status === 'active' && (window.location.href.includes("increase-limit") || window.location.href.includes('activate-shop')))
            || (creditApplication?.type === 'limit_increase' && !creditApplication?.pre_limit && !creditLimit )
            || creditApplication?.status === 'failed' && creditApplication?.type === 'limit_increase'
        ) {
            setTitle("最高额度(美元)");
            setAmount(10000000);
            return;
        } else if (
            (creditApplication?.type !== 'automation' && creditApplication?.status === "failed") ||
            (creditApplication?.type === 'automation' && creditApplication?.status === "failed" && creditApplication?.stage === 'info_processing')
        ) {
            setTitle(undefined);
            setAmount("暂时未有额度");
        } else if (
            ["submitted", "pending"].includes(creditApplication?.status ?? "") && creditApplication?.pre_limit == null && (window.location.href.includes('activate-shop') || !creditLimit || Array.isArray(creditLimit))
        ) {
            setTitle("正在激活额度(美元)");
            setAmount("额度计算中");
        } else if (
            (creditApplication?.type === 'automation' && creditApplication?.status === "failed") ||
            creditApplication?.pre_limit && ["submitted", "pending"].includes(creditApplication?.status)
        ) {
            setTitle("正在激活额度(美元)");
                        setTitle("正在激活额度(美元)");
            const DISPLAY_LIMIT_THRESHOLD = 100000;
            const adjusted_pre_limit = Math.min(
              creditApplication?.pre_limit ?? Infinity,
              creditApplication?.pre_wml   ?? Infinity
            );
            const shouldCapDisplayLimit = approvalLimit >= DISPLAY_LIMIT_THRESHOLD;
            if (shouldCapDisplayLimit){
                setAmount(preWml);
                setHelperText('')
                return;
            }
            setAmount(Math.min(adjusted_pre_limit,DISPLAY_LIMIT_THRESHOLD));
            if (preWml > DISPLAY_LIMIT_THRESHOLD){
                setHelperText(`（总额度$ ${formatNumberToFraction(preWml)}）`);
            }else {
                setHelperText('');
            }
        } else {
            setTitle("还可以用（美元）");
            setAmount(creditLimit?.available_limit);
        }
    }, [creditApplication, creditLimit, isAnyShopAuthed, authedPlatforms, isLoadingLimits, isLoadingApplication, isLoadingAuthedList]);

    if (isLoadingLimits || isLoadingApplication || isAnyShopAuthed === undefined) return

    return <AmountWithTitle title={title} amount={amount} loading={isLoadingLimits || isLoadingApplication || isLoadingAuthedList} helperText={helperText} />;
};

export default LimitTitleContainerCAA;
