import Page from "@/components/shared/Page";
import AmountWithTitle from "@/components/shared/AmountWithTitle";
import IconLabelList from "@/components/shared/IconLabelList";
import {LandingPageIconLabelListCAA} from "@/constants/iconLabelList.ts";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import OverlayContainer from "@/components/Container/overlayContainer.tsx";
import RedPocket from "@/components/RedPocket";
import BannerCarousel from "@/components/shared/BannerCarousel";
import AuthPlatformBanner from "@/components/shared/AuthPlatformBanner";
import {useGeneralStore} from "@/store/general";
import { useEffect } from "react";
import { trackEvent } from "@/utils/matomoTracking";
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from "@/components/shared/tracking/constants";


const CreditApprovalAutomation = () => {
    const setAuthModalOpen = useGeneralStore(state => state.setAuthModalOpen);
    const setLoginMethod = useGeneralStore(state => state.setLoginMethod);
    
    useEffect(()=>{
        trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.VIEW, TRACKING_EVENTS.VIEW_LANDING_PAGE);

    },[])

    return (
        <Page>
            <BackdropContainer isShowOverlay>
                <AmountWithTitle title="最高额度(美元)" amount={10000000}/>
                <IconLabelList items={LandingPageIconLabelListCAA}/>
            </BackdropContainer>

            <OverlayContainer>
                <RedPocket
                    actionTitle="激活领取"
                    type="doubleLine"
                    titleLineOne="激活额度领红包"
                    titleLineTwo="500 美元"
                    redPocketTitle="7天免息金，有店就能用"
                    // isShowHelpingText
                    // helpingText={
                    //     <div>
                    //         <img src={ClockIcon} alt="Clock" width={20} height={20}/> 限时免费领取品类报告
                    //     </div>
                    // }
                    actionClick={() => {
                        setLoginMethod('signup')
                        setAuthModalOpen(true)
                    }}
                />
            </OverlayContainer>
            <BannerCarousel/>
            <AuthPlatformBanner/>
        </Page>
    );
};

export default CreditApprovalAutomation;
