import React, { useEffect, useState } from "react";
import Page from "@/components/shared/Page";
import IconLabelList from "@/components/shared/IconLabelList";
import { LandingPageIconLabelListCAA } from "@/constants/iconLabelList.ts";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";

import BannerCarousel from "@/components/shared/BannerCarousel";
import AuthPlatformBanner from "@/components/shared/AuthPlatformBanner";
import { useCommonStore } from "@/store/common.ts";
import PlatformMainModal from "@/components/Platforms";
import { ModalStep } from "@/types/platforms/modalSteps.ts";
import { useLocation, useNavigate } from "react-router-dom";
import LimitActionsContainerCAA from "@/containers/LimitActionsContainerCAA";
import RedPocketContainerCAA from "@/containers/RedPocketContainerCAA.tsx";
import LimitTitleContainerCAA from "@/containers/LimitTitleContainerCAA";
import { useRedPocketStore } from "@/store/redpocket.ts";
import { useLimitStore } from "@/store/limits.ts";
import { trackEvent } from "@/utils/matomoTracking";
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import ModalDialog from "@/components/shared/ModalDialog";
import {OurWechatModal} from "@/components/shared/OurWechatModal";
import { AuthGuideModal } from "@/components/AuthGuideModal";
import {useSelectedProduct} from "@/hooks/useSelectedProduct.ts";
import { usePositionTracking } from "@/hooks/usePositionTracking";

const CreditApprovalAutomation = () => {
    const navigate = useNavigate();

    const { currentPath, retrieveStoredPosition } = usePositionTracking();

    useEffect(() => {
        retrieveStoredPosition();
    }, []);

    useEffect(() => {
        if (currentPath === "/credit/underwriting/register/signing/loading") {
            navigate("/credit/underwriting/register/signing/loading");
        }
    }, [currentPath]);

    const fetchAuthedList = useCommonStore(s => s.fetchAuthedList);
    const authedPlatforms = useCommonStore(s => s.authedPlatforms);
    const isAnyShopAuthed = useCommonStore(s => s.isAnyShopAuthed);
    const refreshUserInfo = useCommonStore(s => s.refreshUserInfo);
    const { fetchRedPocketList } = useRedPocketStore();
    const product = useSelectedProduct();

    const user = useCommonStore(s => s.userInfo);

    const [modalStep, setModalStep] = useState<ModalStep>("none");
    const [showShopNotEligibleModal, setShowShopNotEligibleModal] = useState<boolean>(false);
    const [wechatModal, setWechatModal] = React.useState<boolean>(false);

    const { search, pathname } = useLocation();
    const [openAuthModal, setOpenAuthModal] = useState(false);
    const [authGuideModalCanClose, setAuthGuideModalCanClose] = useState(true);
    const [controlCheckResultFailed,setControlCheckResultFailed] = useState(true)
    const [thirdPartyCheckResultFailed,setThirdPartyCheckResultFailed] = useState(true)
    
    

    const routeToPage = (url: string) => {
        const baseUrl = product === "xdj" ? "credit/hook" : "credit/underwriting";
        navigate(`/${baseUrl}/${url}`);
    };

    const authGuideModalOnClose = () => {
        setOpenAuthModal(false);
    };
    const { fetchUserLimits, fetchUserCreditApplication, creditApplication, creditLimit } = useLimitStore();

    useEffect(() => {
        trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.VIEW, TRACKING_EVENTS.VIEW_LANDING_PAGE);
    }, []);

    useEffect(() => {
        const handleAuthCallback = async () => {
            try {
                await fetchAuthedList();
            } catch (err) {
                console.error("刷新授权列表出错:", err);
            }
        };
        const params = new URLSearchParams(search);
        const token = params.get("id_token");
        if (!token) {
            void handleAuthCallback();
        }
    }, [search, pathname, navigate, fetchAuthedList, user]);

    useEffect(() => {
        if (!authedPlatforms) {
            return;
        }
        if (!isAnyShopAuthed && modalStep === "none") {
            setModalStep("platformSelection");
        }
    }, [isAnyShopAuthed, authedPlatforms]);

    useEffect(() => {
        refreshUserInfo();
        (async () => {
            await fetchRedPocketList();
        })();
    }, [refreshUserInfo, fetchRedPocketList]);

    useEffect(() => {
        if (creditApplication?.pre_limit == 0 && authedPlatforms?.ouath_change == 0) {
            setShowShopNotEligibleModal(true);
        }
    }, [creditApplication, authedPlatforms]);

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits(true);
            await fetchUserCreditApplication(true);
            if (creditApplication?.type != "limit_increase") {
                setAuthGuideModalCanClose(false);
            }

        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);

    useEffect(() => {
        if (creditApplication?.type ==='automation' && creditApplication?.psp_check_status === 0) {
            setControlCheckResultFailed(!creditApplication?.control_check_result)
            setThirdPartyCheckResultFailed(!creditApplication?.thirdparty_check_result)
            setOpenAuthModal(true);
        }

        
    }, [creditApplication]);

    let isPendingLogoList = false;
    if (
        ["submitted", "pending"].includes(creditApplication?.status ?? "") &&
        creditApplication?.pre_limit == null &&
        (!creditLimit || Array.isArray(creditLimit))
    ) {
        isPendingLogoList = true;
    }

    return (
        <Page>
            <BackdropContainer isShowOverlay>
                <LimitTitleContainerCAA />
                {isPendingLogoList &&
                !(creditApplication?.type === "limit_increase" && !creditApplication?.pre_limit && !creditLimit) ? (
                    <div
                        style={{
                            fontSize: "24px",
                            color: "#BCB9C8",
                            textAlign: "center",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            marginTop: "20px",
                            font: "Source Sans Pro"
                        }}
                    >
                        预计 <span style={{ color: "#635D7E", margin: "5px" }}>30分钟</span>{" "}
                        完成，请点击右侧【线上客服】联系我们，查看额度
                    </div>
                ) : (
                    <IconLabelList items={LandingPageIconLabelListCAA} />
                )}
                <LimitActionsContainerCAA openAuthShopModalDialog={() => setShowShopNotEligibleModal(true)} />
            </BackdropContainer>

            {(!creditApplication ||
                (creditApplication?.type === "automation" && creditApplication?.status === "pending")) && (
                <RedPocketContainerCAA
                    clickEvent={() => {
                        setModalStep("platformSelection");
                    }}
                />
            )}

            <PlatformMainModal modalStep={modalStep} setModalStep={setModalStep} authedPlatforms={authedPlatforms} />
            <AuthGuideModal open={openAuthModal} onClose={authGuideModalOnClose} canClose={authGuideModalCanClose} thirdPartyCheckResultFailed={thirdPartyCheckResultFailed} controlCheckResultFailed={controlCheckResultFailed} />
            <BannerCarousel />
            <AuthPlatformBanner />

            <ModalDialog
                open={showShopNotEligibleModal}
                title={"溫馨提示"}
                isShowCircle={true}
                isClosable={true}
                onClose={() => setShowShopNotEligibleModal(false)}
                actions={[
                    {
                        label: "线上咨询",
                        onClick: () => {
                            setShowShopNotEligibleModal(false);
                            setWechatModal(true);
                        },
                        variant: "secondary"
                    },
                    {
                        label: "增加店铺",
                        onClick: () => {
                            setShowShopNotEligibleModal(false);
                            routeToPage("increase-limit/authorize");
                            return;
                        },
                        variant: "primary"
                    }
                ]}
            >
                很抱歉, 您授权的店铺尚未完全达到要求, 请新增店铺重新申请吧~
            </ModalDialog>
            <OurWechatModal
                open={wechatModal}
                onClose={() => {
                    setWechatModal(false);
                    setShowShopNotEligibleModal(true);
                }}
                hasAlertIcon
                textAlign={"center"}
            />
        </Page>
    );
};

export default CreditApprovalAutomation;
