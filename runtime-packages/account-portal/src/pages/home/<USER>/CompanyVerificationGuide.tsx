import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import type { GetUserCompanyRes } from "@fundpark/fp-api/types/company.ts";
import { CustomButton } from "@/components/custom";
import RightArrowIcon from "@/assets/icons/myCompany/right-arrow.svg?react";
import { KYCStatus } from "@/types/company/enum";
import styles from "../index.module.scss";

const CompanyVerificationGuide: React.FC<{
    companyList: GetUserCompanyRes;
    loadSuccess?: boolean;
}> = ({ companyList, loadSuccess }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [hasCompany, setHasCompany] = useState(false);
    const [kycApproved, setKycApproved] = useState(false);

    useEffect(() => {
        if (loadSuccess) {
            setHasCompany(companyList.length > 0);
            setKycApproved(companyList.some(item => item.kycStatus === KYCStatus.Approved));
        }
    }, [loadSuccess, companyList]);

    if (!loadSuccess) {
        return null;
    }

    if (hasCompany && kycApproved) {
        return null;
    }

    let title = "";
    let desc = "";
    let btnText = "";
    if (hasCompany) {
        if (!kycApproved) {
            title = t("home.notVerifiedCompany");
            desc = t("home.notVerifiedCompanyDesc");
            btnText = t("home.goToCertify");
        }
    } else {
        title = t("home.notConnectedCompany");
        desc = t("home.notConnectedCompanyDesc");
        btnText = t("home.goToConnectCompany");
    }

    return (
        <div className={styles.homeGuide1}>
            <div className="flex-1">
                <div className={styles.homeGuide1Title}>{title}</div>
                <div className={styles.homeGuide1Desc}>{desc}</div>
            </div>
            <CustomButton
                round
                type="primary"
                className={styles.homeGuide1Btn}
                onClick={() => {
                    navigate("/company");
                }}
            >
                {btnText}
                <RightArrowIcon className="text-[12px]" />
            </CustomButton>
        </div>
    );
};

export default CompanyVerificationGuide;
