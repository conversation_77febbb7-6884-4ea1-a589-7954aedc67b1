import { useEffect, useState } from "react";
import cls from "classnames";
import { useTranslation } from "react-i18next";
import type { GetUserCompanyRes } from "@fundpark/fp-api/types/company.ts";
import CheckedIcon from "@/assets/icons/home/<USER>";
import ApprovingIcon from "@/assets/icons/home/<USER>";
import ArrowRightIcon from "@/assets/icons/home/<USER>";
import WarningIcon from "@/assets/icons/home/<USER>";
import { KYCStatus } from "@/types/company/enum";
import styles from "../index.module.scss";

const Rectangle: React.FC<{
    fill?: string;
    fillOpacity?: number;
    style?: React.CSSProperties;
    className?: string;
}> = ({ className, style, fill, fillOpacity }) => {
    return (
        <svg
            className={className}
            style={style}
            width="198"
            height="30"
            viewBox="0 0 198 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 8C0 3.58172 3.58172 0 8 0H183.32C185.637 0 187.84 1.00427 189.359 2.75307L195.441 9.75307C198.056 12.7624 198.056 17.2376 195.441 20.2469L189.359 27.2469C187.84 28.9957 185.637 30 183.32 30H8C3.58172 30 0 26.4183 0 22V8Z"
                fill={fill}
                fillOpacity={fillOpacity}
            />
        </svg>
    );
};

const NewComersGuide: React.FC<{
    companyList: GetUserCompanyRes;
}> = ({ companyList }) => {
    const { t } = useTranslation();
    const [currentInfo, setCurrentInfo] = useState({
        step: 2,
        pending: false
    });

    useEffect(() => {
        if (companyList.some(item => item.kycStatus === KYCStatus.Approved)) {
            setCurrentInfo({
                step: 3,
                pending: false
            });
        } else if (companyList.some(item => item.kycStatus === KYCStatus.InProgress)) {
            setCurrentInfo({
                step: 2,
                pending: true
            });
        } else {
            setCurrentInfo({
                step: 2,
                pending: false
            });
        }
    }, [companyList]);

    const getStepText = (step: number) => {
        if (step === currentInfo.step) {
            return currentInfo.pending ? (
                <>
                    <ApprovingIcon />
                    {t("home.approving")}
                </>
            ) : (
                <>
                    {t("home.applying")}
                    <ArrowRightIcon />
                </>
            );
        } else if (step < currentInfo.step) {
            return (
                <>
                    <CheckedIcon />
                    {t("home.finished")}
                </>
            );
        } else {
            return (
                <>
                    <WarningIcon />
                    {t("home.notStarted")}
                </>
            );
        }
    };

    return (
        <div className={styles.homeNewcomersGuide}>
            <div className={styles.homeCard1Header}>
                <div className={styles.homeCard1Title}>{t("home.guideForNewcomers")}</div>
            </div>
            <div className={styles.homeNewcomersGuideSteps}>
                <div className={cls(styles.homeNewcomersGuideStep, styles.finishedStep)}>
                    <div className={styles.homeNewcomersGuideStepRectWrap}>
                        <Rectangle className={styles.homeNewcomersGuideStepRect} />
                        <div className={styles.homeNewcomersGuideStepIndex}>01</div>
                    </div>
                    <div className={styles.homeNewcomersGuideStepTitle}>{t("home.userRegistration")}</div>
                    <div className={styles.homeNewcomersGuideStepTag}>
                        <CheckedIcon />
                        {t("home.finished")}
                    </div>
                </div>
                <div
                    className={cls(styles.homeNewcomersGuideStep, {
                        [styles.progressStep]: currentInfo.step === 2,
                        [styles.waitStep]: currentInfo.step < 2,
                        [styles.finishedStep]: currentInfo.step > 2
                    })}
                >
                    <div className={styles.homeNewcomersGuideStepRectWrap}>
                        <Rectangle className={styles.homeNewcomersGuideStepRect} />
                        <div className={styles.homeNewcomersGuideStepIndex}>02</div>
                    </div>
                    <div className={styles.homeNewcomersGuideStepTitle}>{t("home.companyOnbording")}</div>
                    <div className={styles.homeNewcomersGuideStepTag}>{getStepText(2)}</div>
                </div>
                <div
                    className={cls(styles.homeNewcomersGuideStep, {
                        [styles.progressStep]: currentInfo.step === 3,
                        [styles.waitStep]: currentInfo.step < 3,
                        [styles.finishedStep]: currentInfo.step > 3
                    })}
                >
                    <div className={styles.homeNewcomersGuideStepRectWrap}>
                        <Rectangle className={styles.homeNewcomersGuideStepRect} />
                        <div className={styles.homeNewcomersGuideStepIndex}>03</div>
                    </div>
                    <div className={styles.homeNewcomersGuideStepTitle}>{t("home.productApply")}</div>
                    <div className={styles.homeNewcomersGuideStepTag}>{getStepText(3)}</div>
                </div>
                <div
                    className={cls(styles.homeNewcomersGuideStep, {
                        [styles.progressStep]: currentInfo.step === 4,
                        [styles.waitStep]: currentInfo.step < 4,
                        [styles.finishedStep]: currentInfo.step > 4
                    })}
                >
                    <div className={styles.homeNewcomersGuideStepLast}>
                        <div className={styles.homeNewcomersGuideStepIndex}>04</div>
                    </div>
                    <div className={styles.homeNewcomersGuideStepTitle}>{t("home.drawdown")}</div>
                    <div className={styles.homeNewcomersGuideStepTag}>{getStepText(4)}</div>
                </div>
            </div>
        </div>
    );
};

export default NewComersGuide;
