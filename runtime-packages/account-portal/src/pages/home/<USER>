// src/pages/Home.tsx

import Page from "@/components/shared/Page";
import AmountWithTitle from "@/components/shared/AmountWithTitle";
import IconLabelList from "@/components/shared/IconLabelList";
import {LandingPageIconLabelListSDG} from "@/constants/iconLabelList.ts";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import OverlayContainer from "@/components/Container/overlayContainer.tsx";
import RedPocket from "@/components/RedPocket";
import ClockIcon from "@/assets-new/icons/common/clock.svg";
import PlatformMainModal from "@/components/Platforms";
import DrawdownForm from "@/components/DrawdownForm";
import BannerCarousel from "@/components/shared/BannerCarousel";
import AuthPlatformBanner from "@/components/shared/AuthPlatformBanner";
import {useGeneralStore} from "@/store/general";

const Home = () => {
    const setAuthModalOpen = useGeneralStore(state => state.setAuthModalOpen);
    return (
        <Page>
            <BackdropContainer
                isShowWhiteBottom={true}
            >
                <AmountWithTitle
                    title={"最低可得(美元)"}
                    amount={2000}
                />
                <IconLabelList
                    items={LandingPageIconLabelListSDG}
                />
            </BackdropContainer>
            <OverlayContainer>
                <RedPocket
                    redPocketTitle={"7天免息用，有店就能用"}
                    actionTitle={"立即0元用"}
                    // isShowHelpingText={true}
                    // helpingText={
                    //     <div>
                    //         <img src={ClockIcon} alt="Clock Icon" width="20" height="20"/> 限时免费领取品类报告
                    //     </div>
                    // }
                    actionClick={() => setAuthModalOpen(true)}
                />
            </OverlayContainer>
            <BannerCarousel/>
            <AuthPlatformBanner/>
        </Page>
    );
};

export default Home;
