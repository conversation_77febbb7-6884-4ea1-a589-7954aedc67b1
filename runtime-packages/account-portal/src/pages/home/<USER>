@use "@/assets/styles/variables.module.scss" as *;

.home {
    position: relative;
    &-topbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        padding: 16px 0;
        position: sticky;
        top: 64px;
        z-index: 100;
        &-text {
            font-size: 14px;
            line-height: 20px;
            font-weight: 600;
        }
    }
    &-guide1 {
        border-radius: 20px;
        border: 1px solid #fad49e;
        background-color: #fdeed8;
        display: flex;
        align-items: center;
        padding: 16px 24px;
        margin-bottom: 24px;
        &-title {
            font-size: 14px;
            line-height: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }
        &-desc {
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            color: $fp-text2-color;
        }
        &-btn {
            background: #f4a83c;
            color: $fp-text-color;
            &:hover {
                color: $fp-text-color !important;
                background: rgba(244, 168, 60, 0.8) !important;
            }
            &:active {
                color: $fp-text-color !important;
                background: #b07a2b !important;
            }
        }
    }
    &-card1 {
        border-radius: 16px;
        background-color: #f6f7fa;
        padding: 24px;
        margin-bottom: 24px;
        &-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }
        &-title {
            flex: 1;
            font-family: "Poppins";
            font-size: 18px;
            line-height: 24px;
            font-weight: 700;
        }
    }
    &-full-layout {
        display: flex;
        gap: 24px;
    }
    &-left {
        flex: 1;
        width: 0;
    }
    &-right {
        width: 348px;
    }
    &-marketing-carousel {
        margin-bottom: 24px;
        img {
            width: 100%;
            cursor: pointer;
        }
    }
    &-card2 {
        border-radius: 16px;
        background-color: #fff;
        border: 1px solid $fp-gray-border-color;
        padding: 24px;
        margin-bottom: 24px;
        &-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        &-title {
            flex: 1;
            font-family: "Poppins";
            font-size: 16px;
            line-height: 22px;
            font-weight: 700;
        }
    }
    &-newcomers-guide {
        border-radius: 20px;
        padding: 24px;
        background-color: rgba(239, 250, 250, 1);
        border: 1px solid rgba(100, 204, 201, 0.2);
        position: relative;
        margin-bottom: 24px;
        overflow: hidden;
        &::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 211px;
            height: 200px;
            background: url("@/assets/images/home/<USER>") no-repeat;
            background-size: 100% 100%;
        }
        &-steps {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            row-gap: 12px;
        }
        &-step {
            flex: 1;
            &-rect {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                &-wrap {
                    position: relative;
                    width: 200px;
                    height: 30px;
                    padding: 5px 8px;
                }
            }
            &-last {
                position: relative;
                background: rgba(76, 111, 110, 0.15);
                width: 200px;
                height: 30px;
                padding: 5px 8px;
                border-radius: 8px;
            }
            &-index {
                position: absolute;
                top: 5px;
                left: 8px;
                display: inline-block;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                text-align: center;
                font-family: "Poppins";
                font-weight: 400;
                font-size: 12px;
                line-height: 20px;
            }
            &-title {
                font-family: "Poppins";
                font-weight: 600;
                font-size: 14px;
                line-height: 22px;
                color: $fp-text2-color;
                margin: 14px 0 8px;
            }
            &-tag {
                display: inline-block;
                font-weight: 600;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 28px;
                svg {
                    font-size: 20px;
                    vertical-align: bottom;
                    margin-right: 2px;
                }
            }
            &.finished-step {
                .home-newcomers-guide-step-rect {
                    path {
                        fill: $fp-primary-color;
                        fill-opacity: 0.2;
                    }
                }
                .home-newcomers-guide-step-index {
                    color: $fp-primary-color;
                    border: 1px solid $fp-primary-color;
                }
                .home-newcomers-guide-step-tag,
                .home-newcomers-guide-step-last {
                    color: $fp-primary-color;
                    background-color: rgba(100, 204, 201, 0.2);
                }
            }
            &.wait-step {
                .home-newcomers-guide-step-rect {
                    path {
                        fill: #4c6f6e;
                        fill-opacity: 0.15;
                    }
                }
                .home-newcomers-guide-step-index {
                    color: rgba(32, 23, 71, 0.3);
                    border: 1px solid rgba(32, 23, 71, 0.3);
                }
                .home-newcomers-guide-step-tag {
                    color: $fp-info-color;
                    background-color: rgba(244, 168, 60, 0.2);
                }
            }
            &.progress-step {
                .home-newcomers-guide-step-rect {
                    path {
                        fill: $fp-accent-color;
                    }
                }
                .home-newcomers-guide-step-index {
                    color: #fff;
                    border: 1px solid #fff;
                }
                .home-newcomers-guide-step-tag {
                    color: #fff;
                    background: linear-gradient(270.58deg, #212f76 12.92%, #000000 122.34%);
                }
            }
        }
    }
    &-loan-summary {
        display: flex;
        border-radius: 20px;
        background: linear-gradient(180deg, #ebedf3 16.41%, #e3e4ea 112.88%);
        margin-bottom: 24px;
        &-available {
            flex: 1;
            border-radius: 20px;
            padding: 24px;
            background: linear-gradient(189.01deg, #212f76 3.79%, #000000 143.29%);
            &-title {
                flex: 1;
                font-weight: 400;
                font-size: 16px;
                line-height: 22px;
                color: #fff;
                margin-bottom: 4px;
            }
            &-limit-amount {
                font-weight: 900;
                font-size: 24px;
                line-height: 32px;
                color: $fp-primary-color;
            }
        }
        &-oustanding {
            flex: 1;
            padding: 24px;
            &-title {
                flex: 1;
                font-weight: 400;
                font-size: 16px;
                line-height: 22px;
                color: $fp-text2-color;
                margin-bottom: 4px;
            }
            &-limit-amount {
                font-weight: 900;
                font-size: 24px;
                line-height: 32px;
                color: $fp-accent-color;
                margin-bottom: 24px;
            }
            &-repay-btn {
                color: $fp-primary-color;
            }
        }
        &-grid {
            display: flex;
            &-item {
                flex: 1;
                & + & {
                    border-left: 1px solid rgba(233, 232, 237, 0.2);
                    margin-left: 20px;
                    padding-left: 20px;
                }
                &-label {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20px;
                    margin-bottom: 4px;
                    color: rgba(255, 255, 255, 0.7);
                }
                &-num {
                    font-weight: 900;
                    font-size: 14px;
                    line-height: 20px;
                    color: #fff;
                }
            }
            &-item2 {
                flex: 1;
                & + & {
                    border-left: 1px solid $fp-gray-border-color;
                    margin-left: 20px;
                    padding-left: 20px;
                }
                &-label {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20px;
                    color: $fp-text2-color;
                }
                &-num {
                    font-weight: 900;
                    font-size: 14px;
                    line-height: 20px;
                    color: #fff;
                    margin: 4px 0;
                }
                button {
                    text-decoration: underline;
                }
            }
        }
    }
    &-product {
        display: flex;
        flex-direction: column;
        gap: 16px;
        &-item {
            border-radius: 16px;
            background-color: #fff;
            border: 1px solid #fff;
            padding: 16px 24px;
            position: relative;
            overflow: hidden;
			cursor: pointer;
            transition: all 0.3s;
            &:hover {
                border-color: $fp-accent-color;
            }
            &-title {
                font-size: 16px;
                line-height: 22px;
                font-weight: 700;
                font-family: "Poppins";
                margin-bottom: 16px;
            }
            &-status {
                position: absolute;
                top: 0;
                right: 0;
                font-size: 14px;
                padding: 6px 12px;
                border-top-left-radius: 24px;
                border-bottom-left-radius: 24px;
                font-size: 14px;
                line-height: 20px;
                font-weight: 400;
                &.product-active {
                    background-color: #e0f5f4;
                    color: $fp-success-color;
                }
                &.product-closed {
                    background-color: #e9e8ed;
                    color: $fp-text-color;
                }
                &.product-expired {
                    background-color: $fp-gray-border-color;
                    color: $fp-text-subtle-color;
                }
            }
            &-summary {
                display: flex;
                gap: 6px;
                &-label {
                    font-size: 14px;
                    line-height: 20px;
                    font-weight: 400;
                    color: $fp-text2-color;
                    margin-bottom: 4px;
                }
                &-num {
                    font-size: 14px;
                    line-height: 20px;
                    font-weight: 600;
                    font-family: "Poppins";
                }
            }
        }
    }
    &-exchange-rate-form {
        :global {
            .ant-form-item-label > label {
                color: $fp-text-color;
				user-select: none;
            }
        }
        &-reverse {
            position: absolute;
            top: 0;
            right: 42px;
			padding: 6px;
			border-radius: 4px;
            cursor: pointer;
            z-index: 10;
            color: $fp-text-subtle-color;
            transition: all 0.3s;
            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
            }
        }
    }
}
