import { useEffect, useState } from "react";
import cls from "classnames";
import { Spin } from "antd";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { GetFacilityList } from "@fundpark/fp-api";
import type { FacilityListVO } from "@fundpark/fp-api/types/home.ts";
import { CustomButton } from "@/components/custom";
import EmptyImg from "@/assets/images/home/<USER>";
import RightArrowIcon from "@/assets/icons/myCompany/right-arrow.svg?react";
import { moneyFormat } from "@fundpark/ui-utils";
import { FacilityStatusEnum } from "@/types/facility";
import styles from "../index.module.scss";

const Product: React.FC = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [facilityList, setFacilityList] = useState<FacilityListVO[]>([]);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        GetFacilityList({ pageNum: 1, pageSize: 100 })
            .then(res => {
                if (res.data) {
                    setFacilityList(res.data.records || []);
                }
            })
            .finally(() => {
                setLoading(false);
            });
    }, []);

    const statusMap: Record<string, { color: string; text: string }> = {
        [FacilityStatusEnum.Activated]: {
            color: styles.productActive,
            text: t("home.active")
        },
        [FacilityStatusEnum.Closed]: {
            color: styles.productClosed,
            text: t("home.closed")
        },
        [FacilityStatusEnum.Expired]: {
            color: styles.productExpired,
            text: t("home.expired")
        }
    };

    const goToDetail = (item: FacilityListVO) => {
        navigate("/facility/detail/" + item.id);
    };

    return (
        <div className={styles.homeCard1}>
            <div className={styles.homeCard1Header}>
                <div className={styles.homeCard1Title}>{t("home.myProducts")}</div>
            </div>
            <Spin spinning={loading}>
                {facilityList.length === 0 && (
                    <div className="flex flex-col justify-center items-center">
                        <EmptyImg />
                        <div className="text-[16px] font-semibold mt-[4px] mb-[24px]">{t("home.noProduct")}</div>
                        <CustomButton
                            round
                            type="primary"
                            onClick={() => {
                                // navigate("/company");
                            }}
                        >
                            {t("home.goApplyNow")}
                            <RightArrowIcon className="text-[12px]" />
                        </CustomButton>
                    </div>
                )}
                <div className={styles.homeProduct}>
                    {facilityList.map(item => {
                        const statusOption = statusMap[item.facilityStatus];
                        return (
                            <div key={item.id} className={styles.homeProductItem} onClick={() => goToDetail(item)}>
                                <div className={cls(styles.homeProductItemStatus, statusOption?.color)}>
                                    {statusOption?.text || item.facilityStatus}
                                </div>
                                <div className={styles.homeProductItemTitle}>{item.financingType}</div>
                                <div className={styles.homeProductItemSummary}>
                                    <div className="flex-1">
                                        <div className={styles.homeProductItemSummaryLabel}>
                                            {t("home.availableFunding")}
                                        </div>
                                        <div
                                            className={styles.homeProductItemSummaryNum}
                                            style={{ color: styles.fpPrimaryColor }}
                                        >
                                            {moneyFormat(item.facilityAvailableFundingAmount)}
                                        </div>
                                    </div>
                                    <div className="flex-1">
                                        <div className={styles.homeProductItemSummaryLabel}>{t("home.watermark")}</div>
                                        <div className={styles.homeProductItemSummaryNum}>
                                            {moneyFormat(item.watermarkAmount)}
                                        </div>
                                    </div>
                                    <div className="flex-1">
                                        <div className={styles.homeProductItemSummaryLabel}>
                                            {t("home.facilityLimit")}
                                        </div>
                                        <div className={styles.homeProductItemSummaryNum}>
                                            {moneyFormat(item.facilityLimitAmount)}
                                        </div>
                                    </div>
                                    <div className="flex-1">
                                        <div className={styles.homeProductItemSummaryLabel}>{t("home.oustanding")}</div>
                                        <div className={styles.homeProductItemSummaryNum}>
                                            {moneyFormat(item.currentOutstandingAmount)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </Spin>
        </div>
    );
};

export default Product;
