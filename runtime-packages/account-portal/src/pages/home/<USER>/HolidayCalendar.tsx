import {} from "react";
import { useTranslation } from "react-i18next";
import { Calendar, Button } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import styles from "../index.module.scss";

const HolidayCalendar: React.FC = () => {
    const { t } = useTranslation();

    return (
        <div className={styles.homeCard2}>
            <div className={styles.homeCard2Header}>
                <div className={styles.homeCard2Title}>{t("home.bankHolidayCalendar")}</div>
            </div>
            <div>
                <Calendar
                    fullscreen={false}
                    headerRender={({ value, onChange }) => {
                        return (
                            <div className="flex items-center justify-between mb-[16px]">
                                <div className="text-[16px] leading-[20px] font-semibold">
                                    {value.format("YYYY-MM")}
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        type="text"
                                        size="middle"
                                        icon={<LeftOutlined />}
                                        onClick={() => {
                                            onChange(value.clone().subtract(1, "month"));
                                        }}
                                    ></Button>
                                    <Button
                                        type="text"
                                        size="middle"
                                        icon={<RightOutlined />}
                                        onClick={() => {
                                            onChange(value.clone().add(1, "month"));
                                        }}
                                    ></Button>
                                </div>
                            </div>
                        );
                    }}
                />
            </div>
        </div>
    );
};

export default HolidayCalendar;
