import { useState } from "react";
import { useTranslation } from "react-i18next";
import EmptyImg from "@/assets/images/home/<USER>";
import styles from "../index.module.scss";

const Repayment: React.FC = () => {
    const { t } = useTranslation();

    return (
        <div className={styles.homeCard1}>
            <div className={styles.homeCard1Header}>
                <div className={styles.homeCard1Title}>{t("home.repaymentSchedule")}</div>
            </div>
            <div className="flex flex-col justify-center items-center">
                <EmptyImg />
                <div className="text-[16px] font-semibold mt-[4px] mb-[24px]">{t("common.nodata")}</div>
            </div>
        </div>
    );
};

export default Repayment;
