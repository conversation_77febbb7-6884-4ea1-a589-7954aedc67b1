import { useEffect, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Select, Form } from "antd";
import { SwapOutlined } from "@ant-design/icons";
import { GetCurrencyRate } from "@fundpark/fp-api";
import type { CurrencyRateVO } from "@fundpark/fp-api/types/common.ts";
import RefreshIcon from "@/assets/icons/refresh2.svg?react";
import TransformIcon from "@/assets/icons/home/<USER>";
import { CustomButton } from "@/components/custom";
import AmountInput from "@/components/AmountInput";
import styles from "../index.module.scss";

interface TransformInput {
    fromCurrency: string;
    from: number;
    toCurrency: string;
    to: number;
}

const ExchangeRates: React.FC = () => {
    const { t } = useTranslation();
    const [currencyRate, setCurrencyRate] = useState<(CurrencyRateVO & { value: string })[]>([]);
    const [loading, setLoading] = useState(true);
    const [form] = Form.useForm<TransformInput>();
    const rateMap = useRef<Record<string, number>>({});

    useEffect(() => {
        initData();
    }, []);

    const initData = async () => {
        setLoading(true);
        try {
            const res = await GetCurrencyRate({ pageNum: 1, pageSize: 100, fxStandard: "Standard", fxType: "Buy" });
            if (res.data) {
                const newRateMap: typeof rateMap.current = {};
                const list = (res.data.records || []).map(item => {
                    const from = item.currency;
                    item.dynamicCurrencyList.forEach(item2 => {
                        const to = item2.currency;
                        const rate = parseFloat(item2.rate);
                        newRateMap[`${from}_${to}`] = rate;
                    });
                    return {
                        ...item,
                        value: item.currency
                    };
                });
                rateMap.current = newRateMap;

                const values = form.getFieldsValue(true) as TransformInput;
                if (list.length > 0 && (!values.fromCurrency || !values.toCurrency)) {
                    form.setFields([
                        { name: "fromCurrency", value: list[0].currency },
                        { name: "toCurrency", value: list[0].currency }
                    ]);
                }

                onFromChange();
                setCurrencyRate(list);
            }
        } finally {
            setLoading(false);
        }
    };

    const onFromChange = () => {
        const values = form.getFieldsValue(true) as TransformInput;
        if (values.fromCurrency && values.toCurrency && values.from !== undefined) {
            const rate = rateMap.current[`${values.fromCurrency}_${values.toCurrency}`];
            if (rate !== undefined) {
                const to = +(values.from * rate).toFixed(2);
                form.setFields([{ name: "to", value: to }]);
            } else {
                form.setFields([{ name: "to", value: undefined }]);
            }
        }
    };

    const onToChange = () => {
        const values = form.getFieldsValue(true) as TransformInput;
        if (values.fromCurrency && values.toCurrency && values.from !== undefined) {
            const rate = rateMap.current[`${values.toCurrency}_${values.fromCurrency}`];
            if (rate !== undefined) {
                const from = +(values.to * rate).toFixed(2);
                form.setFields([{ name: "from", value: from }]);
            } else {
                form.setFields([{ name: "from", value: undefined }]);
            }
        }
    };

    const reverseCurrency = () => {
        const values = form.getFieldsValue(true) as TransformInput;
        form.setFields([
            { name: "fromCurrency", value: values.toCurrency },
            { name: "toCurrency", value: values.fromCurrency }
        ]);

        onFromChange();
    };

    return (
        <div className={styles.homeCard2}>
            <div className={styles.homeCard2Header}>
                <div className={styles.homeCard2Title}>{t("home.exchangeRates")}</div>
                <CustomButton
                    type="text"
                    style={{ color: styles.fpTextSubtleColor }}
                    loading={loading}
                    onClick={initData}
                >
                    {t("home.update")}
                    <RefreshIcon className="text-black" />
                </CustomButton>
            </div>
            <div className="mb-[12px] font-semibold leading-[20px] text-[14px]">{t("home.searchRate")}</div>
            <Form form={form} layout="vertical" className={styles.homeExchangeRateForm}>
                <Form.Item label={t("home.currency")}>
                    <div className="fp-compact">
                        <Form.Item noStyle name="from">
                            <AmountInput
                                variant="borderless"
                                className="flex-1"
                                size="middle"
                                controls={false}
                                onChange={onFromChange}
                            />
                        </Form.Item>
                        <Form.Item noStyle name="fromCurrency">
                            <Select
                                variant="borderless"
                                size="middle"
                                style={{ width: 120 }}
                                options={currencyRate}
                                onChange={onFromChange}
                            />
                        </Form.Item>
                    </div>
                </Form.Item>
                <div className="relative">
                    <SwapOutlined onClick={reverseCurrency} className={`${styles.homeExchangeRateFormReverse} rotate-90`} />
                    {/* <TransformIcon onClick={reverseCurrency} className={styles.homeExchangeRateFormReverse} /> */}
                    <Form.Item label={t("home.currency")}>
                        <div className="fp-compact">
                            <Form.Item noStyle name="to">
                                <AmountInput
                                    variant="borderless"
                                    className="flex-1"
                                    size="middle"
                                    controls={false}
                                    onChange={onToChange}
                                />
                            </Form.Item>
                            <Form.Item noStyle name="toCurrency">
                                <Select
                                    variant="borderless"
                                    size="middle"
                                    style={{ width: 120 }}
                                    options={currencyRate}
                                    onChange={onFromChange}
                                />
                            </Form.Item>
                        </div>
                    </Form.Item>
                </div>
            </Form>
        </div>
    );
};

export default ExchangeRates;
