import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Progress } from "antd";
import {} from "es-toolkit";
import { GetFacilitySummary } from "@fundpark/fp-api";
import type { GetFacilitySummaryRes } from "@fundpark/fp-api/types/home.ts";
import { moneyFormat } from "@fundpark/ui-utils";
import { CustomButton } from "@/components/custom";
import { useCommonStore } from "@/store/common";
import styles from "../index.module.scss";

const LoanSummary: React.FC = () => {
    const { t } = useTranslation();
    const [loanSummary, setLoanSummary] = useState<GetFacilitySummaryRes>({} as GetFacilitySummaryRes);

    useEffect(() => {
        const store = useCommonStore.getState();
        if (store.currentCompany) {
            GetFacilitySummary({ id: store.currentCompany.id }).then(res => {
                if (res.data) {
                    const data = res.data;
                    setLoanSummary(data);
                }
            });
        }
    }, []);

    function getAvailablePercent() {
        // 分子：可用额度, 分母：水位额度和授信额度哪一个最小
        const percent =
            Number(loanSummary.facilityAvailableFundingAmount || 0) /
            Math.min(Number(loanSummary.watermarkAmount || 0), Number(loanSummary.facilityLimitAmount || 0));
        if (!isNaN(percent)) {
            return percent * 100;
        }
        return 0;
    }

    return (
        <div className={styles.homeLoanSummary}>
            <div className={styles.homeLoanSummaryAvailable}>
                <div className="flex items-center">
                    <div className={styles.homeLoanSummaryAvailableTitle}>{t("home.availableLimit")}</div>
                    <CustomButton customPrimary type="primary" round size="middle">
                        {t("home.drawdownNow")}
                    </CustomButton>
                </div>
                <div className={styles.homeLoanSummaryAvailableLimitAmount}>
                    {moneyFormat(loanSummary.facilityAvailableFundingAmount)}
                </div>
                <div className="my-[16px]">
                    <Progress
                        strokeColor={styles.fpPrimaryColor}
                        trailColor="rgba(255, 255, 255, 0.3)"
                        percent={getAvailablePercent()}
                        showInfo={false}
                        size={{ height: 16 }}
                    />
                </div>
                <div className={styles.homeLoanSummaryGrid}>
                    <div className={styles.homeLoanSummaryGridItem}>
                        <div className={styles.homeLoanSummaryGridItemLabel}>{t("home.watermark")}</div>
                        <div className={styles.homeLoanSummaryGridItemNum} style={{ color: styles.fpTertiaryColor }}>
                            {moneyFormat(loanSummary.watermarkAmount)}
                        </div>
                    </div>
                    <div className={styles.homeLoanSummaryGridItem}>
                        <div className={styles.homeLoanSummaryGridItemLabel}>{t("home.totalLimit")}</div>
                        <div className={styles.homeLoanSummaryGridItemNum}>
                            {moneyFormat(loanSummary.facilityLimitAmount)}
                        </div>
                    </div>
                    <div className={styles.homeLoanSummaryGridItem}>
                        <div className={styles.homeLoanSummaryGridItemLabel}>{t("home.usedLimit")}</div>
                        <div className={styles.homeLoanSummaryGridItemNum}>
                            {moneyFormat(loanSummary.facilityUsedLimitAmount)}
                        </div>
                    </div>
                </div>
            </div>
            <div className={styles.homeLoanSummaryOustanding}>
                <div className="flex items-center">
                    <div className={styles.homeLoanSummaryOustandingTitle}>{t("home.outstandingLoan")}</div>
                    <CustomButton
                        className={styles.homeLoanSummaryOustandingRepayBtn}
                        type="primary"
                        round
                        size="middle"
                    >
                        {t("home.repayNow")}
                    </CustomButton>
                </div>
                <div className={styles.homeLoanSummaryOustandingLimitAmount}>
                    {moneyFormat(loanSummary.currentOutstandingAmount)}
                </div>
                <div className={styles.homeLoanSummaryGrid}>
                    <div className={styles.homeLoanSummaryGridItem2}>
                        <div className={styles.homeLoanSummaryGridItem2Label}>{t("home.applying")}</div>
                        <div className={styles.homeLoanSummaryGridItem2Num} style={{ color: styles.fpSuccessColor }}>
                            {moneyFormat(loanSummary.watermarkAmount)}
                        </div>
                        <CustomButton type="link" size="small">
                            0 {t("home.records")}
                        </CustomButton>
                    </div>
                    <div className={styles.homeLoanSummaryGridItem2}>
                        <div className={styles.homeLoanSummaryGridItem2Label}>{t("home.dueIn7days")}</div>
                        <div className={styles.homeLoanSummaryGridItem2Num} style={{ color: styles.fpWarningColor }}>
                            {moneyFormat(loanSummary.facilityLimitAmount)}
                        </div>
                        <CustomButton type="link" size="small">
                            0 {t("home.records")}
                        </CustomButton>
                    </div>
                    <div className={styles.homeLoanSummaryGridItem2}>
                        <div className={styles.homeLoanSummaryGridItem2Label}>{t("home.dueIn30days")}</div>
                        <div className={styles.homeLoanSummaryGridItem2Num} style={{ color: styles.fpErrorColor }}>
                            {moneyFormat(loanSummary.facilityUsedLimitAmount)}
                        </div>
                        <CustomButton type="link" size="small">
                            0 {t("home.records")}
                        </CustomButton>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoanSummary;
