import {useEffect, useState} from "react";
import {Button as <PERSON><PERSON><PERSON><PERSON><PERSON>, Form} from "antd";
import {SendResetPasswordEmail} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper";
import {useTranslation} from "react-i18next";
import FormContainer from "@/components/common/form/FormContainer";
import {Input} from "@/components/common/Input";
import {Button} from "@/components/common";
import {validateEmail} from "@fundpark/ui-utils";
import styles from "./index.module.scss";


interface ForgetPasswordProps {
    onBack: () => void;
    onResetSent: (email: string) => void;
}

const ForgetPassword: React.FC<ForgetPasswordProps> = ({onBack, onResetSent}) => {
    const {t} = useTranslation();
    const [loginForm] = Form.useForm();
    const [loading, setLoading] = useState(false);

    const EMAIL_RULES = [
        {required: true, message: t("login.emailRequired")},
        {
            validator: async (_: any, value: string) => {
                if (!value) return;
                if (!validateEmail(value)) {
                    throw new Error(t("login.emailInvalid"));
                }
            }
        }
    ];

    useEffect(() => {
        appHelper.clearAccessInfo();
    }, []);

    const handleResendEmail = async () => {
        const values = await loginForm.validateFields();
        const req = {
            email: values.email,
            host_name: "ACCOUNT_PORTAL_HOST",
            reset_page: "reset-password"
        }
        try {
            setLoading(true);
            const res = await SendResetPasswordEmail(req);
            if (res.code === 0) {
                onResetSent(values.email);
            } else {
                let message;
                switch (res.code) {
                    case 1:
                        message = "邮箱不存在"
                        break
                    case 2:
                        message = "邮箱已存在"
                        break
                    default:
                        message = "验证码发送失败"
                        break
                }
                loginForm.setFields([{
                    name: "email",
                    errors: [message]
                }]);
            }
        } catch (error: any) {
            loginForm.setFields([{
                name: "email",
                errors: [error?.message || "验证码发送失败"]
            }]);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <div style={{
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
            }}>
                <FormContainer>
                    <Form form={loginForm} layout="vertical" className="w-full" requiredMark={false}>
                        <div className={styles.title}>{t("forgotPassword.title")}</div>

                        <div className={styles.content}>{t("forgotPassword.help")}</div>

                        <Form.Item label={t("login.email")} name="email" rules={EMAIL_RULES} className="mb-6">
                            <Input disabled={loading} placeholder={t("login.emailRequired")} size="large"/>
                        </Form.Item>
                        <Button
                            onClick={() => handleResendEmail()}
                            loading={loading}
                            label={t("forgotPassword.send")}
                            className="mt-6 mb-6"
                        />
                        <GenericButton
                            className={styles.back}
                            type="link"
                            onClick={onBack}
                        >
                            {t("forgotPassword.backToLogin")}
                        </GenericButton>

                    </Form>
                </FormContainer>
            </div>
        </>
    );
};

export default ForgetPassword;
