import { Checkbox, Col, Form, message, Row} from 'antd';
import './index.scss';
import {Input} from "@/components/common";
import {useTranslation} from "react-i18next";
import {useState} from "react";
import {validateMobilePhone} from "@fundpark/ui-utils";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants.ts";
import {MATOMO_CONFIG} from "@/utils/matomoConfig.ts";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import CountryCodePhoneInput from "@/components/shared/CountryCodePhoneInput";
import {DEFAULT_AREA_CODES} from "@/components/common/constants.ts";
import SendButton from "@/components/common/SendButton";
import {Signup as SignupHandler, SignupValidateToken} from "@fundpark/fp-api";
import fundparkXDJWaiveInterestPlan from "@/assets-new/pdfs/fundpark-xdj-waive-interest-plan.pdf"
import fundparkRevenueFinancingSubsidyPlan from "@/assets-new/pdfs/fundpark-revenue-financing-subsidy-plan.pdf"
import {useNavigate, useSearchParams} from "react-router-dom";
import {getEmailRules, getMobileRules, getWechatRules} from "@/constants/data_validation_rules.ts";
import React, { forwardRef, useImperativeHandle } from 'react';


const SignupWrapper = forwardRef(({inputStyle, onSubmit, placeholderColor, checkboxTextColor, errorTextColor, countryCodeColor, rowGap, form}, ref) => {
    const {t} = useTranslation();
    // const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const {trackEventWithDimensions} = useMatomoContext();
    const [validationError, setValidationError] = useState('');
    const defaultInputStyle = {
        '--placeholder-color': placeholderColor || '#282830'
    };

    // Combine default styles with incoming styles
    const combinedInputStyle = { ...defaultInputStyle, ...inputStyle};

    const handlePhoneNumberChange = () => {
        let countryCode = form.getFieldValue("phoneCountryCode");
        let phoneNumber = form.getFieldValue("phoneNumber");

        if (countryCode === undefined) {
            countryCode = "";
        }

        if (phoneNumber === undefined) {
            phoneNumber = "";
        }

        const value = countryCode + phoneNumber

        trackEventWithDimensions({
            category: TRACKING_CATEGORIES.FORM,
            action: TRACKING_ACTIONS.INPUT,
            name: TRACKING_EVENTS.INPUT_PHONE_NUMBER,
            customDimensions: {
                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: value
            }
        });
    };

    const EMAIL_RULES = getEmailRules(t)
    const MOBILE_RULES = getMobileRules(t, form, "phoneCountryCode");
    const WECHAT_RULES = getWechatRules()

    const handleSendMobileVerification = async () => {
        try {
            const phone = form.getFieldValue("phoneNumber");
            const areaCode = form.getFieldValue("phoneCountryCode");

            if (!phone) {
                message.error(t("myProfile.mobileRequired"));
                return false;
            }

            const res = await SignupValidateToken({
                phone_country_code: areaCode,
                phone_number: phone
            } as any);

            if (res.message === "success") {
                message.success(t("myProfile.verificationCodeSent"));
                return true;
            } else {
                form.setFields([
                    {
                        name: "mobileVerification",
                        errors: [res.message]
                    }
                ]);
                return false;
            }
        } catch (error) {
            console.error("Mobile verification error:", error);
            form.setFields([
                {
                    name: "mobileVerification",
                    errors: [t("signup.phoneVerificationCodeFail")]
                }
            ]);
            return false;
        }
    };

    const validateCheckbox = (_: any, checked: boolean) => {
        if (checked) {
            setValidationError(''); 
            return Promise.resolve();
        } else {
            setValidationError(t("signup.agreementRequired"));
            return Promise.reject(new Error(t("signup.agreementRequired")));
        }
    };

    return (
        <div>
            <Form
                form={form}
                onFinish={onSubmit}
                layout="vertical"
                className="w-full"
                requiredMark={false}
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    marginTop: '15px',
                    maxWidth: '430px',
                    gap: rowGap? `${rowGap}px` : '5px'
                }}
                initialValues={{ phoneCountryCode: '+86' }}
            >
                <Form.Item style={{'--errorText-color': errorTextColor, height: '40px', width: '100%'}}>
                    <CountryCodePhoneInput
                        countryCodeName="phoneCountryCode"
                        phoneNumberName="phoneNumber"
                        countryCodeOptions={DEFAULT_AREA_CODES}
                        countryCodeRules={[
                            {required: true, message: "请选择国家/地区代码"},
                            {
                                validator: (_, value) => {
                                    if (value && value.replace("+", "") === "") {
                                        return Promise.reject(new Error("请选择有效的国家/地区代码"));
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                        phoneNumberRules={MOBILE_RULES}
                        countryCodePlaceholder="选择"
                        phoneNumberPlaceholder={t("login.mobileRequired")}
                        disabled={loading}
                        onCountryCodeChange={handlePhoneNumberChange}
                        onPhoneNumberBlur={handlePhoneNumberChange}
                        combinedInputStyle={combinedInputStyle}
                        placeholderColor={placeholderColor}
                        countryCodeColor={countryCodeColor}
                        errorColor={errorTextColor}
                    />
                </Form.Item>

                <Form.Item
                    className={`mb-${rowGap? rowGap:2}  w-full`}
                    name={"mobileVerification"}
                    rules={[{required: true, message: t("signup.verificationCodeRequired")}]}
                    style={{'--errorText-color': errorTextColor}}
                >
                    <Input
                        placeholder={t("signup.verificationCodeRequired")}
                        size="large"
                        className="!pr-1"
                        style={combinedInputStyle}
                        suffix={
                            <Form.Item
                                noStyle
                                shouldUpdate={(prev, curr) =>
                                    prev.phoneNumber !== curr.phoneNumber ||
                                    prev.phoneCountryCode !== curr.phoneCountryCode
                                }
                            >
                                {({getFieldValue}) => {
                                    const isValidInput = validateMobilePhone(
                                        getFieldValue("phoneNumber"),
                                        getFieldValue("phoneCountryCode")
                                    );

                                    return (
                                        <SendButton
                                            onClick={async () => {
                                                return handleSendMobileVerification();
                                            }}
                                            countdown={60}
                                            disabled={!isValidInput}
                                            title={"发送验证码"}
                                            bgc={"red"}
                                        />
                                    );
                                }}
                            </Form.Item>
                        }
                    />
                </Form.Item>

                <Form.Item name="email" rules={EMAIL_RULES} className={`mb-${rowGap? rowGap:2}  w-full`} style={{'--errorText-color': errorTextColor}}>
                    <Input
                        disabled={loading}
                        placeholder={t("login.emailRequired")}
                        size="large"
                        style={combinedInputStyle}
                        onBlur={e => {
                            trackEventWithDimensions({
                                category: TRACKING_CATEGORIES.FORM,
                                action: TRACKING_ACTIONS.INPUT,
                                name: TRACKING_EVENTS.INPUT_EMAIL,
                                customDimensions: {
                                    [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                }
                            });
                        }}
                    />
                </Form.Item>

                <Form.Item
                    name="agreeCheckBox"
                    valuePropName="checked"
                    style={{
                        marginBottom: "0px",
                        paddingBottom: "0px",
                    }}
                    rules={[{ validator: validateCheckbox }]}
                    validateStatus={validationError ? 'error' : ''}
                    help={validationError ? <span style={{ color: errorTextColor || 'red'}}>{validationError}</span> : ''}
                >
                    <Checkbox
                        style={{
                            fontSize: "12px",
                            fontWeight: "400",
                            fontFamily: "Source Sans Pro, sans-serif",
                            marginBottom: '10px',
                            padding: 0,
                            textAlign: "left",
                        }}
                    >
                        <span style={{margin: 0, padding: 0, display: "inline", color: checkboxTextColor || 'black'}}>
                            {t("signup.agreementBody")}
                            <a
                                type="link"
                                className="signup-tnc-button"
                                style={{fontSize: "12px"}}
                                onClick={(e) => {
                                    e.preventDefault();
                                    window.open("https://www.fundpark.com/en/terms-and-conditions/");
                                }}
                            >
                                {t("signup.termsOfUse")}
                            </a>
                            ,&nbsp; &nbsp;
                            <a
                                type="link"
                                className="signup-tnc-button"
                                style={{fontSize: "12px"}}
                                onClick={(e) => {
                                    e.preventDefault();
                                    window.open("https://www.fundpark.com/en/privacy-policy/");
                                }}
                            >
                                {t("signup.termsOfPrivacy")}
                            </a>
                            ,&nbsp;
                            <a
                                type="link"
                                onClick={(e) => {
                                    e.preventDefault();
                                    window.open(fundparkXDJWaiveInterestPlan);
                                }}
                                className="signup-tnc-button"
                                style={{fontSize: "12px"}}
                            >
                                {t("signup.termsOfFundparkXDJWaiveInterestPlan")}
                            </a>
                            {t("signup.and")}
                            <a
                                type="link"
                                onClick={(e) => {
                                    e.preventDefault();
                                    window.open(fundparkRevenueFinancingSubsidyPlan);
                                }}
                                className="signup-tnc-button"
                                style={{fontSize: "12px"}}
                            >
                                {t("signup.termsOfFundparkRevenueFinancingSubsidyPlan")}
                            </a>
                            {t("signup.ofTermsAndConditions")}
                        </span>
                    </Checkbox>
                </Form.Item>
            </Form>

        </div>
    )
});

export default SignupWrapper