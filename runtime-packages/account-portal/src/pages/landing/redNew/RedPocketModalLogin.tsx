import {Modal as AntModal, Form} from "antd";
import React, {useRef, useState, useEffect} from "react";
import styles from "./index.module.scss";
import redPocketBody from "@/assets-new/images/landing/redNew/LoginRedPocket.png";
import congratsGoldBanner from "@/assets-new/images/landing/redNew/congrats_gold_title.png";
import SignupWrapper from "./SignupWrapper.tsx";
import SignupButton from "../SignupBtn/index";
import {Signup as SignupHandler, SignupValidateToken} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper.ts";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants.ts";
import {MATOMO_CONFIG} from "@/utils/matomoConfig.ts";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import {useNavigate, useSearchParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import './index.scss';

interface RedPocketModalLoginProps {
    open: boolean;
    onClose: () => void;
    style?: React.CSSProperties;
    width?: number | "auto";
    height?: number | string;
    backgroundColor?: string;
    trackingId?: string;
    actionTitle?: string;
    descText?: string;
    closeText?: string;
    actionEvent?: () => void;
}

const RedPocketModalLogin: React.FC<RedPocketModalLoginProps> = ({
                                                                     open,
                                                                     onClose,
                                                                     style,
                                                                     height = "auto",
                                                                     closeText,
                                                                 }) => {

    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const {trackEventWithDimensions, trackEvent} = useMatomoContext();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const {t} = useTranslation();
    const tabfBaseURL = import.meta.env.VITE_TABF_BASE_URL as string;
                                                                    
    const handleClose = () => {
        onClose();
    };

    const handleSignup = async () => {
        await form.validateFields();

        if (form) {
            try {
                setLoading(true);

                const email = form.getFieldValue("email")
                const phoneNumber = `${form.getFieldValue("phoneCountryCode")}${form.getFieldValue("phoneNumber")}`

                const res = await SignupHandler({
                    phone_country_code: form.getFieldValue("phoneCountryCode"),
                    phone_number: form.getFieldValue("phoneNumber"),
                    phone_code: form.getFieldValue("mobileVerification"),
                    email: email,
                    wechat: form.getFieldValue("wechat") || null,
                    register_source: 'small_shop_financing',
                    tpid: searchParams.get("tpid") || null,
                    cpid: searchParams.get("cpid") || null,
                    platform_source: searchParams.get("platform_source") || null
                } as any);

                switch (res.code){
                    case 0:
                        appHelper.msgApi.success(t("signup.success"));
                        trackEventWithDimensions({
                            category: TRACKING_CATEGORIES.UI,
                            action: TRACKING_ACTIONS.SIGNUP,
                            name: TRACKING_EVENTS.SIGNUP_SUCCESS,
                            customDimensions: {
                                [MATOMO_CONFIG.NEW_USER_SIGNUP_DIMENSION_ID]: `${email} ${phoneNumber}`
                            }
                        });
                        trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.LOGIN, TRACKING_EVENTS.LOGIN_SUCCESS);
                        appHelper.msgApi.success(t("login.success"));
                        appHelper.setAccessInfo({accessToken: res.data.user.login.token});
                        appHelper.setUserInfo(res.data.user.login.user);
                        return navigate("/");
                    case 1:
                        form.setFields([
                            {
                                name: "phoneNumber",
                                errors: [res.message]
                            }
                        ]);
                        break;
                    case 3:
                        form.setFields([
                            {
                                name: "email",
                                errors: [res.message]
                            }
                        ]);
                        break;
                    case 30400:
                    case 30450:
                        //walmart
                        navigate("/");
                        break;
                    case 30410:
                    case 30430:
                        // sdg
                        navigate(`/credit/hook?email=${form.getFieldValue("email")}`);
                        break;
                    case 30420:
                    case 30440:
                        // caa
                        navigate(`/credit/underwriting?email=${form.getFieldValue("email")}`);
                        break;
                    case 30460:
                        window.location.href = tabfBaseURL;
                        break;
                    default:
                        appHelper.msgApi.error(res.message);
                        console.log(res.message);
                }
            } catch (error) {
                console.error("Login error:", error);
            } finally {
                setLoading(false);
            }
        } else {
            console.error('the form is not yet filled');
        }
    }

    useEffect(() => {
        // Track with custom title and URL
        trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.OPEN_2507_RED_MODAL); 
    }, []);



    return (
        <AntModal
            open={open}
            onCancel={handleClose}
            maskClosable={false} 
            footer={null}
            className={`rounded-[24px] [&_.ant-modal-content]:!p-0 [&_.ant-modal-content]:!rounded-[24px] [&_.ant-modal-content]:!bg-transparent [&_.ant-modal-content]:!shadow-none [&_.ant-modal-content]:!border-none ${
                !closeText
                    ? "[&_.ant-modal-close]:!w-10 [&_.ant-modal-close]:!h-10 [&_.ant-modal-close]:!bg-white/40 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:!rounded-full [&_.ant-modal-close-icon]:!text-white [&_.ant-modal-close-icon]:!text-[24px]"
                    : "[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!shadow-none [&_.ant-modal-close]:!border-none hover:[&_.ant-modal-close]:!bg-[transparent] active:[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!px-3 [&_.ant-modal-close]:!py-1 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:before:!content-[''] [&_.ant-modal-close]:after:!content-[''] [&_.ant-modal-close-x]:!bg-[transparent] [&_.ant-modal-close]:mr-2 [&_.ant-modal-close]:!w-auto"
            }`}
            centered
            width={"450px"}
            style={{
                backgroundColor: "transparent",
                boxShadow: "none",
                border: "none",
                top: '-30px'
            }}
        >
            <img src={congratsGoldBanner} alt="Top" className={styles.titleContainer} />
            <div className={styles.centerImageContainer}>
                <img src={redPocketBody} alt="Center" className={styles.centerImage} />
                <div className={styles.inputContainer}>
                    <SignupWrapper 
                    inputStyle={{width: '100%', backgroundColor: 'rgba(255, 255, 255, 0.8)', border: '1px solid #FFFFFF', height: '40px'}}
                    placeholderColor='rgba(40, 40, 48, 0.55)'
                    form={form}
                    onSubmit={handleSignup}
                    checkboxTextColor={'#FFFFFF'}
                    errorTextColor={'#FFFFFF'}
                    countryCodeColor={'rgba(255, 255, 255, 0.8)'}
                    rowGap={4}/>
                </div>
            </div>
            <div style={{width: '430px', position: 'fixed', display: 'flex', justifyContent: 'center',}}>
                <SignupButton
                    type={"redNewModal"}
                    buttonTitle={""}
                    isShowHelpingText={true}
                    helpingText={""}
                    onClick={() => {
                        handleSignup()
                    }}
                />
            </div>
        </AntModal>
    );
};

export default RedPocketModalLogin;
