import './index.scss';
import { Col, Row, Form } from 'antd';
import HeaderBanner from "@/assets-new/images/landing/redNew/newRedHeaderBanner2x.png"
import fpLogo from "@/assets-new/images/landing/redNew/fundparkLogo.png"
import RedPocketAdv from "@/assets-new/images/landing/redNew/newJoiner.png"
import LowBaseLine from "@/assets-new/images/landing/redNew/lowBaseLine.png"
import LargeQuota from "@/assets-new/images/landing/redNew/largeQuota.png"
import QuickDisbursement from "@/assets-new/images/landing/redNew/quickDisbursement.png"
import FPReasons from "@/assets-new/images/landing/redNew/fpReasons.png"
import ThirtyK from "@/assets-new/images/landing/redNew/thirtyK.png"
import ABS from "@/assets-new/images/landing/redNew/abs.png"
import NineYears from "@/assets-new/images/landing/redNew/nineYears.png"
import ThreeSteps from "@/assets-new/images/landing/redNew/3StepsQuick.png"
import SupportPlatforms from "@/assets-new/images/landing/redNew/supportPlatforms.png"
import AllPlatforms from "@/assets-new/images/landing/redNew/allPlatforms.png"
import FastApplication from "@/assets-new/images/landing/red/fast-application.png"
import FastMoney from "@/assets-new/images/landing/red/fast-money.png"
import FastRegister from "@/assets-new/images/landing/red/fast-register.png"
import ArrowRight from "@/assets-new/images/landing/red/arrow-right.png"
import Text from "@/components/shared/Text/index.tsx";
import SignupWrapper from "./SignupWrapper.tsx";
import GenericLayout from "@/layouts/GenericLayout/index.tsx";
import hsbcFundParkFirstAd from "@/assets-new/images/hsbcFundParkAd.jpg";
import goldmanFundParkAd from "@/assets-new/images/goldmanFundParkAd.jpg";
import AutoplayAdBanner from "@/components/adBanners/AutoplayAdBanner";
import RedPocketModalLogin from './RedPocketModalLogin.tsx';
import {WechatImage} from "@/components/WechatImage.t.tsx";
import WechatBase from "@/assets-new/images/landing/redNew/rednewlanding-wechat-base.png"
import React, {useRef, useState} from "react";
import {POSITION_STATUS} from "@/constants/position-status";
import {usePositionTracking} from "@/hooks/usePositionTracking";
import {useMatomoContext} from "@/contexts/MatomoContext";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import SignupButton from "../SignupBtn/index";
import appHelper from "@/utils/appHelper.ts";
import {MATOMO_CONFIG} from "@/utils/matomoConfig.ts";
import {useNavigate, useSearchParams} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {Signup as SignupHandler, SignupValidateToken} from "@fundpark/fp-api";

const Landing = () => {
    const {trackPosition} = usePositionTracking();
    const [isRedPocketModalOpen, setIsRedPocketModalOpen] = useState(true);
    const [form] = Form.useForm(); // Create a ref for the form
    const [loading, setLoading] = useState(false);
    const {trackEventWithDimensions, trackEvent} = useMatomoContext();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const {t} = useTranslation();
    const tabfBaseURL = import.meta.env.VITE_TABF_BASE_URL as string;

    const handleSignup = async () => {
        await form.validateFields();
        
        if (form) {
            try {
                setLoading(true);

                const email = form.getFieldValue("email")
                const phoneNumber = `${form.getFieldValue("phoneCountryCode")}${form.getFieldValue("phoneNumber")}`

                const res = await SignupHandler({
                    phone_country_code: form.getFieldValue("phoneCountryCode"),
                    phone_number: form.getFieldValue("phoneNumber"),
                    phone_code: form.getFieldValue("mobileVerification"),
                    email: email,
                    wechat: form.getFieldValue("wechat") || null,
                    register_source: 'small_shop_financing',
                    tpid: searchParams.get("tpid") || null,
                    cpid: searchParams.get("cpid") || null,
                    platform_source: searchParams.get("platform_source") || null
                } as any);

                switch (res.code) {
                    case 0:
                        appHelper.msgApi.success(t("signup.success"));
                        trackEventWithDimensions({
                            category: TRACKING_CATEGORIES.UI,
                            action: TRACKING_ACTIONS.SIGNUP,
                            name: TRACKING_EVENTS.SIGNUP_SUCCESS,
                            customDimensions: {
                                [MATOMO_CONFIG.NEW_USER_SIGNUP_DIMENSION_ID]: `${email} ${phoneNumber}`
                            }
                        });
                        trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.LOGIN, TRACKING_EVENTS.LOGIN_SUCCESS);
                        appHelper.msgApi.success(t("login.success"));
                        appHelper.setAccessInfo({ accessToken: res.data.user.login.token });
                        appHelper.setUserInfo(res.data.user.login.user);
                        return navigate("/");
                    case 1:
                        form.setFields([
                            {
                                name: "phoneNumber",
                                errors: [res.message]
                            }
                        ]);
                        break;
                    case 3:
                        form.setFields([
                            {
                                name: "email",
                                errors: [res.message]
                            }
                        ]);
                        break;
                    case 30400:
                    case 30450:
                        //walmart
                        navigate("/");
                        break;
                    case 30410:
                    case 30430:
                        // sdg
                        navigate(`/credit/hook?email=${form.getFieldValue("email")}`);
                        break;
                    case 30420:
                    case 30440:
                        // caa
                        navigate(`/credit/underwriting?email=${form.getFieldValue("email")}`);
                        break;
                    case 30460:
                        window.location.href = tabfBaseURL;
                        break;
                    default:
                        appHelper.msgApi.error(res.message);
                        console.log(res.message);
                }
            } catch (error) {
                console.error("Login error:", error);
            } finally {
                setLoading(false);
            }
        } else {
            console.error('the form is not yet filled');
        }
    }

    return (
        <GenericLayout type="red">
            <RedPocketModalLogin
                open={isRedPocketModalOpen}
                onClose={() => {
                    setIsRedPocketModalOpen(false);
                    trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.CLOSE_2507_RED_MODAL); 
                }}
            />
            <AutoplayAdBanner
                ads={[
                    {name: 'HSBC & FundPark 2.5B', image: hsbcFundParkFirstAd},
                    {name: 'Goldman Sachs & FundPark 5B', image: goldmanFundParkAd}
                ]}
                intervalSeconds={5}
            />
            <div style={{
            paddingBottom: '4rem',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center', 
            minHeight: '100vh',
            }}>  
            <div style={{
                position: 'relative',
                width: '100%',
                overflow: 'hidden',
                display: 'flex',
                justifyContent: 'center'
            }}>
                <img src={HeaderBanner} alt="HeaderBanner" className={'header-banner'}
                    style={{
                        position: 'relative',
                        objectFit: 'cover',
                        objectPosition: 'center',
                        minWidth: '1920px'
                    }}/>
                <div
                    style={{
                        position: 'absolute',
                        top: '1.5rem',
                        left: '2rem',
                        display: 'flex',
                        alignItems: 'left',
                        justifyContent: 'left',
                        color: 'white',
                        textAlign: 'left',
                }}
                className={'responsive-overall'}>
                    <img src={fpLogo} alt="fpLogo" style={{maxWidth: '10rem'}}/>
                </div>
            </div>

          
                <div className="login-wrapper-container-rednew" >
                    <div style={{ display: 'flex', alignContent: 'center', justifyContent: 'center', width: '100%', gap: '10px', minHeight: '360px', height: 'auto', marginTop: '10px', marginBottom: '10px'}}>        
                        <div style={{width: '480px'}}>
                            <SignupWrapper form={form} onSubmit={handleSignup} inputStyle={{ height: '48px', width:'430px'}} />
                            <div style={{ width: '430px', marginTop: '10px' }}>
                                <SignupButton
                                    type={"redNew"}
                                    buttonTitle={""}
                                    isShowHelpingText={true}
                                    helpingText={"7天免息试用*"}
                                    onClick={() => {
                                        handleSignup()
                                    }}
                                />
                            </div>
                        </div>

                        <div style={{ width: '260px' }}>
                            <img src={WechatBase} alt="WechatBase" style={{ marginTop: '15px' }} />
                            <WechatImage
                                style={{
                                    width: '167px',
                                    height: '167px',
                                    position: 'absolute',
                                    transform: 'translate(-135%, 65%)',
                                    border: '1px solid #FF4545',
                                    borderRadius: '4px',
                                }}
                            />
                        </div>
                        
                    </div>
                </div>
                    <div style={{textAlign: "center", marginTop: "1rem"}} className={"responsive-container-rednew"}>
                        <img className="responsive-img" src={RedPocketAdv} alt="LoginBanner"/>
                    </div>

                    <div style={{width: '100%', marginTop: "1rem"}} className={"responsive-container-rednew"}>
                        <div>
                            <img src={QuickDisbursement} alt="QuickDisbursement"/>
                        </div>
                        <div>
                            <img src={LargeQuota} alt="LargeQuota"/>
                        </div>
                        <div>
                            <img src={LowBaseLine} alt="LowBaseLine"/>
                        </div>
                    </div>
                    
                    <div style={{textAlign: "center", marginTop: "2rem"}} className={"responsive-container-rednew"}>
                        <img className="responsive-img" src={FPReasons} alt=""/>
                    </div>
                    <div style={{width: '100%', marginTop: "2rem", gap: '2.3rem'}} className={"responsive-container-rednew"}>
                        <div>
                            <img src={ThirtyK} alt="ThirtyK"/>
                        </div>

                        <div>
                            <img src={ABS} alt="ABS"/>
                        </div>

                        <div>
                            <img src={NineYears} alt="NineYears"/>
                        </div>
                    </div>

                    <div style={{textAlign: "center", marginTop: "3rem"}} className={"responsive-container-rednew"}>
                        <img className="responsive-img" src={ThreeSteps} alt="ThreeSteps"/>
                    </div>

                    <Row justify="center" gutter={[60, 60]} style={{maxWidth: '100%', marginTop: "2rem"}} className={"responsive-container-rednew"}>
                        <Col style={{textAlign: 'center'}}>
                            <div>
                                <img src={FastRegister} alt="FastRegister" className="responsive-img"/>
                                <Text style={{fontSize: "36px", color: "white", display: "block", marginTop: "1rem", fontWeight: '900'}}>
                                    10秒注册+授权​
                                </Text>
                            </div>
                        </Col>

                        <Col style={{textAlign: 'center', display: 'flex', alignItems: 'center', marginTop: "1rem"}}>
                            <div>
                                <img src={ArrowRight} alt="ArrowRight" className="responsive-img"/>
                                <Text style={{
                                    fontSize: "36px",
                                    color: "white",
                                    display: "block",
                                    marginTop: "20px",
                                    visibility: "hidden",
                                }}>
                                    0
                                </Text>
                            </div>
                        </Col>

                        <Col style={{textAlign: 'center'}}>
                            <div>
                                <img src={FastApplication} alt="FastApplication" className="responsive-img"/>
                                <Text style={{fontSize: "36px", color: "white", display: "block", marginTop: "1rem", fontWeight: '900'}}>
                                    填卡号+公司信息​
                                </Text>
                            </div>
                        </Col>

                        <Col style={{textAlign: 'center', display: 'flex', alignItems: 'center', marginTop: "1rem"}}>
                            <div>
                                <img src={ArrowRight} alt="ArrowRight" className="responsive-img"/>
                                <Text style={{
                                    fontSize: "36px",
                                    color: "white",
                                    display: "block",
                                    marginTop: "20px",
                                    visibility: "hidden",
                                }}>
                                    0
                                </Text>
                            </div>
                        </Col>

                        <Col style={{textAlign: 'center'}}>
                            <div>
                                <img src={FastMoney} alt="FastMoney" className="responsive-img"/>
                                <Text style={{fontSize: "36px", color: "white", display: "block", marginTop: "1rem", textShadow: '0px 3px 2px 0px #0000006B', fontWeight: '900'}}>
                                    签约+到账
                                </Text>
                            </div>
                        </Col>
                    </Row>
                    <div style={{textAlign: "center", marginTop: "2rem"}} className={"responsive-container-rednew"}>
                        <img className="responsive-img" src={SupportPlatforms} alt="SupportPlatforms"/>
                    </div>

                    <div style={{textAlign: "center", width: '1250px', marginTop: "2rem"}} className={"responsive-container-rednew"}>
                        <img className="responsive-img" src={AllPlatforms} alt="AllPlatforms" style={{width: '1250px'}}/>
                    </div>
            </div>
        </GenericLayout>
    );

};

export default Landing;
