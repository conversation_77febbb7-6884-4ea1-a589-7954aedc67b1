.responsive-img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.login-wrapper-container-rednew {
  width: 1250px;
  min-width: 850px;
  border-radius: 24px;
  border-width: 2px;
  background: rgba(255, 241, 235, 0.6);
  border: 2px solid #FFFFFF;
  backdrop-filter: blur(24px);
  margin: 2rem;
  display: 'flex';
  height: auto;
  justify-items: 'center';
}


.ant-form-item-explain-error  {
  text-align: left !important; 
  color:var(--errorText-color, red)!important;
}

.responsive-container-rednew  {
  transform: scale(1) translateY(0);
  min-width: 77rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1366px) {
  .responsive-container-rednew  {
    transform: scale(0.75) translateY(-10px);
  }
  .responsive-overall {
    transform: scale(0.75);
  }
  .login-wrapper-container-rednew {
    width: auto;
    min-width: 950px;
  }
}

@media (min-width: 1920px) {
  .header-banner {
    min-width: 100%;
  }
}

.helpingContainerRedNewTexts {
  font-family: Source Sans Pro;
  font-weight: 900;
  font-size: 28px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  color: #DE0900;
}
