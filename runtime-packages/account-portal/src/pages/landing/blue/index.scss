.responsive-img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.login-wrapper-container {
  width: 524px;
  min-height: 364px;
  top: 50px;
  left: 1056px;
  border-radius: 24px;
  padding: 24px 32px;
  gap: 13px;
  background: linear-gradient(360deg, rgba(255, 249, 223, 0.8) 0%, rgba(255, 253, 247, 0.8) 75.57%);
}

.login-wrapper-container {
  .ant-form-item-explain-error {
    text-align: left;
  }
}

.item-container {
  border-radius: 24px;
  border: 2px solid white;
  max-width: 65%;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  backdrop-filter: blur(54px);
  background-color: #FFFFFF1A;
}

.item-container-title {
  font-family: Source Sans Pro, serif;
  font-weight: 900;
  font-size: 32px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  color: #201747;
  margin-bottom: 2.5rem;
}

.item-container-content {
  font-family: Source Sans Pro, serif;
  font-weight: 900;
  font-size: 26px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;

  color: #201747;
}

.responsive-container {
  transform: scale(1) translateY(0);
}

@media (max-width: 1550px) {
  .responsive-container {
    transform: scale(0.75) translateY(-10px);
  }
  .responsive-item-img {
    width: 100px;
  }
}