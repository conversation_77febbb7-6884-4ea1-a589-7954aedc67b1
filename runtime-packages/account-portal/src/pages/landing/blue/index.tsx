import './index.scss';
import {Col, Row} from 'antd';
import Banner from "@/assets-new/images/landing/blue/banner.png"
import ApplyThreeMins from "@/assets-new/images/landing/blue/applyIn3Minutes.png";
import InstantTransfer from "@/assets-new/images/landing/blue/instantTransfer.png";
import SecureSignup from "@/assets-new/images/landing/blue/secureSignup10s.png";
import Separator from "@/assets-new/images/landing/blue/separator.png";
import Platforms from "@/assets-new/images/landing/blue/platforms.png";
import SignupWrapper from "@/pages/landing/blue/SignupWrapper.tsx";
import GenericLayout from "@/layouts/GenericLayout/index.tsx";
import AutoplayAdBanner from "@/components/adBanners/AutoplayAdBanner";
import hsbcFundParkFirstAd from "@/assets-new/images/hsbcFundParkAd.jpg";
import goldmanFundParkAd from "@/assets-new/images/goldmanFundParkAd.jpg";

const Landing = () => {

    return (
        <GenericLayout type="blue">
            <AutoplayAdBanner
                ads={[
                    {name: 'HSBC & FundPark 2.5B', image: hsbcFundParkFirstAd},
                    {name: 'Goldman Sachs & FundPark 5B', image: goldmanFundParkAd}
                ]}
                intervalSeconds={5}
            />
            <div style={{paddingBottom: '4rem', minWidth: '1240px'}}>
                <div style={{textAlign: "center", marginTop: "1rem"}}>
                    <img src={Banner} alt="Banner" style={{width:'100%'}}/>
                </div>

                <Row justify="center" style={{marginTop: '1rem'}}>
                    <Col className="item-container" span={16} style={{
                        "paddingTop": "20px",
                        "paddingBottom": "20px",
                    }}>
                        <SignupWrapper/>
                    </Col>
                </Row>

                <Row justify="center" style={{marginTop: '1rem'}}  >
                    <Col className="item-container" span={16} style={{
                        "paddingTop": "48px",
                        "paddingBottom": "56px",
                    }}>
                        <div className="item-container-title">
                            3步快速提款
                        </div>
                        <Row justify="center" gutter={[60, 60]} >
                            <Col></Col>
                            <Col style={{textAlign: 'center'}}>
                                <div>
                                    <img src={SecureSignup} alt="SecureSignup" className="responsive-item-img"/>
                                    <div className="item-container-content">
                                        10秒加密注册
                                    </div>
                                </div>
                            </Col>

                            <Col style={{textAlign: 'center', display: 'flex', alignItems: 'center'}}>
                                <div>
                                    <img src={Separator} alt="Separator" className="responsive-item-img"/>
                                </div>
                            </Col>

                            <Col style={{textAlign: 'center'}}>
                                <div>
                                    <img src={ApplyThreeMins} alt="ApplyThreeMins" className="responsive-item-img"/>
                                    <div className="item-container-content">
                                        3分钟申请
                                    </div>
                                </div>
                            </Col>

                            <Col style={{textAlign: 'center', display: 'flex', alignItems: 'center'}}>
                                <div>
                                    <img src={Separator} alt="Separator" className="responsive-item-img"/>
                                </div>
                            </Col>

                            <Col style={{textAlign: 'center'}}>
                                <div>
                                    <img src={InstantTransfer} alt="InstantTransfer" className="responsive-item-img"/>
                                    <div className="item-container-content">
                                        极速到账
                                    </div>
                                </div>
                            </Col>
                            <Col></Col>
                        </Row>
                    </Col>
                </Row>

                <Row justify="center" style={{marginTop: '1rem'}}>
                    <Col className="item-container" span={16} style={{
                        "paddingTop": "48px",
                        "paddingBottom": "56px",
                    }}>
                        <div className="item-container-title">
                            支持店铺授权平台
                        </div>
                        <div>
                            <img src={Platforms} alt="Platforms" className="responsive-img responsive-container" />
                        </div>
                    </Col>
                </Row>
            </div>
        </GenericLayout>
    );
};

export default Landing;
