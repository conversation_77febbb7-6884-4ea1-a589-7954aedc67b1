import { Checkbox, Col, Form, message, Row} from 'antd';
import './index.scss';
import {Input} from "@/components/common";
import {useTranslation} from "react-i18next";
import {useState} from "react";
import {validateMobilePhone} from "@fundpark/ui-utils";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants.ts";
import {MATOMO_CONFIG} from "@/utils/matomoConfig.ts";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import CountryCodePhoneInput from "@/components/shared/CountryCodePhoneInput";
import {DEFAULT_AREA_CODES} from "@/components/common/constants.ts";
import SendButton from "@/components/common/SendButton";
import {Signup as SignupHandler, SignupValidateToken} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper.ts";
import {WechatImage} from "@/components/WechatImage.t.tsx";
import WechatBase from "@/assets-new/images/landing/red/redlanding-wechat-base.png"
import SignupButton from "../SignupBtn/index";
import {useNavigate, useSearchParams} from "react-router-dom";
import fundparkXDJWaiveInterestPlan from "@/assets-new/pdfs/fundpark-xdj-waive-interest-plan.pdf"
import fundparkRevenueFinancingSubsidyPlan from "@/assets-new/pdfs/fundpark-revenue-financing-subsidy-plan.pdf"
import {getEmailRules, getMobileRules, getWechatRules} from "@/constants/data_validation_rules.ts";

const SignupWrapper = () => {
    const {t} = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const {trackEventWithDimensions} = useMatomoContext();
    const { trackEvent } = useMatomoContext();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const tabfBaseURL = import.meta.env.VITE_TABF_BASE_URL as string;

    const handlePhoneNumberChange = () => {
        let countryCode = form.getFieldValue("phoneCountryCode");
        let phoneNumber = form.getFieldValue("phoneNumber");

        if (countryCode === undefined) {
            countryCode = "";
        }

        if (phoneNumber === undefined) {
            phoneNumber = "";
        }

        const value = countryCode + phoneNumber

        trackEventWithDimensions({
            category: TRACKING_CATEGORIES.FORM,
            action: TRACKING_ACTIONS.INPUT,
            name: TRACKING_EVENTS.INPUT_PHONE_NUMBER,
            customDimensions: {
                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: value
            }
        });
    };

    const EMAIL_RULES = getEmailRules(t)
    const MOBILE_RULES = getMobileRules(t, form, "phoneCountryCode");
    const WECHAT_RULES = getWechatRules()


    const handleSendMobileVerification = async () => {
        try {
            const phone = form.getFieldValue("phoneNumber");
            const areaCode = form.getFieldValue("phoneCountryCode");

            if (!phone) {
                message.error(t("myProfile.mobileRequired"));
                return false;
            }

            const res = await SignupValidateToken({
                phone_country_code: areaCode,
                phone_number: phone
            } as any);

            if (res.message === "success") {
                message.success(t("myProfile.verificationCodeSent"));
                return true;
            } else {
                form.setFields([
                    {
                        name: "mobileVerification",
                        errors: [res.message]
                    }
                ]);
                return false;
            }
        } catch (error) {
            console.error("Mobile verification error:", error);
            form.setFields([
                {
                    name: "mobileVerification",
                    errors: [t("signup.phoneVerificationCodeFail")]
                }
            ]);
            return false;
        }
    };

    const handleSignup = async () => {
        await form.validateFields();

        try {
            setLoading(true);

            const email = form.getFieldValue("email")
            const phoneNumber = `${form.getFieldValue("phoneCountryCode")}${form.getFieldValue("phoneNumber")}`

            const res = await SignupHandler({
                phone_country_code: form.getFieldValue("phoneCountryCode"),
                phone_number: form.getFieldValue("phoneNumber"),
                phone_code: form.getFieldValue("mobileVerification"),
                email: email,
                wechat: form.getFieldValue("wechat") || null,
                register_source: 'small_shop_financing',
                tpid: searchParams.get("tpid") || null,
                cpid: searchParams.get("cpid") || null,
                platform_source: searchParams.get("platform_source") || null
            } as any);

            switch (res.code){
                case 0:
                    appHelper.msgApi.success(t("signup.success"));
                    trackEventWithDimensions({
                        category: TRACKING_CATEGORIES.UI,
                        action: TRACKING_ACTIONS.SIGNUP,
                        name: TRACKING_EVENTS.SIGNUP_SUCCESS,
                        customDimensions: {
                            [MATOMO_CONFIG.NEW_USER_SIGNUP_DIMENSION_ID]: `${email} ${phoneNumber}`
                        }
                    });
                    trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.LOGIN, TRACKING_EVENTS.LOGIN_SUCCESS);
                    appHelper.msgApi.success(t("login.success"));
                    appHelper.setAccessInfo({accessToken: res.data.user.login.token});
                    appHelper.setUserInfo(res.data.user.login.user);
                    return navigate("/");
                case 1:
                    form.setFields([
                        {
                            name: "phoneNumber",
                            errors: [res.message]
                        }
                    ]);
                    break;
                case 3:
                    form.setFields([
                        {
                            name: "email",
                            errors: [res.message]
                        }
                    ]);
                    break;
                case 30400:
                case 30450:
                    //walmart
                    navigate("/");
                    break;
                case 30410:
                case 30430:
                    // sdg
                    navigate(`/credit/hook?email=${form.getFieldValue("email")}`);
                    break;
                case 30420:
                case 30440:
                    // caa
                    navigate(`/credit/underwriting?email=${form.getFieldValue("email")}`);
                    break;
                case 30460:
                    window.location.href = tabfBaseURL;
                    break;
                default:
                    appHelper.msgApi.error(res.message);
                    console.log(res.message);
            }
        } catch (error) {
            console.error("Login error:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="login-wrapper-container">
            <Row gutter={[16, 16]} style={{width: '100%'}}>
                <Col span={17}>
                    <Form
                        form={form}
                        layout="vertical"
                        className="w-full"
                        requiredMark={false}
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'flex-start',
                        }}
                        initialValues={{ phoneCountryCode: '+86' }}
                    >
                        <Form.Item className="mb-3 w-full">
                            <CountryCodePhoneInput
                                className="mb-0"
                                countryCodeName="phoneCountryCode"
                                phoneNumberName="phoneNumber"
                                countryCodeOptions={DEFAULT_AREA_CODES}
                                countryCodeRules={[
                                    {required: true, message: "请选择国家/地区代码"},
                                    {
                                        validator: (_, value) => {
                                            if (value && value.replace("+", "") === "") {
                                                return Promise.reject(new Error("请选择有效的国家/地区代码"));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                                phoneNumberRules={MOBILE_RULES}
                                countryCodePlaceholder="选择"
                                phoneNumberPlaceholder={t("login.mobileRequired")}
                                disabled={loading}
                                onCountryCodeChange={handlePhoneNumberChange}
                                onPhoneNumberBlur={handlePhoneNumberChange}
                            />
                        </Form.Item>

                        <Form.Item
                            className="mb-3  w-full"
                            name={"mobileVerification"}
                            rules={[{required: true, message: t("signup.verificationCodeRequired")}]}
                        >
                            <Input
                                placeholder={t("signup.verificationCodeRequired")}
                                size="large"
                                className="!pr-1"
                                suffix={
                                    <Form.Item
                                        noStyle
                                        shouldUpdate={(prev, curr) =>
                                            prev.phoneNumber !== curr.phoneNumber ||
                                            prev.phoneCountryCode !== curr.phoneCountryCode
                                        }
                                    >
                                        {({getFieldValue}) => {
                                            const isValidInput = validateMobilePhone(
                                                getFieldValue("phoneNumber"),
                                                getFieldValue("phoneCountryCode")
                                            );

                                            return (
                                                <SendButton
                                                    onClick={async () => {
                                                        return handleSendMobileVerification();
                                                    }}
                                                    countdown={60}
                                                    disabled={!isValidInput}
                                                    title={"发送验证码"}
                                                    bgc={"red"}
                                                />
                                            );
                                        }}
                                    </Form.Item>
                                }
                            />
                        </Form.Item>

                        <Form.Item name="email" rules={EMAIL_RULES} className="mb-3  w-full">
                            <Input
                                disabled={loading}
                                placeholder={t("login.emailRequired")}
                                size="large"
                                onBlur={e => {
                                    trackEventWithDimensions({
                                        category: TRACKING_CATEGORIES.FORM,
                                        action: TRACKING_ACTIONS.INPUT,
                                        name: TRACKING_EVENTS.INPUT_EMAIL,
                                        customDimensions: {
                                            [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                        }
                                    });
                                }}
                            />
                        </Form.Item>

                        <Form.Item name="wechat" rules={WECHAT_RULES} className="mb-1  w-full">
                            <Input
                                disabled={loading}
                                placeholder={"请输入您的微信号"}
                                size="large"
                                onBlur={e => {
                                    trackEventWithDimensions({
                                        category: TRACKING_CATEGORIES.FORM,
                                        action: TRACKING_ACTIONS.INPUT,
                                        name: TRACKING_EVENTS.INPUT_EMAIL,
                                        customDimensions: {
                                            [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                        }
                                    });
                                }}
                            />
                        </Form.Item>

                        <Form.Item
                            name="agreeCheckBox"
                            valuePropName="checked"
                            style={{
                                marginBottom: "0px",
                                paddingBottom: "0px",
                            }}
                            rules={[
                                {
                                    validator: (_: any, checked: boolean) =>
                                        checked
                                            ? Promise.resolve()
                                            : Promise.reject(new Error(t("signup.agreementRequired")))
                                }
                            ]}
                        >
                            <Checkbox
                                style={{
                                    fontSize: "12px",
                                    fontWeight: "400",
                                    fontFamily: "Source Sans Pro, sans-serif",
                                    margin: 0,
                                    padding: 0,
                                    textAlign: "left",
                                }}
                            >
                                <span style={{margin: 0, padding: 0, display: "inline"}}>
                                    {t("signup.agreementBody")}
                                    <a
                                        type="link"
                                        className="signup-tnc-button"
                                        style={{fontSize: "12px"}}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            window.open("https://www.fundpark.com/en/terms-and-conditions/");
                                        }}
                                    >
                                        {t("signup.termsOfUse")}
                                    </a>
                                    ,&nbsp;
                                    <a
                                        type="link"
                                        className="signup-tnc-button"
                                        style={{fontSize: "12px"}}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            window.open("https://www.fundpark.com/en/privacy-policy/");
                                        }}
                                    >
                                        {t("signup.termsOfPrivacy")}
                                    </a>
                                    ,&nbsp;
                                    <a
                                        type="link"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            window.open(fundparkXDJWaiveInterestPlan);
                                        }}
                                        className="signup-tnc-button"
                                        style={{fontSize: "12px"}}
                                    >
                                        {t("signup.termsOfFundparkXDJWaiveInterestPlan")}
                                    </a>
                                    {t("signup.and")}
                                    <a
                                        type="link"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            window.open(fundparkRevenueFinancingSubsidyPlan);
                                        }}
                                        className="signup-tnc-button"
                                        style={{fontSize: "12px"}}
                                    >
                                        {t("signup.termsOfFundparkRevenueFinancingSubsidyPlan")}
                                    </a>
                                    {t("signup.ofTermsAndConditions")}
                                </span>
                            </Checkbox>
                        </Form.Item>
                    </Form>
                </Col>
                <Col span={7}>
                    <img src={WechatBase} alt="WechatBase"/>
                    <WechatImage
                        style={{ width: '106px', height: '105px', position: 'absolute', transform: 'translate(-40%, -114%)', border: '1px solid #FF4545', borderRadius: '4px'}}
                    />
                </Col>
            </Row>

            <Row style={{width: '100%', marginTop: '1.5rem'}}>
                <SignupButton
                    type={"red"}
                    buttonTitle={"注册领红包"}
                    isShowHelpingText={true}
                    helpingText={"7天免息试用*"}
                    onClick={() => {
                        handleSignup()
                    }}
                />
            </Row>

        </div>
    )
}

export default SignupWrapper