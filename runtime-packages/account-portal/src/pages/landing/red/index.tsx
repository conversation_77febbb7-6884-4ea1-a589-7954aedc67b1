import './index.scss';
import {Col, Row} from 'antd';
import LoginBanner from "@/assets-new/images/landing/red/login-banner.png"
import RedPocketAdv from "@/assets-new/images/landing/red/rp.png"
import AllAspect from "@/assets-new/images/landing/red/all-aspect.png"
import LargeQuota from "@/assets-new/images/landing/red/large-quota.png"
import QuickDisbursement from "@/assets-new/images/landing/red/quick-disbursement.png"
import Safe from "@/assets-new/images/landing/red/safe.png"
import CreditModel from "@/assets-new/images/landing/red/credit-model.png"
import FiveB from "@/assets-new/images/landing/red/50-b.png"
import NineYears from "@/assets-new/images/landing/red/9years.png"
import ThreeSteps from "@/assets-new/images/landing/red/3-steps.png"
import FastApplication from "@/assets-new/images/landing/red/fast-application.png"
import FastMoney from "@/assets-new/images/landing/red/fast-money.png"
import FastRegister from "@/assets-new/images/landing/red/fast-register.png"
import ArrowRight from "@/assets-new/images/landing/red/arrow-right.png"
import Money from "@/assets-new/images/landing/red/money.png"
import CAAMoney from "@/assets-new/images/landing/red/caa-money.png"
import PSP from "@/assets-new/images/landing/red/psp.png"
import Text from "@/components/shared/Text/index.tsx";
import SignupWrapper from "./SignupWrapper.tsx";
import GenericLayout from "@/layouts/GenericLayout/index.tsx";
import hsbcFundParkFirstAd from "@/assets-new/images/hsbcFundParkAd.jpg";
import goldmanFundParkAd from "@/assets-new/images/goldmanFundParkAd.jpg";
import AutoplayAdBanner from "@/components/adBanners/AutoplayAdBanner";

const Landing = () => {

    return (
        <GenericLayout type="red">
            <AutoplayAdBanner
                ads={[
                    {name: 'HSBC & FundPark 2.5B', image: hsbcFundParkFirstAd},
                    {name: 'Goldman Sachs & FundPark 5B', image: goldmanFundParkAd}
                ]}
                intervalSeconds={5}
            />
            <div style={{paddingBottom: '4rem', minWidth: '1440px'}}>
                <div style={{position: 'relative', display: 'inline-block', minWidth: '1440px'}}>
                    <img
                        className="responsive-img"
                        src={LoginBanner}
                        alt="LoginBanner"
                        style={{display: 'block', width: '100vw', minWidth: '1440px'}}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            textAlign: 'center',
                        }}
                    >
                        <Row style={{width: '100%'}}>
                            <Col
                                span={13}
                                style={{
                                    paddingTop: '1.5rem',
                                    paddingLeft: '2rem',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'flex-start',
                                    alignItems: 'flex-end',
                                }}
                            >
                                <div
                                    className={"responsive-container"}
                                    style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                    }}
                                >
                                    <img src={Money} alt="Money"/>
                                    <img src={CAAMoney} alt="CAAMoney"/>
                                    <img
                                        style={{marginTop: '28px'}}
                                        src={PSP}
                                        alt="PSP"
                                    />
                                </div>

                            </Col>

                            <Col
                                span={8}
                                className={"responsive-container"}
                            >
                                <SignupWrapper/>
                            </Col>
                        </Row>
                    </div>
                </div>


                <div style={{textAlign: "center", marginTop: "1rem"}} className={"responsive-container"}>
                    <img className="responsive-img" src={RedPocketAdv} alt="LoginBanner"/>
                </div>

                <Row justify="center" gutter={[20, 20]} style={{marginTop: '1rem', maxWidth: '100%'}} className={"responsive-container"}>
                    <Col>
                        <img src={QuickDisbursement} alt="QuickDisbursement"/>
                    </Col>
                    <Col>
                        <img src={LargeQuota} alt="LargeQuota"/>
                    </Col>
                    <Col>
                        <img src={AllAspect} alt="AllAspect"/>
                    </Col>
                </Row>

                <div style={{textAlign: "center", marginTop: "3rem"}} className={"responsive-container"}>
                    <img className="responsive-img" src={Safe} alt="Safe"/>
                </div>

                <Row justify="center" gutter={[32, 32]} style={{marginTop: '1.5rem', maxWidth: '100%'}} className={"responsive-container"}>
                    <Col>
                        <img src={FiveB} alt="FiveB"/>
                    </Col>

                    <Col>
                        <img src={NineYears} alt="NineYears"/>
                    </Col>

                    <Col>
                        <img src={CreditModel} alt="CreditModel"/>
                    </Col>
                </Row>

                <div style={{textAlign: "center", marginTop: "3rem"}} className={"responsive-container"}>
                    <img className="responsive-img" src={ThreeSteps} alt="ThreeSteps"/>
                </div>

                <Row justify="center" gutter={[60, 60]} style={{marginTop: '1.5rem',  maxWidth: '100%'}} className={"responsive-container"}>
                    <Col style={{textAlign: 'center'}}>
                        <div>
                            <img src={FastRegister} alt="FastRegister" className="responsive-img"/>
                            <Text style={{fontSize: "36px", color: "white", display: "block", marginTop: "1rem"}}>
                                10秒加密注册
                            </Text>
                        </div>
                    </Col>

                    <Col style={{textAlign: 'center', display: 'flex', alignItems: 'center'}}>
                        <div>
                            <img src={ArrowRight} alt="ArrowRight" className="responsive-img"/>
                            <Text style={{
                                fontSize: "36px",
                                color: "white",
                                display: "block",
                                marginTop: "1rem",
                                visibility: "hidden",
                            }}>
                                0
                            </Text>
                        </div>
                    </Col>

                    <Col style={{textAlign: 'center'}}>
                        <div>
                            <img src={FastApplication} alt="FastApplication" className="responsive-img"/>
                            <Text style={{fontSize: "36px", color: "white", display: "block", marginTop: "1rem"}}>
                                3分钟申请
                            </Text>
                        </div>
                    </Col>

                    <Col style={{textAlign: 'center', display: 'flex', alignItems: 'center'}}>
                        <div>
                            <img src={ArrowRight} alt="ArrowRight" className="responsive-img"/>
                            <Text style={{
                                fontSize: "36px",
                                color: "white",
                                display: "block",
                                marginTop: "1rem",
                                visibility: "hidden",
                            }}>
                                0
                            </Text>
                        </div>
                    </Col>

                    <Col style={{textAlign: 'center'}}>
                        <div>
                            <img src={FastMoney} alt="FastMoney" className="responsive-img"/>
                            <Text style={{fontSize: "36px", color: "white", display: "block", marginTop: "1rem"}}>
                                极速到账
                            </Text>
                        </div>
                    </Col>
                </Row>
            </div>
        </GenericLayout>
    );

};

export default Landing;
