.signupBtnContainer {
  position: relative;
  overflow: visible;

  transition: box-shadow 0.3s ease, transform 0.2s ease;
    &:hover {
    transform: translateY(-2px);
  }

}

.image {
  display: block;
  width: 100%;
  height: 100%;
}

.imageBlue {
  display: block;
  width: 100%;
  height: 53px;
}

.signupBtnTitle {
  position: absolute;
  top:   30%;
  left:  50%;
  transform: translateX(-50%);
  width: 100%;
  font-family: Poppins, serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  background: #321E00;
  background-clip: text;
  text-fill-color: transparent;

}




.redPocketActionButton {
  position: absolute;
  width: 56%;
  height: 28%;
  top:   64%;
  left:  50%;
  transform: translate(-50%, 0);

  padding: 0;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.redPocketActionButtonText {
  position: absolute;
  top: 15px;
  left: 0;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 900;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0;
  horiz-align: center;
  color: #491800;
  width: 100%;
}

.helpingWrapper {
  position: absolute;
  top:   -36%;
  left:  94%;
  display: inline-block;
}

.helpingContainer {
  position: absolute;
  top: -35px;
  left: 320px;
  width:  148px;
  height: 49px;
  pointer-events: none;

  @media (max-width: 992px)  { // smaller than lg
    left: 8%;
  }
}

.helpingContainerRedNew {
  position: absolute;
  top: -35px;
  left: 280px;
  width:  148px;
  height: 49px;
  pointer-events: none;
}

.helpingContainerBlue {
  position: absolute;
  top: -35px;
  left: 59.2%;
  width:  148px;
  height: 49px;
  pointer-events: none;

  @media (max-width: 1592px)  {
    left: 56%;
  }

  @media (max-width: 992px)  {
    left: 45%;
  }
}

.helpingContainer img {
  display: block;
  width: 100%;
  height: auto;
}

.redPocketHelpingText {
  position: absolute;
  top: -31px;
  left: 210px;
  z-index: 1;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  white-space: nowrap;

  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  vertical-align: middle;
  color: #FFFFFF;
  @media (max-width: 992px) { // smaller than lg
    left: 14%;
  }
}



.redPocketHelpingContainerLeft {
  position: absolute;
  top: -35px;
  left: -220px;
  width:  100%;
  height: 100%;
  pointer-events: none;
}
.redPocketHelpingContainerLeft img {
  display: block;
  width: 100%;
  height: auto;
}

.redPocketHelpingTextLeft {
  position: absolute;
  top: -33px;
  left: -210px;
  z-index: 1;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  white-space: nowrap;

  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  vertical-align: middle;
  color: #FFFFFF;
  @media (max-width: 992px) { // smaller than lg
    top:  -37%;
    left: 14%;
  }
}