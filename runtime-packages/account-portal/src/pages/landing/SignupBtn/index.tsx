import React, {ReactNode} from "react";
import styles from "./index.module.scss";
import bubbleDialog from "@/assets-new/images/landing/red/bubbleDialog.png";
import SignupRed from "@/assets-new/images/landing/red/signup-red.png";
import SignupRedNew from "@/assets-new/images/landing/redNew/signupBtnSolidRed.png";
import SignupRedNewModal from "@/assets-new/images/landing/redNew/newRedModalSignupBtn.png";
// import SignupBlue from "@/assets-new/images/landing/blue/signup-blue.png";

interface LongButtonProps {
    buttonTitle: string;
    actionTitle?: string;
    isShowHelpingText?: boolean;
    helpingText?: ReactNode | null;
    width?: string;
    height?: string;
    onClick?: () => void;
    type?: "red" | "blue" | "redNew" | "redNewModal" | string;
    helpingTextStyle?: "left" | "right";
}

const SignupButton: React.FC<LongButtonProps> = ({
                                                   buttonTitle,
                                                   isShowHelpingText = false,
                                                   helpingTextStyle = "right",
                                                   helpingText = null,
                                                   onClick,
                                                   type
                                               }) => {

    let buttonImage
    let buttonTextStyle
    let helpingContainer = styles.helpingContainer;

    const cursorStyle = type === "disabled" ? "not-allowed" : "pointer";
    let containerStyle: React.CSSProperties = {
        cursor: cursorStyle,

    };

    switch (type) {
        case "red":
            buttonImage = <img src={SignupRed} alt="long button" className={styles.image}/>
            buttonTextStyle = {color: 'white'};
            helpingContainer = styles.helpingContainer
            break;
        case "blue":
            buttonImage = <div style={{
                backgroundColor: '#FF8A14',
                width: '100%',
                height: '53px',
                borderRadius: '30px',
            }}></div>
            buttonTextStyle = {color: 'white', fontSize: "24px"};
            helpingContainer = styles.helpingContainerBlue
            break;
        case "redNew":
            buttonImage = <img src={SignupRedNew} alt="long button" className={styles.image}/>
            buttonTextStyle = {color: 'white'};
            helpingContainer = styles.helpingContainerRedNew
            break;
        case "redNewModal":
            buttonImage = <img src={SignupRedNewModal} alt="SignupRedNewModal" className={styles.image} style={{width: '358px'}}/>
            helpingContainer = styles.helpingContainer
            break;
    }

    return (
        <div className={styles.signupBtnContainer} style={containerStyle} onClick={onClick}>
            <div>
                {buttonImage}
            </div>
            <div className={styles.signupBtnTitle} style={buttonTextStyle}>{buttonTitle}</div>

            {helpingTextStyle === 'right' && isShowHelpingText && helpingText && (
                <div>
                    <div className={helpingContainer}>
                       <img src={bubbleDialog} alt="Action"/>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SignupButton;
