import { useTranslation } from "react-i18next";
import { Result, But<PERSON> } from "antd";
import { useNavigate } from "react-router-dom";

const Error: React.FC = () => {
	const { t } = useTranslation();
    const navigate = useNavigate();

    return (
        <Result
            status="500"
            title="500"
            subTitle="Sorry, something went wrong."
            extra={
                <Button type="primary" onClick={() => navigate(-1)}>
                    Back
                </Button>
            }
        />
    );
};

export default Error;
