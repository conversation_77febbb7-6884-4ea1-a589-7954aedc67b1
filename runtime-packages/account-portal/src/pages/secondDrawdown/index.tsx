import React, { useEffect } from "react";
import Page from "@/components/shared/Page";
import { DrawdownAccountForm } from "@/components/DrawdownForm/secondDrawdown";
// import './index.scss';
import DrawdownFormContainer from "@/components/Container/DrawdownFormContainer/DrawdownFormContainer";
import { useLocation } from "react-router-dom";
import { LandingPageIconLabelListCAA, LandingPageIconLabelListSDG } from "@/constants/iconLabelList";
import { useLimitStore } from "@/store/limits";
import { useRedPocketStore } from "@/store/redpocket";
import { useState } from "react";

const DrawdownAccountPage: React.FC = () => {
    const location = useLocation();
    const isXdjCheck = location.pathname === "/credit/hook/second-drawdown";
    const landingPageIconLabelList = isXdjCheck ? LandingPageIconLabelListSDG : LandingPageIconLabelListCAA;
    const { fetchUserLimits, fetchUserCreditApplication } = useLimitStore();

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits();
            await fetchUserCreditApplication();
        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);

    const limit = useLimitStore().creditLimit?.available_limit;

    return (
        <Page>
            <DrawdownFormContainer
                title="还可以用（美元）"
                landingPageIconLabelList={landingPageIconLabelList}
                limit={limit}
            >
                <DrawdownAccountForm limit={limit} />
            </DrawdownFormContainer>
        </Page>
    );
};

export default DrawdownAccountPage;
