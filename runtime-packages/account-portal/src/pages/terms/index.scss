@use "@/assets/styles/variables.module.scss" as *;
/* 通用字体样式 */
$base-font-family: "Source Sans Pro", sans-serif;
$base-font-size: 14px;
$base-line-height: 20px;

.cp-terms {
	height: 100%;
    &-tabs {
		height: 100%;
        > .ant-tabs-content-holder {
            max-height: 100%;
			padding-right: 4px;
            overflow-y: auto;
        }
    }

    /* 标题样式 */
    .title {
        font-family: $base-font-family;
        font-size: 32px;
        font-weight: 900;
        line-height: 40.22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: $fp-text-primary-color;
    }

    /* 时间样式 */
    .text-time {
        font-family: $base-font-family;
        font-size: $base-font-size;
        font-weight: 400;
        line-height: $base-line-height;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: $fp-text-subtle-color;

        .time-separator {
            display: inline-block;
            margin: 0 4px;
            &::before {
                content: '|';
                margin: 0 4px;
            }
        }
    }

    /* 内容部分容器样式 */
    .paragraph-container {
        display: flex;
        padding-left: 16px;
        border-left: 1px solid;
        border-image-source: linear-gradient(180deg, rgba(223, 225, 229, 0) 0%, #dfe1e5 10.21%, #dfe1e5 100%);
        border-image-slice: 1;
    }

    /* 内容文字样式 */
    .paragraph-content {
        font-family: $base-font-family;
        font-size: $base-font-size;
        font-weight: 700;
        line-height: 22px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: $fp-text-primary-color;

        /* 针对 span 的样式 */
        span {
            display: block;
            margin: 20px 0;
            font-weight: 400;

            b {
                font-weight: 700;
            }
        }
    }

    /* 自定义 Tabs 样式 */
    .ant-tabs-left > .ant-tabs-nav {
        margin-top: 50px;
        width: 240px;
        border-right: none !important;
    }

    /* 通用 Tab 样式（未选中状态 & 悬停效果） */
    .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
        display: flex;
        align-items: center;
        width: 100%;
        height: 46px;
        padding: 12px 24px;
        gap: 10px;
        border-radius: 20px 0 0 20px;
        opacity: 1;
        background: none;
        font-family: $base-font-family;
        font-size: 14px;
        font-weight: 700;
        color: $fp-text-primary-color;
        transition: all 0.3s ease;
        border-right: none !important;
    }

    /* 未选中 Tab 悬停效果 */
    .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab:hover {
        background: linear-gradient(
            90deg,
            rgba(114, 202, 196, 0.5) 0%,
            rgba(242, 242, 242, 0.5) 56.72%,
            rgba(250, 250, 250, 0) 103.12%
        );
    }

    /* 选中 Tab 样式 */
    .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab-active {
        color: $fp-text-primary-color;
        background: linear-gradient(90deg, #72cac4 0%, #f2f2f2 56.72%, rgba(250, 250, 250, 0) 103.12%);
        border-right: none !important;
        box-shadow: none !important;
    }

    /* 去除选中标签的默认边框和颜色 */
    .ant-tabs-tab-active .ant-tabs-tab-btn {
        color: inherit !important;
        background: transparent !important;
        border: none !important;
    }

	.ant-tabs-ink-bar {
		display: none;
	}
}
