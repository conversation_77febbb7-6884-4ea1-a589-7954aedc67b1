import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Tabs, Typography, Row, Col } from "antd";
import { useNavigate } from "react-router-dom";
import { Locale, parseQuery } from "@fundpark/ui-utils";
import getEnUSPbContent from "./en-us";
import getZhCNPbContent from "./zh-cn";
import getZhHKPbContent from "./zh-hk";
import "./index.scss";

const { Title, Text } = Typography;

// 提取标题和时间部分为公共组件
const HeaderSection: React.FC<{ title: string }> = ({ title }) => {
    const { t } = useTranslation();

    return (
        <Row justify="space-between" align="middle">
            <Col>
                <Title level={1} className="title">
                    {title}
                </Title>
            </Col>
            <Col>
                <Text className="text-time">
                    {t("pb.updateTime")}: 2024-12-12
                    <span className="time-separator" />
                    {t("pb.effectiveTime")}: 2024-12-12
                </Text>
            </Col>
        </Row>
    );
};

const TermsPage: React.FC = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [activeKey, setActiveKey] = useState(() => {
        const query = parseQuery<{ tab?: string }>(window.location.search);
        return query.tab || "terms";
    });

    const lang = i18n.language as Locale;

    const pbMap: Record<Locale, () => React.FC<{}>[]> = {
        en_US: getEnUSPbContent,
        zh_CN: getZhCNPbContent,
        zh_HK: getZhHKPbContent
    };

    const [Pb1, Pb2, Pb3] = (pbMap[lang] || getEnUSPbContent)();
    const handleTabChange = (key: string) => {
        setActiveKey(key);
        navigate(`?tab=${key}`, { replace: true });
    };

    const items = [
        {
            key: "terms",
            label: t("pb.tab1"),
            children: (
                <div>
                    <HeaderSection title={t("pb.tab1")} />
                    <Pb1 key={lang} />
                </div>
            )
        },
        {
            key: "privacy",
            label: t("pb.tab2"),
            children: (
                <div>
                    <HeaderSection title={t("pb.tab2")} />
                    <Pb2 key={lang} />
                </div>
            )
        },
        {
            key: "dataPolicy",
            label: t("pb.tab3"),
            children: (
                <div>
                    <HeaderSection title={t("pb.tab3")} />
                    <Pb3 key={lang} />
                </div>
            )
        }
    ];

    return (
        <div className="cp-terms">
            <Tabs
                className="cp-terms-tabs"
                tabPosition="left"
                activeKey={activeKey}
                onChange={handleTabChange}
                items={items}
            />
        </div>
    );
};

export default TermsPage;
