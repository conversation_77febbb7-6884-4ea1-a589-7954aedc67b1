import { OnSiteMessage, MessageStatus, OwnerType } from "@fundpark/fp-api/types/onSiteMessage.js";
import { NotificationSentBy } from '@/types/notifications/enum';

// Mock messages data
export const mockMessages: OnSiteMessage[] = [
  {
    id: "msg1",
    noticeId: "notice1",
    ownerId: "owner1",
    ownerName: "John Doe",
    ownerType: "Client_User" as OwnerType,
    title: "Important Account Update",
    content: "<p>Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.Your account has been successfully updated. Please review your profile information to ensure everything is correct.</p>",
    messageStatus: "Unread" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-15T09:30:00.000Z",
    receivedTime: "2023-06-15T09:30:05.000Z",
    readTime: null,
    url: "",
    extId: "",
    messagetype: "Notification"
  },
  {
    id: "msg2",
    noticeId: "notice2",
    ownerId: "owner1",
    ownerName: "John Doe",
    ownerType: "Client_User" as OwnerType,
    title: "New Feature Available",
    content: "<p>We've added a new feature to our platform. You can now export your data directly to Excel.</p>",
    messageStatus: "Read" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-10T14:15:00.000Z",
    receivedTime: "2023-06-10T14:15:10.000Z",
    readTime: "2023-06-10T15:20:00.000Z",
    url: "",
    extId: "",
    messagetype: "Notification"
  },
  {
    id: "msg3",
    noticeId: "notice3",
    ownerId: "owner1",
    ownerName: "John Doe",
    ownerType: "Client_User" as OwnerType,
    title: "Payment Confirmation",
    content: "<p>Your payment of $1,000 has been successfully processed. Thank you for your business!</p>",
    messageStatus: "Unread" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-05T11:45:00.000Z",
    receivedTime: "2023-06-05T11:45:05.000Z",
    readTime: null,
    url: "",
    extId: "",
    messagetype: "Notification"
  },
  {
    id: "msg4",
    noticeId: "notice4",
    ownerId: "owner1",
    ownerName: "John Doe",
    ownerType: "Client_User" as OwnerType,
    title: "Join Company Invitation",
    content: "<p>You have been invited to join ABC Company as an administrator.</p>",
    messageStatus: "Unread" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-01T16:20:00.000Z",
    receivedTime: "2023-06-01T16:20:05.000Z",
    readTime: null,
    url: "?busType=join",
    extId: "company123",
    messagetype: "Notification"
  }
];

// Mock announcements data
export const mockAnnouncements: OnSiteMessage[] = [
  {
    id: "ann1",
    noticeId: "notice5",
    ownerId: "admin1",
    ownerName: "Admin",
    ownerType: "Internal_User" as OwnerType,
    title: "System Maintenance",
    content: "<p>Our system will undergo maintenance on June 30, 2023 from 2:00 AM to 5:00 AM (UTC). Some services may be unavailable during this time.</p>",
    messageStatus: "Unread" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-20T10:00:00.000Z",
    receivedTime: "2023-06-20T10:00:05.000Z",
    readTime: null,
    url: "",
    extId: "",
    messagetype: "Announcement"
  },
  {
    id: "ann2",
    noticeId: "notice6",
    ownerId: "admin1",
    ownerName: "Admin",
    ownerType: "Internal_User" as OwnerType,
    title: "New Terms of Service",
    content: "<p>We've updated our Terms of Service. Please review the changes at your earliest convenience.</p><p>The new terms will take effect on July 1, 2023.</p>",
    messageStatus: "Read" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-18T08:30:00.000Z",
    receivedTime: "2023-06-18T08:30:10.000Z",
    readTime: "2023-06-18T09:15:00.000Z",
    url: "",
    extId: "",
    messagetype: "Announcement"
  },
  {
    id: "ann3",
    noticeId: "notice7",
    ownerId: "admin1",
    ownerName: "Admin",
    ownerType: "Internal_User" as OwnerType,
    title: "Holiday Schedule",
    content: "<p>Our offices will be closed for the upcoming holiday on July 4, 2023. Support services will be limited during this time.</p>",
    messageStatus: "Unread" as MessageStatus,
    category: "Client_User",
    sentBy: NotificationSentBy.System,
    sentTime: "2023-06-15T13:45:00.000Z",
    receivedTime: "2023-06-15T13:45:05.000Z",
    readTime: null,
    url: "",
    extId: "",
    messagetype: "Announcement"
  }
]; 