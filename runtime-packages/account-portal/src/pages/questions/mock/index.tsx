import React from 'react';
import { MockOnSiteMessageProvider } from './MockOnSiteMessageProvider';

// This component wraps the application with the mock provider
export const withMockOnSiteMessageProvider = (Component: React.ComponentType) => {
  return function WithMockProvider(props: any) {
    return (
      <MockOnSiteMessageProvider>
        <Component {...props} />
      </MockOnSiteMessageProvider>
    );
  };
};

export { useOnSiteMessage } from './MockOnSiteMessageProvider';
export * from './mockData'; 