import { List, Tag, Pagination, Button } from 'antd';
import { useTranslation } from "react-i18next";
import styles from '../index.module.scss';
import { useState, useEffect, useRef } from 'react';
import { OnSiteMessage, MessageStatus } from "@fundpark/fp-api/types/onSiteMessage.js";
import dayjs from "dayjs";
import DOMPurify from "dompurify";
import { useNavigate } from "react-router-dom";

import { NotificationSentBy } from '@/types/notifications/enum';
import EmptyImg from '@/assets/images/notifications/empty-notifications.svg?react';
import SearchBar from '../../components/SearchBar';
import { MessageStatus as MessageStatusEnum } from '@fundpark/fp-api/types/noticeMessage.js';
import ArrowDown from '@/assets/icons/arrow-down.svg?react';
import ArrowUp from '@/assets/icons/arrow-up.svg?react';
import { useOnSiteMessage } from '../../mock/MockOnSiteMessageProvider';

const MessageList: React.FC = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [pageSize, setPageSize] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [searchText, setSearchText] = useState('');
    const [filter, setFilter] = useState<'All' | MessageStatus>('All');
    const [expandedMessages, setExpandedMessages] = useState<Set<string>>(new Set());
    const [truncatedMessages, setTruncatedMessages] = useState<Set<string>>(new Set());
    const contentRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

    const { 
        messages, 
        messagesTotal: total, 
        messagesLoading: loading,
        fetchMessages,
        updateMessageStatus,
        revalidateMessages
    } = useOnSiteMessage();

    useEffect(() => {
        fetchMessages({
            pageSize,
            pageNum: currentPage,
            messageStatus: filter === 'All' ? undefined : filter,
            content: searchText
        });
    }, [fetchMessages, currentPage, pageSize, searchText, filter]);

    useEffect(() => {
        // Check which messages are truncated after render and content load
        const checkTruncatedMessages = () => {
            const newTruncatedMessages = new Set<string>();
            messages.forEach(message => {
                const contentElement = contentRefs.current[message.id];
                if (contentElement) {
                    // Reset any inline styles that might affect height calculation
                    contentElement.style.height = '';
                    contentElement.style.maxHeight = '';
                    
                    // For expanded messages, we need to check if they would be truncated when collapsed
                    if (expandedMessages.has(message.id)) {
                        // Temporarily remove expanded state for measurement
                        contentElement.style.maxHeight = '120px'; // notificationContent-description-content max-height
                        if (contentElement.scrollHeight > contentElement.clientHeight) {
                            newTruncatedMessages.add(message.id);
                        }
                        contentElement.style.maxHeight = ''; // Reset back
                    } else {
                        if (contentElement.scrollHeight > contentElement.clientHeight) {
                            newTruncatedMessages.add(message.id);
                        }
                    }
                }
            });
            setTruncatedMessages(newTruncatedMessages);
        };

        checkTruncatedMessages();
        // Add a small delay to ensure content is fully rendered
        const timeoutId = setTimeout(checkTruncatedMessages, 100);
        
        // Add resize observer to handle dynamic content changes
        const resizeObserver = new ResizeObserver(() => {
            checkTruncatedMessages();
        });
        
        // Observe all message content elements
        messages.forEach(message => {
            const element = contentRefs.current[message.id];
            if (element) {
                resizeObserver.observe(element);
            }
        });

        return () => {
            clearTimeout(timeoutId);
            resizeObserver.disconnect();
        };
    }, [messages, expandedMessages]); // Add expandedMessages to dependencies

    const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const newPageSize = parseInt(event.target.value);
        setPageSize(newPageSize);
        setCurrentPage(1); // Reset to first page when changing page size
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleSearch = (text: string, filterValue: string) => {
        setSearchText(text);
        setFilter(filterValue as MessageStatus);
        setCurrentPage(1); // Reset to first page when searching or filtering
    };

    const handleMessageClick = async (message: OnSiteMessage) => {
        try {
            if (message.messageStatus === 'Unread') {
                await updateMessageStatus(message.id, 'Read');
            }

            // Toggle expand state
            setExpandedMessages(prev => {
                const newSet = new Set(prev);
                if (newSet.has(message.id)) {
                    newSet.delete(message.id);
                } else {
                    newSet.add(message.id);
                }
                return newSet;
            });
        } catch (error) {
            console.error("Failed to update message status:", error);
        }
    };

    const formatSentBy = (sentBy: string | undefined) => {
        switch (sentBy) {
            case NotificationSentBy.System:
                return "System";
            default:
                return sentBy;
        }
    }

    const ExpandBtn = () => {
        return (
            <div className={styles.expandBtn}>
                <span className={styles.expandText}>{t('notifications.messageList.collapse')}</span>
                <div className={styles.expandIcon}><ArrowUp /></div>
            </div>
        )
    }

    const CollapseBtn = () => {
        return (
            <div className={styles.expandBtn}>
                <span className={styles.expandText}>{t('notifications.messageList.expand')}</span>
                <div className={styles.expandIcon}><ArrowDown /></div>
            </div>
        )
    }

    return (
        <>
            <SearchBar 
                onSearch={handleSearch} 
                unreadMessagesIds={messages.filter(item => item.messageStatus === MessageStatusEnum.Unread).map(item => item.id)} 
                refreshData={revalidateMessages} 
            />
            <div className={styles.notificationList}>
                {!loading && messages.length === 0 && (
                    <div className={styles.emptyList}>
                        <div className={styles.emptyListImg}>
                            <EmptyImg />
                        </div>
                        <div className={styles.emptyListText}>{t("notifications.empty")}</div>
                    </div>
                )}
                {(loading || messages.length !== 0) && (
                    <>
                        <List
                            loading={loading}
                            dataSource={messages}
                            renderItem={(item) => (
                                <List.Item
                                    key={item.id}
                                    onClick={() => handleMessageClick(item)}
                                    className={styles.notificationItem}
                                >
                                    <div className={styles.notificationContent}>
                                        <div className={styles.header}>
                                            {item.messageStatus === 'Read' ?
                                                <span className={styles.readDot} /> :
                                                <span className={styles.unreadDot} />
                                            }
                                            <span className={styles.title}>{item.title}</span>
                                        </div>
                                        <div className={`${styles.description} ${expandedMessages.has(item.id) ? styles.expanded : ''}`}>
                                            <div
                                                ref={el => contentRefs.current[item.id] = el}
                                                className={styles.content}
                                            >
                                                <div dangerouslySetInnerHTML={{
                                                    __html: DOMPurify.sanitize(item.content)
                                                }} />
                                                {item.url?.includes('busType=join') && item.extId && (
                                                    <div className={styles.joinButtonContainer} onClick={e => e.stopPropagation()}>
                                                        <Button 
                                                            type="primary"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                navigate("/company?busType=join", { 
                                                                    state: { companyId: item.extId }
                                                                });
                                                            }}
                                                            className={styles.joinButton}
                                                        >
                                                            {t('notifications.messageList.joinNow')}
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className={styles.footer}>
                                            <Tag className={styles.tag}>{formatSentBy(item.sentBy)}</Tag>
                                            <span className={styles.timestamp}>
                                                {dayjs(item.sentTime).format("YYYY/MM/DD HH:mm:ss")}
                                            </span>
                                            {truncatedMessages.has(item.id) && (
                                                <span className={styles.expandText}>
                                                    {expandedMessages.has(item.id) ? <ExpandBtn /> : <CollapseBtn />}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </List.Item>
                            )}
                        />

                        <div className={styles.pagination}>
                            <span className={styles.total}>
                                {t('notifications.messageList.totalItems', { count: messages.length, total })}
                            </span>
                            <Pagination
                                total={total}
                                pageSize={pageSize}
                                current={currentPage}
                                onChange={handlePageChange}
                                showSizeChanger={false}
                            />
                            <div className={styles.showRows}>
                                {t('notifications.messageList.showText')}
                                <select value={pageSize} className={styles.rowNumber} onChange={handlePageSizeChange}>
                                    <option value="10">10 {t('notifications.messageList.rowsText')}</option>
                                    <option value="20">20 {t('notifications.messageList.rowsText')}</option>
                                    <option value="50">50 {t('notifications.messageList.rowsText')}</option>
                                    <option value="100">100 {t('notifications.messageList.rowsText')}</option>
                                </select>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </>
    );
};

export default MessageList; 