import React, {useEffect, useState} from "react";
import { useTranslation } from "react-i18next";
import QuestionsLayout from "@/layouts/QuestionsLayout";
import MainLayout from "@/layouts/MainLayout";
import { Grid } from "antd";
const FAQ = () => {
    const screens = Grid.useBreakpoint();
    const queryParams = new URLSearchParams(window.location.search);
    const paramValue = String(queryParams.get('q')) ;

    return (
        <MainLayout>
            <div className={screens.lg?`max-w-[1440px] mx-auto px-4 lg:px-12 h-full relative`:`max-w-[1440px] px-4 lg:px-12 h-full relative`}>
                <QuestionsLayout questionTab={paramValue === 'null'? '' : paramValue }/>
            </div>
        </MainLayout>
    );
};

export default FAQ;
