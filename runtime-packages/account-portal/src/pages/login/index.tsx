import { useEffect, useState } from "react";
import { But<PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "antd";
import { Login as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import FormContainer from "@/components/common/form/FormContainer";
import { Input, Password } from "@/components/common/Input";
import { But<PERSON> } from "@/components/common";
import { validateEmail } from "@fundpark/ui-utils";
import { usePositionTracking } from "@/hooks/usePositionTracking";
import "./index.scss";
import { useMatomoContext } from "@/contexts/MatomoContext";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import { useMappedSelectedProduct } from "@/hooks/useSelectedProduct";
// import { useCommonStore } from "@/store/common.ts";

interface LoginProps {
    onForgotPassword: () => void;
    initialValues?: {
        email?: string;
    };
}

const Login: React.FC<LoginProps> = ({ onForgotPassword, initialValues = {}  }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [loginForm] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const selectedProduct = useMappedSelectedProduct();
    // const { logout } = useCommonStore();
    const { trackEvent } = useMatomoContext();
    const tabfBaseURL = import.meta.env.VITE_TABF_BASE_URL as string;

    // Use the hook for position tracking
    const { navigateToStoredPosition } = usePositionTracking();
    const { setUserId } = useMatomoContext();

    const EMAIL_RULES = [
        { required: true, message: t("login.emailRequired") },
        {
            validator: async (_: any, value: string) => {
                if (!value) return;
                if (!validateEmail(value)) {
                    throw new Error(t("login.emailInvalid"));
                }
            }
        }
    ];

    useEffect(() => {
        appHelper.clearAccessInfo();

        if (initialValues.email ) {
            loginForm.setFieldsValue({
                email: initialValues.email
            });
        }
    }, []);


    const handleLogin = async () => {
        const loginData = await loginForm.validateFields();
        const values = {
            source: selectedProduct,
            ...loginData
        };

        try {
            setLoading(true);
            const res = await LoginHandler(values);
            if (res.message === "success") {
                // Set Matomo user ID
                setUserId(String(res.data.user.id));
                trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.LOGIN, TRACKING_EVENTS.LOGIN_SUCCESS);

                // redirect to other portal if needed
                if (
                    res.data.is_skip &&
                    !["small_shop_financing", "revenue_credit_automation"].includes(res.data.user.register_source)
                ) {
                    const expiryDate = new Date();
                    expiryDate.setTime(expiryDate.getTime() + 2 * 60 * 1000);

                    document.cookie = `AccessToken=${encodeURIComponent(res.data.access_token)}; expires=${expiryDate.toUTCString()}; path=/; domain=.fundpark.com`;
                    appHelper.clearAccessInfo();
                    window.location.replace(res.data.source_index_path);
                    return;
                }

                appHelper.msgApi.success(t("login.success"));
                appHelper.setAccessInfo({ accessToken: res.data.token });
                appHelper.setUserInfo(res.data.user);

                // Navigate to the stored position or default to home page
                try {
                    // Use the hook's navigateToStoredPosition method with a fallback
                    await navigateToStoredPosition("/");
                } catch (error) {
                    console.error("Failed to navigate to last position:", error);
                    navigate("/");
                }
            } else {
                switch (res.code) {
                    case 400:
                        loginForm.setFields([
                            {
                                name: "password",
                                errors: [res.message]
                            }
                        ]);
                        break;
                    case 401:
                        loginForm.setFields([
                            {
                                name: "email",
                                errors: [res.message]
                            }
                        ]);
                        break;
                    case 2003:
                        const expiryDate = new Date();
                        expiryDate.setTime(expiryDate.getTime() + 2 * 60 * 1000);
                        document.cookie = `AccessToken=${encodeURIComponent(res.message)}; expires=${expiryDate.toUTCString()}; path=/; domain=.fundpark.com`;
                        appHelper.clearAccessInfo();
                        window.location.replace(`${tabfBaseURL}/redirect?source=${selectedProduct}`);
                        return;
                    default:
                        appHelper.msgApi.error(t("login.genericError"));
                        break;
                }
            }
        } catch (error) {
            console.error("Login error:", error);
            appHelper.msgApi.error(t("login.genericError"));
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <FormContainer>
                <Form form={loginForm} layout="vertical" className="w-full" requiredMark={false}>
                    <Form.Item label={t("login.email")} name="email" rules={EMAIL_RULES} className="mb-6">
                        <Input disabled={loading} placeholder={t("login.emailRequired")} size="large" />
                    </Form.Item>

                    <Form.Item name="password" rules={[{ required: true, message: t("login.passwordRequired") }]}>
                        <div className="flex flex-col w-full">
                            <div className="w-full flex justify-between items-center mb-2">
                                <span>{t("login.password")}</span>
                                <GenericButton
                                    type="link"
                                    onClick={onForgotPassword}
                                    style={{
                                        marginLeft: "auto",
                                        fontSize: "14px",
                                        fontWeight: "400",
                                        color: "#2463EB"
                                    }}
                                >
                                    {t("login.forgotPassword")}
                                </GenericButton>
                            </div>
                            <Password
                                disabled={loading}
                                visibilityToggle
                                placeholder={t("login.passwordRequired")}
                                size="large"
                            />
                        </div>
                    </Form.Item>

                    <Button
                        onClick={() => handleLogin()}
                        loading={loading}
                        label={t("login.loginBtn")}
                        className="mt-6 mb-6"
                    />
                </Form>
            </FormContainer>
        </>
    );
};

export default Login;
