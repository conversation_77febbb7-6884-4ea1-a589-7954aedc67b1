import { useTranslation } from "react-i18next";
import { Result, But<PERSON> } from "antd";
import { useNavigate } from "react-router-dom";

const NotFound: React.FC = () => {
	const { t } = useTranslation();
    const navigate = useNavigate();

    return (
        <Result
            status="404"
            title="404"
            subTitle="Sorry, the page you visited does not exist."
            extra={
                <Button type="primary" onClick={() => navigate(-1)}>
                    Back
                </Button>
            }
        />
    );
};

export default NotFound;
