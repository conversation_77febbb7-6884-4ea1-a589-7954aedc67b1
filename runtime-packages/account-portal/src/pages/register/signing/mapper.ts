import { ShareholderVerificationResult } from './types';

/**
 * Maps the API response from the shareholder structure API to a more usable format
 * for the UI.
 * 
 * @param apiResponse The API response from getShareholderStructureResult
 * @returns A normalized ShareholderVerificationResult
 */
export const mapShareholderApiToUi = (apiResponse: any): ShareholderVerificationResult => {
  return {
    code: apiResponse.code,
    message: apiResponse.message,
    data: apiResponse.data
  };
};

/**
 * Checks if all verification checks have passed based on response data
 * 
 * @param result The ShareholderVerificationResult
 * @returns boolean indicating if all verification checks passed
 */
export const allVerificationsSuccessful = (result: ShareholderVerificationResult): boolean => {
  const { data } = result;
  
  if (!data) return false;
  
  return (
    data.telphone === 'Y' &&
    data.psp === 'Y' &&
    data.qcc === 'Y' &&
    data.qcc_sharehold === 'Y'
  );
}; 