.loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: calc(100vh - 440px); // Adjust based on your layout's header/footer
}

.loadingContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    padding: 20px;
}

.iconPlaceholder {
    width: 84px;
    height: 84px;
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.processingText {
    color: #201747; // Use your brand's primary text color
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 24px;
}

.estimatedTimeText {
    color: #282830; // Secondary text color
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 70px;
}

.progressBarContainer {
    width: 100%;
    max-width: 859px;
    
    :global {
        .ant-progress-inner {
            border-radius: 12px;
            padding: 3px;
            box-shadow: inset 0 1px 4px 0 rgba(0, 0, 0, 0.25);
        }
        
        .ant-progress-text {
            font-weight: 600;
        }
        
        .ant-progress-bg {
            transition: all 1.5s ease;
            background: linear-gradient(-45deg, #64CCC9, #88FAF7, #64CCC9, #88FAF7);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 70%, rgba(255,255,255,0.2) 85%, rgba(255,255,255,0.3) 100%);
                animation: progressShineFromRight 2.5s infinite;
            }
        }
    }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes progressShineFromRight {
    0% { right: -50%; }
    100% { right: 100%; }
}
