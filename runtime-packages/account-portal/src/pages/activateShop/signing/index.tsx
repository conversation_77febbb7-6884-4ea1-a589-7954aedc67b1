import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { message } from "antd";
import Page from "@/components/shared/Page";
import SigningForm from "@/components/ActivateShopForm/Signing";
import { OurWechatModal } from "@/components/shared/OurWechatModal";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import RedPocket from "@/components/RedPocket";
// import ActivateShopForm from "@/components/ActivateShopForm";
import { getShareholderStructureResult } from "@fundpark/fp-api";
import { ShareholderStructureCode, SigningPageState } from "./types";
import { mapShareholderApiToUi } from "./mapper";
import { ErrorMessages, Routes, VerificationStatusCodes } from "@/constants/signing";
import { storeCurrentPath } from "@/utils/pathTracker";
import "./index.scss";
import { LandingPageIconLabelListCAA, LandingPageIconLabelListSDG } from "@/constants/iconLabelList.ts";
import IconLabelList from "@/components/shared/IconLabelList";
import LimitTitleContainer from "@/containers/LimitTitleContainer";
import { useRedPocketStore } from "@/store/redpocket.ts";
import { useSelectedProduct } from "@/hooks/useSelectedProduct";
import { useLimitStore } from "@/store/limits";
import ShopAuthErrorModal from '@/components/DrawdownForm/Signing/ShopAuthErrorModal';
import MobileVerificationErrorModal from '@/components/DrawdownForm/Signing/MobileVerificationErrorModal';

const SigningPage: React.FC = () => {
    const navigate = useNavigate();
    const [state, setState] = useState<SigningPageState>({
        modalVisible: false,
        modalMessage: "",
        verificationPassed: false
    });
    const [showShopAuthErrorModal, setShowShopAuthErrorModal] = useState(false);
    const [showMobileVerificationErrorModal, setShowMobileVerificationErrorModal] = useState(false);
    const { getRedPocketByType } = useRedPocketStore();
    const {search} = useLocation();
    const params = new URLSearchParams(search);
    const token = params.get("id_token");
    useEffect(() => {
      if (token) {
        const timer = setTimeout(() => {
          verifyShareholderStructure();
        }, 3000);
        
        return () => clearTimeout(timer);
      } else {
        verifyShareholderStructure();
      }
    }, [token]);

    const fetchRedPocketList = useRedPocketStore(state => state.fetchRedPocketList);

    const verifyShareholderStructure = async (): Promise<void> => {
        try {
            const apiResponse = await getShareholderStructureResult();
            console.log("getShareholderStructureResult: ", apiResponse);
            const result = mapShareholderApiToUi(apiResponse);
            handleVerificationResponse(0);
        } catch (error) {
            console.error("Failed to check shareholder structure result:", error);
            message.error(ErrorMessages.VERIFICATION_FAILED);
        }
    };

    const handleVerificationResponse = (code: ShareholderStructureCode): void => {
        switch (code) {
            case VerificationStatusCodes.SUCCESS:
                // Success - Access granted to SigningForm
                setState(prev => ({ ...prev, verificationPassed: true }));
                break;
            case VerificationStatusCodes.WAITING:
                // Waiting - Redirect to loading page
                navigate(Routes.LOADING_PAGE);
                break;
            case VerificationStatusCodes.MANUAL_REVIEW:
            case VerificationStatusCodes.BUSINESS_INFO_FAILED:
                showModal("MANUAL_REVIEW_REQUIRED");
                break;
            case VerificationStatusCodes.MOBILE_ANT_FAILED:
                setShowMobileVerificationErrorModal(true);
                break;
            case VerificationStatusCodes.NOT_ALLOWED_ACCESS:
                setShowShopAuthErrorModal(true);
                break;
            case VerificationStatusCodes.PEOPLE_NOT_FOUND:
                showModal("PEOPLE_NOT_FOUND");
                break;
            default:
                showModal("UNKNOWN_ERROR");
                break;
        }
    };

    const showModal = (type: keyof typeof ErrorMessages): void => {
        setState(prev => ({
            ...prev,
            modalVisible: true,
            modalMessage: ErrorMessages[type]
        }));
    };

    const handleModalClose = (): void => {
        storeCurrentPath({ path: Routes.HOME_PAGE, pathName: "Post Signing Error" });
        setState(prev => ({ ...prev, modalVisible: false }));
        navigate(Routes.HOME_PAGE); // Navigate back to main page
    };

    const product = useSelectedProduct();

    const logoList = product === "xdj" ? LandingPageIconLabelListSDG : LandingPageIconLabelListCAA;

    const { creditApplication } = useLimitStore();

    useEffect(() => {
        fetchRedPocketList();
    }, []);

    return (
        <Page>
            <BackdropContainer isShowWhiteBottom={true} style={{ paddingBottom: "0px" }}>
                <LimitTitleContainer />
                <IconLabelList items={logoList} />

                {getRedPocketByType("deduction")?.remaining_quantity !== 0 &&
                    getRedPocketByType("deduction")?.status === "claimed" && (
                        <RedPocket type="doubleLine" titleLineOne={"申请支用使用红包"} titleLineTwo={"500 美元"} />
                    )}
            </BackdropContainer>
            {state.verificationPassed && <SigningForm type="hook" />}

            <OurWechatModal
                open={state.modalVisible}
                onClose={handleModalClose}
                message={state.modalMessage}
                width={556}
                hasAlertIcon
            />

            <ShopAuthErrorModal
                open={showShopAuthErrorModal}
                onClose={() => setShowShopAuthErrorModal(false)}
                onComplete={() => {
                    setShowShopAuthErrorModal(false);
                    navigate(Routes.LOADING_PAGE);
                }}
            />

            <MobileVerificationErrorModal
                open={showMobileVerificationErrorModal}
                onClose={() => setShowMobileVerificationErrorModal(false)}
                onComplete={() => {
                    setShowMobileVerificationErrorModal(false);
                    navigate(Routes.LOADING_PAGE);
                }}
            />
        </Page>
    );
};

export default SigningPage;
