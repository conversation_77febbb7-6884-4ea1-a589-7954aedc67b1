import { ReactNode } from 'react';

export interface SigningPageState {
  modalVisible: boolean;
  modalMessage: ReactNode;
  verificationPassed: boolean;
}

// Allow any number code for the response as the API might return unexpected values
export type ShareholderStructureCode = number;

export interface ShareholderVerificationResult {
  code: ShareholderStructureCode;
  message: string;
  data?: {
    telphone?: string;
    psp?: string;
    qcc?: string;
    qcc_sharehold?: string;
  };
} 