import React, { useState, useEffect } from "react";
import { Progress } from "antd";
import { useNavigate } from "react-router-dom";
import Page from "@/components/shared/Page";
import FpConnectionIcon from "@/assets-new/icons/fp-connection-icon.svg?react";
import styles from "./styles.module.scss";
import { getShareholderStructureResult } from "@fundpark/fp-api";

const POLLING_INTERVAL = 5000; // 5 seconds

const SigningLoadingPage: React.FC = () => {
    const navigate = useNavigate();
    const [progress, setProgress] = useState(0);
    
    useEffect(() => {
        // Increment to simulate progress
        const timer = setInterval(() => {
            setProgress(prevProgress => {
                // Slow down as we approach 90%
                const increment = prevProgress < 30 ? 2 : prevProgress < 60 ? 1 : 1;
                const nextProgress = prevProgress + increment;
                
                // Cap at 90% - the final completion should happen when the actual task is done
                return nextProgress >= 90 ? 90 : nextProgress;
            });
        }, 500);
        
        return () => {
            clearInterval(timer);
        };
    }, []);

    // Poll for shareholder structure verification result
    useEffect(() => {
        let pollingTimer: number | undefined;

        const checkVerificationStatus = async () => {
            try {
                const response = await getShareholderStructureResult();
                
                switch (response.code) {
                    case 2000:
                        // Still processing - continue polling
                        pollingTimer = window.setTimeout(checkVerificationStatus, POLLING_INTERVAL);
                        break;
                    
                    default:
                        navigate('/credit/hook/register/signing');
                        break;
                }
            } catch (error) {
                console.error('Failed to check shareholder structure result:', error);
                // On error, continue polling but log the error
                pollingTimer = window.setTimeout(checkVerificationStatus, POLLING_INTERVAL);
            }
        };

        // Start polling immediately
        checkVerificationStatus();

        // Clean up on unmount
        return () => {
            if (pollingTimer) {
                clearTimeout(pollingTimer);
            }
        };
    }, [navigate]);

    return (
        <Page>
            <div className={styles.loadingContainer}>
                <div className={styles.loadingContent}>
                    {/* Connection Icon */}
                    <div className={styles.iconPlaceholder}>
                        <FpConnectionIcon />
                    </div>

                    {/* Processing text */}
                    <div className={styles.processingText}>
                        正在处理中，请稍后
                    </div>

                    {/* Estimated time text */}
                    <div className={styles.estimatedTimeText}>
                        预计等待时间2分钟...
                    </div>

                    <div className={styles.progressBarContainer}>
                        {/* Progress bar */}
                        <Progress
                            percent={progress}
                            showInfo={true}
                            strokeColor={{
                                "0%": "#64CCC9",
                                "100%": "#88FAF7",
                            }}
                            trailColor="#FFFFFF"
                            size={['100%', 20]}
                        />
                    </div>
                </div>
            </div>
        </Page>
    );
};

export default SigningLoadingPage;