import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { message } from 'antd';
import Page from "@/components/shared/Page";
import SigningForm from "@/components/ActivateShopForm/Signing";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import RedPocket from "@/components/RedPocket";
import ClockIcon from "@/assets-new/icons/common/clock.svg";
// import ActivateShopForm from "@/components/ActivateShopForm";
import { getShareholderStructureResult } from '@fundpark/fp-api';
import { SigningPageState, ShareholderStructureCode } from './types';
import { mapShareholderApiToUi } from './mapper';
import { VerificationStatusCodes, ErrorMessages, Routes } from '@/constants/signing';
import { storeCurrentPath } from '@/utils/pathTracker';
import './index.scss';
import { LandingPageIconLabelListCAA } from "@/constants/iconLabelList.ts";
import IconLabelList from "@/components/shared/IconLabelList";
import LimitTitleContainerCAA from "@/containers/LimitTitleContainerCAA";
import {useLimitStore} from "@/store/limits.ts";
import ShopAuthErrorModal from '@/components/DrawdownForm/Signing/ShopAuthErrorModal';
import MobileVerificationErrorModal from '@/components/DrawdownForm/Signing/MobileVerificationErrorModal';

const SigningPage: React.FC = () => {
  const { creditApplication } = useLimitStore();
  const navigate = useNavigate();
  const [state, setState] = useState<SigningPageState>({
    modalVisible: false,
    modalMessage: '',
    verificationPassed: false
  });
  const [showShopAuthErrorModal, setShowShopAuthErrorModal] = useState(false);
  const [showMobileVerificationErrorModal, setShowMobileVerificationErrorModal] = useState(false);

  const {search} = useLocation();
  const params = new URLSearchParams(search);
  const token = params.get("id_token");
  useEffect(() => {
    if (token) {
      const timer = setTimeout(() => {
        verifyShareholderStructure();
      }, 3000);
      
      return () => clearTimeout(timer);
    } else {
      verifyShareholderStructure();
    }
  }, [token]);

  const verifyShareholderStructure = async (): Promise<void> => {
    try {
      const apiResponse = await getShareholderStructureResult();
      console.log('getShareholderStructureResult: ', apiResponse);
      const result = mapShareholderApiToUi(apiResponse);
      handleVerificationResponse(0);
    } catch (error) {
      console.error('Failed to check shareholder structure result:', error);
      message.error(ErrorMessages.VERIFICATION_FAILED);
    }
  };

  const handleVerificationResponse = (code: ShareholderStructureCode): void => {
    switch (code) {
      case VerificationStatusCodes.SUCCESS:
        // Success - Access granted to SigningForm
        setState(prev => ({ ...prev, verificationPassed: true }));
        break;
      case VerificationStatusCodes.WAITING:
        // Waiting - Redirect to loading page
        navigate(Routes.LOADING_PAGE_CAA);
        break;
      case VerificationStatusCodes.MANUAL_REVIEW:
      case VerificationStatusCodes.BUSINESS_INFO_FAILED:
        showModal('MANUAL_REVIEW_REQUIRED');
        break;
      case VerificationStatusCodes.MOBILE_ANT_FAILED:
        setShowMobileVerificationErrorModal(true);
        break;
      case VerificationStatusCodes.NOT_ALLOWED_ACCESS:
        showModal('NOT_ALLOWED_ACCESS');
        break;
      case VerificationStatusCodes.PEOPLE_NOT_FOUND:
        showModal('PEOPLE_NOT_FOUND');
        break;
      default:
        showModal('UNKNOWN_ERROR');
        break;
    }
  };

  const showModal = (type: keyof typeof ErrorMessages): void => {
    setState(prev => ({
      ...prev,
      modalVisible: true,
      modalMessage: ErrorMessages[type]
    }));
  };

  const handleModalClose = (): void => {
    storeCurrentPath({ path: Routes.HOME_PAGE_CAA, pathName: 'Post Signing Error'});
    setState(prev => ({ ...prev, modalVisible: false }));
    navigate(Routes.HOME_PAGE_CAA); // Navigate back to main page
  };

  return (
    <Page>
      <BackdropContainer
          isShowWhiteBottom={true}
          style={{ paddingBottom: '0px' }}
      >
          <LimitTitleContainerCAA />
          <IconLabelList
              items={LandingPageIconLabelListCAA}
          />
        {creditApplication?.type !== 'limit_increase' && <RedPocket
              type="doubleLine"
              titleLineOne={"申请支用使用红包"}
              titleLineTwo={"500 美元"}
              // isShowHelpingText={true}
              // helpingText={
              //     <div>
              //         <img src={ClockIcon} alt="Clock Icon" width="20" height="20" /> 限时免费领取品类报告
              //     </div>
              // }
          />}
        </BackdropContainer>
        {state.verificationPassed && <SigningForm type="underwriting"/>}

      <OurWechatModal
        open={state.modalVisible}
        onClose={handleModalClose}
        message={state.modalMessage}
        width={556}
        hasAlertIcon
      />

      <ShopAuthErrorModal
        open={showShopAuthErrorModal}
        onClose={() => setShowShopAuthErrorModal(false)}
        onComplete={() => {
          setShowShopAuthErrorModal(false);
          navigate(Routes.LOADING_PAGE_CAA);
        }}
      />

      <MobileVerificationErrorModal
        open={showMobileVerificationErrorModal}
        onClose={() => setShowMobileVerificationErrorModal(false)}
        onComplete={() => {
          setShowMobileVerificationErrorModal(false);
          navigate(Routes.LOADING_PAGE_CAA);
        }}
      />
    </Page>
  );
};

export default SigningPage; 