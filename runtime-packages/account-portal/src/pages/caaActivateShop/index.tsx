// src/pages/SDG.tsx

import Page from "@/components/shared/Page";
import React, { useEffect, useState } from "react";
import IconLabelList from "@/components/shared/IconLabelList";
import { LandingPageIconLabelListCAA } from "@/constants/iconLabelList.ts";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import CAAActivateShopForm from "@/components/ActivateShopForm/CAAActivateShopForm";
import { useLimitStore } from "@/store/limits.ts";
import { Spin } from "antd";
import LimitTitleContainerCAA from "@/containers/LimitTitleContainerCAA";
import AuthErrorModal from "@/components/Platforms/authErrorModal";
import { getAuthCallbackResult } from "@fundpark/fp-api";
import { useLocation, useNavigate } from "react-router-dom";
import { useCommonStore } from "@/store/common.ts";
import AppHelper from "@/utils/appHelper.ts";
import { OurWechatModal } from "@/components/shared/OurWechatModal";
import LimitIncreaseMessage from "@/components/shared/LimitIncreaseMessage";
import { usePaymentGatewayStore } from "@/store/platform/paymentGatewayStore";


export interface AuthErrorParams {
    errorMsg: string;
    width?: number;
    height?: number;
    contactMsg?: string;
}

const caaActivateShop = () => {
    const { isLoadingLimits, fetchUserCreditApplication, fetchUserLimits, hasLimitIncrease, creditApplication } =
        useLimitStore();
    const [authErrorParams, setAuthErrorParams] = useState<AuthErrorParams | null>(null);
    const [showAuthError, setShowAuthError] = useState(false);
    const [showWechatModal, setShowWechatModal] = useState(false);
    const { search, pathname } = useLocation();
    const fetchAuthedList = useCommonStore(s => s.fetchAuthedList);

    const {fetchPaymentGateway} = usePaymentGatewayStore();
    

    useEffect(()=>{
        fetchPaymentGateway(true)
    },[])


    React.useEffect(() => {
        const params = new URLSearchParams(search);
        const token = params.get("id_token");

        if (token) {
            (async () => {
                await handleAuthComplete(token);
            })();
        }
    }, []);

    const handleAuthComplete = async (token: any) => {
        if (!token) {
            return;
        }
        try {
            const res = await getAuthCallbackResult({ id_token: token });
            await authResp(res.code, res.message);
        } catch (rawErr: any) {
            console.log(rawErr);
        }
    };

    const authResp = async (code: number, msg?: string) => {
        let params: AuthErrorParams;
        switch (code) {
            case 0:
                await fetchAuthedList(true);
                // setModalStep('none');
                AppHelper.msgApi.success("授权成功");
                return;
            case 4004:
                params = {
                    errorMsg: "未收到您的授权申请，授权店铺不会改变IP地址，请放心授权哦",
                    width: 450,
                    height: 220,
                    contactMsg: "遇到其他问题，请点击【联系客服】"
                };
                break;
            case 4003:
                params = {
                    errorMsg: "检测到您的店铺/支付账户已被其他用户绑定授权了，换一个店铺/支付账户继续申请～",
                    width: 450,
                    height: 220,
                    contactMsg: "申诉或遇到其他问题，请点击【联系客服】"
                };
                break;
            default:
                params = {
                    errorMsg: msg || "An unexpected error occurred.",
                    width: 400,
                    height: 200,
                    contactMsg: "遇到其他问题，请点击【联系客服】"
                };
        }
        setAuthErrorParams(params);
        setShowAuthError(code != 0);
    };

    useEffect(() => {
        fetchUserCreditApplication(true);
        fetchUserLimits(true);
    }, []);

    const limitIncreaseHasPreLimit = creditApplication?.type === "limit_increase" && creditApplication?.pre_limit !== null;

    return (
        <Page>
            {isLoadingLimits ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                    <Spin />
                </div>
            ) : (
                <>
                    <BackdropContainer isShowWhiteBottom={true} style={{ paddingBottom: "100px" }}>
                        <LimitTitleContainerCAA />
                        {creditApplication?.status==='pending' && !limitIncreaseHasPreLimit ? (
                            <LimitIncreaseMessage />
                        ) : (
                            <IconLabelList items={LandingPageIconLabelListCAA} />
                        )}
                    </BackdropContainer>
                    <CAAActivateShopForm />
                    <AuthErrorModal
                        open={showAuthError}
                        onClose={() => {
                            setShowAuthError(false);
                        }}
                        onComplete={() => {
                            setShowAuthError(false);
                            setShowWechatModal(true);
                        }}
                        authError={authErrorParams?.errorMsg || ""}
                        contactMsg={authErrorParams?.contactMsg || ""}
                    />
                    <OurWechatModal
                        open={showWechatModal}
                        onClose={() => setShowWechatModal(false)}
                        message={"有任何问题，欢迎联系我们～"}
                        hasAlertIcon={true}
                        textAlign="center"
                    />
                </>
            )}
        </Page>
    );
};

export default caaActivateShop;
