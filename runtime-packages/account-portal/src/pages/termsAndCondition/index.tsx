import React, { useEffect } from "react";
import Page from "@/components/shared/Page";
import TermsAndCondition from "@/components/TermsAndCondition";
import DrawdownFormContainer from "@/components/Container/DrawdownFormContainer/DrawdownFormContainer";
import { useLocation } from "react-router-dom";
import { LandingPageIconLabelListCAA, LandingPageIconLabelListSDG } from "@/constants/iconLabelList";
import { useLimitStore } from "@/store/limits";
import { useCommonStore } from "@/store/common";

const TermsAndConditionPage: React.FC = () => {
    const location = useLocation();
    const isXdjCheck = location.pathname === "/credit/hook/terms-and-condition";
    const landingPageIconLabelList = isXdjCheck ? LandingPageIconLabelListSDG : LandingPageIconLabelListCAA;

    const { fetchUserLimits, fetchUserCreditApplication } = useLimitStore();

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits();
            await fetchUserCreditApplication();
        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);

    const limit = useLimitStore().creditLimit?.available_limit;
    const {refreshUserInfo } = useCommonStore();
    useEffect(() => {
        refreshUserInfo();
    }, []);

    return (
        <Page>
            <DrawdownFormContainer title="还可以用" landingPageIconLabelList={landingPageIconLabelList} limit={limit}>
                <TermsAndCondition />
            </DrawdownFormContainer>
        </Page>
    );
};

export default TermsAndConditionPage;
