import { useEffect, useState } from "react";
import { Button as <PERSON>ric<PERSON>utton, Checkbox, Form, message } from "antd";
import {useNavigate, useSearchParams} from "react-router-dom";
import { Login as <PERSON><PERSON><PERSON><PERSON><PERSON>, Signup as <PERSON><PERSON><PERSON><PERSON><PERSON>, SignupValidateToken } from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper";
import { useTranslation } from "react-i18next";
import FormContainer from "@/components/common/form/FormContainer";
import { Input, Password } from "@/components/common/Input";
import CountryCodePhoneInput from "@/components/shared/CountryCodePhoneInput";
import { Button } from "@/components/common";
import { validateEmail, validateMobilePhone } from "@fundpark/ui-utils";

import { LoginMethod } from "@/types/common/LoginMethodSwitcher/loginMethodSwitcher.types";
import InitAccountModal from "./InitAccountModal";
import "./index.scss";
import SendButton from "@/components/common/SendButton";
import { useMappedSelectedProduct } from "@/hooks/useSelectedProduct.ts";
import { DEFAULT_AREA_CODES } from "@/components/common/constants";
import { useMatomoContext } from "@/contexts/MatomoContext";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import { MATOMO_CONFIG } from "@/utils/matomoConfig";
import fundparkXDJWaiveInterestPlan from "@/assets-new/pdfs/fundpark-xdj-waive-interest-plan.pdf"
import fundparkRevenueFinancingSubsidyPlan from "@/assets-new/pdfs/fundpark-revenue-financing-subsidy-plan.pdf"

const Signup: React.FC = () => {
    const { trackEvent } = useMatomoContext();
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [loginMethod, setLoginMethod] = useState<LoginMethod>("signup");
    const [initAccountVisible, setInitAccountVisible] = useState(false);
    const { trackEventWithDimensions } = useMatomoContext();
    const [searchParams] = useSearchParams();

    const handlePhoneNumberChange = () => {
        let countryCode = form.getFieldValue("phoneCountryCode");
        let phoneNumber = form.getFieldValue("phoneNumber");

        if (countryCode === undefined) {
            countryCode = "";
        }

        if (phoneNumber === undefined) {
            phoneNumber = "";
        }

        const value = countryCode + phoneNumber

        trackEventWithDimensions({
            category: TRACKING_CATEGORIES.FORM,
            action: TRACKING_ACTIONS.INPUT,
            name: TRACKING_EVENTS.INPUT_PHONE_NUMBER,
            customDimensions: {
                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: value
            }
        });
    };

    const [initAccountData, setInitAccountData] = useState<{
        loginMethod: string;
        email?: string;
        phoneNumber?: string;
        phoneCountryCode?: string;
    }>({ loginMethod });
    const product_name = useMappedSelectedProduct();
    const EMAIL_RULES = [
        { required: true, message: t("login.emailRequired") },
        {
            validator: async (_: any, value: string) => {
                if (!value) return;
                if (!validateEmail(value)) {
                    throw new Error(t("login.emailInvalid"));
                }
            }
        }
    ];

    const PASSWORD_RULES = [
        { required: true, message: t("login.passwordRequired") },
        {
            validator: (_: any, value: string) => {
                if (!value) {
                    return Promise.resolve();
                }
                const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d).{8,30}$/;
                if (!passwordPattern.test(value)) {
                    return Promise.reject(new Error(t("signup.passwordMinLength")));
                }
                return Promise.resolve();
            }
        }
    ];

    const MOBILE_RULES = [
        { required: true, message: t("login.mobileRequired") },
        {
            validator: (_: any, value: string) => {
                if (!value) {
                    return Promise.resolve();
                }
                const countryCode = form.getFieldValue("phoneCountryCode");
                let phonePattern: RegExp;
                switch (countryCode) {
                    case "+86":
                        phonePattern = /^1[3-9]\d{9}$/;
                        break;
                    case "+852":
                        phonePattern = /^[5689]\d{7}$/;
                        break;
                    case "+1":
                        phonePattern = /^[1-9]\d{9}$/;
                        break;
                    default:
                        return Promise.resolve();
                }
                if (!phonePattern.test(value)) {
                    return Promise.reject(new Error(t("login.mobileInvalid")));
                }
                return Promise.resolve();
            }
        }
    ];

    useEffect(() => {
        appHelper.clearAccessInfo();
    }, []);

    const handleSendMobileVerification = async () => {
        try {
            const phone = form.getFieldValue("phoneNumber");
            const areaCode = form.getFieldValue("phoneCountryCode");

            if (!phone) {
                message.error(t("myProfile.mobileRequired"));
                return false;
            }

            const res = await SignupValidateToken({
                phone_country_code: areaCode,
                phone_number: phone
            } as any);

            if (res.message === "success") {
                message.success(t("myProfile.verificationCodeSent"));
                return true;
            } else {
                form.setFields([
                    {
                        name: "mobileVerification",
                        errors: [res.message]
                    }
                ]);
                return false;
            }
        } catch (error) {
            console.error("Mobile verification error:", error);
            form.setFields([
                {
                    name: "mobileVerification",
                    errors: [t("signup.phoneVerificationCodeFail")]
                }
            ]);
            return false;
        }
    };

    const handleSignup = async () => {
        const values = await form.validateFields();
        console.log("values", values);
        try {
            setLoading(true);

            const email = form.getFieldValue("email")
            const phoneNumber = `${form.getFieldValue("phoneCountryCode")}${form.getFieldValue("phoneNumber")}`

            const res = await SignupHandler({
                phone_country_code: form.getFieldValue("phoneCountryCode"),
                phone_number: form.getFieldValue("phoneNumber"),
                phone_code: form.getFieldValue("mobileVerification"),
                email: email,
                password: form.getFieldValue("password"),
                password_confirmation: form.getFieldValue("password"),
                register_source: product_name,
                cpid: searchParams.get("cpid") || null,
            } as any);

            if (res.code === 3) {
                form.setFields([
                    {
                        name: "email",
                        errors: [res.message]
                    }
                ]);
                return;
            }

            if (res.code === 1) {
                form.setFields([
                    {
                        name: "phoneNumber",
                        errors: [res.message]
                    }
                ]);
                return;
            }

            if (res.message === "success") {
                appHelper.msgApi.success(t("signup.success"));
                trackEventWithDimensions({
                    category: TRACKING_CATEGORIES.UI,
                    action: TRACKING_ACTIONS.SIGNUP,
                    name: TRACKING_EVENTS.SIGNUP_SUCCESS,
                    customDimensions: {
                        [MATOMO_CONFIG.NEW_USER_SIGNUP_DIMENSION_ID]: `${email} ${phoneNumber}`
                    }
                });
                const loginRes = await LoginHandler({
                    email: form.getFieldValue("email"),
                    password: form.getFieldValue("password")
                });
                if (loginRes.message === "success") {
                    trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.LOGIN, TRACKING_EVENTS.LOGIN_SUCCESS);
                    appHelper.msgApi.success(t("login.success"));
                    appHelper.setAccessInfo({ accessToken: loginRes.data.token });
                    appHelper.setUserInfo(loginRes.data.user);
                } else {
                    appHelper.msgApi.error(loginRes.message);
                }
                return navigate("/");
            } else {
                appHelper.msgApi.error(res.message);
                console.log(res.message);
            }
        } catch (error) {
            console.error("Login error:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <FormContainer>
                <Form form={form} layout="vertical" className="w-full" requiredMark={false}>
                    <Form.Item label={t("login.email")} name="email" rules={EMAIL_RULES} className="mb-6">
                        <Input
                            disabled={loading}
                            placeholder={t("login.emailRequired")}
                            size="large"
                            onBlur={e => {
                                trackEventWithDimensions({
                                    category: TRACKING_CATEGORIES.FORM,
                                    action: TRACKING_ACTIONS.INPUT,
                                    name: TRACKING_EVENTS.INPUT_EMAIL,
                                    customDimensions: {
                                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                    }
                                });
                            }}
                        />
                    </Form.Item>

                    <Form.Item label={t("login.mobile")} className="mb-2">
                        <CountryCodePhoneInput
                            className="mb-0"
                            countryCodeName="phoneCountryCode"
                            phoneNumberName="phoneNumber"
                            countryCodeOptions={DEFAULT_AREA_CODES}
                            countryCodeRules={[
                                { required: true, message: "请选择国家/地区代码" },
                                {
                                    validator: (_, value) => {
                                        if (value && value.replace("+", "") === "") {
                                            console.log("value", value);
                                            console.log(value.replace("+", ""), "value.replace");
                                            return Promise.reject(new Error("请选择有效的国家/地区代码"));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                            phoneNumberRules={MOBILE_RULES}
                            countryCodePlaceholder="选择"
                            phoneNumberPlaceholder={t("login.mobileRequired")}
                            disabled={loading}
                            onCountryCodeChange={handlePhoneNumberChange}
                            onPhoneNumberBlur={handlePhoneNumberChange}
                        />
                    </Form.Item>

                    <Form.Item
                        name={"mobileVerification"}
                        rules={[{ required: true, message: t("signup.verificationCodeRequired") }]}
                    >
                        <Input
                            placeholder={t("signup.verificationCodeRequired")}
                            size="large"
                            className="!pr-1"
                            suffix={
                                <Form.Item
                                    noStyle
                                    shouldUpdate={(prev, curr) =>
                                        prev.phoneNumber !== curr.phoneNumber ||
                                        prev.phoneCountryCode !== curr.phoneCountryCode
                                    }
                                >
                                    {({ getFieldValue }) => {
                                        const isValidInput = validateMobilePhone(
                                            getFieldValue("phoneNumber"),
                                            getFieldValue("phoneCountryCode")
                                        );

                                        return (
                                            <SendButton
                                                onClick={async () => {
                                                    return handleSendMobileVerification();
                                                }}
                                                countdown={60}
                                                disabled={!isValidInput}
                                            />
                                        );
                                    }}
                                </Form.Item>
                            }
                        />
                    </Form.Item>

                    <Form.Item name="password" rules={PASSWORD_RULES}>
                        <div className="flex flex-col w-full">
                            <div className="w-full flex justify-between items-center mb-2">
                                <span>{t("login.password")}</span>
                            </div>
                            <Password
                                disabled={loading}
                                visibilityToggle
                                placeholder={t("login.passwordRequired")}
                                size="large"
                            />
                        </div>
                    </Form.Item>
                    <Form.Item
                        name="agreeCheckBox"
                        valuePropName="checked"
                        rules={[
                            {
                                validator: (_: any, checked: boolean) =>
                                    checked
                                        ? Promise.resolve()
                                        : Promise.reject(new Error(t("signup.agreementRequired")))
                            }
                        ]}
                    >
                        <Checkbox
                            style={{
                                fontSize: "14px",
                                fontWeight: "400",
                                fontFamily: "Source Sans Pro, sans-serif",
                                margin: 0,
                                padding: 0
                            }}
                        >
                            <span style={{ margin: 0, padding: 0, display: "inline" }}>
                                {t("signup.agreementBody")}
                                <GenericButton
                                    type="link"
                                    onClick={() => {
                                        window.open("https://www.fundpark.com/en/terms-and-conditions/");
                                    }}
                                    className="signup-tnc-button"
                                >
                                    {t("signup.termsOfUse")}
                                </GenericButton>
                                ,&nbsp;
                                <GenericButton
                                    type="link"
                                    onClick={() => {
                                        window.open("https://www.fundpark.com/en/privacy-policy/");
                                    }}
                                    className="signup-tnc-button"
                                >
                                    {t("signup.termsOfPrivacy")}
                                </GenericButton>
                                ,&nbsp;
                                <GenericButton
                                    type="link"
                                    onClick={() => {
                                        window.open(fundparkXDJWaiveInterestPlan);
                                    }}
                                    className="signup-tnc-button"
                                >
                                    {t("signup.termsOfFundparkXDJWaiveInterestPlan")}
                                </GenericButton>
                                {t("signup.and")}
                                <GenericButton
                                    type="link"
                                    onClick={() => {
                                        window.open(fundparkRevenueFinancingSubsidyPlan);
                                    }}
                                    className="signup-tnc-button"
                                >
                                    {t("signup.termsOfFundparkRevenueFinancingSubsidyPlan")}
                                </GenericButton>
                                {t("signup.ofTermsAndConditions")}
                            </span>
                        </Checkbox>
                    </Form.Item>

                    <Button
                        onClick={() => handleSignup()}
                        loading={loading}
                        label={t("login.loginBtn")}
                        className="mt-4 mb-6"
                    />
                </Form>
            </FormContainer>

            <InitAccountModal
                open={initAccountVisible}
                onClose={() => setInitAccountVisible(false)}
                data={initAccountData}
            />
        </>
    );
};

export default Signup;
