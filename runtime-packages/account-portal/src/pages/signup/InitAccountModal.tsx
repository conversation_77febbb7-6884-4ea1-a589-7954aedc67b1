import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ReSendInitEmail, GetCaptchaImg } from "@fundpark/fp-api";
import { ErrorCode } from "@fundpark/fp-api/error-code";
import { Button, Input } from "@/components/common";
import { Modal } from "@/components/common/Modal";
import pointer from "@/assets/widgets/pointer.png";
import appHelper from "@/utils/appHelper";

const InitAccountModal: React.FC<{
    open: boolean;
    onClose: () => void;
    data: {
        loginMethod: string;
        email?: string;
        mobilePhone?: string;
        mobilePhoneAreaCode?: string;
    };
}> = ({ open, onClose, data }) => {
    const { t } = useTranslation();
    const [resendInitEmailLoading, setResendInitEmailLoading] = useState(false);
    const [needCaptcha, setNeedCaptcha] = useState(false);
    const [captchaCode, setCaptchaCode] = useState<string>("");
    const [captchaImage, setCaptchaImage] = useState<string>("");
    const baseRef = useRef({
        codeUuid: ""
    });

    useEffect(() => {
        if (open) {
            setCaptchaCode("");
            if (needCaptcha) {
                refreshCaptcha();
            }
        }
    }, [open]);

    const refreshCaptcha = async () => {
        const loginCodeRes = await GetCaptchaImg();
        if (loginCodeRes.success) {
            const { img, uuid } = loginCodeRes as any;
            const imgBase64 = `data:image/png;base64,${img}`;
            baseRef.current.codeUuid = uuid;
            setCaptchaImage(imgBase64);
        }
    };

    const toResendInitEmail = async () => {
        if (needCaptcha && !captchaCode) {
            appHelper.msgApi.warning(t("login.captcha.required"));
            return;
        }

        setResendInitEmailLoading(true);

        try {
            const { loginMethod, email, mobilePhone, mobilePhoneAreaCode } = data;
            const res = await ReSendInitEmail({
                email: loginMethod === "email" ? email : undefined,
                mobilePhone: loginMethod === "mobile" ? mobilePhone : undefined,
                mobilePhoneAreaCode: loginMethod === "mobile" ? mobilePhoneAreaCode : undefined,
                uuid: needCaptcha ? baseRef.current.codeUuid : undefined,
                code: needCaptcha ? captchaCode : undefined
            });
            if (res.success) {
                appHelper.msgApi.success(t("login.resendEmailSuccess"));
                onClose();
            } else if (res.code === ErrorCode.$80019) {
                setNeedCaptcha(true);
                refreshCaptcha();
            } else if (needCaptcha) {
                refreshCaptcha();
            }
        } finally {
            setResendInitEmailLoading(false);
        }
    };

    return (
        <Modal width={456} open={open} onClose={onClose} title={t("login.accountNotInit")}>
            <div className="cp-init-account-modal-content">{t("login.accountNotInitContent")}</div>
            {needCaptcha && (
                <div className="flex items-center gap-3 px-8 mb-10">
                    <Input
                        className="flex-1"
                        placeholder={t("login.captcha.placeholder")}
                        size="large"
                        value={captchaCode}
                        onChange={e => setCaptchaCode(e.target.value)}
                    />
                    {captchaImage && (
                        <img
                            src={captchaImage}
                            alt="Captcha"
                            width={100}
                            height={40}
                            onClick={refreshCaptcha}
                            className="cursor-pointer flex-shrink-0"
                        />
                    )}
                </div>
            )}
            <Button
                onClick={toResendInitEmail}
                loading={resendInitEmailLoading}
                label={
                    <div className="flex items-center gap-1">
                        {t("login.resendEmail")}
                        <img src={pointer} width={16} height={16} />
                    </div>
                }
                className="w-full"
            ></Button>
        </Modal>
    );
};

export default InitAccountModal;
