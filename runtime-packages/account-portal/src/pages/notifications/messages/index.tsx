import { useTranslation } from "react-i18next";
import NotificationsLayout from "@/layouts/NotificationsLayout";
import MessageList from "./components/MessageList";
import Page from "@/components/common/Page";
import { withMockOnSiteMessageProvider } from "../mock";

const Messages: React.FC = () => {
    const { t } = useTranslation();

    return (
        <Page breadcrumbs={[{ label: t("notifications.breadcrumb.notifications") }]}>
            <NotificationsLayout>
                <MessageList />
            </NotificationsLayout>
        </Page>
    );
};

export default withMockOnSiteMessageProvider(Messages);
