@use "@/assets/styles/variables.module.scss" as *;

.searchButton {
  cursor: pointer;
  color: white;
  padding: 6px 24px;
  border-radius: 0 10px 10px 0;
  margin-right: -13px;
  height: 40px;
  display: flex;
  align-items: center;
  font-size: 14px;
  background: linear-gradient(to bottom, #1E285F, #000000);

  &:hover {
    background: linear-gradient(to bottom, rgba(30, 40, 95, 0.9), rgba(0, 0, 0, 0.9)) !important;
  }

  &:active {
    background: #161032 !important;
  }
} 

:global(.ant-select-selector) {
  height: 40px !important;
  display: flex;
  align-items: center;
  padding: 0 16px !important;
  box-shadow: none !important;
  background: none !important;
  border: 1px solid #DDDFE6 !important;
}

:global(.ant-select-selection-item) {
  transition: all 0.3s;
  font-size: 14px;
  color: #282830 !important;
  display: flex !important;
  align-items: center;
}

:global(.ant-select-selection-placeholder) {
  color: #9E9EA3 !important;
  font-size: 14px;
}

:global(.ant-select-arrow) {
  right: 12px;
  color: #9E9EA3 !important;
}

:global(.anticon) {
  transition: all 0.3s !important;
}

.opened {
  :global {
    .ant-select-selector {
      border-color: #201747 !important;
      box-shadow: 0 0 8px rgba(221, 223, 230, 0.6) !important;
    }
  }
}

:global(.ant-select):hover {
  :global {
    .ant-select-selector {
      border-color: #201747 !important;
    }
  }
}

:global(.ant-select):active {
  :global(.ant-select-selector) {
    background-color: #ECEFF6 !important;
  }
}

:global(.ant-select-dropdown) {
  :global(.ant-select-item-option-selected) {
    background-color: rgba(32, 23, 71, 0.08) !important;
    font-weight: 600 !important;
  }
} 