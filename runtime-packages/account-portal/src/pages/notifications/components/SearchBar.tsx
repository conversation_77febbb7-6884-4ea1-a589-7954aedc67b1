import { Input, Select, Button } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import MarkAllAsReadIconDefault from "@/assets/icons/mark-as-read-default.svg?react";
import MarkAllAsReadIconDisabled from "@/assets/icons/mark-as-read-disabled.svg?react";
import { MessageStatus } from '@fundpark/fp-api/types/noticeMessage.js';
import { useOnSiteMessage } from "../mock/MockOnSiteMessageProvider";
import ArrowDown from '@/assets/icons/arrow-down-grey.svg?react';
import ArrowUp from '@/assets/icons/arrow-up-grey.svg?react';
import classNames from "classnames";
import styles from "./styles.module.scss";

interface SearchBarProps {
    onSearch: (text: string, filter: string) => void;
    unreadMessagesIds?: string[];
    refreshData: () => void;
}

export default function SearchBar({ onSearch, unreadMessagesIds, refreshData }: SearchBarProps) {
    const { t } = useTranslation();
    const [searchText, setSearchText] = useState("");
    const [filter, setFilter] = useState("All");
    const [isHovered, setIsHovered] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const { updateMessageStatus } = useOnSiteMessage();

    const handleSearch = () => {
        onSearch(searchText, filter);
    };

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handleSearch();
        }
    };

    const handleFilterChange = (value: string) => {
        setFilter(value);
        onSearch(searchText, value); // Trigger search when filter changes
    };

    const handleMarkAllAsRead = async (ids: string[]) => {
        await Promise.all(ids.map(id => updateMessageStatus(id, MessageStatus.Read)));
        refreshData();
    }

    const getSuffixIcon = () => {
        if (isOpen) return <ArrowUp />;
        return <ArrowDown />;
    };

    return (
        <div className="flex justify-between w-full p-4 bg-[#F6F7FA] rounded-[16px] px-8 py-6 gap-4">
            <Input
                placeholder={t('notifications.search.placeholder')}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onKeyPress={handleKeyPress}
                style={{
                    height: "40px",
                    width: "574px",
                    fontSize: "14px",
                    borderRadius: "10px",
                    padding: "4px 8px"
                }}
                prefix={<SearchOutlined style={{ color: '#bfbfbf', marginLeft: '4px' }} />}
                suffix={
                    <div
                        onClick={handleSearch}
                        className={styles.searchButton}
                    >
                        {t('notifications.search.button')}
                    </div>
                }
            />
            <div className="flex items-center gap-4">
                <Button
                    onClick={() => handleMarkAllAsRead(unreadMessagesIds || [])}
                    style={{
                        height: "40px",
                        borderRadius: "10px",
                        fontSize: "14px"
                    }}
                    disabled={unreadMessagesIds?.length === 0}
                >
                    {unreadMessagesIds?.length === 0 ? <MarkAllAsReadIconDisabled /> : <MarkAllAsReadIconDefault />}
                    {t('notifications.search.markAllRead')}
                </Button>
                <Select
                    defaultValue="All"
                    style={{
                        width: 148,
                        height: "40px",
                        borderRadius: "10px"
                    }}
                    className={classNames({ [styles.opened]: isOpen })}
                    suffixIcon={<ArrowDown />}
                    onChange={handleFilterChange}
                    options={[
                        { value: 'All', label: t('notifications.search.filter.all') },
                        { value: 'Unread', label: t('notifications.search.filter.unread') },
                        { value: 'Read', label: t('notifications.search.filter.read') },
                    ]}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                    onDropdownVisibleChange={(open) => setIsOpen(open)}
                />
            </div>
        </div>
    )
}