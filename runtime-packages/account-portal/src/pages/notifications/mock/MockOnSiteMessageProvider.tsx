import React, { createContext, useContext, useState, ReactNode, useCallback } from 'react';
import { OnSiteMessage, MessageStatus } from "@fundpark/fp-api/types/onSiteMessage.js";
import { mockMessages, mockAnnouncements } from './mockData';

// Interface for the context value
interface OnSiteMessageContextValue {
  // Messages
  messages: OnSiteMessage[];
  messagesTotal: number;
  messagesLoading: boolean;
  fetchMessages: (params: any) => void;
  
  // Announcements
  announcements: OnSiteMessage[];
  announcementsTotal: number;
  announcementsLoading: boolean;
  fetchAnnouncements: (params: any) => void;
  
  // Common
  updateMessageStatus: (id: string, status: MessageStatus) => Promise<void>;
  revalidateMessages: () => void;
  revalidateAnnouncements: () => void;
}

// Create context
const OnSiteMessageContext = createContext<OnSiteMessageContextValue | undefined>(undefined);

// Provider props
interface MockOnSiteMessageProviderProps {
  children: ReactNode;
}

// Provider component
export const MockOnSiteMessageProvider: React.FC<MockOnSiteMessageProviderProps> = ({ children }) => {
  // State for messages
  const [messages, setMessages] = useState<OnSiteMessage[]>(mockMessages);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const messagesTotal = mockMessages.length;

  // State for announcements
  const [announcements, setAnnouncements] = useState<OnSiteMessage[]>(mockAnnouncements);
  const [announcementsLoading, setAnnouncementsLoading] = useState(false);
  const announcementsTotal = mockAnnouncements.length;

  // Fetch messages with filtering and pagination
  const fetchMessages = useCallback((params: any) => {
    setMessagesLoading(true);
    setTimeout(() => {
      let filteredMessages = [...mockMessages];
      
      // Apply filters
      if (params.messageStatus) {
        filteredMessages = filteredMessages.filter(msg => msg.messageStatus === params.messageStatus);
      }
      
      if (params.content) {
        const searchTerm = params.content.toLowerCase();
        filteredMessages = filteredMessages.filter(msg => 
          msg.title.toLowerCase().includes(searchTerm) || 
          msg.content.toLowerCase().includes(searchTerm)
        );
      }
      
      // Calculate pagination
      const pageSize = Number(params.pageSize) || 10;
      const pageNum = Number(params.pageNum) || 1;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      
      const paginatedMessages = filteredMessages.slice(startIndex, endIndex);
      
      setMessages(paginatedMessages);
      setMessagesLoading(false);
    }, 500); // Simulate network delay
  }, []);

  // Fetch announcements with filtering and pagination
  const fetchAnnouncements = useCallback((params: any) => {
    setAnnouncementsLoading(true);
    setTimeout(() => {
      let filteredAnnouncements = [...mockAnnouncements];
      
      // Apply filters
      if (params.messageStatus) {
        filteredAnnouncements = filteredAnnouncements.filter(ann => ann.messageStatus === params.messageStatus);
      }
      
      if (params.content) {
        const searchTerm = params.content.toLowerCase();
        filteredAnnouncements = filteredAnnouncements.filter(ann => 
          ann.title.toLowerCase().includes(searchTerm) || 
          ann.content.toLowerCase().includes(searchTerm)
        );
      }
      
      // Calculate pagination
      const pageSize = Number(params.pageSize) || 10;
      const pageNum = Number(params.pageNum) || 1;
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      
      const paginatedAnnouncements = filteredAnnouncements.slice(startIndex, endIndex);
      
      setAnnouncements(paginatedAnnouncements);
      setAnnouncementsLoading(false);
    }, 500); // Simulate network delay
  }, []);

  // Update message status
  const updateMessageStatus = useCallback(async (id: string, status: MessageStatus) => {
    // Update messages
    setMessages(prevMessages => 
      prevMessages.map(msg => 
        msg.id === id ? { ...msg, messageStatus: status, readTime: status === 'Read' ? new Date().toISOString() : null } : msg
      )
    );
    
    // Also update in mock data to maintain state between fetches
    const messageIndex = mockMessages.findIndex(msg => msg.id === id);
    if (messageIndex !== -1) {
      mockMessages[messageIndex] = {
        ...mockMessages[messageIndex],
        messageStatus: status,
        readTime: status === 'Read' ? new Date().toISOString() : null
      };
    }
    
    // Update announcements
    setAnnouncements(prevAnnouncements => 
      prevAnnouncements.map(ann => 
        ann.id === id ? { ...ann, messageStatus: status, readTime: status === 'Read' ? new Date().toISOString() : null } : ann
      )
    );
    
    // Also update in mock data
    const announcementIndex = mockAnnouncements.findIndex(ann => ann.id === id);
    if (announcementIndex !== -1) {
      mockAnnouncements[announcementIndex] = {
        ...mockAnnouncements[announcementIndex],
        messageStatus: status,
        readTime: status === 'Read' ? new Date().toISOString() : null
      };
    }
    
    // Simulate API delay
    return new Promise<void>(resolve => setTimeout(resolve, 300));
  }, []);

  // Revalidate functions
  const revalidateMessages = useCallback(() => {
    // In a real implementation, this would refetch data
    // Here we just reset to the mock data
    setMessages(mockMessages);
  }, []);

  const revalidateAnnouncements = useCallback(() => {
    // In a real implementation, this would refetch data
    // Here we just reset to the mock data
    setAnnouncements(mockAnnouncements);
  }, []);

  const contextValue: OnSiteMessageContextValue = {
    messages,
    messagesTotal,
    messagesLoading,
    fetchMessages,
    announcements,
    announcementsTotal,
    announcementsLoading,
    fetchAnnouncements,
    updateMessageStatus,
    revalidateMessages,
    revalidateAnnouncements
  };

  return (
    <OnSiteMessageContext.Provider value={contextValue}>
      {children}
    </OnSiteMessageContext.Provider>
  );
};

// Custom hook to use the provider
export const useOnSiteMessage = () => {
  const context = useContext(OnSiteMessageContext);
  if (context === undefined) {
    throw new Error('useOnSiteMessage must be used within a MockOnSiteMessageProvider');
  }
  return context;
}; 