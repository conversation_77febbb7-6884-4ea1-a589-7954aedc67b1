import { List, Tag, Pagination } from 'antd';
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import styles from '../index.module.scss';
import { useState, useEffect } from 'react';
import { OnSiteMessage, MessageStatus as OnSiteMessageStatus } from "@fundpark/fp-api/types/onSiteMessage.js";
import dayjs from "dayjs";
import SearchBar from '../../components/SearchBar';
import { NotificationSentBy } from '@/types/notifications/enum';
import EmptyImg from '@/assets/images/notifications/empty-notifications.svg?react';
import { MessageStatus } from '@fundpark/fp-api/types/noticeMessage.js';
import ArrowRightIcon from '@/assets/icons/arrow-right.svg?react';
import { useOnSiteMessage } from '../../mock/MockOnSiteMessageProvider';

const AnnouncementList: React.FC = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [pageSize, setPageSize] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [searchText, setSearchText] = useState('');
    const [filter, setFilter] = useState<'All' | OnSiteMessageStatus>('All');

    const { 
        announcements: messages,
        announcementsTotal: total,
        announcementsLoading: loading,
        fetchAnnouncements,
        updateMessageStatus,
        revalidateAnnouncements
    } = useOnSiteMessage();

    useEffect(() => {
        fetchAnnouncements({
            pageSize,
            pageNum: currentPage,
            messageStatus: filter !== 'All' ? filter : undefined,
            content: searchText || undefined
        });
    }, [fetchAnnouncements, currentPage, pageSize, searchText, filter]);

    const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const newPageSize = parseInt(event.target.value);
        setPageSize(newPageSize);
        setCurrentPage(1); // Reset to first page when changing page size
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleSearch = (text: string, filterValue: string) => {
        setSearchText(text);
        setFilter(filterValue as OnSiteMessageStatus);
        setCurrentPage(1); // Reset to first page when searching or filtering
    };

    const handleMessageClick = async (message: OnSiteMessage) => {
        try {
            if (message.messageStatus === MessageStatus.Unread) {
                await updateMessageStatus(message.id, MessageStatus.Read);
            }
            navigate(`/announcements/detail`, {
                state: {
                    messageData: message
                }
            });
        } catch (error) {
            console.error("Failed to update message status:", error);
        }
    };

    const formatSentBy = (sentBy: string | undefined) => {
        switch (sentBy) {
            case NotificationSentBy.System:
                return "System"
            default:
                return sentBy;
        }
    }

    return (
        <>
            <SearchBar 
                onSearch={handleSearch} 
                unreadMessagesIds={messages.filter(item => item.messageStatus === MessageStatus.Unread).map(item => item.id)}
                refreshData={revalidateAnnouncements}
            />
            <div className={styles.notificationList}>
                {!loading && messages.length === 0 && (
                    <div className={styles.emptyList}>
                        <div className={styles.emptyListImg}>
                            <EmptyImg />
                        </div>
                        <div className={styles.emptyListText}>{t("notifications.empty")}</div>
                    </div>
                )}
                {(loading || messages.length !== 0) && (
                    <>
                        <List
                            loading={loading}
                            dataSource={messages}
                            renderItem={(item) => (
                                <List.Item
                                    key={item.id}
                                    className={styles.notificationItem}
                                    onClick={() => handleMessageClick(item)}
                                >
                                    <div className={styles.notificationContent}>
                                        <div className={styles.header}>
                                            {item.messageStatus === 'Read' ?
                                                <span className={styles.readDot} /> :
                                                <span className={styles.unreadDot} />
                                            }
                                            <span className={styles.title}>{item.title}</span>
                                        </div>
                                        <div className={styles.footer}>
                                            <Tag className={styles.tag}>{formatSentBy(item.sentBy)}</Tag>
                                            <span className={styles.timestamp}>
                                                {dayjs(item.sentTime).format("YYYY/MM/DD HH:mm:ss")}
                                            </span>
                                        </div>
                                        <div className={styles.detailContainer}>
                                            <span className={styles.detailText}>
                                                {t("notifications.announcementList.detail")}
                                            </span>
                                            <ArrowRightIcon />
                                        </div>
                                    </div>
                                </List.Item>
                            )}
                        />

                        <div className={styles.pagination}>
                            <span className={styles.total}>
                                {t('notifications.announcementList.totalItems', { count: messages.length, total })}
                            </span>
                            <Pagination
                                total={total}
                                pageSize={pageSize}
                                current={currentPage}
                                onChange={handlePageChange}
                                showSizeChanger={false}
                            />
                            <div className={styles.showRows}>
                                {t('notifications.announcementList.showText')}
                                <select value={pageSize} className={styles.rowNumber} onChange={handlePageSizeChange}>
                                    <option value="10">10 {t('notifications.announcementList.rowsText')}</option>
                                    <option value="20">20 {t('notifications.announcementList.rowsText')}</option>
                                    <option value="50">50 {t('notifications.announcementList.rowsText')}</option>
                                    <option value="100">100 {t('notifications.announcementList.rowsText')}</option>
                                </select>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </>
    );
};

export default AnnouncementList; 