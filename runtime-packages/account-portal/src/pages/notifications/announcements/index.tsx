import { useTranslation } from "react-i18next";
import NotificationsLayout from "@/layouts/NotificationsLayout";
import AnnouncementList from "./components/AnnouncementList";
import Page from "@/components/common/Page";
import { withMockOnSiteMessageProvider } from "../mock";

const Announcements: React.FC = () => {
    const { t } = useTranslation();

    return (
        <Page breadcrumbs={[{ label: t("notifications.breadcrumb.notifications") }]}>
            <NotificationsLayout>
                <AnnouncementList />
            </NotificationsLayout>
        </Page>
    );
};

export default withMockOnSiteMessageProvider(Announcements);
