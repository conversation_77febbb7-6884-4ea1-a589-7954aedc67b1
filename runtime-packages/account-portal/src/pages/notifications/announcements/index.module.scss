@use "@/assets/styles/variables.module.scss" as *;

.notificationList {
    // margin: 0 auto;
    padding: 0 16px;
    margin-top: 24px;

    .emptyList {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        width: 100%;
        background: #fff;
        border-radius: 8px;
        padding: 24px;
    }

    .emptyListImg {
        margin-bottom: 8px;
        svg {
            width: 160px;
            height: 160px;
        }
    }

    .emptyListText {
        color: #282830;
        font-size: 16px;
        line-height: 20.11px;
        font-weight: 600;
    }

    .notificationItem {
        &:hover {
            .notificationContent {
                .detailContainer {
                    .detailText {
                        transform: translateX(-4px);
                    }
                    svg {
                        transform: translateX(4px);
                    }
                }
            }
        }

        cursor: pointer;
        padding-top: 20px;
        padding-bottom: 20px;
        background: #fff;
        border-block-end: 1px solid #E5E7EB !important;
        transition: all 0.3s;

        &:hover {
            background: linear-gradient(90deg, 
                #FFFFFF 0%, 
                #F6F7FA 50%,  
                #FFFFFF 100%
            );
            border-block-end: 2px solid #201747 !important;
        }

        :global(.ant-list-item) {
            border-block-end: none !important;
        }
    }

    .notificationContent {
        width: 100%;
        position: relative;
        padding-right: 120px;

        .detailContainer {
            margin-right: 8px;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            width: 100px;

            .detailText {
                color: #64CCC9;
                font-size: 14px;
                font-weight: 600;
                transition: transform 0.3s ease;
                white-space: nowrap;
            }

            svg {
                transition: transform 0.3s ease;
            }
        }
    }

    .header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        width: 100%;

        .title {
            font-size: 20px;
            font-weight: 700;
            color: #282830;
            line-height: 28px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
    }

    .unreadDot {
        width: 8px;
        height: 8px;
        background-color: #DD4C4C;
        border-radius: 50%;
        margin-right: 8px;
        flex-shrink: 0;
    }
    
    .readDot{
        width: 8px;
        height: 8px;
        background-color: #DDDFE6;
        border-radius: 50%;
        margin-right: 8px;
        flex-shrink: 0;
    }

    .description {
        color: #6E6E75;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 12px;
    }

    .footer {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .tag {
        background: #E9EFFD;
        border: none;
        color: #2463EB;
        border-radius: 4px;
        width: 67px;
        height: 24px;
        text-align: center;
        justify-content: center;
        display: flex;
        align-items: center;
        border-radius: 8px;
    }

    .timestamp {
        color: #9E9EA3;
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
    }

    .pagination {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 24px;
        padding: 16px 0;
        position: relative;

        .total {
            color: #666666;
            font-size: 14px;
        }

        .showRows {
            color: #666666;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;

            .rowNumber {
                color: #201747;
                font-weight: 700;
            }

            select {
                appearance: none;
                background: transparent;
                border: none;
                color: #666666;
                font-size: 14px;
                padding-right: 16px;
                cursor: pointer;
                background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.5 4.5L6 8L9.5 4.5' stroke='%23666666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
                background-repeat: no-repeat;
                background-position: right center;

                &:focus {
                    outline: none;
                }

                option {
                    font-size: 16px;
                    font-weight: 700;
                    color: #201747;
                }
            }
        }

        :global(.ant-pagination) {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            background: #F6F7FA;
            border-radius: 30px;
            padding: 4px;

            :global(.ant-pagination-item) {
                border-radius: 10px;
                margin: 0 4px;
                min-width: 32px;
                width: 32px;
                height: 32px;
                line-height: 32px;
                background: #F6F7FA !important;
                border: none;
                
                &:hover {
                    border-color: #201747;
                    background: #F6F7FA !important;
                    a {
                        color: #201747;
                    }
                }

                a {
                    color: #666666;
                }
            }

            :global(.ant-pagination-item-active) {
                background: #201747 !important;
                border-color: #201747 !important;
                border-radius: 10px;
                
                a {
                    color: white !important;
                }

                &:hover {
                    background: #201747 !important;
                    a {
                        color: white !important;
                    }
                }
            }

            :global(.ant-pagination-prev),
            :global(.ant-pagination-next) {
                margin: 0 4px;
                button {
                    border-radius: 10px;
                    min-width: 32px;
                    width: 32px;
                    height: 32px;
                    line-height: 32px;
                    background: #F6F7FA !important;
                    border: none;
                    color: #666666;
                    &:hover {
                        border-color: #201747;
                        color: #201747;
                        background: #F6F7FA !important;
                    }
                }
            }
        }
    }
}

.listContainer {
    background: #fff;
    padding: 24px;
    min-height: 100%;
}

.listItem {
    cursor: pointer;
    transition: background-color 0.3s;
    padding: 16px;
    border-radius: 4px;

    &:hover {
        background-color: #f5f5f5;
    }
}

.messageContent {
    color: #666;
    margin: 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.messageFooter {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 8px;
}

.date {
    color: #999;
    font-size: 12px;
}

.pagination {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
}
