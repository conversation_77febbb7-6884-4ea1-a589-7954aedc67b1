import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { Tag } from "antd";
import styles from "./index.module.scss";
import { useState } from "react";
import { OnSiteMessage } from "@fundpark/fp-api/types/onSiteMessage.js";
import DOMPurify from "dompurify";
import ActionButton from "@/components/ActionButton";
import { useEffect } from "react";
import { withMockOnSiteMessageProvider } from "../mock";

import BackButtonDefault from "@/assets/icons/backButton/default.svg?react";
import BackButtonHover from "@/assets/icons/backButton/hover.svg?react";
import BackButtonClick from "@/assets/icons/backButton/click.svg?react";
import { NotificationSentBy } from "@/types/notifications/enum";

const NotificationDetail: React.FC = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();
    const [loading, setLoading] = useState(false);
    const message = location.state?.messageData as OnSiteMessage;

    useEffect(() => {
        if (!message) {
            navigate('/messages', { replace: true });
        }
    }, [message, navigate]);

    const handleBack = () => {
        navigate(-1);
    };

    const handleConfirm = async () => {
        if (!message?.url) return;

        setLoading(true);
        try {
            if (message.url.includes('busType=join')) {
                navigate(message.url, {
                    state: {
                        companyId: message.extId
                    }
                });
            } else {
                navigate(message.url);
            }
        } catch (error) {
            console.error("Failed to handle confirmation:", error);
        } finally {
            setLoading(false);
        }
    };

    const formatSentBy = (sentBy: string | undefined) => {
        switch (sentBy) {
            case NotificationSentBy.System:
                return t('notifications.detail.system');
            default:
                return sentBy;
        }
    }

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <div className={styles.backButton} onClick={handleBack}>
                    <div className={styles.iconWrapper}>
                        <BackButtonDefault className={`${styles.backIcon} ${styles.defaultIcon}`} />
                        <BackButtonHover className={`${styles.backIcon} ${styles.hoverIcon}`} />
                        <BackButtonClick className={`${styles.backIcon} ${styles.clickIcon}`} />
                    </div>
                    <span className={styles.backText}>{t('notifications.detail.back')}</span>
                </div>
            </div>

            <div className={styles.content}>
                <h1 className={styles.title}>{message?.title || ''}</h1>
                <div className={styles.metadata}>
                    <Tag className={styles.tag}>{formatSentBy(message?.sentBy) || ''}</Tag>
                    <span className={styles.date}>
                        {message?.sentTime ? dayjs(message.sentTime).format("YYYY/MM/DD HH:mm:ss") : ''}
                    </span>
                </div>
                <div
                    className={styles.messageContent}
                    dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(message?.content || '')
                    }}
                />

                {message?.url && (
                    <div className={styles.actions}>
                        <div style={{ minWidth: "100px" }}>
                            <ActionButton onClick={handleBack} label={t('notifications.detail.cancel')} type="default" />
                        </div>
                        <div style={{ minWidth: "100px" }}>
                            <ActionButton
                                onClick={handleConfirm}
                                label={t('notifications.detail.confirm')}
                                type="primary"
                                loading={loading}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default withMockOnSiteMessageProvider(NotificationDetail); 