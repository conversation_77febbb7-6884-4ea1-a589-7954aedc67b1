.container {
    padding: 24px;
    background: #fff;
    min-height: 100%;
}

.header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
}

.backButton {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50px;
    transition: all 0.3s;

    &:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }

    &:active {
        background-color: rgba(0, 0, 0, 0.08);
    }
}

.iconWrapper {
    position: relative;
    width: 32px;
    height: 32px;
}

.backIcon {
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    height: 32px;
}

.defaultIcon {
    display: block;
}

.hoverIcon,
.clickIcon {
    display: none;
}

.backButton:hover {
    .defaultIcon {
        display: none;
    }
    .hoverIcon {
        display: block;
    }
    .clickIcon {
        display: none;
    }
}

.backButton:active {
    .defaultIcon,
    .hoverIcon {
        display: none;
    }
    .clickIcon {
        display: block;
    }
}

.backText {
    font-size: 18px;
    font-weight: 400;
}

.content {
    margin: 0 auto;
}

.title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
}

.metadata {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    justify-content: center;
}

.tag {
    background: #E9EFFD;
    border: none;
    color: #2463EB;
    border-radius: 4px;
    width: 67px;
    height: 24px;
    text-align: center;
    justify-content: center;
    display: flex;
    align-items: center;
    border-radius: 8px;
}

.date {
    color: #666;
    font-size: 14px;
}

.messageContent {
    color: #333;
    line-height: 1.6;
    font-size: 14px;
    padding-top: 24px;
}

.actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 24px;
    padding-top: 24px;
    // border-top: 1px solid #E5E6EB;
}