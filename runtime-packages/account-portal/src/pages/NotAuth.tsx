import { useTranslation } from "react-i18next";
import { Result, But<PERSON> } from "antd";
import { useNavigate } from "react-router-dom";

const NotAuth: React.FC = () => {
	const { t } = useTranslation();
    const navigate = useNavigate();

    return (
        <Result
            status="403"
            title="401"
            subTitle="Sorry, you are not authorized to access this system."
            extra={
                <Button type="primary" onClick={() => navigate(-1)}>
                    Back
                </Button>
            }
        />
    );
};

export default NotAuth;
