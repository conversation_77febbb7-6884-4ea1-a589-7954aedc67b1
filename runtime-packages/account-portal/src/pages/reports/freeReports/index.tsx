import React, {useEffect, useState} from "react";
import { Button, <PERSON>pography, <PERSON> } from 'antd';
import MainLayout from "@/layouts/MainLayout";
import SelectionWithMaxCount from "@/components/SelectionWithMaxCount"
import PrimaryButton from "@/components/shared/Button/index.tsx";
import ReportIconTextButton from "@/components/Button/reportButton.tsx";
import {getCategoryReportResult, getCategoryReports} from "@fundpark/fp-api";
import {useCommonStore} from "@/store/common";
import {useNavigate} from "react-router-dom";

const freeReport = () => {
    const [selectedValues, setSelectedValues] = useState([]);
    const [generating, setGenerating] = useState(false);
    const [reportsGenerated, setReportsGenerated] = useState(null);
    const user = useCommonStore((s) => s.userInfo);
    const baseURL = import.meta.env.VITE_ACC_BASE_URL as string;
    const sampleReportURL = `${baseURL}/category-report-view/http%3A%2F%2Ffundpark-proboost.microdata-inc.com%2Freport%3Fid%3D10114`;
    const navigate = useNavigate();
    
    const fetchData = async () => {
        const resp = await getCategoryReports();
        if (resp.data.data.length > 0 && resp.data.status_result === true) {
            console.log('Generated:::', resp.data.data);
            setReportsGenerated(resp.data.data);
            setGenerating(false);
        } else if (resp.data.data.length > 0 && resp.data.status_result === false) {
            const selectedValues = resp.data.data.map(item => item.keyword);
            setSelectedValues(selectedValues);
            setGenerating(true);
        }
    };

    React.useEffect(() => {
        if (user) {
            fetchData();
            if (!reportsGenerated) {
                const intervalId = setInterval(fetchData, 5000);
                return () => clearInterval(intervalId);
        }} else {
            navigate('/credit/hook');
        }
      }, []);

    const handleSubmit = async () => {
        const resp = await getCategoryReportResult({keywords: selectedValues});
        if (resp.code === 0) {
            setGenerating(true);
        }
    };

    return (
        <MainLayout>
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                }}
            >
                <div
                    style={{
                        padding: '50px',
                        border: '1px solid rgba(20, 20, 20, 0.2)',
                        borderRadius: '12px',
                        margin: '5% 10%'
                    }}
                >
                    <div>
                    <Typography.Title level={3} >查看品类报告</Typography.Title>
                    {!reportsGenerated && 
                    <SelectionWithMaxCount 
                        onChange={setSelectedValues} 
                        style={{width: "100%", height:"40px", marginTop: '20px'}} 
                        placeholder={"请输入关键词，最多可查询3个关键词报告"} 
                        isEditDisabled={generating}
                        value={selectedValues}
                    />}
                    {(!reportsGenerated && !generating) && <div style={{marginTop: '20px', marginLeft: '10px'}}>
                        <Typography.Text
                            style={{ textDecoration: 'underline', cursor: 'pointer', fontSize: '14px', color:'#757575'}}
                            onClick={() => window.open(sampleReportURL, "_blank")}
                        >
                            查看报告样本
                        </Typography.Text>
                    </div>}                    
                    {(!reportsGenerated && !generating) && <div style={{ margin: '20px 0', position: 'relative', display: 'inline-block' }}>
                        <PrimaryButton
                            label="提交"
                            onClick={handleSubmit}
                            style={{
                                height: "54px",
                                borderRadius: "30px",
                                width: "172px",
                                padding: "8px 10px",
                                fontSize: '18px',
                                background: 'linear-gradient(179.92deg, #453894 0.07%, #000000 159.61%)'
                            }}
                        />
                        <div
                            style={{
                                position: 'absolute',
                                top: '-10px',
                                right: '-70px',
                                width: '99px',
                                height: '29px',
                                background: 'linear-gradient(179.92deg, #FDB305 0.07%, #DADADA 159.61%)',
                                color: 'white',
                                textAlign: 'center',
                                fontSize: '16px',
                                lineHeight: '29px', 
                                fontWeight: '400'
                            }}
                        >
                            限时免费
                        </div>
                    </div>}
                    {generating && <div style={{ margin: '40px 0'}}>
                        <Button
                            style={{
                                height: "54px",
                                borderRadius: "30px",
                                width: "172px",
                                padding: "8px 10px",
                                fontSize: '18px',
                                background: 'linear-gradient(179.92deg, rgba(69, 56, 148, 0.4) 0.07%, rgba(0, 0, 0, 0.4) 159.61%)',
                                color: "white"
                            }}
                            disabled={true}
                        >
                            报告生成中...
                        </Button>
                        <span style={{marginLeft: '20px'}}> 预计等待5分钟，生成后页面将会自动刷新，并以邮件通知 </span>
                    </div>}
                    {reportsGenerated && 
                        <div style={{ display: 'flex', gap: '40px', margin: '40px 0'}}>
                            {reportsGenerated.map((item, index) => (
                            <ReportIconTextButton 
                                index={index+1}
                                text={item.keyword}
                                onClick={() => window.open(item.report_link, "_blank")} 
                            />
                            ))}
                        </div>
                    }
                    {!generating && reportsGenerated && <div style={{ margin: '40px 0'}}>
                        <Button
                            style={{
                                height: "54px",
                                borderRadius: "30px",
                                width: "172px",
                                padding: "8px 10px",
                                fontSize: '18px',
                                background: 'linear-gradient(179.92deg, rgba(69, 56, 148, 0.4) 0.07%, rgba(0, 0, 0, 0.4) 159.61%)',
                                color: "white"
                            }}
                            disabled={true}
                        >
                            已生成
                        </Button>

                    </div>}
                    <div style={{marginTop: '20px', marginLeft: '10px'}}>
                    <Typography.Text type="secondary" style={{ fontSize: '13px', width: '100%'}}>
                    *The industry market report contains the data provided by third party service providers and is provided by FundPark solely for your reference in general. Recipients of the industry market report and/or data should conduct their own independent evaluation and assessment of the content, information, and opinions provided and should not make reliance on any content of the industry market report/data/ FundPark makes no warranties or representations, express or implied, as to the accuracy, reliability, completeness, or fitness for any particular purpose, and does not intend to carry on money lending business outside Hong Kong.
                    </Typography.Text>
                    </div>
                    <div style={{marginTop: '20px', marginLeft: '10px'}}>
                    <Typography.Text type="secondary" style={{ fontSize: '13px', width: '100%'}}>
                    本行业市场报告所载之数据由第三方服务商提供，且FundPark 所提供之报告仅供接收人作个人参考之用。行业市场报告和/或数据的接收人应对所提供的内容、信息和意见进行独立评估，并且不应依赖行业市场报告中的任何内容行事。FundPark不对准确性、可靠性、完整性或特定目的的适用性作出明示或暗示的保证或陈述，并无意在香港以外的国家或地区经营放债人业务。
                    </Typography.Text>
                    </div>
                    </div>
                </div>
            </div>
            </MainLayout>
    );
};

export default freeReport;
