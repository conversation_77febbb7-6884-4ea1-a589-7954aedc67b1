import Page from "@/components/shared/Page";
import AmountWithTitle from "@/components/shared/AmountWithTitle";
import IconLabelList from "@/components/shared/IconLabelList";
import { LandingPageIconLabelListSDG } from "@/constants/iconLabelList.ts";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import OverlayContainer from "@/components/Container/overlayContainer.tsx";
import RedPocket from "@/components/RedPocket";
// import ClockIcon from "@/assets-new/icons/common/clock.svg";
import BannerCarousel from "@/components/shared/BannerCarousel";
import AuthPlatformBanner from "@/components/shared/AuthPlatformBanner";
import {useGeneralStore} from "@/store/general";
import RedLanding from "@/pages/landing/red/index.tsx";
import BlueLanding from "@/pages/landing/blue/index.tsx";
import RedNewLanding from "@/pages/landing/redNew/index.tsx";
import {useSearchParams} from "react-router-dom";
import { useEffect } from "react";
import { trackEvent } from "@/utils/matomoTracking";
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import { TEMPLATE_IDS } from "@/constants/templateIds";

const SDG = () => {

    const [searchParams] = useSearchParams();
    const tpid = searchParams.get("tpid");

    useEffect(() => {
        trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.VIEW, TRACKING_EVENTS.VIEW_LANDING_PAGE);
    }, []);

    if (tpid === TEMPLATE_IDS.TEMPLATE_2506_RED) {
        return <RedLanding/>;
    }
    if (tpid === TEMPLATE_IDS.TEMPLATE_2506_BLUE) {
        return <BlueLanding/>;
    }
    if (tpid === TEMPLATE_IDS.TEMPLATE_2507_RED) {
        return <RedNewLanding/>;
    }

    const setAuthModalOpen = useGeneralStore(state => state.setAuthModalOpen);
    const setLoginMethod = useGeneralStore(state => state.setLoginMethod);


    return (
        <Page>
            <BackdropContainer isShowOverlay>
                <AmountWithTitle title="最低可得(美元)" amount={2000} />
                <IconLabelList items={LandingPageIconLabelListSDG} />
            </BackdropContainer>

            <OverlayContainer>
                <RedPocket
                    type="default"
                    redPocketTitle="7天免息金，有店就能用"
                    actionTitle="立即0元用"
                    // isShowHelpingText
                    // helpingText={
                    //     <div>
                    //         <img src={ClockIcon} alt="Clock" width={20} height={20}/> 限时免费领取品类报告
                    //     </div>
                    // }
                    actionClick={() => {
                        setLoginMethod("signup");
                        setAuthModalOpen(true);
                    }}
                />
            </OverlayContainer>
            <BannerCarousel />
            <AuthPlatformBanner />
        </Page>
    );
};

export default SDG;
