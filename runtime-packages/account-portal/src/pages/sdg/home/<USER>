import { useEffect, useState } from "react";
import Page from "@/components/shared/Page";
import IconLabelList from "@/components/shared/IconLabelList";
import { LandingPageIconLabelListCAA, LandingPageIconLabelListSDG } from "@/constants/iconLabelList.ts";
import BackdropContainer from "@/components/Container/backdropContainer.tsx";
import BannerCarousel from "@/components/shared/BannerCarousel";
import AuthPlatformBanner from "@/components/shared/AuthPlatformBanner";
import { useCommonStore } from "@/store/common.ts";
import PlatformMainModal from "@/components/Platforms";
import { ModalStep } from "@/types/platforms/modalSteps.ts";
import { useLocation, useNavigate } from "react-router-dom";
import LimitTitleContainer from "@/containers/LimitTitleContainer";
import LimitActionsContainer from "@/containers/LimitActionsContainer";
import RedPocketContainer from "@/containers/RedPocketContainer.tsx";
import { useRedPocketStore } from "@/store/redpocket.ts";
import { useLimitStore } from "@/store/limits.ts";
import { trackEvent } from "@/utils/matomoTracking";
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import { AuthGuideModal } from "@/components/AuthGuideModal";
import { usePositionTracking } from "@/hooks/usePositionTracking";

const SDG = () => {
    const navigate = useNavigate();

    const { currentPath, retrieveStoredPosition } = usePositionTracking();

    useEffect(() => {
        retrieveStoredPosition();
    }, []);

    useEffect(() => {
        if (currentPath === "/credit/hook/register/signing/loading") {
            navigate("/credit/hook/register/signing/loading");
        }
    }, [currentPath]);

    const fetchAuthedList = useCommonStore(s => s.fetchAuthedList);
    const authedPlatforms = useCommonStore(s => s.authedPlatforms);
    const isAnyShopAuthed = useCommonStore(s => s.isAnyShopAuthed);
    const refreshUserInfo = useCommonStore(s => s.refreshUserInfo);
    const { fetchRedPocketList } = useRedPocketStore();

    const user = useCommonStore(s => s.userInfo);
    const [modalStep, setModalStep] = useState<ModalStep>("none");
    const { search, pathname } = useLocation();

    const { fetchUserLimits, fetchUserCreditApplication, creditApplication } = useLimitStore();
    const [openAuthModal, setOpenAuthModal] = useState(false);
    const [controlCheckResultFailed,setControlCheckResultFailed] = useState(true)
    const [thirdPartyCheckResultFailed,setThirdPartyCheckResultFailed] = useState(true)
    
    const authGuideModalOnClose = () => {
        setOpenAuthModal(false);
    };


    useEffect(() => {
        trackEvent(TRACKING_CATEGORIES.UI, TRACKING_ACTIONS.VIEW, TRACKING_EVENTS.VIEW_LANDING_PAGE);
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            await fetchUserLimits(true);
            await fetchUserCreditApplication(true);
        };
        fetchData().catch(err => {
            console.error("Error fetching data:", err);
        });
    }, []);



    useEffect(() => {

        const hasSeenModal = localStorage.getItem('hasSeenAuthModal');
        

        if (creditApplication?.psp_check_status === 0 && !hasSeenModal) {

            setOpenAuthModal(true);
            setControlCheckResultFailed(!creditApplication?.control_check_result)
            setThirdPartyCheckResultFailed(!creditApplication?.thirdparty_check_result)
            localStorage.setItem('hasSeenAuthModal', 'true');
        }
    }, [creditApplication]);

    useEffect(() => {
        const handleAuthCallback = async () => {
            try {
                await fetchAuthedList();
            } catch (err) {
                console.error("刷新授权列表出错:", err);
            }
        };
        const params = new URLSearchParams(search);
        const token = params.get("id_token");
        if (!token) {
            void handleAuthCallback();
        }
    }, [search, pathname, navigate, fetchAuthedList, user]);

    useEffect(() => {
        if (!authedPlatforms) {
            return;
        }
        if (!isAnyShopAuthed && modalStep === "none") {
            setModalStep("platformSelection");
        }
    }, [isAnyShopAuthed, authedPlatforms]);

    useEffect(() => {
        refreshUserInfo();
        (async () => {
            await fetchRedPocketList();
        })();
    }, [refreshUserInfo, fetchRedPocketList]);

    return (
        <Page>
            <BackdropContainer isShowOverlay>
                <LimitTitleContainer />
                <IconLabelList
                    items={
                        creditApplication?.type === "limit_increase" && creditApplication?.approval_limit > 2000
                            ? LandingPageIconLabelListCAA
                            : LandingPageIconLabelListSDG
                    }
                />
                <LimitActionsContainer />
            </BackdropContainer>

            {creditApplication?.status != "failed" && (
                <RedPocketContainer
                    clickEvent={() => {
                        setModalStep("platformSelection");
                    }}
                />
            )}

            <PlatformMainModal modalStep={modalStep} setModalStep={setModalStep} authedPlatforms={authedPlatforms} />
            <AuthGuideModal open={openAuthModal} onClose={authGuideModalOnClose} canClose={true} thirdPartyCheckResultFailed={thirdPartyCheckResultFailed} controlCheckResultFailed={controlCheckResultFailed} />
            <BannerCarousel />
            <AuthPlatformBanner />
        </Page>
    );
};

export default SDG;
