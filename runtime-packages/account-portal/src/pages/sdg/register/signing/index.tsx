import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { message } from 'antd';
import Page from "@/components/shared/Page";
import SigningForm from "@/components/DrawdownForm/Signing";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import DrawdownFormContainer from '@/components/Container/DrawdownFormContainer/DrawdownFormContainer';
import { getShareholderStructureResult } from '@fundpark/fp-api';
import { SigningPageState, ShareholderStructureCode } from '@/pages/register/signing/types';
import { mapShareholderApiToUi } from '@/pages/register/signing/mapper';
import { VerificationStatusCodes, ErrorMessages, Routes } from '@/constants/signing';
import ShopAuthErrorModal from '@/components/DrawdownForm/Signing/ShopAuthErrorModal';
import MobileVerificationErrorModal from '@/components/DrawdownForm/Signing/MobileVerificationErrorModal';

const SDGSigningPage: React.FC = () => {
  const navigate = useNavigate();
  const [state, setState] = useState<SigningPageState>({
    modalVisible: false,
    modalMessage: '',
    verificationPassed: false
  });
  const [showShopAuthErrorModal, setShowShopAuthErrorModal] = useState(false);
  const [showMobileVerificationErrorModal, setShowMobileVerificationErrorModal] = useState(false);

  const {search} = useLocation();
  const params = new URLSearchParams(search);
  const token = params.get("id_token");
  useEffect(() => {
    if (token) {
      const timer = setTimeout(() => {
        verifyShareholderStructure();
      }, 3000);
      
      return () => clearTimeout(timer);
    } else {
      verifyShareholderStructure();
    }
  }, [token]);

  const verifyShareholderStructure = async (): Promise<void> => {
    try {
      const apiResponse = await getShareholderStructureResult();
      const result = mapShareholderApiToUi(apiResponse);
      handleVerificationResponse(result.code);
    } catch (error) {
      console.error('Failed to check shareholder structure result:', error);
      message.error(ErrorMessages.VERIFICATION_FAILED);
    }
  };

  const handleVerificationResponse = (code: ShareholderStructureCode): void => {
    switch (code) {
      case VerificationStatusCodes.SUCCESS:
        // Success - Access granted to SigningForm
        setState(prev => ({ ...prev, verificationPassed: true }));
        break;
      case VerificationStatusCodes.WAITING:
        // Waiting - Redirect to loading page
        navigate(Routes.LOADING_PAGE);
        break;
      case VerificationStatusCodes.MANUAL_REVIEW:
      case VerificationStatusCodes.BUSINESS_INFO_FAILED:
        showModal('MANUAL_REVIEW_REQUIRED');
        break;
      case VerificationStatusCodes.MOBILE_ANT_FAILED:
        setShowMobileVerificationErrorModal(true);
        break;
      case VerificationStatusCodes.NOT_ALLOWED_ACCESS:
        setShowShopAuthErrorModal(true)
        break;
      case VerificationStatusCodes.PEOPLE_NOT_FOUND:
        showModal('PEOPLE_NOT_FOUND');
        break;
      default:
        showModal('UNKNOWN_ERROR');
        break;
    }
  };

  const showModal = (type: keyof typeof ErrorMessages): void => {
    setState(prev => ({
      ...prev,
      modalVisible: true,
      modalMessage: ErrorMessages[type]
    }));
  };

  const handleModalClose = (): void => {
    setState(prev => ({ ...prev, modalVisible: false }));
    navigate(Routes.HOME_PAGE); // Navigate back to main page
  };

  return (
    <Page>
      <DrawdownFormContainer
        title="最低可得(美元)"
        limit={2000}
      >
        {state.verificationPassed && <SigningForm />}
      </DrawdownFormContainer>

      <OurWechatModal
        open={state.modalVisible}
        onClose={handleModalClose}
        message={state.modalMessage}
        width={556}
        hasAlertIcon
        textAlign='center'
      />

      <ShopAuthErrorModal
        open={showShopAuthErrorModal}
        onClose={() => setShowShopAuthErrorModal(false)}
        onComplete={() => {
          setShowShopAuthErrorModal(false);
          navigate(Routes.LOADING_PAGE);
        }}
      />

      <MobileVerificationErrorModal
        open={showMobileVerificationErrorModal}
        onClose={() => setShowMobileVerificationErrorModal(false)}
        onComplete={() => {
          setShowMobileVerificationErrorModal(false);
          navigate(Routes.LOADING_PAGE);
        }}
      />

    </Page>
  );
};

export default SDGSigningPage; 