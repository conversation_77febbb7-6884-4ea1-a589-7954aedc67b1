import { useCallback, useState } from 'react';
import { useCommonStore } from '@/store/common';
import { useNavigate } from 'react-router-dom';
import type { PositionStatus } from '@/constants/position-status';
import { storePath as storePathApi, getPath } from '@fundpark/fp-api';
import { create } from 'zustand';

// Define PathData interface if it's not available
interface PathData {
  path: string;
  path_name: string;
}

interface PositionState {
  path: string | null;
  status: string | null;
  setPosition: (path: string, status: string) => void;
}

// Create a Zustand store for position tracking
export const usePositionStore = create<PositionState>((set) => ({
  path: null,
  status: null,
  setPosition: (path, status) => set({ path, status }),
}));

/**
 * Custom hook for position tracking
 * 
 * This hook provides methods to:
 * - Store the current position (path and status) in the API and local store
 * - Navigate to the last stored position
 * - Get the current position info from the API or local store
 * 
 * @returns Object with position tracking methods
 * 
 * @example
 * // Basic usage in a component
 * import { usePositionTracking } from '@/hooks/usePositionTracking';
 * 
 * const MyComponent = () => {
 *   const { trackPosition, navigateToStoredPosition } = usePositionTracking();
 *   
 *   // Track the current position with a status when component mounts
 *   useEffect(() => {
 *     trackPosition({ status: 'Dashboard View' });
 *   }, []);
 *   
 *   // Navigate to the stored position on button click
 *   const handleBackClick = async () => {
 *     await navigateToStoredPosition('/default-fallback');
 *   };
 *   
 *   return (
 *     <Button onClick={handleBackClick}>Go Back</Button>
 *   );
 * };
 */
export const usePositionTracking = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  
  // Get isLogin from store to check authentication
  const isLogin = useCommonStore(state => state.isLogin);
  
  // Get position from store
  const { path: positionPath, status: positionStatus, setPosition } = usePositionStore();

  /**
   * Retrieve the stored position from the API
   * 
   * @returns Promise resolving to object with path and status, or null if not found
   */
  const retrieveStoredPosition = useCallback(async () => {
    try {
      setLoading(true);
      const positionRes = await getPath();
      if (positionRes.message === "success" && positionRes.data) {
        // Use type assertion to access the data
        const positionData = positionRes.data as unknown as PathData;
        const path = positionData.path;
        const status = positionData.path_name; // Map API's path_name to status
        
        // Update the zustand store with API data
        setPosition(path, status);
        
        return { path, status };
      }
      return null;
    } catch (error) {
      console.error("Failed to retrieve position:", error);
      return null;
    } finally {
      setLoading(false);
    }
  }, [isLogin, setPosition]);
  
  /**
   * Track the current position in the application flow
   * If path or status is not provided, it will use the stored values from the zustand store,
   * or fall back to API and then default values
   * 
   * @param options Optional parameters for position tracking
   * @param options.path Custom path to track (defaults to stored path or current location)
   * @param options.status Status or context description for the position (defaults to stored status or empty string)
   */
  const trackPosition = useCallback(async ({ path, status }: { path?: string; status?: PositionStatus } = {}) => {
    try {
      // Only retrieve stored position if either path or status is missing
      if (path !== undefined && status !== undefined) {
        // If both are provided, update zustand store and API
        
        if (isLogin) {
          await storePathApi({
            path,
            path_name: status
          });
          setPosition(path, status);
          console.log('Position stored:', path, 'Status:', status);
        }
        return;
      }
      
      // Check if we have values in the zustand store first
      let currentPath = path;
      let currentStatus = status;
      
      if (currentPath === undefined) {
        // If path is not provided, use store value first, then fetch from API if needed
        if (positionPath && typeof positionPath === 'string') {
          currentPath = positionPath;
        } else {
          // Get stored position from API as fallback
          const apiPosition = await retrieveStoredPosition();
          currentPath = apiPosition?.path ?? '/';
        }
      }
      
      if (currentStatus === undefined) {
        // If status is not provided, use store value first, then fetch from API if needed
        if (positionStatus && typeof positionStatus === 'string') {
          currentStatus = positionStatus as PositionStatus;
        } else {
          // Get stored position from API as fallback
          const apiPosition = await retrieveStoredPosition();
          currentStatus = (apiPosition?.status ?? '') as PositionStatus;
        }
      }
      
      // Update zustand store
      
      // Only send to backend API if user is logged in
      if (isLogin) {
        await storePathApi({
          path: currentPath,
          path_name: currentStatus // Map status to API's path_name field
        });
        setPosition(currentPath, currentStatus);
        console.log('Position stored:', currentPath, 'Status:', currentStatus);
      }
    } catch (error) {
      console.error('Failed to store position:', error);
    }
  }, [isLogin, retrieveStoredPosition, positionPath, positionStatus, setPosition]);
  
  /**
   * Navigate to the last stored position if available
   * 
   * @param fallbackPath Path to navigate to if no stored position exists
   * @returns True if navigation was to stored position, false if fallback was used
   */
  const navigateToStoredPosition = useCallback(async (fallbackPath: string) => {
    try {
      setLoading(true);
      
      // First check zustand store with validation
      if (positionPath && typeof positionPath === 'string') {
        // Try to get stored position first to sync state
        const storedPosition = await retrieveStoredPosition();
        if (storedPosition && storedPosition.path) {
          navigate(storedPosition.path);
          return true;
        }
        
        // Fallback to store path if API fails
        navigate(positionPath);
        return true;
      }
      
      // If not in store, try API
      const storedPosition = await retrieveStoredPosition();
      if (storedPosition && storedPosition.path) {
        navigate(storedPosition.path);
        return true;
      } else {
        navigate(fallbackPath);
        return false;
      }
    } catch (error) {
      console.error('Error navigating to stored position:', error);
      navigate(fallbackPath);
      return false;
    } finally {
      setLoading(false);
    }
  }, [navigate, retrieveStoredPosition, positionPath]);
  
  /**
   * Get the current position information from the store or API
   * 
   * @returns Promise resolving to an object with current path and status
   */
  const getCurrentPosition = useCallback(async () => {

    console.log(positionPath,'positionPathpositionPath')
    try {
      // First check zustand store
      // if (positionPath && typeof positionPath === 'string') {
      //   return { path: positionPath, status: positionStatus };
      // }
      
      // If not in store, try API
      return await retrieveStoredPosition();
    } catch (error) {
      console.error('Error getting current position:', error);
      return null;
    }
  }, [retrieveStoredPosition, positionPath, positionStatus]);
  
  return {
    trackPosition,
    retrieveStoredPosition,
    navigateToStoredPosition,
    getCurrentPosition,
    loading,
    currentPath: positionPath,
    currentStatus: positionStatus
  };
}; 