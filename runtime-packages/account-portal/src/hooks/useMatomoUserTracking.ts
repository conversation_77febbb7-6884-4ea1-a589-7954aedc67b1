import { useEffect, useCallback } from 'react';
import { useCommonStore } from '@/store/common';
import { setUserId, resetUserId, getCurrentUserId, initializeUserId } from '@/utils/matomoTracking';

/**
 * Hook for managing Matomo user ID tracking
 * 
 * This hook provides centralized user ID management for Matomo tracking,
 * automatically syncing with the user's login state and providing methods
 * for manual user ID management.
 * 
 * @returns Object with user tracking methods and current state
 */
export const useMatomoUserTracking = () => {
  const { userInfo, isLogin } = useCommonStore();

  // Initialize user ID on mount if user is already logged in
  useEffect(() => {
    if (isLogin && userInfo?.id) {
      setUserId(String(userInfo.id));
    } else if (!isLogin) {
      resetUserId();
    }
  }, [isLogin, userInfo?.id]);

  // Manual methods for user ID management
  const setUserIdManually = useCallback((userId: string) => {
    setUserId(userId);
  }, []);

  const resetUserIdManually = useCallback(() => {
    resetUserId();
  }, []);

  const getCurrentUserIdFromStorage = useCallback(() => {
    return getCurrentUserId();
  }, []);

  const initializeUserIdFromStorage = useCallback(() => {
    initializeUserId();
  }, []);

  // Get current user ID from store
  const currentUserId = userInfo?.id ? String(userInfo.id) : null;

  return {
    // Current state
    currentUserId,
    isUserTracked: Boolean(currentUserId && isLogin),
    
    // Manual methods
    setUserId: setUserIdManually,
    resetUserId: resetUserIdManually,
    getCurrentUserId: getCurrentUserIdFromStorage,
    initializeUserId: initializeUserIdFromStorage,
    
    // Utility methods
    refreshUserTracking: () => {
      if (isLogin && userInfo?.id) {
        setUserId(String(userInfo.id));
      } else {
        resetUserId();
      }
    }
  };
};

export default useMatomoUserTracking; 