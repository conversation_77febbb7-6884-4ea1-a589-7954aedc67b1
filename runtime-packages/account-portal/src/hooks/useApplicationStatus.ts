import { useEffect } from 'react';
import { useApplicationStatusStore } from '@/store/applicationStatus';

interface UseApplicationStatusOptions {
  autoFetch?: boolean;
}


export const useApplicationStatus = (options: UseApplicationStatusOptions = {}) => {
  const { autoFetch = true } = options;
  const store = useApplicationStatusStore();

  useEffect(() => {
    if (autoFetch) {
      store.fetchAllData();
    }
  }, [autoFetch, store.fetchAllData]);

  return {
    data : {
      creditApplication: store.creditApplication,
      creditLimit: store.creditLimit,
      type: store.type,
      status: store.status,
      stage: store.stage,
      pre_limit: store.pre_limit,
    }, 
    authShopButtons : {
      showAddButton: store.showAddButton,
      showBackButton: store.showBackButton,
      showNextButton: store.showNextButton,
      showSubmitButton: store.showSubmitButton,
    },
    hookStatus : {
      isLoadingApplication: store.isLoadingApplication,
      isLoadingLimits: store.isLoadingLimits,
      isLoading: store.isLoading,
      error: store.error,
      hasFetchedApplication: store.hasFetchedApplication,
      hasFetchedLimits: store.hasFetchedLimits,
    },
    hookMethods : {
      fetchCreditApplication: store.fetchCreditApplication,
      fetchCreditLimit: store.fetchCreditLimit,
      fetchAllData: store.fetchAllData,
      computeOutputs: store.computeOutputs,
      clear: store.clear,
    },
    matchedScenarioId: store.matchedScenarioId,
  };
}; 