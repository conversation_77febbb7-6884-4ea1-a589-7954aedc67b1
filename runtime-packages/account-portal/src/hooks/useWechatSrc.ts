// src/hooks/useWechatSrc.ts
import {useCallback, useEffect, useState} from 'react';
import {useSelectedPage} from '@/hooks/useSelectedProduct';
import {wechatMap} from '@/utils/wechat/wechat-map';
import {useWechatStore} from '@/store/wechat';
import {useLocation} from 'react-router-dom';
import {TEMPLATE_IDS} from '@/constants/templateIds';

export function useWechatSrc(): string | null {
    const product = useSelectedPage();
    const {search} = useLocation();
    const [src, setSrc] = useState<string | null>(null);
    const [hasStartedFetch, setHasStartedFetch] = useState(false);

    const {
        cpid: storedCpid,
        wechatBase64,
        setCpid,
        fetchWechatSrc,
        isLoadingWechatSrc,
    } = useWechatStore();

    const loadDefault = useCallback(() => {
        const loader = wechatMap[product ?? 'default'] ?? wechatMap.default;
        loader()
            .then(mod => setSrc(mod.default))
            .catch(() => setSrc(null));
    }, [product]);

    useEffect(() => {
        const params = new URLSearchParams(search);
        const tpid = params.get('tpid');
        const cpidParam = params.get('cpid');

        const isSpecial =
            tpid === TEMPLATE_IDS.TEMPLATE_2506_RED ||
            tpid === TEMPLATE_IDS.TEMPLATE_2506_BLUE ||
            tpid === TEMPLATE_IDS.TEMPLATE_2507_RED;

        if (isSpecial && cpidParam) {
            if (cpidParam !== storedCpid) {
                setCpid(cpidParam);
            }
            setHasStartedFetch(true);
            fetchWechatSrc();
        } else {
            loadDefault();
        }
    }, [search, storedCpid, setCpid, fetchWechatSrc, loadDefault]);

    useEffect(() => {
        const params = new URLSearchParams(search);
        const tpid = params.get('tpid');

        const isSpecial =
            tpid === TEMPLATE_IDS.TEMPLATE_2506_RED ||
            tpid === TEMPLATE_IDS.TEMPLATE_2506_BLUE ||
            tpid === TEMPLATE_IDS.TEMPLATE_2507_RED;

        if (!isSpecial || !hasStartedFetch) return;
        if (isLoadingWechatSrc) return;

        if (wechatBase64) {
            setSrc(wechatBase64);
        } else {
            loadDefault();
        }
    }, [search, hasStartedFetch, isLoadingWechatSrc, wechatBase64, loadDefault]);

    return src;
}
