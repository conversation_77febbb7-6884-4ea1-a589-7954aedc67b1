import { useEffect, useRef } from 'react';
import { useLocation, useNavigationType } from 'react-router-dom';
import { useMatomoContext } from '@/contexts/MatomoContext';

/**
 * Options for route tracking
 */
interface RouteTrackingOptions {
  /**
   * Custom title prefix to use for the page title
   * If not provided, defaults to the route path
   */
  titlePrefix?: string;
  
  /**
   * Map of parameter keys to their display names in the title
   * For example: { tab: 'Tab', category: 'Category' }
   */
  paramTitleMap?: Record<string, string>;
  
  /**
   * Whether to track the route on mount
   * Default: true
   */
  trackOnMount?: boolean;
  
  /**
   * Whether to track parameter changes
   * Default: true
   */
  trackParamChanges?: boolean;
  
  /**
   * Custom function to generate the page title
   * If provided, overrides the default title generation
   */
  getCustomTitle?: (pathname: string, params: URLSearchParams) => string;
  
  /**
   * Custom function to generate the page URL
   * If provided, overrides the default URL
   */
  getCustomUrl?: (pathname: string, params: URLSearchParams) => string;
}

/**
 * Hook that tracks route changes and URL parameter changes
 * 
 * This hook automatically tracks page views when:
 * 1. The route changes (pathname)
 * 2. URL parameters change (if trackParamChanges is enabled)
 * 
 * @example
 * // Basic usage
 * useRouteTracking();
 * 
 * @example
 * // With custom title prefix and parameter tracking
 * useRouteTracking({
 *   titlePrefix: 'Products',
 *   paramTitleMap: { tab: 'Tab', category: 'Category' }
 * });
 * 
 * @example
 * // With custom title generation
 * useRouteTracking({
 *   getCustomTitle: (pathname, params) => {
 *     return `Custom Title - ${params.get('tab') || 'Home'}`;
 *   }
 * });
 */
const useRouteTracking = (options: RouteTrackingOptions = {}) => {
  const location = useLocation();
  const navigationType = useNavigationType();
  const { trackPageView, isEnabled } = useMatomoContext();
  const prevPathRef = useRef(location.pathname);
  const prevSearchRef = useRef(location.search);
  
  const {
    titlePrefix,
    paramTitleMap = {},
    trackOnMount = true,
    trackParamChanges = true,
    getCustomTitle,
    getCustomUrl
  } = options;
  
  // Generate a page title based on the route and parameters
  const generatePageTitle = (pathname: string, params: URLSearchParams): string => {
    if (getCustomTitle) {
      return getCustomTitle(pathname, params);
    }
    
    // Generate page name from pathname
    const pathParts = pathname.split('/').filter(Boolean);
    const pageName = pathParts.length > 0 
      ? pathParts[pathParts.length - 1].charAt(0).toUpperCase() + pathParts[pathParts.length - 1].slice(1)
      : 'Home';
    
    // Generate title prefix
    const prefix = titlePrefix || pageName;
    
    // Check if we have any URL parameters to include in the title
    const paramParts: string[] = [];
    
    params.forEach((value, key) => {
      if (paramTitleMap[key]) {
        paramParts.push(`${paramTitleMap[key]}: ${value}`);
      }
    });
    
    return paramParts.length > 0
      ? `${prefix} - ${paramParts.join(', ')}`
      : prefix;
  };
  
  // Generate custom URL if needed
  const generatePageUrl = (pathname: string, search: string): string => {
    if (getCustomUrl) {
      return getCustomUrl(pathname, new URLSearchParams(search));
    }
    return pathname + search;
  };
  
  useEffect(() => {
    if (!isEnabled) return;
    
    // Skip if not tracking on mount and this is the initial render
    if (!trackOnMount && prevPathRef.current === location.pathname && prevSearchRef.current === location.search) {
      return;
    }
    
    // const isPathChange = prevPathRef.current !== location.pathname;
    // const isSearchChange = prevSearchRef.current !== location.search;
    // Only track if:
    // 1. Path has changed, or
    // 2. Search params have changed and we're tracking param changes
    // if (isPathChange || (isSearchChange && trackParamChanges)) {
    // Track anyways
    const params = new URLSearchParams(location.search);
    const title = generatePageTitle(location.pathname, params);
    const url = generatePageUrl(location.pathname, location.search);
    
    // Add a small delay to allow the page to fully render
    // This helps ensure correct page titles and state
    setTimeout(() => {
      trackPageView(title, url);
    }, 50);
    
    // Update refs
    prevPathRef.current = location.pathname;
    prevSearchRef.current = location.search;
    // }
  }, [
    location.pathname, 
    location.search, 
    isEnabled, 
    trackOnMount, 
    trackParamChanges, 
    trackPageView,
    navigationType
  ]);
  
  // Return the tracking function to allow manual tracking if needed
  return {
    trackPageView: (customTitle?: string, customUrl?: string) => {
      if (!isEnabled) return;
      
      const params = new URLSearchParams(location.search);
      const title = customTitle || generatePageTitle(location.pathname, params);
      const url = customUrl || generatePageUrl(location.pathname, location.search);
      
      trackPageView(title, url);
    }
  };
};

export default useRouteTracking; 