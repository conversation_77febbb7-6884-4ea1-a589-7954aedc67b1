import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useMatomoContext } from '@/contexts/MatomoContext';
import { TRACKING_CATEGORIES, TRACKING_ACTIONS } from '@/components/shared/tracking/constants';

interface ExitTrackingOptions {
  trackVisibilityChange?: boolean;
  trackBeforeUnload?: boolean;
  trackPageHide?: boolean;
  exitEventCategory?: string;
  minTimeOnPage?: number;
  trackTimeSpent?: boolean;
}
const useExitTracking = (options: ExitTrackingOptions = {}) => {
  const location = useLocation();
  const { trackEvent, isEnabled } = useMatomoContext();
  const pageStartTimeRef = useRef<number>(Date.now());
  const lastLocationRef = useRef<string>(location.pathname);
  const hasTrackedExitRef = useRef<boolean>(false);
  
  const {
    trackVisibilityChange = false,
    trackBeforeUnload = true,
    trackPageHide = false,
    exitEventCategory = 'Navigation',
    minTimeOnPage = 1000,
    trackTimeSpent = true
  } = options;
  
  const getCurrentPageInfo = () => {
    const pathParts = location.pathname.split('/').filter(Boolean);
    const pageName = pathParts.length > 0 
      ? pathParts[pathParts.length - 1].charAt(0).toUpperCase() + pathParts[pathParts.length - 1].slice(1)
      : 'Home';
    return {
      pageName,
      fullPath: location.pathname + location.search,
      pathname: location.pathname
    };
  };
  
  const getTimeSpentOnPage = () => {
    return Date.now() - pageStartTimeRef.current;
  };
  
  const trackExitEvent = (exitType: string, additionalInfo?: string) => {
    if (!isEnabled || hasTrackedExitRef.current) return;
    
    const timeSpent = getTimeSpentOnPage();
    
    if (timeSpent < minTimeOnPage) return;
    
    const pageInfo = getCurrentPageInfo();
    const eventName = `exit_${exitType}`;
    const eventValue = trackTimeSpent ? Math.round(timeSpent / 1000) : undefined;
    
    trackEvent(
      exitEventCategory,
      'Page Exit',
      eventName,
      eventValue
    );
    
    if (additionalInfo) {
      trackEvent(
        exitEventCategory,
        'Exit Details',
        `${eventName}_${additionalInfo}`
      );
    }
    
    hasTrackedExitRef.current = true;
    
    console.debug(`Exit tracked: ${exitType} from ${pageInfo.pageName} after ${timeSpent}ms`);
  };
  
  useEffect(() => {
    pageStartTimeRef.current = Date.now();
    lastLocationRef.current = location.pathname;
    hasTrackedExitRef.current = false;
  }, [location.pathname]);
  
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (trackBeforeUnload) {
        trackExitEvent('browser_close');
      }
    };
    
    const handlePageHide = (event: PageTransitionEvent) => {
      if (trackPageHide) {
        trackExitEvent('page_hide', event.persisted ? 'cached' : 'unloaded');
      }
    };
    
    const handleVisibilityChange = () => {
      if (trackVisibilityChange && document.visibilityState === 'hidden') {
        trackExitEvent('tab_switch');
      }
    };
    
    if (trackBeforeUnload) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }
    
    if (trackPageHide) {
      window.addEventListener('pagehide', handlePageHide);
    }
    
    if (trackVisibilityChange) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
    }
    
    return () => {
      if (trackBeforeUnload) {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      }
      
      if (trackPageHide) {
        window.removeEventListener('pagehide', handlePageHide);
      }
      
      if (trackVisibilityChange) {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      }
    };
  }, [
    trackBeforeUnload,
    trackPageHide,
    trackVisibilityChange,
    isEnabled,
    exitEventCategory,
    minTimeOnPage,
    trackTimeSpent
  ]);
  
  return {
    trackManualExit: (exitType: string, additionalInfo?: string) => {
      trackExitEvent(exitType, additionalInfo);
    },
    getTimeOnPage: getTimeSpentOnPage,
    getCurrentPageInfo
  };
};

export default useExitTracking; 