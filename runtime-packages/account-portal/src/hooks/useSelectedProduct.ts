import {useLocation} from "react-router-dom";
import {getProductName, PageType, Product, ProductOfficialName} from "@/types/common/general.type.ts";
import {TEMPLATE_IDS} from "@/constants/templateIds.ts";
import {PAGES} from "@/constants/general";
import {useSearchParams} from "react-router-dom";


export function useSelectedProduct(): Product {
    const {pathname} = useLocation();
    if (pathname.startsWith("/credit/hook")) {
        return "xdj";
    }
    if (pathname.startsWith("/credit/underwriting")) {
        return "credit-approval-automation";
    }
    return undefined;
}

export function useMappedSelectedProduct(): ProductOfficialName {
    const {pathname} = useLocation();
    if (pathname.startsWith("/credit/hook")) {
        return getProductName("xdj");
    }
    if (pathname.startsWith("/credit/underwriting")) {
        return getProductName("credit-approval-automation");
    }

    // FIXME : Fallback to xdj for now
    return getProductName("xdj");
}

export function useSelectedPage(): PageType {
    const {pathname} = useLocation();
    const [searchParams] = useSearchParams();

    if (searchParams.get('tpid') && searchParams.get('tpid') === TEMPLATE_IDS.TEMPLATE_2506_BLUE) {
        return PAGES.ADV_BLUE;
    }

    if (searchParams.get('tpid') && searchParams.get('tpid') === TEMPLATE_IDS.TEMPLATE_2506_RED) {
        return PAGES.ADV_RED;
    }

    if (searchParams.get('tpid') && searchParams.get('tpid') === TEMPLATE_IDS.TEMPLATE_2507_RED) {
        return PAGES.ADV_RED;
    }

    if (pathname.startsWith('/credit/hook')) {
        return PAGES.XDJ;
    }
    if (pathname.startsWith('/credit/underwriting')) {
        return PAGES.CREDIT_APPROVAL_AUTOMATION;
    }

    return undefined;
}