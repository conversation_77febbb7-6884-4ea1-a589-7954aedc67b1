import { useCallback, useEffect } from 'react';
import {
  trackEvent,
  trackEventWithCustomDimensions,
  trackPageView,
  trackSiteSearch,
  trackGoal,
  setCustomDimension,
  setUserId,
  resetUserId,
  forceTrackingDispatch
} from '@/utils/matomoTracking';
import urlStore, { initializeUrlTracking } from '@/utils/urlStore';

// Type definition for tracking event with custom dimensions
export interface TrackEventParams {
  category: string;
  action: string;
  name?: string;
  value?: number;
  customDimensions?: Record<number, string>;
}

/**
 * A hook to use Matomo tracking in React components
 * 
 * @returns An object with methods to interact with Matomo
 */
const useMatomo = () => {
  // Initialize user ID and URL tracking on hook mount
  useEffect(() => {
    try {
      // Initialize URL tracking
      initializeUrlTracking();
      
      // Initialize user ID if user is logged in
      const userInfoString = localStorage.getItem('userInfo');
      const accessToken = localStorage.getItem('accessToken');
      
      // Only set user ID if both userInfo and accessToken exist (user is logged in)
      if (userInfoString && accessToken) {
        const userInfo = JSON.parse(userInfoString);
        if (userInfo?.id) {
          setUserId(String(userInfo.id));
        }
      }
    } catch (error) {
      console.error('Error initializing Matomo user ID:', error);
    }
  }, []);

  // Track URL changes for current session
  useEffect(() => {
    const updateCurrentUrl = () => {
      if (typeof window !== 'undefined') {
        urlStore.updateUrl(window.location.href);
      }
    };

    // Update URL on component mount
    updateCurrentUrl();

    // Listen for route changes (for SPA navigation)
    const handleRouteChange = () => {
      updateCurrentUrl();
    };

    // Add event listener for route changes if using a router
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // Track custom events
  const trackEventAction = useCallback(
    (category: string, action: string, name?: string, value?: number) => 
      trackEvent(category, action, name, value),
    []
  );

  // Track custom events with additional dimensions
  const trackEventWithDimensions = useCallback(
    (params: TrackEventParams) => 
      trackEventWithCustomDimensions(params.category, params.action, params.name, params.value, params.customDimensions),
    []
  );

  // Track page views
  const trackPageViewAction = useCallback(
    (customTitle?: string, customUrl?: string) => 
      trackPageView(customTitle, customUrl),
    []
  );

  // Track site searches
  const trackSiteSearchAction = useCallback(
    (keyword: string, category?: string, resultsCount?: number) => 
      trackSiteSearch(keyword, category, resultsCount),
    []
  );

  // Track goal conversions
  const trackGoalAction = useCallback(
    (goalId: number, conversionValue?: number) => 
      trackGoal(goalId, conversionValue),
    []
  );

  // Set a custom dimension
  const setCustomDimensionAction = useCallback(
    (dimensionId: number, dimensionValue: string) => 
      setCustomDimension(dimensionId, dimensionValue),
    []
  );

  // Set user ID
  const setUserIdAction = useCallback(
    (userId: string) => setUserId(userId),
    []
  );

  // Reset user ID
  const resetUserIdAction = useCallback(
    () => resetUserId(),
    []
  );

  // Force dispatch of tracking calls
  const forceTrackingDispatchAction = useCallback(
    () => forceTrackingDispatch(),
    []
  );

  // Get previous URL
  const getPreviousUrlAction = useCallback(
    () => urlStore.getPreviousUrl(),
    []
  );

  // Get current URL
  const getCurrentUrlAction = useCallback(
    () => urlStore.getCurrentUrl(),
    []
  );

  return {
    trackEvent: trackEventAction,
    trackEventWithDimensions: trackEventWithDimensions,
    trackPageView: trackPageViewAction,
    trackSiteSearch: trackSiteSearchAction,
    trackGoal: trackGoalAction,
    setCustomDimension: setCustomDimensionAction,
    setUserId: setUserIdAction,
    resetUserId: resetUserIdAction,
    forceTrackingDispatch: forceTrackingDispatchAction,
    getPreviousUrl: getPreviousUrlAction,
    getCurrentUrl: getCurrentUrlAction
  };
};

export default useMatomo;

/**
 * Usage example:
 * 
 * import useMatomo from '@/hooks/useMatomo';
 * 
 * const MyComponent = () => {
 *   const { 
 *     trackEvent, 
 *     trackEventWithDimensions,
 *     trackPageView, 
 *     trackSiteSearch,
 *     trackGoal,
 *     getPreviousUrl 
 *   } = useMatomo();
 * 
 *   const handleClick = () => {
 *     // All tracking functions automatically include the previous URL as custom dimension 1
 *     trackEvent('Button', 'Click', 'Submit Button');
 *     
 *     // Track with additional custom dimensions (beyond previous URL)
 *     trackEventWithDimensions({
 *       category: 'Purchase',
 *       action: 'Complete',
 *       name: 'Premium Plan',
 *       value: 99.99,
 *       customDimensions: {
 *         2: 'premium_user', // Custom dimension 2: User role
 *         3: 'mobile'       // Custom dimension 3: Device type
 *       }
 *     });
 *     
 *     // Page views also include previous URL automatically
 *     trackPageView('Custom Page Title');
 *     
 *     // Site search includes previous URL
 *     trackSiteSearch('product search', 'electronics', 25);
 *     
 *     // Goal conversions include previous URL  
 *     trackGoal(1, 99.99);
 *     
 *     // You can also manually get the previous URL if needed
 *     const prevUrl = getPreviousUrl();
 *     console.log('Previous URL:', prevUrl);
 *   };
 * 
 *   useEffect(() => {
 *     // Track a custom page view (includes previous URL automatically)
 *     trackPageView('Custom Page Title');
 *   }, [trackPageView]);
 * 
 *   return <button onClick={handleClick}>Click Me</button>;
 * };
 */ 