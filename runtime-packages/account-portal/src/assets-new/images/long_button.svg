<svg width="243" height="52" viewBox="0 0 243 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_74_35)">
<rect width="240" height="52" rx="26" fill="url(#paint0_linear_74_35)"/>
</g>
<g filter="url(#filter1_i_74_35)">
<rect width="240" height="48" rx="24" fill="url(#paint1_linear_74_35)"/>
</g>
<rect x="0.5" y="0.5" width="239" height="47" rx="23.5" stroke="url(#paint2_linear_74_35)"/>
<defs>
<filter id="filter0_i_74_35" x="-3" y="0" width="243" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_74_35"/>
<feOffset dx="-6" dy="6"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.497694 0 0 0 0 0.162823 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_74_35"/>
</filter>
<filter id="filter1_i_74_35" x="-3" y="0" width="243" height="51" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_74_35"/>
<feOffset dx="-6" dy="6"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_74_35"/>
</filter>
<linearGradient id="paint0_linear_74_35" x1="12.5253" y1="-4.0625" x2="235.648" y2="40.1585" gradientUnits="userSpaceOnUse">
<stop offset="0.00259876" stop-color="#EE740A"/>
<stop offset="0.485839" stop-color="#F0D1A1"/>
<stop offset="1" stop-color="#EF5B1B"/>
</linearGradient>
<linearGradient id="paint1_linear_74_35" x1="12.5253" y1="-3.75" x2="234.193" y2="43.8438" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.253471" stop-color="#FFFAD3"/>
<stop offset="0.591085" stop-color="#FFC266"/>
<stop offset="1" stop-color="#FFF9AF"/>
</linearGradient>
<linearGradient id="paint2_linear_74_35" x1="4.0404" y1="5.625" x2="238.795" y2="51.5344" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFCE2"/>
<stop offset="1" stop-color="#FFEECF"/>
</linearGradient>
</defs>
</svg>
