<svg width="228" height="74" viewBox="0 0 228 74" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_38_71)">
<g filter="url(#filter1_i_38_71)">
<rect x="10" y="17" width="208" height="46" rx="23" fill="url(#paint0_linear_38_71)"/>
</g>
<g filter="url(#filter2_i_38_71)">
<rect x="10" y="9" width="208" height="52" rx="26" fill="url(#paint1_linear_38_71)"/>
</g>
<rect x="10.5" y="9.5" width="207" height="51" rx="25.5" stroke="url(#paint2_linear_38_71)"/>
</g>
<defs>
<filter id="filter0_dd_38_71" x="0" y="-9" width="228" height="91" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_38_71"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.341196 0 0 0 0 0.119368 0 0 0 0 0.112557 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_38_71" result="effect2_dropShadow_38_71"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_38_71" result="shape"/>
</filter>
<filter id="filter1_i_38_71" x="7" y="17" width="211" height="49" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_38_71"/>
<feOffset dx="-6" dy="6"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.497694 0 0 0 0 0.162823 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_71"/>
</filter>
<filter id="filter2_i_38_71" x="7" y="9" width="211" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_38_71"/>
<feOffset dx="-5" dy="5"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.77 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_38_71"/>
</filter>
<linearGradient id="paint0_linear_38_71" x1="20.8552" y1="13.4063" x2="214.522" y2="51.0106" gradientUnits="userSpaceOnUse">
<stop offset="0.00259876" stop-color="#FF5314"/>
<stop offset="0.508598" stop-color="#FFD569"/>
<stop offset="1" stop-color="#FF5314"/>
</linearGradient>
<linearGradient id="paint1_linear_38_71" x1="20.8552" y1="4.9375" x2="216.065" y2="38.4678" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.253471" stop-color="#FFF0D3"/>
<stop offset="0.53465" stop-color="#FFBA82"/>
<stop offset="1" stop-color="#FFF3B9"/>
</linearGradient>
<linearGradient id="paint2_linear_38_71" x1="13.5017" y1="15.0937" x2="219.69" y2="47.352" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFCE2"/>
<stop offset="1" stop-color="#FFEECF"/>
</linearGradient>
</defs>
</svg>
