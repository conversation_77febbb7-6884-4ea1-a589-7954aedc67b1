<svg width="228" height="77" viewBox="0 0 228 77" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_1_36)">
<g filter="url(#filter1_i_1_36)">
<rect x="10" y="9" width="208" height="57" rx="28.5" fill="url(#paint0_linear_1_36)"/>
</g>
<g filter="url(#filter2_i_1_36)">
<rect x="10" y="9" width="208" height="54" rx="27" fill="url(#paint1_linear_1_36)"/>
</g>
<rect x="10.5" y="9.5" width="207" height="53" rx="26.5" stroke="url(#paint2_linear_1_36)"/>
</g>
<defs>
<filter id="filter0_dd_1_36" x="0" y="0" width="228" height="86" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_36"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.341196 0 0 0 0 0.119368 0 0 0 0 0.112557 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_1_36" result="effect2_dropShadow_1_36"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1_36" result="shape"/>
</filter>
<filter id="filter1_i_1_36" x="7" y="9" width="211" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_1_36"/>
<feOffset dx="-6" dy="6"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.497694 0 0 0 0 0.162823 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_36"/>
</filter>
<filter id="filter2_i_1_36" x="7" y="9" width="211" height="57" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_1_36"/>
<feOffset dx="-5" dy="5"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.77 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_36"/>
</filter>
<linearGradient id="paint0_linear_1_36" x1="20.8552" y1="4.54688" x2="217.008" y2="35.2837" gradientUnits="userSpaceOnUse">
<stop offset="0.00259876" stop-color="#FF5314"/>
<stop offset="0.508598" stop-color="#FFD569"/>
<stop offset="1" stop-color="#FF5314"/>
</linearGradient>
<linearGradient id="paint1_linear_1_36" x1="20.8552" y1="4.78125" x2="216.472" y2="37.1371" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.253471" stop-color="#FFF0D3"/>
<stop offset="0.53465" stop-color="#FFBA82"/>
<stop offset="1" stop-color="#FFF3B9"/>
</linearGradient>
<linearGradient id="paint2_linear_1_36" x1="13.5017" y1="15.3281" x2="220.049" y2="46.4457" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFFCE2"/>
<stop offset="1" stop-color="#FFEECF"/>
</linearGradient>
</defs>
</svg>
