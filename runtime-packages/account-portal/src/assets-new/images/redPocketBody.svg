<svg width="400" height="196" viewBox="0 0 400 196" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_32_21)">
<g filter="url(#filter0_f_32_21)">
<path d="M385 339C385 352.807 373.807 364 360 364H40C26.1929 364 15 352.807 15 339V83C15 69.1929 26.1929 58 40 58H360C373.807 58 385 69.1929 385 83V339Z" fill="url(#paint0_linear_32_21)"/>
</g>
<rect x="15" y="62" width="370" height="170" rx="20" fill="url(#paint1_linear_32_21)"/>
<rect x="15.5" y="62.5" width="369" height="169" rx="19.5" stroke="url(#paint2_linear_32_21)" stroke-opacity="0.56"/>
<g filter="url(#filter1_d_32_21)">
<rect x="43" y="15" width="314" height="166" rx="20" fill="white"/>
<rect x="43.5" y="15.5" width="313" height="165" rx="19.5" stroke="url(#paint3_linear_32_21)"/>
</g>
<rect x="49.5" y="21.5" width="301" height="147" rx="15.5" stroke="url(#paint4_linear_32_21)"/>
<path d="M15 116V116C130.922 176.467 269.078 176.467 385 116V116V315H15V116Z" fill="#FF5752"/>
<path d="M15 116V116C130.922 176.467 269.078 176.467 385 116V116V315H15V116Z" fill="url(#paint5_linear_32_21)" fill-opacity="0.4" style="mix-blend-mode:luminosity"/>
<g clip-path="url(#clip1_32_21)">
<g filter="url(#filter2_di_32_21)">
<path d="M385 117C269.078 177.467 130.922 177.467 15 117V102C130.922 162.467 269.078 162.467 385 102V117Z" fill="url(#paint6_linear_32_21)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_32_21" x="8" y="51" width="384" height="320" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3.5" result="effect1_foregroundBlur_32_21"/>
</filter>
<filter id="filter1_d_32_21" x="33" y="13" width="334" height="186" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_32_21"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_32_21" result="shape"/>
</filter>
<filter id="filter2_di_32_21" x="10" y="97" width="380" height="70.3505" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.321101 0 0 0 0 0.171043 0 0 0 0 0.17231 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_32_21"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_32_21" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_32_21"/>
</filter>
<linearGradient id="paint0_linear_32_21" x1="189.5" y1="55" x2="200" y2="355.417" gradientUnits="userSpaceOnUse">
<stop stop-color="#CFCFCF"/>
<stop offset="1" stop-color="#515151"/>
</linearGradient>
<linearGradient id="paint1_linear_32_21" x1="200" y1="62" x2="200" y2="175.636" gradientUnits="userSpaceOnUse">
<stop stop-color="#F88A8B"/>
<stop offset="0.235507" stop-color="#ED585A"/>
<stop offset="1" stop-color="#85292A"/>
</linearGradient>
<linearGradient id="paint2_linear_32_21" x1="200" y1="62" x2="200" y2="232" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_32_21" x1="200" y1="15" x2="200" y2="181" gradientUnits="userSpaceOnUse">
<stop stop-color="#D2D2D2"/>
<stop offset="1" stop-color="#D2D2D2" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_32_21" x1="200" y1="21" x2="173.808" y2="153.315" gradientUnits="userSpaceOnUse">
<stop stop-color="#F9D0BF"/>
<stop offset="1" stop-color="#DEC8A6"/>
</linearGradient>
<linearGradient id="paint5_linear_32_21" x1="139.5" y1="215.5" x2="15" y2="215.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_32_21" x1="3.5" y1="137.175" x2="385" y2="137.175" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB58D"/>
<stop offset="0.1" stop-color="#FFFBE3"/>
<stop offset="0.133054" stop-color="#FFFBE3"/>
<stop offset="0.22267" stop-color="#FFB588"/>
<stop offset="0.35" stop-color="#FD8D6D"/>
<stop offset="0.499795" stop-color="#FFF5E2"/>
<stop offset="0.706683" stop-color="#FFA46C"/>
<stop offset="0.769952" stop-color="#FD7537"/>
<stop offset="0.89863" stop-color="#FFF5E1"/>
<stop offset="0.925794" stop-color="#FFF5E1"/>
<stop offset="1" stop-color="#FF8D6C"/>
</linearGradient>
<clipPath id="clip0_32_21">
<rect width="400" height="196" fill="white"/>
</clipPath>
<clipPath id="clip1_32_21">
<rect width="370" height="187" fill="white" transform="translate(15 9)"/>
</clipPath>
</defs>
</svg>
