<svg width="212" height="42" viewBox="0 0 212 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="path-1-inside-1_32_44" fill="white">
<path d="M193 0C203.493 0 212 8.50659 212 19C212 29.4934 203.493 38 193 38H19.6602L12.0518 41.0479C11.0265 41.4583 9.86285 40.9598 9.45215 39.9346L6.9502 33.6904C2.70696 30.2059 0 24.9192 0 19C0 8.50659 8.50659 0 19 0H193Z"/>
</mask>
<g filter="url(#filter0_i_32_44)">
<path d="M193 0C203.493 0 212 8.50659 212 19C212 29.4934 203.493 38 193 38H19.6602L12.0518 41.0479C11.0265 41.4583 9.86285 40.9598 9.45215 39.9346L6.9502 33.6904C2.70696 30.2059 0 24.9192 0 19C0 8.50659 8.50659 0 19 0H193Z" fill="url(#paint0_linear_32_44)"/>
</g>
<path d="M19.6602 38V37.4H19.5444L19.437 37.443L19.6602 38ZM12.0518 41.0479L12.2748 41.6049L12.2749 41.6048L12.0518 41.0479ZM9.45215 39.9346L10.0091 39.7115L10.0091 39.7114L9.45215 39.9346ZM6.9502 33.6904L7.50715 33.4673L7.44991 33.3244L7.33098 33.2267L6.9502 33.6904ZM193 0V0.6C203.162 0.6 211.4 8.83796 211.4 19H212H212.6C212.6 8.17522 203.825 -0.6 193 -0.6V0ZM212 19H211.4C211.4 29.162 203.162 37.4 193 37.4V38V38.6C203.825 38.6 212.6 29.8248 212.6 19H212ZM193 38V37.4H19.6602V38V38.6H193V38ZM19.6602 38L19.437 37.443L11.8286 40.4909L12.0518 41.0479L12.2749 41.6048L19.8833 38.557L19.6602 38ZM12.0518 41.0479L11.8287 40.4908C11.1113 40.7781 10.2967 40.4293 10.0091 39.7115L9.45215 39.9346L8.89518 40.1577C9.429 41.4903 10.9417 42.1386 12.2748 41.6049L12.0518 41.0479ZM9.45215 39.9346L10.0091 39.7114L7.50715 33.4673L6.9502 33.6904L6.39324 33.9136L8.89519 40.1577L9.45215 39.9346ZM6.9502 33.6904L7.33098 33.2267C3.22055 29.8512 0.6 24.732 0.6 19H0H-0.6C-0.6 25.1063 2.19338 30.5605 6.56941 34.1541L6.9502 33.6904ZM0 19H0.6C0.6 8.83796 8.83796 0.6 19 0.6V0V-0.6C8.17522 -0.6 -0.6 8.17522 -0.6 19H0ZM19 0V0.6H193V0V-0.6H19V0Z" fill="url(#paint1_linear_32_44)" mask="url(#path-1-inside-1_32_44)"/>
<defs>
<filter id="filter0_i_32_44" x="0" y="0" width="212" height="45.1916" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.45 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_32_44"/>
</filter>
<linearGradient id="paint0_linear_32_44" x1="-133" y1="-75.5" x2="184.5" y2="33.5" gradientUnits="userSpaceOnUse">
<stop offset="0.209047" stop-color="#FFB824"/>
<stop offset="0.865205" stop-color="#EE4444"/>
<stop offset="1" stop-color="#EE4444"/>
</linearGradient>
<linearGradient id="paint1_linear_32_44" x1="-34.5" y1="-22" x2="235.651" y2="73.3259" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFC2C1"/>
</linearGradient>
</defs>
</svg>
