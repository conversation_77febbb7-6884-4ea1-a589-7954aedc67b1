<svg width="290" height="317" viewBox="0 0 290 317" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.75 94C5.75 81.1594 16.1594 70.75 29 70.75H145H261C273.841 70.75 284.25 81.1594 284.25 94V238C284.25 259.677 266.677 277.25 245 277.25H45C23.3228 277.25 5.75 259.677 5.75 238V94Z" fill="url(#paint0_linear_33_18)" stroke="url(#paint1_linear_33_18)" stroke-width="1.5"/>
<g filter="url(#filter0_d_33_18)">
<path d="M25 26C25 12.7452 35.7452 2 49 2H241C254.255 2 265 12.7452 265 26V141C265 154.255 254.255 165 241 165H49C35.7452 165 25 154.255 25 141V26Z" fill="url(#paint2_linear_33_18)"/>
</g>
<g filter="url(#filter1_i_33_18)">
<path d="M5 110L18.8248 118.911C100.735 171.707 206.804 168.156 285 110V292C285 305.255 274.255 316 261 316H29C15.7452 316 5 305.255 5 292L5 110Z" fill="url(#paint3_linear_33_18)"/>
</g>
<g filter="url(#filter2_di_33_18)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M285 105C200.709 163.101 89.2913 163.101 5 105V120C89.2913 178.101 200.709 178.101 285 120V105Z" fill="url(#paint4_linear_33_18)"/>
</g>
<g filter="url(#filter3_i_33_18)">
<path d="M5.69839 105L5.25682 291.943C5.22546 305.22 15.9798 316 29.2568 316H259.858C273.091 316 283.827 305.289 283.858 292.057L284.3 105" stroke="url(#paint5_linear_33_18)" stroke-width="1.5"/>
</g>
<defs>
<filter id="filter0_d_33_18" x="15" y="0" width="260" height="183" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_33_18"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_33_18" result="shape"/>
</filter>
<filter id="filter1_i_33_18" x="5" y="110" width="310" height="206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="30"/>
<feGaussianBlur stdDeviation="17"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.902523 0 0 0 0 0.775053 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_33_18"/>
</filter>
<filter id="filter2_di_33_18" x="0" y="100" width="290" height="68.5756" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.321101 0 0 0 0 0.171043 0 0 0 0 0.17231 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_33_18"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_33_18" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_33_18"/>
</filter>
<filter id="filter3_i_33_18" x="4.50675" y="104.998" width="304.543" height="211.752" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="30"/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_33_18"/>
</filter>
<linearGradient id="paint0_linear_33_18" x1="145" y1="-29.4989" x2="145" y2="235.831" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF7044"/>
<stop offset="0.169579" stop-color="#FF4444"/>
<stop offset="0.668878" stop-color="#CE0000"/>
</linearGradient>
<linearGradient id="paint1_linear_33_18" x1="145" y1="151.968" x2="145" y2="-39.4488" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFECB8"/>
<stop offset="1" stop-color="#FFE2A9"/>
</linearGradient>
<linearGradient id="paint2_linear_33_18" x1="145" y1="2" x2="145" y2="165" gradientUnits="userSpaceOnUse">
<stop offset="0.0216371" stop-color="#FFF1F0"/>
<stop offset="0.157096" stop-color="white"/>
<stop offset="0.639548" stop-color="white"/>
<stop offset="0.824861" stop-color="#FFF2E6"/>
</linearGradient>
<linearGradient id="paint3_linear_33_18" x1="305" y1="214.698" x2="5" y2="214.698" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF5752"/>
<stop offset="0.216838" stop-color="#E9342F"/>
<stop offset="0.608013" stop-color="#EB312C"/>
<stop offset="1" stop-color="#FF5B45"/>
</linearGradient>
<linearGradient id="paint4_linear_33_18" x1="-3.7027" y1="139.141" x2="285" y2="139.141" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB58D"/>
<stop offset="0.1" stop-color="#FFFBE3"/>
<stop offset="0.133054" stop-color="#FFFBE3"/>
<stop offset="0.22267" stop-color="#FFB588"/>
<stop offset="0.35" stop-color="#FD8D6D"/>
<stop offset="0.499795" stop-color="#FFF5E2"/>
<stop offset="0.706683" stop-color="#FFA46C"/>
<stop offset="0.769952" stop-color="#FD7537"/>
<stop offset="0.89863" stop-color="#FFF5E1"/>
<stop offset="0.925794" stop-color="#FFF5E1"/>
<stop offset="1" stop-color="#FF8D6C"/>
</linearGradient>
<linearGradient id="paint5_linear_33_18" x1="154.718" y1="105" x2="154.718" y2="363.258" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFE8B3"/>
<stop offset="1" stop-color="#FFA679"/>
</linearGradient>
</defs>
</svg>
