<svg width="243" height="52" viewBox="0 0 243 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5">
<g filter="url(#filter0_i_75_49)">
<rect width="240" height="52" rx="24" fill="url(#paint0_linear_75_49)"/>
</g>
<rect width="240" height="48" rx="24" fill="url(#paint1_linear_75_49)"/>
</g>
<defs>
<filter id="filter0_i_75_49" x="-3" y="0" width="243" height="55" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="1" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_75_49"/>
<feOffset dx="-6" dy="6"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.497694 0 0 0 0 0.162823 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_75_49"/>
</filter>
<linearGradient id="paint0_linear_75_49" x1="12.5253" y1="-4.0625" x2="235.648" y2="40.1585" gradientUnits="userSpaceOnUse">
<stop offset="0.00259876" stop-color="#1D285D"/>
<stop offset="0.485839" stop-color="#1E295F"/>
<stop offset="1" stop-color="#1D275C"/>
</linearGradient>
<linearGradient id="paint1_linear_75_49" x1="-9.69696" y1="-30.375" x2="217.301" y2="67.006" gradientUnits="userSpaceOnUse">
<stop offset="0.117917" stop-color="#4359B6"/>
<stop offset="0.56971" stop-color="#0C0F1F"/>
<stop offset="0.965631" stop-color="#28377F"/>
</linearGradient>
</defs>
</svg>
