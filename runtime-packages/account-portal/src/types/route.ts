import type { RouteObject } from 'react-router-dom';
import type { PositionStatus } from '@/constants/position-status';

/**
 * Configuration for status-based route protection
 */
interface StatusProtection {
  requiredStatus: PositionStatus | PositionStatus[];
  fallbackPath: string;
}

/**
 * Extended route configuration that includes status protection
 */
export type StatusProtectedRouteConfig = RouteObject & {
  statusProtection?: StatusProtection;
  children?: StatusProtectedRouteConfig[];
}; 