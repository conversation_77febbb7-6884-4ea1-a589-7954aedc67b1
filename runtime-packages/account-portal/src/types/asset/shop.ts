import type { PlatformVO, ShopVO, ShopAccountVO } from "@fundpark/fp-api/types/assetShop.ts";

export type PlatformView = PlatformVO;

export interface ShopView extends ShopVO {}

export interface ShopAccountView extends ShopAccountVO {}

export interface AddShopAccountView {
    accountList: {
        shopName: string;
        accountLoginName: string;
        accountLoginPwd: string;
        accountLoginUrl: string;
    }[];
}

export const enum ShopStatusEnum {
    Authorized = "Authorized",
    Unauthorized = "Unauthorized",
    Expired = "Expired",
    Pending = "Pending"
}

export const enum AccountStatusEnum {
    Normal = "Normal",
    Pending = "Pending",
    Abnormal = "Abnormal"
}
