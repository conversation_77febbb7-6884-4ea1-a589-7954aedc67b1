import dayjs from "dayjs";
import type { DocumentDataDTO, RiskAssessment } from "@fundpark/fp-api/types/company.ts";

export interface CommonKYCData {
    riskPointId: string;
    riskPointName: string;
    riskPointNameCN: string;
    riskPointItemId: string;
    riskPointItemName: string;
    riskPointItemNameCN: string;
    sortNo: string;
    value: string;
    label: string;
}

export interface CommonKYCDataMap {
    brand: CommonKYCData[];
    businessNature: CommonKYCData[];
    companyEntity: CommonKYCData[];
    ConnectedEntityCapacity: CommonKYCData[];
    ConnectedPersonCapacity: CommonKYCData[];
    ConnectedPersonUltimateBeneficialOwnerSourceOfWealth: CommonKYCData[];
    InitialSourceOfWealth: CommonKYCData[];
    NameScreeningResult: CommonKYCData[];
    OngoingSourceOfWealth: CommonKYCData[];
    PersonIdType: CommonKYCData[];
    SourceOfCorporateFunds: CommonKYCData[];
    SupplySource: CommonKYCData[];
    TypeGoods: CommonKYCData[];
    PhoneAreaCode: CommonKYCData[];
    IndustryService: CommonKYCData[];
    YearOfIndustryExperience: CommonKYCData[];
    AMLScreening: CommonKYCData[];
    companyDocument: CommonKYCData[];
    personDocument: CommonKYCData[];
    entityDocument: CommonKYCData[];
    AMLScreeningDocument: CommonKYCData[];
}

export interface CommonRegionData {
    id: string;
    name: string;
    nameChi: string;
    riskPoint: string;
    status: string;
    value: string;
    label: string;
}

export interface CommonPageRef {
    validate: () => Promise<void>;
    save: () => Promise<void>;
}

export interface CommonPageProps {
    disabled?: boolean;
    query: PageQuery;
    commonData: Partial<CommonKYCDataMap>;
    regions: CommonRegionData[];
    changeStep: (step: number) => void;
}

export interface CompanyInfo {
    id?: string;
    companyId?: string;
    companyType?: string;
    companyName?: string;
    region?: string;
    uscc?: string;
    brn?: string;
    cr?: string;
    kycApplyType?: string;
    registerNameEn?: string;
    registerNameCn?: string;
    incorporationDate?: dayjs.Dayjs | null;
    registeredAddressRegin?: string;
    registeredAddressDetail?: string;
    principleBusiAddressType?: string;
    principleBusiAddressRegion?: string;
    principleBusiAddressDetail?: string;
    correspondenceAddressType?: string;
    correspondenceAddressRegion?: string;
    correspondenceAddressDetail?: string;
    kycStatus?: string;
    creditNextReviewDate?: string;
    amlScreeningSummary?: string;
    companyDocumentsList: CommonDocumentViewData[];
}

export interface BusinessInfo {
    id?: string;
    kycId?: string;
    latestAnnualSalesAmount?: number;
    latestAnnualSalesCurrency?: string;
    businessYear?: string;
    employeeNumber?: number;
    businessNatureCode?: string;
    businessNatureName?: string;
    goodsType?: string;
    goodsOther?: string;
    initialSourceResult?: string[];
    ongoingSourceResult?: string[];
    corporateSourceResult?: string[];
    supplierThreeList1?: string;
    supplierThreeList2?: string;
    supplierThreeList3?: string;
    buyerThreeList1?: string;
    buyerThreeList2?: string;
    buyerThreeList3?: string;
    corporateFundsRegion?: string;
    industry?: string;
}

export interface ConnectedParties {
    kycId?: string;
    connectedPersonList: Array<{
        opType: string;
        id?: string;
        kycId?: string;
        source?: string;
        fullNameEn?: string;
        fullNameCn?: string;
        capacityRiskPointId?: string;
        capacityResult: string[];
        personCapacityType?: string;
        ownershipPercent?: string;
        position?: string;
        idType?: string;
        idTypeName?: string;
        idNumber?: string;
        birthday?: dayjs.Dayjs | null;
        nationality?: string;
        currentResidentialRegion?: string;
        currentResidentialAddress?: string;
        permanentAddressType?: string;
        permanentAddressRegion?: string;
        permanentAddressDetail?: string;
        mobilePhoneAreaCode?: string;
        mobilePhoneNumber?: string;
        email?: string;
        experienceYearType?: string;
        uboSourceResult: string[];
        uboRegion?: string;
        /** Director, Shareholder */
        corporateParticipantType?: string;
        personDocumentsList: CommonDocumentViewData[];
    }>;
    connectedCompanyList?: Array<{
        opType: string;
        id?: string;
        source?: string;
        entityType?: string;
        incorporationRegion?: string;
        registeredNameEn?: string;
        registeredNameCn?: string;
        capacityResult: string[];
        idvType?: string;
        ownershipPercent?: number;
        brn?: string;
        uscc?: string;
        cr?: string;
        registeredAddressRegion?: string;
        registeredAddressDetail?: string;
        principleBusiAddressType?: string;
        principleBusiAddressRegion?: string;
        principleBusiAddressDetail?: string;
        correspondenceAddressType?: string;
        correspondenceAddressRegion?: string;
        correspondenceAddressDetail?: string;
        businessNature?: string;
        businessRegion?: string;
        industry?: string;
    }>;
}

export interface CommonDocumentViewData {
    required?: boolean;
    title: string;
    id?: string;
    kycId?: string;
    ownerType?: "KYC" | "Person" | "Entity";
    connectedId?: string;
    kycRiskPointItemId: string;
    kycRiskPointId: string;
    docType?: string;
    docUrl?: string;
    docName?: string;
    fileSize?: number;
    expiryDate?: string;
}

export interface DocumentData {
    kycId?: string;
    companyDocumentsList: CommonDocumentViewData[];
    companyAmlScreeningResultList: CommonDocumentViewData[];
    connectedPersons: Array<{
        connectedId: string;
        targetName: string;
        documentsList: CommonDocumentViewData[];
        amlScreeningResultList: CommonDocumentViewData[];
    }>;
    connectedEntities: Array<{
        connectedId: string;
        targetName: string;
        documentsList: CommonDocumentViewData[];
        amlScreeningResultList: CommonDocumentViewData[];
    }>;
}

export interface PageQuery {
    kycId?: string;
    companyId?: string;
}

export interface KYCData {
    companyInfo: CompanyInfo;
    businessInfo: BusinessInfo;
    connectedParties: {
        connectedPersonList: ConnectedParties["connectedPersonList"];
    };
    riskAssessment: {
        amlscreeningDTOList?: RiskAssessment["amlscreeningVOList"];
    };
    documentsData: DocumentDataDTO;
}
