import type { GetKYCDataRes, CommonDocumentData } from "@fundpark/fp-api/types/company.ts";
import type { CompanyProfile } from "./index";

type BaseConnectedPerson = NonNullable<GetKYCDataRes["connectedPartiesVO"]["connectedPersonVOList"]>[0];

interface ConnectedPerson extends Omit<BaseConnectedPerson, "capacityResult"> {
    capacityResult: string[];
    documentList: CommonDocumentData[];
}

export interface KYCData {
    companyProfile: CompanyProfile["company"];
    companyInfo: GetKYCDataRes["generalInformationVO"] & {
        documentList?: CommonDocumentData[];
    };
    businessInfo: Omit<
        GetKYCDataRes["businessInformationVO"],
        "initialSourceResult" | "ongoingSourceResult" | "corporateSourceResult"
    > & {
        initialSourceResult?: string[];
        ongoingSourceResult?: string[];
        corporateSourceResult?: string[];
    };
    connectedParties: {
        connectedPersonList: ConnectedPerson[];
    };
}
