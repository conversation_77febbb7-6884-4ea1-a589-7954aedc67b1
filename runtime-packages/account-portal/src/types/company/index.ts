import type { GetCompanyProfileRes, KycApplicationVO } from "@fundpark/fp-api/types/company.ts";

export interface CompanyInfo {
    id: string;
    companyId: string;
    registerNameEn: string;
    registerNameCn: string;
    region: string;
    kycCompanyType: string;
    brn: string;
    uscc: string;
    cr: string;
    isDefault: boolean;
    kycStatus: string;
    roleId: string | null;
    roleName: string | null;
}

export type KycApplication = KycApplicationVO;

export interface CompanyProfile {
	company: GetCompanyProfileRes['companyVO'];
	kycApplication: KycApplicationVO[];
}
