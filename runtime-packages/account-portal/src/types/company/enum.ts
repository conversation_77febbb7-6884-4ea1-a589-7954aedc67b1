export const enum CompanyProfileRegion {
    HongKong = "Hong Kong",
    ChinaMainland = "China Mainland",
    Others = "Others"
}

export const enum KYCRiskPoint {
    LimitedCompany = "527",
    Partnership = "528",
    SoleProprietorship = "529",
    HKAreaCode = "288",
    ChinaAreaCode = "289"
}

export const enum KYCRiskRegion {
    HongKong = "1",
    MainlandChina = "2"
}

export const enum KYCCapacity {
    Director = "559",
    UltimateBeneficialOwner = "560",
    LegalRepresentative = "643",
    CorporateDirector = "574",
    CorporateShareholder = "575"
}

export const enum KYCStatus {
    Draft = "draft",
    Submitted = "submitted",
    Approved = "approved",
    InProgress = "in progress",
    Rejected = "rejected",
    Return = "returned"
}

export const enum KYCCardType {
    HK = "564",
    ChinaMainland = "565",
    Passport = "566"
}

export const enum KYCPersonCorporateParticipantType {
    Director = "Director",
    Shareholder = "Shareholder"
}
