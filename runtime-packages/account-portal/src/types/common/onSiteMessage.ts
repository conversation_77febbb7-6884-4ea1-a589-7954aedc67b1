export type MessageStatus = 'Read' | 'Unread';
export type OwnerType = 'Client_User' | 'Internal_User';
export type MessageCategory = 'Client_User' | 'Internal_User' | 'Verification';

export interface OnSiteMessage {
  id: string;
  noticeId: string | null;
  ownerId: string;
  ownerName: string;
  ownerType: OwnerType;
  title: string;
  content: string;
  messageStatus: MessageStatus;
  category: MessageCategory;
  sentBy: string;
  sentTime: string;
  receivedTime: string;
  readTime: string | null;
  url: string;
  extId: string;
}

export interface OnSiteMessageListRequest {
  pageSize: string;
  pageNum: string;
  messageStatus?: MessageStatus;
  category?: MessageCategory;
}

export interface OnSiteMessageListResponse {
  records: OnSiteMessage[];
  total: string;
  size: string;
  current: string;
  pages: string;
}

export interface OnSiteMessageReadRequest {
  id: number;
  messageStatus: MessageStatus;
}

export interface CompanyUserAffiliationRequest {
  companyid: number;
} 