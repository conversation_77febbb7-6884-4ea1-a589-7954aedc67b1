import {PAGES} from "@/constants/general";

export type Product = "xdj" | "credit-approval-automation" | undefined;

export type ProductOfficialName =
    | "small_shop_financing"
    | "revenue_credit_automation"
    | "unknown_product";

type KnownProduct = Exclude<Product, undefined>;

export const productNameMap: Record<KnownProduct, ProductOfficialName> = {
    xdj: "small_shop_financing",
    "credit-approval-automation": "revenue_credit_automation",
};

export function getProductName(p: Product): ProductOfficialName {
    return p === undefined
        ? "unknown_product"
        : productNameMap[p];
}


export type PageType =
    | (typeof PAGES)[keyof typeof PAGES]
    | "" | undefined;