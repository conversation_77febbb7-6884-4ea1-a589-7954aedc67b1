/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />
/// <reference types="@fundpark/account-types" />

interface ImportMetaEnv {
  readonly MODE: string;
  readonly VITE_NODE_ENV?: 'development' | 'staging' | 'production';
  readonly VITE_MATOMO_SITE_ID?: string;
  readonly VITE_MATOMO_BASE_URL?: string;
  readonly VITE_MATOMO_SCRIPT_URL?: string;
  readonly VITE_MATOMO_DEBUG?: 'true' | 'false';
  readonly VITE_MATOMO_DISABLE_IN_DEV?: 'true' | 'false';
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}