/**
 * Matomo configuration settings
 * 
 * This file contains all the Matomo-related configuration settings.
 * Configuration is determined by environment variables for different environments.
 */

interface MatomoConfig {
  // The ID of the website in your Matomo installation
  siteId: string;
  
  // Base URL for your Matomo installation (must include trailing slash)
  baseUrl: string;
  
  // Complete URL to the matomo.js script
  scriptUrl: string;
  
  // Enable debug mode for troubleshooting
  enableDebug: boolean;
  
  // Disable tracking in development mode
  disableInDevelopment: boolean;
}

interface EnvironmentMatomoConfig {
  development: MatomoConfig;
  developmentAlt: MatomoConfig;
  staging: MatomoConfig;
  production: MatomoConfig;
}

// Environment-specific configurations matching package.json scripts
const environmentConfigs: EnvironmentMatomoConfig = {
  development: {
    siteId: '7',
    baseUrl: 'https://analysic-uat.fundpark.com/',
    scriptUrl: 'https://analysic-uat.fundpark.com/matomo.js',
    enableDebug: false,
    disableInDevelopment: false
  },

  developmentAlt: {
    siteId: '7',
    baseUrl: 'https://analysic-uat.fundpark.com/',
    scriptUrl: 'https://analysic-uat.fundpark.com/matomo.js',
    enableDebug: true,
    disableInDevelopment: false
  },
  
  staging: {
    siteId: '8',
    baseUrl: 'https://analysic-uat.fundpark.com/',
    scriptUrl: 'https://analysic-uat.fundpark.com/matomo.js',
    enableDebug: true,
    disableInDevelopment: false
  },
  
  production: {
    siteId: '5',
    baseUrl: 'https://analysic.fundpark.com/',
    scriptUrl: 'https://analysic.fundpark.com/matomo.js',
    enableDebug: true,
    disableInDevelopment: false
  }
};

// Get current environment from Vite environment variables
const getCurrentEnvironment = (): keyof EnvironmentMatomoConfig => {
  const mode = import.meta.env.MODE;
  const nodeEnv = import.meta.env.VITE_NODE_ENV;
  
  // Priority: VITE_NODE_ENV > MODE > default to development
  if (nodeEnv && nodeEnv in environmentConfigs) {
    return nodeEnv as keyof EnvironmentMatomoConfig;
  }
  
  if (mode && mode in environmentConfigs) {
    return mode as keyof EnvironmentMatomoConfig;
  }
  
  // Default to development if no valid environment is found
  return 'development';
};

// Override with environment variables if provided
const getConfigWithEnvOverrides = (baseConfig: MatomoConfig): MatomoConfig => {
  return {
    siteId: import.meta.env.VITE_MATOMO_SITE_ID || baseConfig.siteId,
    baseUrl: import.meta.env.VITE_MATOMO_BASE_URL || baseConfig.baseUrl,
    scriptUrl: import.meta.env.VITE_MATOMO_SCRIPT_URL || baseConfig.scriptUrl,
    enableDebug: import.meta.env.VITE_MATOMO_DEBUG === 'true' ? true : baseConfig.enableDebug,
    disableInDevelopment: import.meta.env.VITE_MATOMO_DISABLE_IN_DEV === 'true' ? true : baseConfig.disableInDevelopment
  };
};

// Get the configuration for the current environment
const currentEnvironment = getCurrentEnvironment();
const baseConfig = environmentConfigs[currentEnvironment];
const config = getConfigWithEnvOverrides(baseConfig);

// Log current configuration in debug mode
if (config.enableDebug) {
  console.log(`[Matomo] Using configuration for environment: ${currentEnvironment}`, {
    siteId: config.siteId,
    baseUrl: config.baseUrl,
    scriptUrl: config.scriptUrl,
    enableDebug: config.enableDebug,
    disableInDevelopment: config.disableInDevelopment
  });
}

export default config; 