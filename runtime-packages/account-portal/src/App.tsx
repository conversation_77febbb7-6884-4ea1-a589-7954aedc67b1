import { useLayoutEffect, useState } from "react";
import { ConfigProvider as AntdConfigProvider, message } from "antd";;
import type { ThemeConfig } from "antd";
import { I18nextProvider } from "react-i18next";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import antdZhCN from "antd/locale/zh_CN";
import antdZhHK from "antd/locale/zh_HK";
import antdEnUS from "antd/locale/en_US";
import "dayjs/locale/zh-cn";
import "dayjs/locale/zh-hk";
import "dayjs/locale/en";
import i18n from "@/i18n";
import { useSettingsStore } from "@/store/settings";
import appHelper from "@/utils/appHelper";
import Router from "@/Router.tsx";
import FullSpin from "@/components/FullSpin";
import Matomo from "@/components/Matomo";
import matomoConfig from "@/config/matomo";
import { MatomoProvider } from "@/contexts/MatomoContext";
import "@/assets/styles/index.scss";

dayjs.locale("en");
dayjs.extend(relativeTime);

const App: React.FC = () => {
    const [config, setConfig] = useState<{
        antdLocale: typeof antdEnUS;
        antdTheme: ThemeConfig;
    }>(() => {
        const store = useSettingsStore.getState();
        return {
            antdLocale: antdEnUS,
            antdTheme: store.antdTheme
        };
    });
    const [msgApi, msgContext] = message.useMessage();

    useLayoutEffect(() => {
        appHelper.setMsgApi(msgApi);
        function localeChangeHandle(locale: string) {
            appHelper.setLocale(locale);

            let antdLocale;
            switch (locale) {
                case "zh_CN":
                    antdLocale = antdZhCN;
                    dayjs.locale("zh-cn");
                    break;
                case "zh_HK":
                    antdLocale = antdZhHK;
                    dayjs.locale("zh-hk");
                    break;
                default:
                    antdLocale = antdEnUS;
                    dayjs.locale("en");
                    break;
            }
            setConfig(pre => ({
                ...pre,
                antdLocale
            }));
        }

        localeChangeHandle(useSettingsStore.getState().locale);

        const unsubscribe = useSettingsStore.subscribe((state, preState) => {
            if (state.locale !== preState.locale) {
                localeChangeHandle(state.locale);
            }
        });

        return () => {
            unsubscribe();
        };
    }, []);

    return (
        <AntdConfigProvider locale={config.antdLocale} theme={config.antdTheme} componentSize="large">
            {msgContext}
            <I18nextProvider i18n={i18n}>
                <Matomo 
                    siteId={matomoConfig.siteId}
                    trackerUrl={matomoConfig.baseUrl}
                    scriptUrl={matomoConfig.scriptUrl}
                    enableDebug={matomoConfig.enableDebug}
                />
                <FullSpin />
                <MatomoProvider>
                    <Router />
                </MatomoProvider>
            </I18nextProvider>
        </AntdConfigProvider>
    );
};

export default App;
