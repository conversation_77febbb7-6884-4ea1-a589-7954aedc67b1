/**
 * URL Store utility for tracking previous URLs in Matomo analytics
 * 
 * This utility manages the current and previous URL state to enable
 * tracking of user navigation patterns in analytics events.
 */

interface UrlState {
  currentUrl: string;
  previousUrl: string | null;
}

class UrlStore {
  private state: UrlState = {
    currentUrl: '',
    previousUrl: null
  };

  /**
   * Initialize the URL store with the current URL
   */
  initialize(): void {
    if (typeof window !== 'undefined') {
      this.state.currentUrl = window.location.href;
      this.state.previousUrl = null;
    }
  }

  /**
   * Update the URL state when navigation occurs
   * @param newUrl - The new URL to set as current
   */
  updateUrl(newUrl: string): void {
    if (this.state.currentUrl !== newUrl) {
      this.state.previousUrl = this.state.currentUrl;
      this.state.currentUrl = newUrl;
    }
  }

  /**
   * Get the current URL
   * @returns Current URL
   */
  getCurrentUrl(): string {
    return this.state.currentUrl;
  }

  /**
   * Get the previous URL
   * @returns Previous URL or null if no previous URL exists
   */
  getPreviousUrl(): string | null {
    return this.state.previousUrl;
  }

  /**
   * Get both current and previous URLs
   * @returns Object containing current and previous URLs
   */
  getUrlState(): UrlState {
    return { ...this.state };
  }

  /**
   * Reset the URL state
   */
  reset(): void {
    this.state = {
      currentUrl: typeof window !== 'undefined' ? window.location.href : '',
      previousUrl: null
    };
  }
}

// Create a singleton instance
const urlStore = new UrlStore();

export default urlStore;

/**
 * Hook to automatically track URL changes
 * Call this in your app's router or main component
 */
export const initializeUrlTracking = (): void => {
  if (typeof window !== 'undefined') {
    urlStore.initialize();
    
    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', () => {
      urlStore.updateUrl(window.location.href);
    });
    
    // Override pushState and replaceState to track programmatic navigation
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      urlStore.updateUrl(window.location.href);
    };
    
    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      urlStore.updateUrl(window.location.href);
    };
  }
}; 