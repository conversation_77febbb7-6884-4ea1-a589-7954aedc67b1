/**
 * Form validation patterns and utilities for reuse across the application.
 * This file contains common regex patterns and validation functions.
 */

/**
 * Regex pattern that allows only English characters and common symbols.
 * Useful for input fields that should only accept English text and punctuation.
 */
export const ENGLISH_CHARS_AND_SYMBOLS_PATTERN = /^[A-Za-z0-9\s.,\-\(\)'"\_&@!?+=:;#$%^*\[\]{}<>~`\\/’]*$/;


/**
 * Regex pattern for validating decimal numbers with up to 2 decimal places.
 */
export const DECIMAL_NUMBER_PATTERN = /^\d+(\.\d{0,2})?$/;

/**
 * Regex pattern for validating SWIFT codes.
 */
export const SWIFT_CODE_PATTERN = /^[a-zA-Z0-9]*$/; 