/**
 * Matomo tracking utilities
 * 
 * This file provides helper functions to track events, goals, and other metrics
 * using Matomo analytics. Import these functions to add tracking to components.
 */

import urlStore from './urlStore';
import { MATOMO_CONFIG } from './matomoConfig';

// Type definition for user info stored in localStorage
interface StoredUserInfo {
  id: string | number;
  [key: string]: any;
}

/**
 * Helper function to set previous URL as custom dimension before tracking
 * and clear it after tracking to avoid affecting subsequent actions
 */
const withPreviousUrl = (trackingCallback: () => void): void => {
  if (!window._paq) return;
  
  // Get the previous URL from the URL store
  const previousUrl = urlStore.getPreviousUrl();
  
  // Set previous URL as a custom dimension if available
  if (previousUrl) {
    window._paq.push(['setCustomDimension', MATOMO_CONFIG.PREVIOUS_URL_DIMENSION_ID, previousUrl]);
  }
  
  // Execute the tracking action
  trackingCallback();
  
  // Clear the custom dimension after tracking to avoid affecting subsequent actions
  if (previousUrl) {
    window._paq.push(['deleteCustomDimension', MATOMO_CONFIG.PREVIOUS_URL_DIMENSION_ID]);
  }
};

/**
 * Track a custom event in Matomo
 * @param category - Event category (e.g., 'User', 'Navigation')
 * @param action - Event action (e.g., 'Click', 'Submit')
 * @param name - Optional event name for more specificity
 * @param value - Optional numeric value associated with the event
 */
export const trackEvent = (
  category: string,
  action: string,
  name?: string,
  value?: number
): void => {
  withPreviousUrl(() => {
    window._paq.push(['trackEvent', category, action, name, value]);
  });
};

/**
 * Track a site search in Matomo
 * @param keyword - The search term
 * @param category - Optional search category
 * @param resultsCount - Optional number of search results
 */
export const trackSiteSearch = (
  keyword: string,
  category?: string,
  resultsCount?: number
): void => {
  withPreviousUrl(() => {
    window._paq.push(['trackSiteSearch', keyword, category, resultsCount]);
  });
};

/**
 * Track a goal conversion in Matomo
 * @param goalId - The ID of the goal to track
 * @param conversionValue - Optional monetary value of the conversion
 */
export const trackGoal = (goalId: number, conversionValue?: number): void => {
  withPreviousUrl(() => {
    window._paq.push(['trackGoal', goalId, conversionValue]);
  });
};

/**
 * Track a page view manually
 * @param customTitle - Optional custom page title
 * @param customUrl - Optional custom URL (defaults to current URL)
 */
export const trackPageView = (customTitle?: string, customUrl?: string): void => {
  withPreviousUrl(() => {
    // Set custom URL if provided
    if (customUrl) {
      window._paq.push(['setCustomUrl', customUrl]);
    }
    
    // Set document title if provided
    if (customTitle) {
      window._paq.push(['setDocumentTitle', customTitle]);
    } else {
      window._paq.push(['setDocumentTitle', document.title]);
    }
    
    // Track the page view
    window._paq.push(['trackPageView']);
  });
};

/**
 * Set a custom dimension in Matomo
 * Note: This does NOT automatically include previous URL since it's typically used for setting context
 * @param dimensionId - The ID of the custom dimension
 * @param dimensionValue - The value to set for the dimension
 */
export const setCustomDimension = (
  dimensionId: number,
  dimensionValue: string
): void => {
  if (window._paq) {
    window._paq.push(['setCustomDimension', dimensionId, dimensionValue]);
  }
};

/**
 * Track an event with additional custom dimensions beyond the previous URL
 * This allows you to set multiple custom dimensions for a single event
 * @param category - Event category
 * @param action - Event action  
 * @param name - Optional event name
 * @param value - Optional numeric value
 * @param customDimensions - Object with dimensionId: value pairs for additional dimensions
 */
export const trackEventWithCustomDimensions = (
  category: string,
  action: string,
  name?: string,
  value?: number,
  customDimensions?: Record<number, string>
): void => {
  withPreviousUrl(() => {
    // Set additional custom dimensions if provided
    if (customDimensions) {
      Object.entries(customDimensions).forEach(([dimensionId, dimensionValue]) => {
        window._paq.push(['setCustomDimension', parseInt(dimensionId), dimensionValue]);
      });
    }
    
    // Track the event
    window._paq.push(['trackEvent', category, action, name, value]);
    
    // Clear additional custom dimensions after tracking
    if (customDimensions) {
      Object.keys(customDimensions).forEach((dimensionId) => {
        window._paq.push(['deleteCustomDimension', parseInt(dimensionId)]);
      });
    }
  });
};

/**
 * Set user ID for cross-device user tracking
 * @param userId - Unique, non-empty string that identifies the user
 */
export const setUserId = (userId: string): void => {
  if (!userId || typeof userId !== 'string') {
    console.warn('Matomo: Invalid user ID provided. User ID must be a non-empty string.');
    return;
  }
  
  if (window._paq) {
    window._paq.push(['setUserId', userId]);
    console.debug('Matomo: User ID set to:', userId);
  } else {
    console.warn('Matomo: _paq not available. User ID not set.');
  }
};

/**
 * Reset the user ID (for logouts)
 */
export const resetUserId = (): void => {
  if (window._paq) {
    window._paq.push(['resetUserId']);
    console.debug('Matomo: User ID reset');
  } else {
    console.warn('Matomo: _paq not available. User ID not reset.');
  }
};

/**
 * Get current user ID from localStorage
 * Only returns user ID if user is actually logged in (has valid access token)
 * @returns Current user ID or null if not available or user is not logged in
 */
export const getCurrentUserId = (): string | null => {
  try {
    // Check if user is actually logged in by verifying access token exists
    const accessToken = localStorage.getItem('accessToken');
    if (!accessToken) {
      return null; // User is not logged in
    }
    
    const userInfoString = localStorage.getItem('userInfo');
    if (userInfoString) {
      const userInfo: StoredUserInfo = JSON.parse(userInfoString);
      return userInfo?.id ? String(userInfo.id) : null;
    }
    return null;
  } catch (error) {
    console.error('Matomo: Error retrieving current user ID:', error);
    return null;
  }
};

/**
 * Initialize user ID from localStorage if user is logged in
 * This should be called on app startup
 */
export const initializeUserId = (): void => {
  try {
    const userInfoString = localStorage.getItem('userInfo');
    const accessToken = localStorage.getItem('accessToken');
    
    // Only set user ID if both userInfo and accessToken exist (user is logged in)
    if (userInfoString && accessToken) {
      const userInfo: StoredUserInfo = JSON.parse(userInfoString);
      if (userInfo?.id) {
        setUserId(String(userInfo.id));
      }
    }
  } catch (error) {
    console.error('Matomo: Error initializing user ID:', error);
  }
};

/**
 * Send all currently queued tracking calls to the Matomo server
 */
export const forceTrackingDispatch = (): void => {
  if (window._paq) {
    window._paq.push(['ping']);
  }
};

/**
 * Get current visitor ID for debugging purposes
 * @returns Current visitor ID or null if not available
 */
export const getCurrentVisitorId = (): string | null => {
  if (window._paq) {
    try {
      // Get visitor ID from Matomo
      let visitorId: string | null = null;
      window._paq.push([function(this: any) {
        visitorId = this.getVisitorId();
      }]);
      return visitorId;
    } catch (error) {
      console.error('Matomo: Error retrieving visitor ID:', error);
      return null;
    }
  }
  return null;
};

/**
 * Log current visitor and user IDs for debugging
 */
export const logMatomoIds = (): void => {
  if (window._paq) {
    window._paq.push([function(this: any) {
      console.log('Matomo Debug - Visitor ID:', this.getVisitorId());
      console.log('Matomo Debug - User ID:', getCurrentUserId());
    }]);
  }
}; 