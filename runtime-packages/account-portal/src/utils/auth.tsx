import {useCommonStore} from "@/store/common.ts";
import {Navigate, Outlet, useLocation} from "react-router-dom";
import {getRedirectPath} from "@/utils/login.ts";
import LoanNoticeBanner from "@/components/LoanNoticeBanner";
import Cookies from "js-cookie";
import {GetUserInfoByAccessToken, Login, getOauthShopList} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper.ts";
import {FC, useEffect, useState} from "react";
import { Spin } from "antd";


export const AuthGuard: React.FC = () => {
    const user = useCommonStore((s) => s.userInfo);
    const location = useLocation();

    if (user) {
        return (
            <Navigate
                to={`${getRedirectPath(user)}${location.search}`}
                replace
            />
        );
    }

    return <Outlet/>;
};


export const XDJGuard: React.FC = () => {
    const user = useCommonStore((s) => s.userInfo);
    const {pathname, search} = useLocation();
    const notifications = useCommonStore((s) => s.notificationList);
    if (!user) {
        return <Navigate to="/credit/hook" replace/>;
    } else {
        if (pathname === "/credit/hook") {
            return <Navigate to={`/credit/hook/home${search}`} replace/>;
        }
    }
    return (
        <>
            {notifications && notifications.length > 0 && <LoanNoticeBanner notices={notifications}/>}
            <Outlet/>
        </>

    );
};

export const RegisterGuard: FC = () => {
    const user = useCommonStore((s) => s.userInfo);
    const [loading, setLoading] = useState(true);
    const [hasOAuthShops, setHasOAuthShops] = useState(false);

    useEffect(() => {
        setLoading(true);
        const checkOAuthShops = async () => {
            try {
                const res = await getOauthShopList({});
                
                if (res.code === 0 && res.data && (res.data as any).shop && (res.data as any).shop.length > 0) {
                    setHasOAuthShops(true);
                } else {
                    setHasOAuthShops(false);
                }
            } catch (error) {
                console.error("Error checking OAuth shops:", error);
                setHasOAuthShops(false);
            } finally {
                setLoading(false);
            }
        };

        if (user) {
            checkOAuthShops();
        } else {
            setLoading(false);
        }
    }, [user]);

    if (loading) {
        return (
            <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin />
            </div>
        );
    }

    if (!user) {
        return <Navigate to="/" replace />;
    }

    if (!hasOAuthShops) {
        appHelper.msgApi.error("没有权限");
        return <Navigate to="/credit/hook/home" replace />;
    }

    return <Outlet />;
};


export const CreditApprovalAutomationLayoutGuard: React.FC = () => {
    const user = useCommonStore((s) => s.userInfo);
    const {pathname, search} = useLocation();
    const notifications = useCommonStore((s) => s.notificationList);

    if (!user) {
        return <Navigate to="/credit/underwriting" replace/>;
    } else {
        if (pathname === "/credit/underwriting") {
            return <Navigate to={`/credit/underwriting/home${search}`} replace/>;
        }
    }
    return (<>
        {notifications && notifications.length > 0 && <LoanNoticeBanner notices={notifications}/>}
        <Outlet/>
    </>);
};


export const UnauthGuard: FC = () => {
    const user = useCommonStore((s) => s.userInfo);
    const {search} = useLocation();
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const accessToken = Cookies.get("AccessToken");
        console.log(accessToken, "accessTokenaccessToken")
        console.log(Cookies, "CookiesCookiesCookies")
        if (!accessToken) {
            setLoading(false);
            return;
        }

        (async () => {
            try {
                const res = await GetUserInfoByAccessToken({accessToken});
                console.log(res.data, "res.data")
                if (res.message === "success" && res.data?.user) {
                    appHelper.msgApi.success("登录成功");
                    appHelper.setAccessInfo({accessToken: res.data.token});
                    appHelper.setUserInfo(res.data.user);
                } else {
                    appHelper.msgApi.error(res.message === 'success' ? "登录失败" : res.message);
                }
            } catch (err) {
                appHelper.msgApi.error("网络错误，请重试");
            } finally {
                Cookies.remove("AccessToken", { path: "/", domain: "fundpark.com" });
                console.log(Cookies.get("AccessToken"), "token after remove")
                setLoading(false);
            }
        })();
    }, []);

    if (loading) {
        return (
            <div style={{ textAlign: 'center', padding: '40px' }}>
                <Spin />
            </div>
        );
    }

    if (user) {
        return <Navigate to={`${getRedirectPath(user)}${search}`} replace/>;
    }

    return <Outlet/>;
};


