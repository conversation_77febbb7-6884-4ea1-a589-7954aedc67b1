/**
 * Test utility to verify Matomo user ID tracking behavior during logout
 * 
 * This file provides functions to test and debug Matomo user ID tracking,
 * especially to ensure that user IDs are properly reset during logout.
 * 
 * Usage in browser console:
 * import { testMatomoLogout } from '@/utils/testMatomoLogout';
 * testMatomoLogout();
 */

import { getCurrentUserId, resetUserId, setUserId } from './matomoTracking';
import { useCommonStore } from '@/store/common';

/**
 * Test the Matomo logout behavior
 * This function simulates a logout and verifies that user ID tracking is properly reset
 */
export const testMatomoLogout = () => {
  console.log('=== Testing Matomo Logout Behavior ===');
  
  // Step 1: Check initial state
  console.log('1. Initial state:');
  console.log('   - Access Token:', localStorage.getItem('accessToken') ? 'Present' : 'Not present');
  console.log('   - User Info:', localStorage.getItem('userInfo') ? 'Present' : 'Not present');
  console.log('   - Current User ID from tracking:', getCurrentUserId());
  
  // Step 2: Simulate setting a user ID (like during login)
  console.log('\n2. Simulating login with user ID 1013:');
  localStorage.setItem('accessToken', 'test-token');
  localStorage.setItem('userInfo', JSON.stringify({ id: 1013, email: '<EMAIL>' }));
  setUserId('1013');
  console.log('   - Access Token:', localStorage.getItem('accessToken') ? 'Present' : 'Not present');
  console.log('   - User Info:', localStorage.getItem('userInfo') ? 'Present' : 'Not present');
  console.log('   - Current User ID from tracking:', getCurrentUserId());
  
  // Step 3: Simulate logout
  console.log('\n3. Simulating logout:');
  const store = useCommonStore.getState();
  store.logout();
  console.log('   - Access Token after logout:', localStorage.getItem('accessToken') ? 'Present' : 'Not present');
  console.log('   - User Info after logout:', localStorage.getItem('userInfo') ? 'Present' : 'Not present');
  console.log('   - Current User ID from tracking after logout:', getCurrentUserId());
  
  // Step 4: Test tracking functions after logout
  console.log('\n4. Testing tracking functions after logout:');
  console.log('   - getCurrentUserId() should return null:', getCurrentUserId());
  
  // Step 5: Test edge case - userInfo exists but no access token
  console.log('\n5. Testing edge case - userInfo without access token:');
  localStorage.setItem('userInfo', JSON.stringify({ id: 1013, email: '<EMAIL>' }));
  // Don't set access token
  console.log('   - Access Token:', localStorage.getItem('accessToken') ? 'Present' : 'Not present');
  console.log('   - User Info:', localStorage.getItem('userInfo') ? 'Present' : 'Not present');
  console.log('   - Current User ID from tracking (should be null):', getCurrentUserId());
  
  // Clean up
  localStorage.removeItem('userInfo');
  localStorage.removeItem('accessToken');
  
  console.log('\n=== Test Complete ===');
};

/**
 * Monitor Matomo user ID changes
 * This function logs whenever the Matomo user ID changes
 */
export const monitorMatomoUserID = () => {
  if (!window._paq) {
    console.warn('Matomo not initialized. Cannot monitor user ID changes.');
    return;
  }
  
  let lastUserId: string | null = null;
  
  const checkUserId = () => {
    const currentUserId = getCurrentUserId();
    if (currentUserId !== lastUserId) {
      console.log(`Matomo User ID changed: ${lastUserId} -> ${currentUserId}`);
      lastUserId = currentUserId;
    }
  };
  
  // Check every second
  const interval = setInterval(checkUserId, 1000);
  
  console.log('Monitoring Matomo User ID changes. Call clearInterval(' + interval + ') to stop.');
  return interval;
};

/**
 * Debug current Matomo state
 * This function logs all relevant Matomo tracking information
 */
export const debugMatomoState = () => {
  console.log('=== Matomo Debug State ===');
  console.log('Access Token:', localStorage.getItem('accessToken') ? 'Present' : 'Not present');
  console.log('User Info:', localStorage.getItem('userInfo'));
  console.log('Current User ID from tracking:', getCurrentUserId());
  console.log('Store isLogin:', useCommonStore.getState().isLogin);
  console.log('Store userInfo:', useCommonStore.getState().userInfo);
  
  if (window._paq) {
    window._paq.push([function(this: any) {
      console.log('Matomo Visitor ID:', this.getVisitorId());
      console.log('Matomo User ID (internal):', this.getUserId());
    }]);
  } else {
    console.log('Matomo not initialized');
  }
  console.log('=== End Debug State ===');
}; 