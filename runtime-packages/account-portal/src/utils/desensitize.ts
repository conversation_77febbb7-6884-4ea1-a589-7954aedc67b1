import { desensitize } from "@fundpark/ui-utils";

/**
 * 邮箱脱敏 abc***@***.com
 * @param {string} email - 原始字符串
 * @returns {string}
 */
export function desensitizeEmail(email?: string): string {
    if (typeof email !== "string") return "";

    const [localPart, domainPart] = email.split("@");
    const maskedLocal = localPart.slice(0, localPart.length - 3) + "***";
    const maskedDomain = "***" + domainPart.slice(3);

    return `${maskedLocal}@${maskedDomain}`;
}

/**
 * 中国大陆手机号脱敏 138*****234
 * @param {string} phoneNumber - 原始字符串
 * @returns {string}
 */
export function desensitizeCNMobilePhone(phoneNumber: string): string {
    return desensitize(phoneNumber, 3, 4);
}

/**
 * 中国香港手机号/海外脱敏 55*****55
 * @param {string} phoneNumber - 原始字符串
 * @returns {string}
 */
export function desensitizeHkMobilePhone(phoneNumber: string): string {
    return desensitize(phoneNumber, 2, 2);
}

/**
 * 中国身份证号码脱敏 11011*********1234
 * @param {string} phoneNumber - 原始字符串
 * @returns {string}
 */
export function desensitizeCNIdType(idNumber: string): string {
    return desensitize(idNumber, 5, 4);
}

/**
 * 中国香港证件号码脱敏/海外数据脱敏 5*****5(2)
 * @param {string} phoneNumber - 原始字符串
 * @returns {string}
 */
export function desensitizeHKIdType(idNumber: string): string {
    return desensitize(idNumber, 2, 4);
}

export function desensitizeUSMobilePhone(phoneNumber: string): string {
    return desensitize(phoneNumber, 3, 3);
}
