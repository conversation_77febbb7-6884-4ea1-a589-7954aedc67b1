export const formatNumberToFraction = (
    amount: number | null | undefined
): string => {
    if (amount === null || amount === undefined) {
        return "—";
    }

    if (amount == 0){
        return "0.00";
    }

    // Truncate the number to two decimal places
    const factor = 100; // For 2 decimal places
    const truncatedNum = Math.trunc(amount * factor) / factor;

    // Convert to fixed-point notation and split into whole and decimal parts
    const [whole, decimal] = truncatedNum.toString().split('.');

    // Add thousand separators to the whole part
    const withThousandSeparators = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    // Determine the formatted output based on decimal places
    if (decimal) {
        if (decimal.length >= 2) {
            return `${withThousandSeparators}.${decimal.slice(0, 2)}`;
        } else {
            return `${withThousandSeparators}.${decimal.replace(/0+$/, '')}`;
        }
    } else {
        return withThousandSeparators;
    }
};