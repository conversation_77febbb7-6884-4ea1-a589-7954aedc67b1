import { storePath } from '@fundpark/fp-api';

/**
 * Interface for position tracking parameters
 */
export interface PositionTrackingParams {
  /**
   * The path to store (e.g., '/home')
   * If not provided, it will try to use window.location.pathname
   */
  path?: string;
  
  /**
   * Status for the position (e.g., 'Home Page')
   * If not provided, it will be sent as empty string
   */
  status?: string;
}

/**
 * Stores the current position with the API
 * 
 * @param params Object with optional path and status
 * @returns Promise that resolves when the position is stored
 * 
 * @example
 * // Basic usage with current path
 * import { storeCurrentPosition } from '@/utils/positionTracker';
 * 
 * // Store current browser path with custom status
 * const handleButtonClick = () => {
 *   storeCurrentPosition({ status: 'Dashboard Page' });
 * };
 * 
 * // Store specific path with status
 * storeCurrentPosition({ path: '/dashboard', status: 'Dashboard Page' });
 * 
 * // Store specific path without status
 * storeCurrentPosition({ path: '/dashboard' });
 */
export const storeCurrentPosition = async (params: PositionTrackingParams = {}): Promise<void> => {
  try {
    // Use provided path or get current path from window location
    const path = params.path || (typeof window !== 'undefined' ? window.location.pathname : '');
    
    // Use provided status or empty string
    const status = params.status || '';
    
    await storePath({
      path,
      path_name: status
    });
    
    console.log('Position stored:', path);
  } catch (error) {
    console.error('Failed to store position:', error);
    throw error;
  }
}; 