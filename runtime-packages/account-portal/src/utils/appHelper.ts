import type {TypeO<PERSON>} from "antd/es/message/interface";
import {request} from "@fundpark/fp-api";
import {useCommonStore} from "@/store/common";
import type {AccessInfo} from "@/types/common/accessInfo";
import {UserInfo} from '@fundpark/fp-api/types/login.ts'
import eventBus from "@/event";
import {CompanyNotification} from '@fundpark/fp-api/types/notifications.ts'
import {useLimitStore} from "@/store/limits.ts";
import {useRedPocketStore} from "@/store/redpocket.ts";
import {usePaymentGatewayStore} from "@/store/platform/paymentGatewayStore.tsx";
import {usePlatformStore} from "@/store/platform/platformStore.tsx";
class AppHelper {
    constructor() {
        this._init();
    }

    private _msgApi: {
        info: TypeOpen;
        success: TypeOpen;
        error: TypeOpen;
        warning: TypeOpen;
        loading: TypeOpen;
    } = null as any;

    get msgApi() {
        return this._msgApi;
    }

    public setLocale(locale: string) {
        request.setLocale(locale as any);
    }

    public setAccessInfo(accessInfo: AccessInfo | null) {
        const store = useCommonStore.getState();
        store.setAccessToken(accessInfo);
        request.setToken(accessInfo?.accessToken || null);
    }

    public setUserInfo(userInfo: UserInfo | null) {
        const store = useCommonStore.getState();
        store.setUserInfo(userInfo);
    }

    public clearAccessInfo() {
        const store = useCommonStore.getState();
        const limitStore = useLimitStore.getState();
        const redPocketStore = useRedPocketStore.getState();
        const pgStore = usePaymentGatewayStore.getState();
        const platformStore = usePlatformStore.getState();
        store.logout();
        limitStore.clear();
        redPocketStore.clear();
        pgStore.clear();
        platformStore.clear();
    }

    public setMsgApi(message: typeof this._msgApi) {
        this._msgApi = message;
    }

    public showLoading() {
        eventBus.emit("toggleFullLoading", true);
    }

    public hideLoading() {
        setTimeout(() => {
            eventBus.emit("toggleFullLoading", false);
        }, 500);
    }

    public getAppHost(includeProtocol: boolean = false): string {
        if (typeof window === "undefined") {
            return process.env.REACT_APP_DEFAULT_HOST || "";
        }

        const {hostname, host, origin} = window.location;
        const isLocalhost =
            hostname === "localhost" ||
            hostname === "127.0.0.1" ||
            hostname === "[::1]";

        if (includeProtocol) {
            return origin;
        }

        return isLocalhost ? host : origin;
    }

    private _init() {
        const store = useCommonStore.getState();
        request.setToken(store?.accessToken || null);
    }
    
    public setNotifications(notificationList: CompanyNotification[] | null) {
        const store = useCommonStore.getState();
        store.setNotifications(notificationList);
    }
}

export default new AppHelper();
