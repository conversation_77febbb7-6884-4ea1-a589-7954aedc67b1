import { storePath } from '@fundpark/fp-api';

/**
 * Interface for path tracking parameters
 */
export interface PathTrackingParams {
  /**
   * The path to store (e.g., '/home')
   * If not provided, it will try to use window.location.pathname
   */
  path?: string;
  
  /**
   * Name for the path (e.g., 'Home Page')
   * If not provided, it will be sent as empty string
   */
  pathName?: string;
}

/**
 * Stores the current path with the API
 * 
 * @param params Object with optional path and pathName
 * @returns Promise that resolves when the path is stored
 * 
 * @example
 * // Basic usage with current path
 * import { storeCurrentPath } from '@/utils/pathTracker';
 * 
 * // Store current browser path with custom name
 * const handleButtonClick = () => {
 *   storeCurrentPath({ pathName: 'Dashboard Page' });
 * };
 * 
 * // Store specific path with name
 * storeCurrentPath({ path: '/dashboard', pathName: 'Dashboard Page' });
 * 
 * // Store specific path without name
 * storeCurrentPath({ path: '/dashboard' });
 */
export const storeCurrentPath = async (params: PathTrackingParams = {}): Promise<void> => {
  try {
    // Use provided path or get current path from window location
    const path = params.path || (typeof window !== 'undefined' ? window.location.pathname : '');
    
    // Use provided pathName or empty string
    const pathName = params.pathName || '';
    
    await storePath({
      path,
      path_name: pathName
    });
    
    console.log('Path stored:', path);
  } catch (error) {
    console.error('Failed to store path:', error);
    throw error;
  }
}; 