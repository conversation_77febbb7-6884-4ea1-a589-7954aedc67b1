import { PageType } from '@/types/common/general.type.ts';
import {PAGES} from "@/constants/general";

type PageKey = Exclude<PageType, undefined>;

export const wechatMap = {
  [PAGES.XDJ]:                        () => import('@/assets-new/images/wechat/hook.png'),
  [PAGES.CREDIT_APPROVAL_AUTOMATION]: () => import('@/assets-new/images/wechat/lite.png'),
  [PAGES.ADV_RED]:                    () => import('@/assets-new/images/wechat/adv-red.png'),
  [PAGES.ADV_BLUE]:                   () => import('@/assets-new/images/wechat/adv-blue.png'),

  default: () => import('@/assets-new/images/wechat/hook.png'),
  '':      () => import('@/assets-new/images/wechat/hook.png'),
} satisfies Record<PageKey | 'default', () => Promise<{ default: string }>>;
