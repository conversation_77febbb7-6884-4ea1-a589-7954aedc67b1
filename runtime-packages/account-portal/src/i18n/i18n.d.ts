import "i18next";
import { resources, defaultNS } from "./index";

declare module "i18next" {
    interface CustomTypeOptions {
        defaultNS: typeof defaultNS;
        resources: {
            translation: typeof resources["en_US"][typeof defaultNS] & {
                Http: {
                    requestFail: string;
                    requestError: string;
                }
            }
        }
    }
}
