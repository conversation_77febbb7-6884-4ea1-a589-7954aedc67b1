{"shop": {"bread1": "<PERSON><PERSON>", "bread2": "My e-commerce shops", "bread3": "Authorize Shop", "addAuthorize": "Authorize Shop", "title": "My E-Commerce Shops", "tab1": "Shop", "tab2": "Account", "delete": "Delete", "resultsOfSearch": "Here are the results of a search for", "searchDrawerTitle": "Select platform", "all": "All", "import": "Import", "export": "Export", "shop": {"search": {"shopName": "Input shop name", "platform": "Select platform", "shopId": "Input shop ID", "psp": "Select PSP", "authorizationStatus": "Select authorization status", "authorizationDate": "Authorization", "expireDate": "Expird"}, "list": {"shopName": "Shop name", "platform": "Platform", "shopId": "Shop ID", "psp": "PSP", "authorizationStatus": "Authorization status", "authorizationDate": "Authorization date", "expireDate": "Expire date", "migrate": "Migrate", "authorize": "Authorize", "expiringSoon": "Expiring soon"}, "status": {"authorized": "Authorized", "pending": "Pending", "unauthorized": "Unauthorized", "expired": "Expired", "normal": "Normal", "abnormal": "Abnormal"}, "step1": {"selectPlatform": "Select e-commerce platform", "plsInput": "Please enter the platform name", "viewMore": "View more"}, "step2": {"shopIndustry": "Shop industry", "psp": "Payment Service Provider", "shopName": "Shop name", "plsInputShopName": "Enter shop name for easier management", "selectAgain": "Select again", "complete": "Complete", "authorizeTips": "Redirect to {{platform}} portal for authorization", "authorizeContentP1": "After clicking \"<strong>Confirm</strong>,\" you will need to log in to your {{platform}} Seller Central account.", "authorizeContentP2": "Please authorize from the shop's usual IP environment to avoid association issues.", "authorizeContentP3": "Your current IP address: {{ip}}", "authorizationInProgress": "Shop authorization in progress", "authorizationInProgressDesc": "Please complete the shop authorization process in {{platform}} Seller Central."}, "deleteShopReminderTitle": "Delete shop authorization reminders", "deleteNoShopNameReminderContent": "Are you sure delete the shop?", "deleteShopReminderContent": "Are you sure delete the shop<strong>[{{name}}]</strong>?", "migrateTitle": "Migrate shop to another company", "migrateContent1": "Are you sure you want to migrate this shop to another company?", "migrateContent2": "This will move all corresponding data to the selected company.", "deleteSuccess": "Delete shop success", "migrateSuccess": "Migrate shop success", "noCurrentCompany": "No current company", "fromCompany": "From company", "toCompany": "To company", "noShopName": "No shop name", "noShopId": "No shop ID"}, "account": {"detail": "Detail", "delete": "Delete", "addSuccess": "Add account success", "editSuccess": "Edit account success", "deleteSuccess": "Delete account success", "search": {"accountLogin": "Input account login", "shopName": "Input shop name", "platform": "Select platform", "status": "Select status", "updateTime": "Select update time"}, "list": {"accountLogin": "Account login", "shopName": "Shop name", "platform": "Platform", "status": "Status", "updateTime": "Update time"}, "step2": {"currentSelectPlatform": "Currently selected e-Commerce Platform", "account": "Account", "shopName": "Shop name", "loginUrl": "Login URL", "accountLogin": "Account login", "password": "Password", "add": "+ Add", "plsReSelectPlatform": "Please re-select the e-Commerce Platform", "plsAddAccount": "Please add account"}, "complete": {"title": "Submission Successful!", "desc": "Thank you for your submission. Our relationship manager will be in touch with you soon.", "backToShop": "Back to my shop", "continuing": "Continuing authorization"}, "edit": "Edit", "accountDetail": "Account detail", "deleteAccountReminderTitle": "Delete account reminders", "deleteAccountReminderContent": "Are you sure delete the account<strong>[{{name}}]</strong>?"}}}