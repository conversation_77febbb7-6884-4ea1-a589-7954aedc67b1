{"facility": {"title": "Facility Detail", "breadcrumbs": {"home": "Home", "facilityDetail": "Facility Detail"}, "facilityInfo": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "availableLimit": "Available limit", "facilityLimit": "Facility limit", "watermark": "Watermark", "outstanding": "Outstanding", "pendingDrawdown": "Pending drawdown", "frozenLimit": "Frozen limit", "availableFunding": "Available funding", "availableBalanceNote": "*Available Balance = Min[Limit, <PERSON>mark] - Outstanding - Pending drawdown - Frozen limit", "statusLabels": {"active": "Active", "expired": "Expired", "closed": "Closed"}}, "productInfo": {"title": "Product Information", "productCode": "Product code", "productType": "Product type", "facilityId": "Facility ID", "effectiveDate": "Effective date", "nextReviewDate": "Next review date"}, "loanDetails": {"title": "<PERSON>an <PERSON>", "noData": "No loan details available", "loanStructureId": "Loan structure ID", "loanType": "Loan type", "maximumOutstanding": "Maximum outstanding", "maximumTenor": "Maximum tenor", "minimumInterestTenor": "Minimum interest tenor", "gracePeriod": "Grace period", "repaymentMethod": "Repayment method", "repaymentAllocationRule": "Repayment allocation rule", "minimumRepaymentAmount": "Minimum repayment amount", "overdueInterestEnabled": "Overdue interest enabled", "overdueInterestMultiplier": "Overdue interest multiplier", "interestPaymentTiming": "Interest payment timing", "yes": "Yes", "no": "No"}, "interestRate": {"interestRateType": "Interest rate type", "drawdownCurrency": "Drawdown currency", "benchmarkRate": "Benchmark rate", "interestRate": "Interest rate", "accrualBasis": "Accrual basis"}}}