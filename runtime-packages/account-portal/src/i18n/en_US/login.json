{"login": {"title": "Sign In", "email": "Email", "password": "Password", "loginBtn": "授权店铺，立即用款", "emailRequired": "Please enter your email", "passwordRequired": "Please enter your password", "forgotPassword": "Forgot your password", "noAccountYet": "No account yet?", "createNow": "Create now", "success": "Login successful", "invalidCredentials": "Invalid credentials", "genericError": "<PERSON><PERSON> failed", "captcha": {"title": "Security Verification", "error": "Get verification code error", "label": "Verification Code", "placeholder": "Please enter the verification code", "required": "Please enter the verification code", "tooManyAttempts": "You have requested more than 3 times today. Please enter the verification code to continue.", "submit": "Submit"}, "emailInvalid": "Please enter a valid email address", "mobile": "Mobile", "mobileInvalid": "Please enter a valid mobile number", "mobileRequired": "Please enter your mobile number", "mobilePlaceholder": "Please enter your mobile number", "userNotExist": "User does not exist", "accountNotInit": "Account Not Verified", "accountNotInitContent": "Please complete email verification in order to activate your account", "resendEmail": "Send Activation Email", "resendEmailSuccess": "Activation email sent. Please check your inbox to complete the verification.", "welcomeTo": "Welcome To"}, "navbar": {"profile": "Profile", "logout": "Logout"}, "initPassword": {"title": "Set Password", "password": "Password", "passwordRequired": "Please enter your password", "passwordMin": "Password must be at least 8 characters", "passwordPlaceholder": "Please enter your password", "confirmPassword": "Confirm Password", "confirmPasswordRequired": "Please enter your password again", "confirmPasswordMatch": "Passwords do not match", "confirmPasswordPlaceholder": "Please enter your password again", "saveBtn": "Set Password", "returnToLogin": "Return to login"}, "forgotPassword": {"title": "Forgot Password", "help": "Please enter the email you used to register. We will send you an email to reset your password.", "backToLogin": "< Return to login", "emailRequired": "Please enter your email", "codeSent": "Verification code sent to your email", "emailPlaceholder": "Please enter your email", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Please enter the verification code", "send": "Confirm Send", "emailInvalid": "Please enter a valid email address", "email": "Email", "noAccount": "No account yet?", "createNow": "Create now", "submit": "Reset Password", "newPassword": "New Password", "verificationCodeRequired": "Please enter the verification code", "mobileRequired": "Please enter your mobile number", "mobilePlaceholder": "Please enter your mobile number", "mobile": "Mobile", "resetSuccess": "Password reset successful", "userNotExist": "Login user does not exist", "genericError": "Reset password failed"}, "sendButton": {"send": "Send", "resend": "Resend", "seconds": "s"}, "signup": {"title": "Sign Up", "emailRequired": "Please enter your email", "phoneAreaCodeRequired": "Please select your phone area code", "phoneRequired": "Please enter your mobile number", "emailVerificationCodeSent": "Email verification code sent to your email", "phoneVerificationCodeSent": "Mobile verification code sent to your mobile", "phoneVerificationCodeFail": "Mobile verification code sending failed", "success": "Sign up successful", "agreementRequired": "Please accept the terms and conditions", "agreementBody": "I have already agreed", "emailInvalid": "Please enter a valid email address", "emailPlaceholder": "Please enter your email", "verificationCode": "Verification Code", "verificationCodePlaceholder": "Please enter the verification code", "send": "Send", "newPassword": "New Password", "verificationCodeRequired": "Please enter the verification code", "passwordRequired": "Please set your login password", "passwordMinLength": "Password must be at least 8 characters", "phonePlaceholder": "Please enter your mobile number", "phoneInvalid": "Please enter a valid mobile number", "passwordPlaceholder": "Please set your login password", "create": "Create", "password": "Password", "email": "Email", "phone": "Mobile", "confirmPassword": "Confirm Password", "confirmPasswordRequired": "Please enter your password again", "confirmPasswordMatch": "Passwords do not match", "confirmPasswordPlaceholder": "Please enter your password again", "passwordMismatch": "Passwords do not match", "createYour": "Create Your", "account": "Account", "readAndAgreed": "I have read and agreed to the", "termsOfUse": "Terms of Use", "termsOfPrivacy": "Privacy Policy", "termsOfFundparkXDJWaiveInterestPlan": "Fundpark XDJ Waive Interest Plan", "termsOfFundparkRevenueFinancingSubsidyPlan": "Fundpark Revenue Financing Subsidy Plan", "ofTermsAndConditions": "'s Terms and Conditions", "privacy": "Privacy", "and": "and", "dataPolicy": "Data Policy"}, "alreadyHaveAccount": {"alreadyHaveAccount": "Already have an account? ", "signIn": "Sign in"}, "validation": {"password": {"length": "Password length must be 8-16 characters", "format": "Password must contain uppercase and lowercase letters, numbers, and special characters"}}, "breadcrumb": {"home": "Home", "companyProfile": "Company Profile", "myProfile": "My Profile", "companyUser": "Company User", "bankPSPAccount": "Bank & PSP Account", "myCompany": "My Company", "onboarding": "Company Verification", "createOnboarding": "Create Company"}, "password": {"requirements": "Password must:", "lengthRequirement": "contains 8-16 characters", "complexityRequirement": "contains uppercase and lowercase letters, numbers, and special characters", "reminder": "Be sure to confirm and remember your password~"}}