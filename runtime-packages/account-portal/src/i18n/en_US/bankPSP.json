{"bankPSP": {"delete": "Delete", "edit": "Edit", "deleteAccountTips": "Are you sure to delete the account[<strong>{{account}}</strong>]?", "deleteReminders": "Delete reminders", "switchBtn": {"bank": "Bank Account", "psp": "PSP Account"}, "viewDetail": "View Detail", "bank": {"empty": "You haven't added your bank account yet", "add": "Add Bank Account", "addModalTitle": "Add Bank Account", "editModalTitle": "Edit Bank Account", "viewModalTitle": "View Bank Account", "addSuccess": "Bank account added successfully", "editSuccess": "Bank account edited successfully", "swiftCode": "SWIFT Code", "region": "Region", "currency": "<PERSON><PERSON><PERSON><PERSON>", "relationType": "Relation Type", "bankName": "Bank Name", "accountNumber": "Account Number", "accountName": "Account Name", "branch": "Branch", "address": "Address", "regionRequired": "Please select Country / Region", "currencyRequired": "Please select currency", "relationTypeRequired": "Please select relation type", "bankNameRequired": "Please input bank name", "accountNumberRequired": "Please input account number", "accountNameRequired": "Please input account name", "branchRequired": "Please input branch", "addressRequired": "Please input address", "swiftCodeInvalid": "Please input valid SWIFT Code", "view": {"verifiedStatus": "Verified Status", "authorizedStatus": "Authorized Status"}}, "psp": {"empty": "You haven't added your PSP account yet", "addModalTitle": "Add PSP Account", "editModalTitle": "Edit PSP Account", "viewModalTitle": "View PSP Account", "addSuccess": "PSP account added successfully", "editSuccess": "PSP account edited successfully", "region": "Region", "relationType": "Relation Type", "pspName": "PSP Platform Name (EN)", "accountNumber": "Account Number in PSP", "accountName": "Registered Company Name in PSP", "currency": "<PERSON><PERSON><PERSON><PERSON>", "add": "Add PSP Account", "regionRequired": "Please select Country / Region", "pspNameRequired": "Please select PSP platform name (EN)", "accountNumberRequired": "Please input account number in PSP", "relationTypeRequired": "Please select relation type", "accountNameRequired": "Please input registered company name in PSP", "currencyRequired": "Please select currency", "view": {"platformName": "Platform Name", "companyName": "Company Name", "accountNumber": "Account Number", "verifiedStatus": "Verified Status", "authorizedStatus": "Authorized Status"}}, "deleteSuccess": "Successfully deleted", "failedLoadAccountDetails": "Failed to load account details"}}