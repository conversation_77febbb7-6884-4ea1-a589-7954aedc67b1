import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { getLang } from "@fundpark/ui-utils";
import common_enUS from "./en_US/common.json";
import menu_enUS from "./en_US/menu.json";
import system_enUS from "./en_US/system.json";
import login_enUS from "./en_US/login.json";
import layout_enUS from "./en_US/layout.json";
import pb_enUS from "./en_US/pb.json";
import company_enUS from "./en_US/company.json";
import myProfile_enUS from "./en_US/myProfile.json";
import companyUser_enUS from "./en_US/companyUser.json";
import bankPSP_enUS from "./en_US/bankPSP.json";
import notifications_enUS from "./en_US/notifications.json";
import shop_enUS from "./en_US/shop.json";
import chargeOrder_enUS from "./en_US/chargeOrder.json";
import home_enUS from "./en_US/home.json";
import applicationRecords_enUS from "./en_US/applicationRecords.json";
import facility_enUS from "./en_US/facility.json";

import common_zhCN from "./zh_CN/common.json";
import menu_zhCN from "./zh_CN/menu.json";
import system_zhCN from "./zh_CN/system.json";
import login_zhCN from "./zh_CN/login.json";
import layout_zhCN from "./zh_CN/layout.json";
import pb_zhCN from "./zh_CN/pb.json";
import company_zhCN from "./zh_CN/company.json";
import myProfile_zhCN from "./zh_CN/myProfile.json";
import companyUser_zhCN from "./zh_CN/companyUser.json";
import bankPSP_zhCN from "./zh_CN/bankPSP.json";
import notifications_zhCN from "./zh_CN/notifications.json";
import shop_zhCN from "./zh_CN/shop.json";
import chargeOrder_zhCN from "./zh_CN/chargeOrder.json";
import home_zhCN from "./zh_CN/home.json";
import applicationRecords_zhCN from "./zh_CN/applicationRecords.json";
import facility_zhCN from "./zh_CN/facility.json";

import common_zhHK from "./zh_HK/common.json";
import menu_zhHK from "./zh_HK/menu.json";
import system_zhHK from "./zh_HK/system.json";
import login_zhHK from "./zh_HK/login.json";
import layout_zhHK from "./zh_HK/layout.json";
import pb_zhHK from "./zh_HK/pb.json";
import company_zhHK from "./zh_HK/company.json";
import myProfile_zhHK from "./zh_HK/myProfile.json";
import companyUser_zhHK from "./zh_HK/companyUser.json";
import bankPSP_zhHK from "./zh_HK/bankPSP.json";
import notifications_zhHK from "./zh_HK/notifications.json";
import shop_zhHK from "./zh_HK/shop.json";
import chargeOrder_zhHK from "./zh_HK/chargeOrder.json";
import home_zhHK from "./zh_HK/home.json";
import applicationRecords_zhHK from "./zh_HK/applicationRecords.json";
import facility_zhHK from "./zh_HK/facility.json";

export const defaultNS = "translation" as const;

export const resources = {
    en_US: {
        [defaultNS]: {
            ...common_enUS,
			...menu_enUS,
            ...system_enUS,
            ...login_enUS,
            ...layout_enUS,
            ...pb_enUS,
            ...company_enUS,
            ...myProfile_enUS,
            ...companyUser_enUS,
            ...bankPSP_enUS,
            ...notifications_enUS,
			...shop_enUS,
            ...chargeOrder_enUS,
           	...home_enUS,
            ...applicationRecords_enUS,
            ...facility_enUS
        }
    },
    zh_CN: {
        [defaultNS]: {
            ...common_zhCN,
			...menu_zhCN,
            ...system_zhCN,
            ...login_zhCN,
            ...layout_zhCN,
            ...pb_zhCN,
            ...company_zhCN,
            ...myProfile_zhCN,
            ...companyUser_zhCN,
            ...bankPSP_zhCN,
            ...notifications_zhCN,
			...shop_zhCN,
            ...chargeOrder_zhCN,
			...home_zhCN,
            ...applicationRecords_zhCN,
            ...facility_zhCN
        }
    },
    zh_HK: {
        [defaultNS]: {
            ...common_zhHK,
			...menu_zhHK,
            ...system_zhHK,
            ...login_zhHK,
            ...layout_zhHK,
            ...pb_zhHK,
            ...company_zhHK,
            ...myProfile_zhHK,
            ...companyUser_zhHK,
            ...bankPSP_zhHK,
            ...notifications_zhHK,
			...shop_zhHK,
            ...chargeOrder_zhHK,
			...home_zhHK,
            ...applicationRecords_zhHK,
            ...facility_zhHK
        }
    }
} as const;

i18n.use(initReactI18next).init({
    defaultNS,
    resources,
    lng: "zh_CN",
    fallbackLng: "zh_CN",
    debug: false,
    interpolation: {
        escapeValue: false
    },
    react: {
        transKeepBasicHtmlNodesFor: ["br", "strong"]
    }
});

export default i18n;
