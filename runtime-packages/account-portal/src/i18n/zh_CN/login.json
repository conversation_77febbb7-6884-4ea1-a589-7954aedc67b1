{"login": {"title": "登录", "email": "邮箱", "password": "密码", "loginBtn": "授权店铺，立即用款", "emailRequired": "请输入您的邮箱", "passwordRequired": "请输入您的密码", "forgotPassword": "忘记密码", "noAccountYet": "还没有账号？", "createNow": "立即创建", "success": "登录成功", "invalidCredentials": "无效的凭据", "genericError": "登录失败", "captcha": {"title": "安全验证", "error": "获取验证码错误", "label": "验证码", "placeholder": "请输入验证码", "required": "请输入验证码", "tooManyAttempts": "您今天已请求超过3次。请输入验证码以继续。", "submit": "提交"}, "emailInvalid": "请输入有效的电子邮件地址", "mobile": "手机号码", "mobileInvalid": "请输入有效的手机号码", "mobileRequired": "请输入您的手机号码", "mobilePlaceholder": "请输入您的手机号码", "userNotExist": "用户不存在", "accountNotInit": "账户未验证", "accountNotInitContent": "请完成电子邮件验证以激活您的账户", "resendEmail": "发送激活邮件", "resendEmailSuccess": "激活邮件已发送。请检查您的收件箱以完成验证。", "welcomeTo": "欢迎来到"}, "navbar": {"profile": "我的资料", "logout": "退出登录"}, "initPassword": {"title": "设置密码", "password": "密码", "passwordRequired": "请输入您的密码", "passwordMin": "密码必须至少8个字符", "passwordPlaceholder": "请输入您的密码", "confirmPassword": "确认密码", "confirmPasswordRequired": "请再次输入您的密码", "confirmPasswordMatch": "密码不匹配", "confirmPasswordPlaceholder": "请再次输入您的密码", "saveBtn": "设置密码", "returnToLogin": "返回登录"}, "forgotPassword": {"title": "忘記密码", "help": "请输入您注册的电子邮件。我们会发送电子邮件给您以重设密码。", "backToLogin": "< 返回登录", "emailRequired": "请输入您的电子邮件", "codeSent": "重设密码的连接已发送至您的电子邮件", "emailPlaceholder": "请输入您的电子邮件", "verificationCode": "验证码", "verificationCodePlaceholder": "请输入验证码", "send": "确认发送", "emailInvalid": "请输入有效的电子邮件地址", "email": "电子邮件", "noAccount": "还没有账号？", "createNow": "立即创建", "submit": "重置密码", "newPassword": "新密码", "verificationCodeRequired": "请输入验证码", "mobileRequired": "请输入您的手机号码", "mobilePlaceholder": "请输入您的手机号码", "mobile": "手机号码", "resetSuccess": "密码重置成功", "userNotExist": "登录用户不存在", "genericError": "重置密码失败"}, "sendButton": {"send": "发送", "resend": "重新发送", "seconds": "秒"}, "signup": {"title": "注册", "emailRequired": "请输入您的电子邮件", "phoneAreaCodeRequired": "请选择您的电话区号", "phoneRequired": "请输入您的手机号码", "emailVerificationCodeSent": "电子邮件验证码已发送至您的电子邮件", "phoneVerificationCodeSent": "手机验证码已发送至您的手机", "phoneVerificationCodeFail": "手机验证码发送失败", "success": "注册成功", "agreementRequired": "请接受条款和条件", "agreementBody": "我已经同意", "emailInvalid": "请输入有效的电子邮件地址", "emailPlaceholder": "请输入您的电子邮件", "verificationCode": "验证码", "verificationCodePlaceholder": "请输入验证码", "send": "发送", "newPassword": "新密码", "verificationCodeRequired": "请输入验证码", "passwordRequired": "请设置您的登录密码", "passwordMinLength": "密码必须至少8个字符, 至少1个字母 + 1个数字", "phonePlaceholder": "请输入您的手机号码", "phoneInvalid": "请输入有效的手机号码", "passwordPlaceholder": "请设置您的登录密码", "create": "创建", "password": "密码", "email": "电子邮件", "phone": "手机号码", "confirmPassword": "确认密码", "confirmPasswordRequired": "请再次输入您的密码", "confirmPasswordMatch": "密码不匹配", "confirmPasswordPlaceholder": "请再次输入您的密码", "passwordMismatch": "密码不匹配", "createYour": "创建您的", "account": "账户", "readAndAgreed": "我已阅读并同意", "termsOfUse": "使用条款", "termsOfPrivacy": "隐私政策", "termsOfFundparkXDJWaiveInterestPlan": "丰泊⼩店⾦七天免息计划", "termsOfFundparkRevenueFinancingSubsidyPlan": "丰泊丰收融补贴计划", "ofTermsAndConditions": "的条款及细则", "privacy": "隐私", "and": "及", "dataPolicy": "数据政策"}, "alreadyHaveAccount": {"alreadyHaveAccount": "已经有账号了？", "signIn": "登录"}, "validation": {"password": {"length": "密码长度必须为8-16个字符", "format": "密码必须包含大小写字母、数字和特殊字符"}}, "breadcrumb": {"home": "首页", "companyProfile": "公司资料", "myProfile": "我的资料", "companyUser": "公司用户", "bankPSPAccount": "银行&支付平台账户", "myCompany": "我的公司", "onboarding": "公司验证", "createOnboarding": "创建公司"}, "password": {"requirements": "密码必须：", "lengthRequirement": "包含8-16个字符", "complexityRequirement": "包含大小写字母、数字和特殊字符", "reminder": "请确认并记住您的密码~"}}