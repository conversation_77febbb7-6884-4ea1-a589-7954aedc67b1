// import { GetKYCCommonData } from "@fundpark/fp-api";
// import { kycCommonDataHandle } from "@/pages/company/onboarding/business/common";
// import { useBusinessDataStore } from "@/store/businessData";

/**
 * 登录态有效下，执行初始化操作
 */
export function onAliveReady() {
    return
    // GetKYCCommonData().then(res => {
    //     if (!res.success) {
    //         const commonKycData = kycCommonDataHandle(res.data || []);
    //         useBusinessDataStore.getState().update({
    //             commonKycData
    //         });
    //     }
    // });
}
