export type ChargeOrderStatus = 'Active' | 'Suspend' | 'Collected' | 'Partial Collected' | 'Voided';

export interface ChargeOrder {
  id: string;
  chargeOrderId: string;
  facilityId: string;
  type: string;
  feeRate?: string;
  status: ChargeOrderStatus;
  collectionMethod: string;
  totalOrderAmount: number;
  outstandingAmount: number;
  pendingAmount: number;
  receivedAmount: number;
  createdTime: string;
  subOrderCount?: number;
  subChargeList?: SubOrder[];
}

export interface SubOrder {
  id: string;
  subOrderId: string;
  drawdownId: string;
  collectionMethod: string;
  createdTime: string;
  totalOrderAmount: number;
  outstandingAmount: number;
  receivedAmount: number;
  pendingAmount: number;
  status: ChargeOrderStatus;
}

export interface ChargeOrderTableProps {
  chargeOrders: ChargeOrder[];
  isLoading: boolean;
  onViewDetails: (chargeOrderId: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  rowsPerPage: number;
  onRowsPerPageChange: (rows: number) => void;
}

export interface ChargeOrderRowProps {
  chargeOrder: ChargeOrder;
  onViewDetails: (chargeOrderId: string) => void;
}

export interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  rowsPerPage: number;
  onRowsPerPageChange: (rows: number) => void;
  totalItems: number;
  startItem: number;
  endItem: number;
}