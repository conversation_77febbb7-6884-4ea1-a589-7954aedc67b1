import React from 'react';
import { ChargeOrderTableProps } from './types';
import ChargeOrderRow from './ChargeOrderRow';
import styles from './index.module.scss';
import Pagination from '../pagination';

export const ChargeOrderTable: React.FC<ChargeOrderTableProps> = ({
  chargeOrders,
  isLoading,
  onViewDetails,
  currentPage,
  totalPages,
  onPageChange,
  rowsPerPage,
  onRowsPerPageChange
}) => {
  const startItem = (currentPage - 1) * rowsPerPage + 1;
  const endItem = Math.min(startItem + rowsPerPage - 1, chargeOrders.length);
  const totalItems = chargeOrders.length;

  // Handle page change with pageSize
  const handlePageChange = (page: number, pageSize: number) => {
    onPageChange(page);
    if (pageSize !== rowsPerPage) {
      onRowsPerPageChange(pageSize);
    }
  };

  return (
    <div>
      <div className={styles.tableContainer}>
        <table className={styles.chargeOrderTable}>
          <thead>
            <tr>
              <th>Charge order ID</th>
              <th>Collection method</th>
              <th>Total order amount</th>
              <th>Outstanding amount</th>
              <th>Pending amount</th>
              <th>Received amount</th>
              <th>Created time</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={8} style={{ textAlign: 'center', padding: '40px 0' }}>
                  Loading...
                </td>
              </tr>
            ) : chargeOrders.length === 0 ? (
              <tr>
                <td colSpan={8} style={{ textAlign: 'center', padding: '40px 0' }}>
                  No charge orders found
                </td>
              </tr>
            ) : (
              chargeOrders.map((chargeOrder) => (
                <ChargeOrderRow
                  key={chargeOrder.id}
                  chargeOrder={chargeOrder}
                  onViewDetails={onViewDetails}
                />
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className={styles.paginationWrapper}>
        <div className={styles.pageInfo}>
          {chargeOrders.length > 0
            ? `${startItem}-${endItem} of ${totalItems} items`
            : '0 items'}
        </div>

        <Pagination
          current={currentPage}
          pageSize={rowsPerPage}
          total={totalPages * rowsPerPage}
          onChange={handlePageChange}
        />

        <div>
          Show 
          <select
            value={rowsPerPage}
            onChange={(e) => onRowsPerPageChange(Number(e.target.value))}
            style={{ margin: '0 8px' }}
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          rows
        </div>
      </div>
    </div>
  );
};

export default ChargeOrderTable; 