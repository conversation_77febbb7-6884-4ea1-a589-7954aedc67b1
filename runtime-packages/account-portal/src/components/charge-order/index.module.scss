@use "@/assets/styles/variables.module.scss" as *;

.chargeOrderTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  
  th {
    background-color: $fp-bg-color;
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: $fp-text-primary-color;
    border-bottom: 1px solid $fp-border-color;
  }

  td {
    padding: 12px 16px;
    border-bottom: 1px solid $fp-border-color;
    color: $fp-text-secondary-color;
  }

  tbody tr {
    &:hover {
      background-color: $fp-divider-color;
    }
  }
}

.tableContainer {
  overflow-x: auto;
  max-width: 100%;
  border-radius: 8px;
  border: 1px solid $fp-border-color;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chargeOrderRow {
  transition: background-color 0.2s;
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;

  &.active {
    background-color: rgba(46, 204, 113, 0.15);
    color: #27ae60;
  }

  &.suspended {
    background-color: rgba(255, 152, 0, 0.15);
    color: #f39c12;
  }

  &.collected {
    background-color: rgba(52, 152, 219, 0.15);
    color: #2980b9;
  }

  &.partialCollected {
    background-color: rgba(142, 68, 173, 0.15);
    color: #8e44ad;
  }

  &.voided {
    background-color: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
  }
}

.paginationWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 16px;
}

.pageInfo {
  color: $fp-text-secondary-color;
  font-size: 0.9rem;
}

.viewDetailsLink {
  color: $fp-primary-color;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.amount {
  &.outstanding {
    color: $fp-error-color;
  }
} 