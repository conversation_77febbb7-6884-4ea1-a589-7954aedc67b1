import React from 'react';
import { ChargeOrderRowProps } from './types';
import styles from './index.module.scss';
import classNames from 'classnames';

export const ChargeOrderRow: React.FC<ChargeOrderRowProps> = ({
  chargeOrder,
  onViewDetails
}) => {
  const {
    chargeOrderId,
    facilityId,
    type,
    status,
    collectionMethod,
    totalOrderAmount,
    outstandingAmount,
    pendingAmount,
    receivedAmount,
    createdTime
  } = chargeOrder;

  const getStatusClassName = () => {
    switch (status) {
      case 'Active':
        return styles.active;
      case 'Suspend':
        return styles.suspend;
      case 'Collected':
        return styles.collected;
      case 'Partial Collected':
        return styles.partialCollected;
      case 'Voided':
        return styles.voided;
      default:
        return '';
    }
  };

  const formatCurrency = (amount: number) => {
    return `USD ${amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  return (
    <tr className={styles.chargeOrderRow}>
      <td>
        <div>{type}</div>
        <div className={classNames(styles.statusBadge, getStatusClassName())}>{status}</div>
        <div>charge order ID:{chargeOrderId}</div>
        <div>Facility ID:{facilityId}</div>
      </td>
      <td>{collectionMethod}</td>
      <td>{formatCurrency(totalOrderAmount)}</td>
      <td className={outstandingAmount > 0 ? styles.outstanding : ''}>
        {outstandingAmount > 0 ? formatCurrency(outstandingAmount) : '-'}
      </td>
      <td>{pendingAmount > 0 ? formatCurrency(pendingAmount) : '-'}</td>
      <td>{receivedAmount > 0 ? formatCurrency(receivedAmount) : '-'}</td>
      <td>{formatDate(createdTime)}</td>
      <td>
        <a
          href="#"
          className={styles.viewDetailsLink}
          onClick={(e) => {
            e.preventDefault();
            onViewDetails(chargeOrderId);
          }}
        >
          1 sub order &gt;
        </a>
      </td>
    </tr>
  );
};

export default ChargeOrderRow; 