import { useTranslation } from "react-i18next";
// import emailIcon from "@/assets/images/email-icon.png";
// import mobileIcon from "@/assets/images/mobile-icon.png";
// import emailIconInactive from "@/assets/images/email-icon-grey.png";
// import mobileIconInactive from "@/assets/images/mobile-icon-grey.png";
import { LoginMethodSwitcherProps } from "@/types/common/LoginMethodSwitcher/loginMethodSwitcher.types";
// import { useState } from "react";
import styles from "./index.module.scss";

const LoginMethodSwitcher: React.FC<LoginMethodSwitcherProps> = ({ loginMethod, onMethodChange }) => {
    const { t } = useTranslation();
    // const [hoveredMethod, setHoveredMethod] = useState<string | null>(null);

    return (
        <div className={styles.container}>
            <div
                className={`${styles.leftButton} ${loginMethod === "login" ? styles.active : styles.inactive}`}
                onClick={() => onMethodChange("login")}
                // onMouseEnter={() => setHoveredMethod("email")}
                // onMouseLeave={() => setHoveredMethod(null)}
            >
                {/*<div className={styles.iconContainer}>*/}
                {/*    <img*/}
                {/*        src={loginMethod === "email" || hoveredMethod === "email" ? emailIcon : emailIconInactive}*/}
                {/*        alt="Email"*/}
                {/*        className={styles.emailIcon}*/}
                {/*    />*/}
                {/*</div>*/}
                <span className={styles.methodText}>{t("login.title")}</span>
            </div>
            <div
                className={`${styles.rightButton} ${loginMethod === "signup" ? styles.active : styles.inactive}`}
                onClick={() => onMethodChange("signup")}
                // onMouseEnter={() => setHoveredMethod("mobile")}
                // onMouseLeave={() => setHoveredMethod(null)}
            >
                {/*<div className={styles.iconContainer}>*/}
                {/*    <img*/}
                {/*        src={loginMethod === "mobile" || hoveredMethod === "mobile" ? mobileIcon : mobileIconInactive}*/}
                {/*        alt="Mobile"*/}
                {/*        className={styles.mobileIcon}*/}
                {/*    />*/}
                {/*</div>*/}
                <span className={styles.methodText}>{t("signup.title")}</span>
            </div>
        </div>
    );
};

export default LoginMethodSwitcher;