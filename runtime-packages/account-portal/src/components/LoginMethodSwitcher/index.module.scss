.container {
  display: flex;
  width: 400px;
  height: 36px;
  margin-bottom: 32px;
  max-width: 100%; /* Ensure it doesn't overflow on smaller screens */
}

.methodButton {
  flex: 1;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid;
  transition: colors;
}

.leftButton {
  composes: methodButton;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}

.rightButton {
  composes: methodButton;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.active {
  border-color: black;
  background-color: #F6F7FA;
  color: black;
}

.inactive {
  border-color: #e5e7eb; /* gray-200 */
  color: #9E9EA3;
  
  &:hover {
    border-color: black;
    color: black;
  }
}

.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.emailIcon {
  width: 22px;
  height: 18px;
}

.mobileIcon {
  width: 16px;
  height: 22px;
}

.methodText {
  font-size: 14px;
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 480px) {
  .container {
    width: 100%;
    min-width: 280px;
  }
  
  .iconContainer {
    margin-right: 6px;
  }
  
  .methodText {
    font-size: 13px;
  }
}

@media (max-width: 360px) {
  .container {
    height: 36px;
  }
  
  .iconContainer {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
  
  .emailIcon {
    width: 18px;
    height: 15px;
  }
  
  .mobileIcon {
    width: 14px;
    height: 18px;
  }
  
  .methodText {
    font-size: 12px;
  }
}
