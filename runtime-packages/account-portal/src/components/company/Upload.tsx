import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Upload, Space, Image } from "antd";
import type { UploadProps, UploadFile } from "antd";
import { uploadFile as uploadFileApi, deleteSignerFile } from "@fundpark/fp-api";
import UploadIcon from "@/assets/icons/myCompany/upload.svg?react";
import FilePreview, { FilePreviewRef } from "@/components/common/FilePreview";
import appHelper from "@/utils/appHelper";
import "./upload.scss";

const { Dragger } = Upload;

interface ValueFile {
    url: string;
    fileName: string;
    size: number;
    file_id: number;
}

interface FileResponseData {
    file_id: number;
    file_path: string;
    file_name: string;
    file_size: string;
    file_mime: string;
    file_link: string;
}

const CommonUpload: React.FC<{
    value?: string;
    fileName?: string;
    onChange?: (value?: string) => void;
    onFileChange?: (value?: ValueFile) => void;
    disabled?: boolean;
    /** MB */
    maxFileSize?: number;
    allowedExtensions?: string;
    /**
     * TODO: not implemented yet
     */
    multiple?: boolean;
    title?: React.ReactNode;
    /** Whether to show the delete button */
    showDeleteBtn?: boolean;
    /** ID for the signer when deleting files */
    signerId?: string;
    /** Type of file for delete API */
    fileType?: 'front_id_file_id' | 'back_id_file_id' | 'front_file_id' | 'back_file_id';
}> = ({ 
    title, 
    maxFileSize = 20, 
    allowedExtensions, 
    multiple = false, 
    value, 
    fileName, 
    disabled = false, 
    onChange, 
    onFileChange,
    showDeleteBtn = true,
    signerId,
    fileType
}) => {
    const [files, setFiles] = useState<Array<UploadFile<any>>>([]);
    const [isDeleting, setIsDeleting] = useState<boolean>(false);
    const { t } = useTranslation();
    const previewRef = useRef<FilePreviewRef>(null);
    const hasFile = !multiple && value && files.length > 0;

    useEffect(() => {
        if (value) {
            setFiles([
                {
                    name: fileName || "",
                    status: "done",
                    uid: value,
                    url: value
                }
            ]);
        } else {
            setFiles([]);
        }
    }, [value, fileName]);

    const handleDelete = async () => {
        if (signerId && fileType) {
            try {
                setIsDeleting(true);
                // Create request payload with fileType key and empty string value
                const deletePayload = {
                    [fileType]: ""
                };
                
                // Call the delete API
                const response = await deleteSignerFile(signerId)(deletePayload);
                
                if (response.code === 0) {
                    // appHelper.msgApi.success(t("company.onboarding.upload.deleteSuccess"));
                    appHelper.msgApi.success("文件删除成功");
                    onChange?.();
                    onFileChange?.();
                    setFiles([]);
                } else {
                    // appHelper.msgApi.error(response.message || t("company.onboarding.upload.deleteFailed"));
                    appHelper.msgApi.error(response.message || "文件删除失败");
                }
            } catch (error) {
                console.error('Error deleting file:', error);
                // appHelper.msgApi.error(t("company.onboarding.upload.deleteFailed"));
                appHelper.msgApi.error("文件删除失败");
            } finally {
                setIsDeleting(false);
            }
        } else {
            // If there's no signerId or fileType, just clear the state without API call
            onChange?.();
            onFileChange?.();
            setFiles([]);
        }
    };

    const handlePreview = async () => {
        if (value && files.length > 0) {
            console.log('Previewing file:', { value, fileName: files[0].name });
            try {
                await previewRef.current!.open(value, files[0].name);
            } catch (error) {
                console.error('Error opening file preview:', error);
                // Fallback: try to open the file directly
                window.open(value, '_blank');
            }
        }
    };

    const uploadProps: UploadProps = {
        disabled: hasFile || !!disabled || isDeleting,
        multiple,
        accept: allowedExtensions
            ? allowedExtensions
                  .split(",")
                  .map(ext => `.${ext.trim()}`)
                  .join(",")
            : "*",
        fileList: multiple ? files : [],
        showUploadList: multiple,
        beforeUpload: file => {
            const extension = (file.name.split(".").pop() || "").toLowerCase();
            const validType = allowedExtensions
                ? allowedExtensions
                      .split(",")
                      .map(ext => ext.trim().toLowerCase())
                      .includes(extension)
                : true;
            const validSize = file.size <= maxFileSize * 1024 * 1024;

            if (!validType) {
                appHelper.msgApi.warning(
                    file.name + " " + t("company.onboarding.upload.invalidExtension", { allowedExtensions })
                );
                return Upload.LIST_IGNORE;
            }
            if (!validSize) {
                appHelper.msgApi.warning(
                    file.name + " " + t("company.onboarding.upload.exceedsMaxSize", { maxFileSize })
                );
                return Upload.LIST_IGNORE;
            }

            return true;
        },
        onChange: ({ file, fileList }) => {
            if (file.status === "removed") {
                onChange?.();
                onFileChange?.();
            }
            setFiles(fileList);
        },
        isImageUrl: () => false,
        onPreview: async file => {
            if (value) {
                previewRef.current!.open(value, file.name);
            }
        },
        customRequest: async options => {
            const { onError, onSuccess } = options;
            const file = options.file as File;
            try {
                // Create FormData to send file as a stream
                const formData = new FormData();
                formData.append('files', file); // Append the file as 'files' parameter
                formData.append('dst', 'XDJ'); // Set the destination to 'papers'
                
                // Using uploadFile API from drawdownForm.ts with FormData
                const response = await uploadFileApi(formData as any);
                
                if (response.code === 0) { // Code 0 indicates success according to API docs
                    appHelper.msgApi.success(file.name + " " + t("company.onboarding.upload.uploadSuccess"));
                    
                    // We need to access the nested file_link property
                    // Using a type assertion with 'unknown' as an intermediate step
                    const responseData = response.data as unknown as FileResponseData;
                    const fileLink = responseData.file_link;
                    const fileId = responseData.file_id;
                    
                    onSuccess?.(response.data, file);
                    onChange?.(fileLink);
                    onFileChange?.({
                        file_id: fileId,
                        url: fileLink,
                        fileName: file.name,
                        size: file.size
                    });
                } else {
                    onError?.(new Error(response.message));
                    // @ts-ignore
                    setFiles(pre => pre.filter(item => item.uid !== file.uid));
                }
            } catch (error) {
                onError?.(error as any);
                // @ts-ignore
                setFiles(pre => pre.filter(item => item.uid !== file.uid));
                throw error;
            }
        }
    };

    const renderSingleFilePreview = () => {
        if (!value || files.length === 0) return null;
        
        return (
            <div className="cp-company-upload-preview">
                <div className="cp-company-upload-preview-icon">
                    <Image 
                        src={value} 
                        width={60} 
                        height={45} 
                        alt={files[0].name}
                        style={{ objectFit: 'cover', borderRadius: '8px' }}
                        preview={false}
                    />
                </div>
                <div className="cp-company-upload-preview-filename">
                    {files[0].name}
                </div>
                <Space className="cp-company-upload-preview-actions">
                    {showDeleteBtn && !disabled && (
                        <span 
                            className={`cp-company-upload-preview-delete ${isDeleting ? 'cp-company-upload-preview-delete-disabled' : ''}`}
                            onClick={(e) => {
                                e.stopPropagation();
                                console.log('Deleting file:', { signerId, fileType });
                                if (!isDeleting) {
                                    handleDelete();
                                }
                            }} 
                        >
                            {/* {isDeleting ? t("common.deleting") : t("common.delete") || "删除"} */}
                            {isDeleting ? "删除中..." : "删除"}
                        </span>
                    )}
                    <span 
                        className="cp-company-upload-preview-view"
                        onClick={(e) => {
                            e.stopPropagation();
                            handlePreview();
                        }} 
                    >
                        {/* {t("common.view") || "查看"} */}
                        查看
                    </span>
                </Space>
            </div>
        );
    };

    return (
        <>
            <Dragger {...uploadProps} className={`cp-company-upload ${hasFile ? 'cp-company-upload-has-file' : ''}`}>
                {hasFile ? (
                    renderSingleFilePreview()
                ) : (
                    <div className="flex flex-col items-center">
                        <UploadIcon className="cp-company-upload-icon" />
                        {title ? title : ''}
                        <div className="cp-company-upload-text">
                            <div>
                            <span className="cp-company-upload-text-point">单击或拖动文件</span>
                            到此区域进行上传</div>
                            <span>仅支持：PNG/JPG/JPEG,大小不超过10MB</span>
                        </div>
                    </div>
                )}
            </Dragger>
            <FilePreview ref={previewRef} />
        </>
    );
};

export default CommonUpload;
