import { useState } from "react";
import { Button } from "antd";
import { CaretDownOutlined } from "@ant-design/icons";
import "@/assets/styles/components/card-box.scss";

const CardBox: React.FC<{
    title?: React.ReactNode;
    extra?: React.ReactNode;
    children?: React.ReactNode;
    showCollapse?: boolean;
}> = props => {
    const { title, extra, showCollapse, children } = props;
    const [isCollapse, setIsCollapse] = useState(false);

    return (
        <div className="card-box">
            <div className="card-box-header">
                <div className="card-box-header-title">
                    {title}
                    {showCollapse && (
                        <Button
                            className={`collapse-btn${!isCollapse ? " rotate-status" : ""}`}
                            type="text"
                            icon={<CaretDownOutlined />}
                            disabled={false}
                            onClick={() => {
                                setIsCollapse(pre => !pre);
                            }}
                        ></Button>
                    )}
                </div>
                <div className="card-box-header-extra">{extra}</div>
            </div>
            <div
                className="card-box-content"
                style={{
                    height: isCollapse ? 0 : "auto"
                }}
            >
                {children}
            </div>
        </div>
    );
};

export default CardBox;
