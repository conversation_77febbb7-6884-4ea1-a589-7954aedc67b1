import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button, Radio, Spin } from "antd";
import classnames from "classnames";
import { useNavigate } from "react-router-dom";
import { GetUserCompany, ChangeDefaultCompany } from "@fundpark/fp-api";
import { useCommonStore } from "@/store/common";
import { Modal } from "@/components/common/Modal";
import type { CompanyInfo } from "@/types/company";
import { KYCStatus } from "@/types/company/enum";
import eventBus from "@/event";
import Company1Icon from "@/assets/icons/myCompany/company1.svg?react";
import Company2Icon from "@/assets/icons/myCompany/company2.svg?react";
import Company3Icon from "@/assets/icons/myCompany/company3.svg?react";
import Company4Icon from "@/assets/icons/myCompany/company4.svg?react";
import Company5Icon from "@/assets/icons/myCompany/company5.svg?react";
import RightArrowIcon from "@/assets/icons/myCompany/right-arrow.svg?react";
import EmptyImg from "@/assets/images/company/empty-company.svg?react";
import appHelper from "@/utils/appHelper";
import "./change-company-modal.scss";
import { IconButton } from "@/components/IconButton";
import { Button as CommonButton } from "@/components/common";

const ChangeCompany: React.FC<{
    visible: boolean;
    onClose: () => void;
}> = ({ visible, onClose }) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [companyList, setCompanyList] = useState<CompanyInfo[]>([]);
    const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>();
    const [dataLoading, setDataLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    const Icons = [Company1Icon, Company2Icon, Company3Icon, Company4Icon, Company5Icon];

    const initCompanyList = async () => {
        setDataLoading(true);
        const store = useCommonStore.getState();
        const currentCompanyId = store.currentCompanyId;
        setSelectedCompanyId(currentCompanyId);

        GetUserCompany({
            userId: store.userInfo.userId
        })
            .then(res => {
                if (res.data) {
                    const list = (res.data || []).filter(item => item.kycStatus === KYCStatus.Approved).reverse();
                    list.some((item, index) => {
                        if (item.companyId === currentCompanyId) {
                            list.splice(index, 1);
                            list.unshift(item);
                            return true;
                        }
                    });
                    setCompanyList(list);
                }
            })
            .finally(() => {
                setDataLoading(false);
            });
    };

    useEffect(() => {
        const init = async () => {
            if (visible) {
                contentRef.current!.scrollTo(0, 0);
                await initCompanyList();
            }
        };
        init();
    }, [visible]);

    const updateDefaultCompany = async (companyId: string) => {
        if (!companyId) return;
        setLoading(true);
        try {
            const { userInfo } = useCommonStore.getState();
            const res = await ChangeDefaultCompany({
                userId: userInfo.userId,
                companyId
            });
            if (res.success) {
                if (res.data) {
                    appHelper.msgApi.success(t("company.change.defaultSuccess"));
                    eventBus.emit("defaultCompanyChange");
                    await initCompanyList();
                } else {
                    appHelper.msgApi.warning(t("company.change.defaultFailed"));
                }
            }
        } finally {
            setLoading(false);
        }
    };

    const onConfirm = async () => {
        const store = useCommonStore.getState();
        if (!selectedCompanyId || selectedCompanyId === store.currentCompanyId) {
            onClose();
            return;
        }
        const company = companyList.find(item => item.companyId === selectedCompanyId);
        if (!company) return;

		appHelper.msgApi.success(t("company.change.changeCompanySuccess"));
		eventBus.emit("companyChange");
        store.setCurrentCompany(company);
        onClose();
    };

    return (
        <Modal width={620} open={visible} onClose={onClose} title={t("company.change.title")}>
            <Spin spinning={dataLoading}>
                <div className="cp-change-company-modal-container">
                    <div 
                        ref={contentRef} 
                        className="cp-common-modal-content cp-change-company-modal-content"
                    >
                        {companyList.length === 0 ? (
                            <div className="cp-change-company-modal-empty">
                                <div className="cp-change-company-modal-empty-img">
                                    <EmptyImg />
                                </div>
                                <div className="cp-change-company-modal-empty-text">{t("company.empty2")}</div>
                                <Button
                                    type="primary"
                                    className="cp-btn-primary-round"
                                    onClick={() => {
                                        navigate("/company");
                                        onClose();
                                    }}
                                >
                                    {t("company.joinCompany")}
                                    <RightArrowIcon />
                                </Button>
                            </div>
                        ) : (
                            <>
                                {companyList.map((item, index) => {
                                    const isCheck = selectedCompanyId === item.companyId;
                                    const Icon = Icons[index % 5];

                                    const clickHandle = () => setSelectedCompanyId(item.companyId);

                                    return (
                                        <div
                                            key={item.id}
                                            className={classnames("cp-change-company-modal-content-item", {
                                                selected: isCheck
                                            })}
                                            onClick={clickHandle}
                                        >
                                            <Radio value={item.companyId} checked={isCheck} onChange={clickHandle} />
                                            <Icon className="cp-change-company-modal-content-item-icon" />
                                            <div className="cp-change-company-modal-content-item-text">
                                                {item.registerNameEn || item.registerNameCn}
                                            </div>
                                            {item.isDefault ? (
                                                <div className="cp-change-company-modal-content-item-default-tag">
                                                    {t("company.default")}
                                                </div>
                                            ) : (
                                                <div
                                                    className="cp-change-company-modal-content-item-set-tag cursor-pointer"
                                                    onClick={e => {
                                                        e.stopPropagation();
                                                        updateDefaultCompany(item.companyId);
                                                    }}
                                                >
                                                    {t("company.change.setAsDefault")}
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                            </>
                        )}
                    </div>
                    
                    {companyList.length > 0 && (
                        <div className="cp-change-company-modal-footer">
                            <IconButton
                                label={t("common.cancel")}
                                onClick={onClose}
                                disabled={loading}
                            />
                            <CommonButton
                                label={t("common.confirm")}
                                onClick={onConfirm}
                                loading={loading}
                                style={{
                                    width: "88px",
                                    height: "40px"
                                }}
                            />
                        </div>
                    )}
                </div>
            </Spin>
        </Modal>
    );
};

export default ChangeCompany;
