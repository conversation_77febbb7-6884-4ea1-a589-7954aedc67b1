@use "@/assets/styles/variables.module.scss" as *;

.cp-company-upload {
	.ant-upload-drag {
		height: 140px;
		background-color: #fff;
	}
	.ant-upload-btn {
		padding: 23px;
	}
    &-icon {
        font-size: 24px;
		margin-bottom: 8px;
        color: $fp-text-secondary-color;
    }
    &-text {
        display: flex;
        flex-direction: column;
        font-family: "Poppins";
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
		text-align: center;
        color: $fp-text-secondary-color;
        &-point {
            color: $fp-primary-color;
        }
    }
    
    &-has-file {
        .ant-upload-drag {
            background-color: #2017470A;
        }
    }
    
    &-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
        
        &-icon {
            width: 60px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: $fp-text-secondary-color;
            margin-bottom: 8px;
            
            svg {
                width: 40px;
                height: 40px;
            }
        }
        
        &-filename {
            font-family: "Poppins";
            font-size: 12px;
            line-height: 16px;
            font-weight: 400;
            color: #6E6E75;
            margin-bottom: 8px;
            max-width: 80%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
        }
        
        &-actions {
            display: flex;
            gap: 16px;
            
            span {
                font-family: "Poppins";
                font-size: 14px;
                font-weight: 400;
                cursor: pointer;
            }
        }
        
        &-delete {
            color: #DD4C4C;
            
            &:hover {
                opacity: 0.8;
            }
        }
        
        &-view {
            color: #2463EB;
            
            &:hover {
                opacity: 0.8;
            }
        }
    }
}
