@use "@/assets/styles/variables.module.scss" as *;

.cp-change-company-modal {
    color: #fff;
    &-container {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    &-content {
        max-height: 55vh;
        overflow-y: auto;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding-bottom: 8px;
        
        /* Transparent scrollbar by default */
        &::-webkit-scrollbar {
            width: 6px;
            background: transparent;
        }
        
        &::-webkit-scrollbar-track {
            background: transparent;
        }
        
        &::-webkit-scrollbar-thumb {
            background-color: transparent;
            border-radius: 3px;
            transition: background-color 0.3s ease;
        }
        
        /* Show scrollbar when scrolling/hovering */
        &:hover::-webkit-scrollbar-thumb,
        &:active::-webkit-scrollbar-thumb,
        &:focus::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
        }
        
        /* For Firefox */
        scrollbar-width: thin;
        scrollbar-color: transparent transparent; /* Transparent by default */
        &:hover, &:active, &:focus {
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }
        
        /* For Microsoft Edge and IE */
        -ms-overflow-style: -ms-autohiding-scrollbar;
        
        &-item {
            position: relative;
            border-radius: 20px;
            border: 1px solid #dddfe6;
            background-color: #fff;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
            &-default-tag {
                position: absolute;
                top: 0;
                right: 0;
                color: #FFFFFF;
                background-color: $fp-primary-color;
                font-size: 12px;
                line-height: 20px;
                font-weight: 400;
                padding: 4px 10px;
                border-top-right-radius: 53px;
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
            }
            &-set-tag {
                position: absolute;
                top: 0;
                right: 0;
                color: $fp-text-color;
                background-color: #2017470A;
                font-size: 12px;
                line-height: 20px;
                font-weight: 400;
                padding: 4px 10px;
                border-top-right-radius: 51px;
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
                transition: all 0.3s;
                &:hover {
                    background-color: #2017471A;
                }
                &:active {
                    background-color: #20174733;
                }
                &.selected {
                    opacity: 1;
                    color: $fp-primary-color;
                    background-color: #e0f5f4;
                }
            }
            &.selected {
                border-color: $fp-accent-color;
                background-color: #f6f7fa;
                .cp-change-company-modal-content-item-set-tag {
                    opacity: 1;
                }
            }
            &:hover {
                background-color: #f6f7fa;
                .cp-change-company-modal-content-item-set-tag {
                    opacity: 1;
                }
            }
            &-icon {
                font-size: 40px;
				border-radius: 50%;
                margin-left: 20px;
                margin-right: 12px;
            }
            &-text {
                font-size: 16px;
                line-height: 20px;
                font-weight: 600;
                color: $fp-text-color;
            }
        }
    }
    &-footer {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        padding: 40px 0 0;
        margin-top: auto;
        position: sticky;
        bottom: 0;
    }
	&-empty {
        margin-top: 56px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &-img {
            width: 160px;
            height: 160px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        &-text {
            font-weight: 600;
            font-size: 16px;
            margin: 8px 0 32px;
        }
        button {
            width: 240px;
            height: 40px;
        }
    }
}
