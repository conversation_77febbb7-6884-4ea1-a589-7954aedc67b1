import React from 'react';
import { SearchOutlined } from '@ant-design/icons'; 
import { Select } from 'antd';
import {getCategoryReportKeyword} from "@fundpark/fp-api";

const MAX_COUNT = 3;

const SelectionWithMaxCount = ({ onChange, style, placeholder, isEditDisabled, value}) => {
  const [data, setData] = React.useState([]);

  const handleChange = (newValue: [string]) => {
    onChange(newValue);
  };

  const handleSearch = async (input: string) => {
    const data = await getCategoryReportKeyword({keywords: input});
    const mappedData = (data.data).map(item => ({
      value: item,
      label: item,
    }));
    console.log('mappedData: ', data.data.data);
    setData(mappedData);
  };

  const tagRender = (props) => {
    const { label, closable, onClose } = props;
    const index = value.indexOf(props.value);
    return (
        <div style={{ display: 'flex', alignItems: 'center', marginRight: '8px'}}>
          <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                padding: '2px 10px', 
                background: '#f0f0f0', 
                borderRadius: '20px', 
                marginRight: '8px'
            }}>
                <span style={{ margin: '1px 4px', fontSize: '14px' }}>{label}</span>
                {closable && (
                    <div
                        onClick={onClose}
                        style={{
                            width: '17px',
                            height: '17px',
                            borderRadius: '50%',
                            background: 'black',
                            color: 'white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '17px',
                            cursor: 'pointer',
                            marginLeft: '5px'
                        }}
                    >
                        &times;
                    </div>
                )}
            </div>
              {index < value.length - 1 && (
                  <div style={{ width: '1px', height: '15px', background: '#464F60', margin: '0 8px' }} />
              )}
        </div>
    );
  };


  return (
    <Select
            mode="multiple"
            maxCount={MAX_COUNT}
            value={value}
            style={{
                ...style,
                backgroundColor: isEditDisabled ? '#f5f5f5' : 'white',
                color: isEditDisabled ? '#a9a9a9' : 'black',
                pointerEvents: isEditDisabled ? 'none' : 'auto'
            }}
            onChange={handleChange}
            suffixIcon={<SearchOutlined style={{ fontSize: '16px', color: isEditDisabled ? '#a9a9a9' : undefined }} />}
            placeholder={placeholder}
            options={data}
            tagRender={tagRender}
            onSearch={handleSearch}
            dropdownRender={menu => isEditDisabled ? null : menu}
        />
  );
};

export default SelectionWithMaxCount;