import React, { ReactNode } from 'react';
import styles from "./index.module.scss";
import redPocketBtn from "@/assets-new/images/redPocketBtn.svg";
import redPocketBody from "@/assets-new/images/redPocketBody.svg"
import bubbleDialog from "@/assets-new/images/bubbleDialog.svg"

interface RedPocketProps {
    redPocketTitle?: string;
    actionTitle?: string | null;
    isShowHelpingText?: boolean;
    helpingText?: ReactNode | null;
    width?: string;
    height?: string;
    type: 'default' | 'doubleLine';
    titleLineOne?: string;
    titleLineTwo?: string;
    actionClick?: () => void;
}

const RedPocket: React.FC<RedPocketProps> = ({
    redPocketTitle,
    actionTitle,
    isShowHelpingText = false,
    helpingText = null,
    type = 'default',
    titleLineOne = null,
    titleLineTwo = null,
    actionClick
}) => {
    return (

        <div
            className={styles.redPocketContainer}
        >
            <img
                src={redPocketBody}
                alt="Red Pocket"
                className={styles.redPocketImage}
            />
            {
                type === 'default' ? (
                    <div className={styles.redPocketTitle}>{redPocketTitle}</div>
                ) : (
                    <div className={styles.twoLineTitleContainer}>
                        <div className={styles.titleLineOne}>{titleLineOne}</div>
                        <div className={styles.titleLineTwo}>{titleLineTwo}</div>
                    </div>
                )
            }
            {actionTitle && (
                <button className={styles.redPocketActionButton} onClick={actionClick}>
                    <img
                        src={redPocketBtn}
                        alt="Action"
                    />
                    <div className={styles.redPocketActionButtonText}>{actionTitle}</div>
                </button>
            )}
            {isShowHelpingText && helpingText && (
                <div>
                    <div className={styles.redPocketHelpingContainer}>
                        <img
                            src={bubbleDialog}
                            alt="Action"
                        />

                    </div>
                    <div className={styles.redPocketHelpingText}>{helpingText}</div>
                </div>
            )}
        </div>
    );
};

export default RedPocket;
