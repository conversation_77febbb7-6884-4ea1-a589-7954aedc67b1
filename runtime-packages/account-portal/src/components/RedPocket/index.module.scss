.redPocketContainer {
  position: relative;
  overflow: visible;
  /* width/height come from inline style now */
  margin: 0 auto; /* Center horizontally */
  top: 1px;
  display: flex;
  justify-content: center;
  width: 400px;
  height: 196px;
  @media (max-width: 576px) { // smaller than sm
    width: 300px;
  }
}

.redPocketImage {
  display: block;
  width: 100%;
  height: 100%;
}

.redPocketTitle {
  position: absolute;
  top: 32%;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;

  font-family: Source Sans Pro, sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  background: linear-gradient(265.09deg, #DFA856 -10.48%, #EA8D01 32.28%, #C98D32 68.71%, #E6AD56 101.03%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  @media (max-width: 576px) { // smaller than sm
    font-size: 18px;
  }
}


.redPocketActionButton {
  position: absolute;
  width: 56%;
  height: 28%;
  top: 64%;
  left: 50%;
  transform: translate(-50%, 0);

  padding: 0;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  transition: box-shadow 0.3s ease,
  transform 0.2s ease;

  &:focus {
    outline: none;
  }

  &:hover {
    transform: translate(-50%, -2px);
  }

  &:active {
    transform: translate(-50%, 0);
  }

  img {
    transition: transform 0.3s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.redPocketActionButtonText {
  position: absolute;
  top: 15px;
  left: 0;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 900;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0;
  horiz-align: center;
  color: #491800;
  width: 100%;
}

.redPocketHelpingContainer {
  position: absolute;
  top: 0.1%;
  left: 80%;
  width: 57%;
  height: 22%;

  @media (max-width: 768px) { // smaller than md
    left: 20%;
  }
}


.redPocketHelpingText {
  position: absolute;
  top: 5.5%;
  left: 83%;

  width: 60%;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  vertical-align: middle;
  color: #FFFFFF;

  @media (max-width: 768px) { // smaller than md
    left: 23%;
  }
}

.twoLineTitleContainer {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
}

.titleLineOne {
  font-family: Source Sans Pro, sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  color: #282830;
  margin-bottom: 12px;
}

.titleLineTwo {
  font-family: Poppins;
  font-weight: 700;
  font-size: 32px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  background: linear-gradient(264.25deg, #FF9A00 13.73%, #DC8400 53.36%, #FFC164 84.38%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

}