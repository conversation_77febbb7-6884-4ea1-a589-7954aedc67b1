import {
    useState,
    useEffect
} from "react";
import AdBannerLayout from "@/layouts/AdBannerLayout"
import './styles.scss'

interface AdConfig {
    name: string;
    image: string;
}

interface AutoplayAdBannerProps {
    ads: AdConfig[];
    intervalSeconds?: number;
}

const AutoplayAdBanner = ({
    ads,
    intervalSeconds = 5,
}: AutoplayAdBannerProps) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);

    useEffect(() => {
        const timer = setInterval(() => {
            // Start transition effect
            setIsTransitioning(true);

            // Change image after fade out completes
            setTimeout(() => {
                const nextIndex = (currentImageIndex + 1) % ads.length;
                setCurrentImageIndex(nextIndex);
                setIsTransitioning(false);
            }, 500); // This should match the transition duration in CSS

        }, intervalSeconds * 1000);

        return () => clearInterval(timer);
    }, [currentImageIndex, ads, intervalSeconds]);

    return (
        <AdBannerLayout>
            <div className="autoplay-ad-banner">
                <img
                    src={ads[currentImageIndex].image}
                    alt={ads[currentImageIndex].name}
                    className={`autoplay-image ${isTransitioning ? 'fade-out' : 'fade-in'}`}
                />
            </div>
        </AdBannerLayout>
    );
};

export default AutoplayAdBanner;