import { useEffect, useState } from "react";
import { useRouteError, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import SystemUpdateImg from "@/assets/images/default-empty/systemUpdate.svg?react";

const RouteBoundary: React.FC = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const routeErr = useRouteError() as Error;
    const [status, setStatus] = useState({ versionUpdate: false });

    useEffect(() => {
        console.error("route err: ", routeErr);
        if (import.meta.env.PROD) {
            const staticNotFoundReason = ["Failed to fetch dynamically imported module", "Unable to preload CSS for"];
            if (staticNotFoundReason.some(msg => routeErr.message.includes(msg))) {
                setStatus({ versionUpdate: true });

                setTimeout(() => {
                    navigate("..", { replace: true });
                    setTimeout(() => {
                        window.location.reload();
                    }, 0);
                }, 3000);
            } else {
                navigate("/error", { replace: true });
            }
        }
    }, []);

    if (status.versionUpdate) {
        return (
            <div className="flex flex-col items-center justify-center h-[80vh]">
                <SystemUpdateImg />
                <div className="fp-accent-color font-semibold text-lg leading-7 mt-5">{t("system.versionUpdate")}</div>
            </div>
        );
    }

    return null;
};

export default RouteBoundary;
