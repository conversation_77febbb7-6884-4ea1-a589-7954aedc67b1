import React from 'react';
import { Button } from 'antd'; // Assuming you're using Ant Design for the button
import reportIcon from "@/assets-new/icons/reportIcon.png";

interface CustomButtonProps {
    index: number;
    text: string;
    onClick: () => void;
}

export const ReportIconTextButton: React.FC<CustomButtonProps> =  ({ index, text, onClick }) => {
    return (
        <Button 
            onClick={onClick} 
            style={{
                display: 'flex',
                alignItems: 'center',
                width: '329px', 
                height: '93px',
                borderRadius: '15px',
                padding: '10px',
                backgroundColor: '#342B71',
                border: 'none',
            }}
        >
            <div style={{ flex: '0 0 25%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <img src={reportIcon} alt="report-icon" style={{marginLeft: '10px'}} />
            </div>
            <div style={{ 
                    flex: '1', 
                    textAlign: 'left', 
                    color: 'white', 
                    paddingRight: '30px', 
                    display: 'flex', 
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                <span style={{ marginRight: '20px' }}>{`关联词 ${index} : `}</span>
                <span style={{ 
                    textDecoration: 'underline', 
                    wordWrap: 'break-word',
                    whiteSpace: 'normal', 
                    display: 'flex',
                    alignItems: 'center'
                }}>
                    {text}
                </span>
            </div>
        
        </Button>
    );
};

export default ReportIconTextButton;