import React from 'react';
import { Button } from 'antd';
import { trackEvent } from '@/utils/matomoTracking';

interface TrackingButtonProps {
  label: string;
  category: string;
  action: string;
  name?: string;
  value?: number;
  onClick?: () => void;
}

/**
 * A button component that tracks clicks using Matomo
 */
const TrackingButton: React.FC<TrackingButtonProps> = ({
  label,
  category,
  action,
  name,
  value,
  onClick
}) => {
  const handleClick = () => {
    // Track the click event in Matomo
    trackEvent(category, action, name, value);
    
    // Call the original onClick handler if provided
    if (onClick) {
      onClick();
    }
  };

  return (
    <Button onClick={handleClick}>
      {label}
    </Button>
  );
};

export default TrackingButton;

/**
 * Usage example:
 * 
 * import TrackingButton from '@/components/Button/MatomoTrackingExample';
 * 
 * const MyComponent = () => {
 *   return (
 *     <TrackingButton 
 *       label="Submit Form"
 *       category="Form"
 *       action="Submit"
 *       name="Contact Form"
 *       onClick={() => console.log('Form submitted')}
 *     />
 *   );
 * };
 */ 