import { Modal } from "@/components/common/Modal";
import { Form, Input, message } from "antd";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/common";
import { validateEmail, validateMobilePhone } from "@fundpark/ui-utils";
import { SignupValidateToken, GetCaptchaImg } from "@fundpark/fp-api";
import SendButton from "@/components/common/SendButton";
import type { UpdateUserInfoReq } from "@fundpark/fp-api/types/user.ts";
import { useCommonStore } from "@/store/common";
import { useState } from "react";

interface MissingInfoModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (values: UpdateUserInfoReq) => Promise<boolean>;
    missingEmail?: boolean;
    missingMobile?: boolean;
}

type TranslationKey = "myProfile.emailRequired" | "myProfile.emailPlaceholder" | "myProfile.emailVerificationCodeRequired" | "myProfile.emailVerificationCodePlaceholder" | "myProfile.mobileRequired" | "myProfile.mobilePlaceholder" | "myProfile.mobileVerificationCodeRequired" | "myProfile.mobileVerificationCodePlaceholder";

interface FormField {
    name: "email" | "mobilePhoneNumber";
    needsVerification: true;
    verificationName: "emailCode" | "phoneCode";
    requiredMessage: TranslationKey;
    placeholder: TranslationKey;
    verificationRequiredMessage: TranslationKey;
    verificationPlaceholder: TranslationKey;
}

const FORM_FIELDS: FormField[] = [
    {
        name: "email",
        needsVerification: true,
        verificationName: "emailCode",
        requiredMessage: "myProfile.emailRequired",
        placeholder: "myProfile.emailPlaceholder",
        verificationRequiredMessage: "myProfile.emailVerificationCodeRequired",
        verificationPlaceholder: "myProfile.emailVerificationCodePlaceholder"
    },
    {
        name: "mobilePhoneNumber",
        needsVerification: true,
        verificationName: "phoneCode",
        requiredMessage: "myProfile.mobileRequired",
        placeholder: "myProfile.mobilePlaceholder",
        verificationRequiredMessage: "myProfile.mobileVerificationCodeRequired",
        verificationPlaceholder: "myProfile.mobileVerificationCodePlaceholder"
    }
];

export const MissingInfoModal: React.FC<MissingInfoModalProps> = ({
    open,
    onClose,
    onSubmit,
    missingEmail,
    missingMobile
}) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const { userInfo } = useCommonStore();
    const [captchaImage, setCaptchaImage] = useState<string>();
    const [captchaUuid, setCaptchaUuid] = useState<string>();
    const [showEmailCaptcha, setShowEmailCaptcha] = useState(false);
    const [showMobileCaptcha, setShowMobileCaptcha] = useState(false);

    const handleCaptcha = async () => {
        try {
            const loginCodeRes = await GetCaptchaImg();
            if (loginCodeRes.success) {
                const { img, uuid } = loginCodeRes as any;
                setCaptchaImage(img);
                setCaptchaUuid(uuid);
                return { success: true, uuid, img };
            }
            return { success: false };
        } catch (error) {
            console.error("Captcha error:", error);
            return { success: false };
        }
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const type: ("email" | "mobile")[] = [];
            const updateData: UpdateUserInfoReq = {
                id: userInfo.id,
                type: type
            };

            if (missingEmail && values.email) {
                updateData.email = values.email;
                const emailVerificationCode = form.getFieldValue("emailCode");
                if (!emailVerificationCode) {
                    message.error(t("myProfile.emailVerificationCodeRequired"));
                    return;
                }
                updateData.emailCode = emailVerificationCode;
                type.push("email");
            }

            if (missingMobile && values.mobilePhoneNumber) {
                updateData.mobilePhoneNumber = values.mobilePhoneNumber;
                updateData.mobilePhoneAreaCode = values.mobilePhoneAreaCode;
                const phoneVerificationCode = form.getFieldValue("phoneCode");
                if (!phoneVerificationCode) {
                    message.error(t("myProfile.mobileVerificationCodeRequired"));
                    return;
                }
                updateData.phoneCode = phoneVerificationCode;
                type.push("mobile");
            }

            const submitRes = await onSubmit(updateData);
            if (!submitRes) {
                handleCaptcha();
            }
        } catch (error) {
            console.error("Form validation failed:", error);
        }
    };

    const handleSendEmailVerification = async (validateCode?: string) => {
        try {
            const email = form.getFieldValue("email");
            if (!email) {
                message.error(t("myProfile.emailRequired"));
                return false;
            }

            if (!validateEmail(email)) {
                message.error(t("myProfile.emailInvalid"));
                return false;
            }

            const res = await SignupValidateToken({
                channel: "email",
                email: email,
                code: validateCode,
                uuid: validateCode ? captchaUuid : undefined
            } as any);

            if (res.success) {
                message.success(t("myProfile.verificationCodeSent"));
                setShowEmailCaptcha(false);
                setCaptchaImage(undefined);
                setCaptchaUuid(undefined);
                return true;
            }

            if (res.code === 80019) {
                const captchaRes = await handleCaptcha();
                if (captchaRes.success) {
                    setShowEmailCaptcha(true);
                }
            } else if (showEmailCaptcha) {
                handleCaptcha();
            }
            return false;
        } catch (error) {
            console.error("Email verification error:", error);
            return false;
        }
    };

    const handleSendMobileVerification = async (validateCode?: string) => {
        try {
            const phone = form.getFieldValue("mobilePhoneNumber");
            const areaCode = form.getFieldValue("mobilePhoneAreaCode");
            if (!phone) {
                message.error(t("myProfile.mobileRequired"));
                return false;
            }

            const res = await SignupValidateToken({
                channel: "sms_update",
                mobilePhoneAreaCode: areaCode,
                mobilePhone: phone,
                code: validateCode,
                uuid: validateCode ? captchaUuid : undefined
            } as any);

            if (res.success) {
                message.success(t("myProfile.verificationCodeSent"));
                setShowMobileCaptcha(false);
                setCaptchaImage(undefined);
                setCaptchaUuid(undefined);
                return true;
            }

            if (res.code === 80019) {
                const captchaRes = await handleCaptcha();
                if (captchaRes.success) {
                    setShowMobileCaptcha(true);
                }
            } else if (showMobileCaptcha) {
                handleCaptcha();
            }
            return false;
        } catch (error) {
            console.error("Mobile verification error:", error);
            return false;
        }
    };

    const handleRefreshCaptcha = () => {
        handleCaptcha();
    };

    return (
        <Modal
            open={open}
            onClose={onClose}
            title={t("myProfile.missingInfo.title")}
            width={500}
            closeText={t("common.skip")}
        >
            <Form
                form={form}
                layout="vertical"
                requiredMark={false}
                onFinish={handleSubmit}
            >
                <div className="text-sm text-[#6E6E75] text-center max-w-[269px] mx-auto mb-10">
                    {t("myProfile.missingInfo.description")}
                </div>

                {FORM_FIELDS.map(field => (
                    <div key={field.name}>
                        {((field.name === "email" && missingEmail) || (field.name === "mobilePhoneNumber" && missingMobile)) && (
                            <>
                                <Form.Item
                                    name={field.name}
                                    rules={[
                                        { required: true, message: t(field.requiredMessage) },
                                        {
                                            validator: async (_, value) => {
                                                if (!value) return;

                                                if (field.name === "email" && !validateEmail(value)) {
                                                    throw new Error(t("myProfile.emailInvalid"));
                                                }

                                                if (field.name === "mobilePhoneNumber") {
                                                    const areaCode = form.getFieldValue("mobilePhoneAreaCode");
                                                    if (!validateMobilePhone(value, areaCode)) {
                                                        throw new Error(t("myProfile.mobileInvalid"));
                                                    }
                                                }
                                            }
                                        }
                                    ]}
                                    className="mb-2"
                                    dependencies={field.name === "mobilePhoneNumber" ? ["mobilePhoneAreaCode"] : []}
                                >
                                    <Input
                                        className="h-[40px]"
                                        placeholder={t(field.placeholder)}
                                        size="large"
                                        addonBefore={
                                            field.name === "mobilePhoneNumber" && (
                                                <Form.Item name="mobilePhoneAreaCode" noStyle initialValue="+852">
                                                    <select className="border-0 bg-transparent outline-none text-[14px]">
                                                        <option value="+86">+86</option>
                                                        <option value="+852">+852</option>
                                                    </select>
                                                </Form.Item>
                                            )
                                        }
                                    />
                                </Form.Item>

                                {((field.name === "email" && showEmailCaptcha) || (field.name === "mobilePhoneNumber" && showMobileCaptcha)) && (
                                    <Form.Item
                                        name={`${field.name}Captcha`}
                                        rules={[{ required: true, message: t("login.captcha.required") }]}
                                        className="mb-2"
                                    >
                                        <div className="flex items-center gap-4">
                                            <Input
                                                placeholder={t("login.captcha.placeholder")}
                                                size="large"
                                            />
                                            {captchaImage && (
                                                <img
                                                    src={`data:image/png;base64,${captchaImage}`}
                                                    alt="Captcha"
                                                    onClick={handleRefreshCaptcha}
                                                    className="h-[40px] w-[100px] cursor-pointer flex-shrink-0"
                                                />
                                            )}
                                        </div>
                                    </Form.Item>
                                )}

                                <Form.Item
                                    name={field.verificationName}
                                    rules={[{ required: true, message: t(field.verificationRequiredMessage) }]}
                                    className="mb-10"
                                >
                                    <Input
                                        placeholder={t(field.verificationPlaceholder)}
                                        size="large"
                                        className="!pr-1"
                                        suffix={
                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(prevValues, currentValues) =>
                                                    field.name === "email"
                                                        ? prevValues.email !== currentValues.email || prevValues.emailCaptcha !== currentValues.emailCaptcha
                                                        : prevValues.mobilePhoneNumber !== currentValues.mobilePhoneNumber ||
                                                        prevValues.mobilePhoneAreaCode !== currentValues.mobilePhoneAreaCode ||
                                                        prevValues.mobilePhoneNumberCaptcha !== currentValues.mobilePhoneNumberCaptcha
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    const isEmail = field.name === "email";
                                                    const showCaptcha = isEmail ? showEmailCaptcha : showMobileCaptcha;
                                                    const captchaValue = getFieldValue(`${field.name}Captcha`);
                                                    const isValidInput = isEmail
                                                        ? validateEmail(getFieldValue("email"))
                                                        : validateMobilePhone(
                                                            getFieldValue("mobilePhoneNumber"),
                                                            getFieldValue("mobilePhoneAreaCode")
                                                        );

                                                    return (
                                                        <SendButton
                                                            onClick={async () => {
                                                                if (isEmail) {
                                                                    return handleSendEmailVerification(showCaptcha ? captchaValue : undefined);
                                                                } else {
                                                                    return handleSendMobileVerification(showCaptcha ? captchaValue : undefined);
                                                                }
                                                            }}
                                                            countdown={60}
                                                            disabled={
                                                                !isValidInput || (showCaptcha && !captchaValue?.trim())
                                                            }
                                                        />
                                                    );
                                                }}
                                            </Form.Item>
                                        }
                                    />
                                </Form.Item>
                            </>
                        )}
                    </div>
                ))}

                <Button
                    onClick={handleSubmit}
                    label={t("common.submit")}
                    className="w-full"
                />
            </Form>
        </Modal>
    );
}; 