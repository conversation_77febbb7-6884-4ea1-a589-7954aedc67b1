import { useTranslation } from "react-i18next";
import { Button } from "@/components/common";

interface SubmitButtonProps {
    onClick: () => void;
    className?: string;
    loading?: boolean;
}

export const SubmitButton: React.FC<SubmitButtonProps> = ({ onClick, className, loading }) => {
	const { t } = useTranslation();

    return (
        <Button
            label={t("common.submit")}
            onClick={onClick}
            className={`${className || ''}`}
            style={{
                width: '88px',
                height: '40px',
                borderRadius: '60px'
            }}
            loading={loading}
        />
    );
};
