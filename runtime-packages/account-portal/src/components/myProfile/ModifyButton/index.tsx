import { useTranslation } from "react-i18next";
import ModifyBlack from "@/assets/icons/modify.svg?react";
import { IconButton } from "@/components/IconButton";

interface ModifyButtonProps {
    onClick: () => void;
}

export const ModifyButton: React.FC<ModifyButtonProps> = ({ onClick }) => {
    const { t } = useTranslation();

    return (
        <IconButton
            icon={<ModifyBlack />}
            label={t("common.modify")}
            onClick={onClick}
        />
    );
};