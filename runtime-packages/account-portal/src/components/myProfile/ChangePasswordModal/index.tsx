import { Form } from "antd";
import { useTranslation } from "react-i18next";
import { Password } from "@/components/common/Input";
import { Button } from "@/components/common";
import { validatePassword } from "@fundpark/ui-utils";
import { Modal } from "@/components/common/Modal";

interface ChangePasswordModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (values: { oldPassword: string; newPassword: string }) => Promise<{ code: number }>;
}

export const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({ 
    open, 
    onClose,
    onSubmit 
}) => {
	const { t } = useTranslation();
    const [form] = Form.useForm();

    const handleClose = () => {
        form.resetFields();
        onClose();
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const response = await onSubmit(values);
            if (response?.code === 200) {
                form.resetFields();
                onClose();
            }
        } catch (error) {
            console.error("Validation failed:", error);
        }
    };

    return (
        <Modal
            open={open}
            onClose={handleClose}
            title={t("myProfile.changePassword")}
        >
            <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                requiredMark={false}
            >
                <Form.Item
                    name="oldPassword"
                    label={t("myProfile.oldPassword")}
                    rules={[
                        { required: true, message: t("myProfile.oldPasswordPlaceHolder") }
                    ]}
                    className="mb-6"
                >
                    <Password 
                        placeholder={t("myProfile.oldPasswordPlaceHolder")}
                        size="large"
                        disableAutoComplete
                    />
                </Form.Item>

                <Form.Item
                    name="newPassword"
                    label={t("myProfile.newPassword")}
                    rules={[
                        { required: true, message: t("myProfile.newPasswordPlaceHolder") },
                        {
                            validator: (_, value) => {
                                const validation = validatePassword(value || '');
                                if (!validation.isValid) {
                                    return Promise.reject(new Error(validation.message!));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    help={''}
                    className="mb-2"
                >
                    <Password 
                        placeholder={t("myProfile.newPasswordPlaceHolder")}
                        size="large"
                        showVerificationTips
						disableAutoComplete
                    />
                </Form.Item>

                <Form.Item
                    name="confirmPassword"
                    dependencies={['newPassword']}
                    rules={[
                        { required: true, message: t("myProfile.confirmPasswordPlaceHolder") },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('newPassword') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error('The two passwords do not match'));
                            }
                        })
                    ]}
                >
                    <Password 
                        placeholder={t("myProfile.confirmPasswordPlaceHolder")}
                        size="large"
						disableAutoComplete
                    />
                </Form.Item>

                <Form.Item
                    className="mt-10 mb-0"
                >
                    <Button
                        onClick={() => form.submit()}
                        label={t("common.submit")}
                        className="w-full rounded-full"
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};