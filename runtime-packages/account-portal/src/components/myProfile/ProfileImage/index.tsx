interface ProfileImageProps {
    src: string;
    alt: string;
    userName: string;
}

export const ProfileImage: React.FC<ProfileImageProps> = ({ src, alt, userName }) => {
    return (
        <div className="flex items-center">
            <div className="w-[122px] h-[122px] rounded-full bg-white absolute -bottom-16 left-44 shadow-xl flex justify-center items-center">
                <img src={src} alt={alt} className="w-[114px] h-[114px] object-cover" />
            </div>
            <span className="absolute -bottom-11 left-80 text-[22px] font-[600] font-poppins">{userName}</span>
        </div>
    );
};