import { useTranslation } from "react-i18next";
import ChangePassword from "@/assets/icons/change-password.svg?react";

interface ChangePasswordButtonProps {
    onClick: () => void;
}

export const ChangePasswordButton: React.FC<ChangePasswordButtonProps> = ({ onClick }) => {
	const { t } = useTranslation();

    return (
        <div
            className="w-auto h-[32px] bg-transparent border-[1px] border-solid border-[#FFFFFF] rounded-[52px] flex items-center justify-center py-[6px] px-3 justify-between cursor-pointer hover:bg-[#FFFFFF] hover:bg-opacity-20 active:bg-opacity-40"
            onClick={onClick}
            role="button"
            tabIndex={0}
        >
            <div className="flex items-center justify-center h-[20px] w-[20px]">
                <ChangePassword />
            </div>
            <span className="text-[#FFFFFF] text-[14px] ml-[10px]">{t("myProfile.changePassword")}</span>
        </div>
    );
};