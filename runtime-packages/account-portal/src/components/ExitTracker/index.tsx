import React from 'react';
import useExitTracking from '@/hooks/useExitTracking';

interface ExitTrackerProps {
  trackVisibilityChange?: boolean;
  trackBeforeUnload?: boolean;
  trackPageHide?: boolean;
  exitEventCategory?: string;
  minTimeOnPage?: number;
  trackTimeSpent?: boolean;
}

const ExitTracker: React.FC<ExitTrackerProps> = (props) => {
  useExitTracking(props);
  return null;
};

export default ExitTracker; 