import React from 'react';
import useRouteTracking from '@/hooks/useRouteTracking';

/**
 * MatomoPageTracker - Tracks page views within the router context.
 * 
 * This component doesn't render anything but automatically
 * tracks page views when the route changes. Place this in your
 * layout components to track all pages within that layout.
 * 
 * @example
 * // In your layout component:
 * return (
 *   <Layout>
 *     <MatomoPageTracker />
 *     {children}
 *   </Layout>
 * );
 */
const MatomoPageTracker: React.FC = () => {
  // Use the route tracking hook with default options
  useRouteTracking();
  
  // Component doesn't render anything
  return null;
};

export default MatomoPageTracker; 