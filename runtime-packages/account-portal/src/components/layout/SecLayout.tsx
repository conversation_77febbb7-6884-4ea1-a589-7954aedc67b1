const SecLayout: React.FC<{
    title?: React.ReactNode;
    extra?: React.ReactNode;
    children?: React.ReactNode;
}> = ({ title, extra, children }) => {
    return (
        <div className="cp-sec-layout">
            <div className="cp-sec-layout-header">
                <div className="cp-sec-layout-header-title">{title}</div>
                {extra && <div className="cp-sec-layout-header-right">{extra}</div>}
            </div>
            <div>{children}</div>
        </div>
    );
};

export default SecLayout;
