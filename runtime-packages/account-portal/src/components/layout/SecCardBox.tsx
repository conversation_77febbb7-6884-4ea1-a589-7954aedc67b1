import cls from "classnames";

const SecCardBox: React.FC<{
    className?: string;
    children?: React.ReactNode;
    footerLeft?: React.ReactNode;
    footerRight?: React.ReactNode;
    onClick?: () => void;
}> = ({ className, children, footerLeft, footerRight, onClick }) => {
    const handleFooterRightClick = (e: React.MouseEvent) => {
        e.stopPropagation();
    };

    return (
        <div className={cls("cp-sec-layout-item", className, onClick ? "cursor-pointer" : "")} onClick={onClick}>
            <div className="cp-sec-layout-item-main">{children}</div>
            {(footerLeft || footerRight) && (
                <div className="cp-sec-layout-item-footer">
                    <div className="cp-sec-layout-item-footer-left">{footerLeft}</div>
                    <div className="cp-sec-layout-item-footer-right" onClick={handleFooterRightClick}>
                        {footerRight}
                    </div>
                </div>
            )}
        </div>
    );
};

export default SecCardBox;
