.redPocketContainer {
  position: relative;
  overflow: visible;
  /* width/height come from inline style now */
}

.redPocketImage {
  display: block;
  width: 100%;
  height: 100%;
}



.actionTitle {
  position: absolute;
  top:   17%;
  left:  46%;
  transform: translateX(-50%);
  width: 100%;

  font-family: Poppins, sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  background: linear-gradient(#FFFFFF,#FFEFCC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

}

.redPocketTitle {
  position: absolute;
  top:   15%;
  left:  50%;
  transform: translateX(-50%);
  width: 100%;

  font-family: Poppins, sans-serif;
  font-weight: 700;
  font-size: 40px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  background: linear-gradient(#FF9A00,#DC8400,#FFC164);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

}



.redPocketActionButton {
  position: absolute;
  width: 50%;
  height: 28%;
  top:   12%;
  left:  50%;
  transform: translate(-50%, 0);

  padding: 0;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.extraActionButton {
  position: absolute;
  top:   37%;
  left:  50%;
  transform: translate(-50%, 0);

  padding: 0;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.redPocketActionButtonText {
  position: absolute;
  top: 45%;
  left: 0;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 900;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  horiz-align: center;
  color: #FFFFFF;
  width: 100%;
}


.extraActionButtonText{
  position: absolute;
  top: 35%;
  left: 0;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 900;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0;
  horiz-align: center;
  color: #9A0000 ;
  width: 100%;
}

.descText{
  position: absolute;
  top: 50%;
  left: 10%;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 900;
  font-size: 11px;
  line-height: 100%;
  letter-spacing: 0;
  horiz-align: center;
  color: #FFFFFF ;
  width: 100%;
}

.descTextMinor{
  position: absolute;
  top: 57%;
  left: 10%;
  font-family: "Source Sans Pro", sans-serif;
  font-weight: 900;
  font-size: 11px;
  line-height: 100%;
  letter-spacing: 0;
  horiz-align: center;
  color: #FFFFFF ;
  opacity: 70%;
  width: 100%;
}