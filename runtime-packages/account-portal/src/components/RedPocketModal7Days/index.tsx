import {Modal as AntModal} from "antd";
import React, {useEffect, useState} from "react";
import styles from "./index.module.scss";
import redPocketBody from "@/assets-new/images/full_red_pocket_body.svg";
import activateButton from "@/assets-new/images/red_pocket_activate_button.svg";
import pocketUnavailable from "@/assets-new/images/pocket_unavailable.svg";
import alertIcon from "@/assets-new/icons/common/alert-circle.svg";


interface RedPocketModal7DaysProps {
    open: boolean;
    onClose: () => void;
    style?: React.CSSProperties;
    width?: number | "auto";
    height?: number | string;
    backgroundColor?: string;
    closeText?: string;
    trackingId?: string;
    actionTitle?: string;
    descText?: string;
    actionEvent?: () => void;
}

const RedPocketModal7Days: React.FC<RedPocketModal7DaysProps> = ({
                                                                     open,
                                                                     onClose,
                                                                     style,
                                                                     height = "auto",
                                                                     closeText,
                                                                     trackingId,
                                                                     actionEvent,
                                                                 }) => {

    const handleClose = () => {
        setShowRules(false);
        onClose();
    };

    const [showRules, setShowRules] = useState(false);

    const toggleRules = () => {
        setShowRules(!showRules);
    };

    return (
        <AntModal
            open={open}
            onCancel={handleClose}
            footer={null}
            className={`rounded-[24px] [&_.ant-modal-content]:!p-0 [&_.ant-modal-content]:!rounded-[24px] [&_.ant-modal-content]:!bg-transparent [&_.ant-modal-content]:!shadow-none [&_.ant-modal-content]:!border-none ${
                !closeText
                    ? "[&_.ant-modal-close]:!w-8 [&_.ant-modal-close]:!h-8 [&_.ant-modal-close]:!bg-white/40 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:!rounded-full"
                    : "[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!shadow-none [&_.ant-modal-close]:!border-none hover:[&_.ant-modal-close]:!bg-[transparent] active:[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!px-3 [&_.ant-modal-close]:!py-1 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:before:!content-[''] [&_.ant-modal-close]:after:!content-[''] [&_.ant-modal-close-x]:!bg-[transparent] [&_.ant-modal-close]:mr-2 [&_.ant-modal-close]:!w-auto"
            }`}
            // maskClosable={false}
            centered
            width={"300px"}
            style={{
                ...style,
                backgroundColor: "transparent",
                boxShadow: "none",
                border: "none"
            }} // Add transparent background to the modal style
            height={height}
            // closeIcon={null}
        >
            <div style={{padding: "8%"}}></div>
            <div className="h-full min-h-[600px] rounded-lg bg-transparent">
                <div>
                    <div style={{width: "100% "}}>
                        <img src={redPocketBody} alt="Red Pocket" style={{width: "300px", display: "block"}}/>
                        <div className={styles.redPocketTitle}>
                            7天免息券
                        </div>
                        <button disabled className={styles.redPocketActionButton}>
                            <img src={pocketUnavailable} alt="Action" style={{width: "50%"}}/>
                            <div className={styles.redPocketActionButtonText}>已领取</div>
                        </button>
                        <div className={styles.descText} style={{width: "68%"}}></div>
                        <button className={styles.extraActionButton} onClick={actionEvent}>
                            <img src={activateButton} alt="Action"/>
                            <div className={styles.extraActionButtonText}>立即0元用
                            </div>
                        </button>
                        <button
                          style={{
                            display: "flex",
                            alignItems: "center",
                            background: "none",
                            border: "none",
                            padding: 0,
                            cursor: "pointer",
                            position: "absolute",
                            top: "50%",
                            left: "35%",
                            opacity: showRules ? 1 : 0.6,
                            transition: "opacity 150ms ease-in-out",
                          }}
                          onClick={toggleRules}
                        >
                          <img
                            src={alertIcon}
                            alt="alert"
                            style={{
                              filter: "brightness(0) invert(1)",
                              opacity: showRules ? 1 : 0.6,
                            }}
                          />
                          <span
                            style={{
                              color: showRules ? "#FFFFFF" : "rgba(255,255,255,0.6)",
                              fontSize: "12px",
                              marginLeft: 4,
                              transition: "color 150ms ease-in-out",
                            }}
                          >
                            使用规则
                          </span>
                        </button>

                        {showRules && (
                        <div
                          style={{
                            backgroundColor: "#000",
                            color: "white",
                            borderRadius: "24px",
                            marginTop: "5%",
                            padding: "10px",
                              paddingLeft: "16px",
                          }}
                        >
                          <span
                            style={{
                              display: "block",
                              paddingLeft: "1em",
                              textIndent: "-1em",
                              marginBottom: "0.5em",
                            }}
                          >
                            ・红包使用方式：第一次用款在首七天内一次性还清，可自动免除利息
                          </span>
                          <span
                            style={{
                              display: "block",
                              paddingLeft: "1em",
                              textIndent: "-1em",
                            }}
                          >
                            ・优惠受条款及细则约束，丰泊国际对此有绝对决定权，可随时更改或中止而毋需通知
                          </span>
                        </div>)}
                    </div>
                </div>
            </div>
        </AntModal>
    );
};

export default RedPocketModal7Days;
