import React from 'react';
import { Button } from '@/components/shared/Button';
import SecondaryButton from '@/components/shared/SecondaryButton';
import Text from '@/components/shared/Text';
import ArrowRightIcon from '@/assets-new/icons/arrow-right-sm.svg?react';
import ArrowLeftIcon from '@/assets-new/icons/arrow-left-sm.svg?react';
import './ActionButtons.scss';

interface ActionButtonsProps {
  showBackButton: boolean;
  showSubmitButton: boolean;
  showNextButton: boolean;
  isSubmitting?: boolean;
  onNext: () => void;
  onBack: () => void;
  onSubmit: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  showBackButton,
  showSubmitButton,
  showNextButton,
  isSubmitting,
  onNext,
  onBack,
  onSubmit,
}) => {
  return (
    <div className="action-buttons-container">
      <div className="action-buttons-row">
        {showBackButton && (
          <SecondaryButton
            onClick={onBack}
            style={{ 
              width: 320, 
              backgroundColor: "white", 
              color: "#9E9EA3" 
            }}
            icon={<ArrowLeftIcon style={{ marginLeft: '4px' }} />}
            label="返回"
            disabled={isSubmitting}
          />
        )}
        
        {showSubmitButton && (
          <Button
            style={{ width: 320 }}
            label={
              <span className="flex items-center justify-center">
                提交
                {!isSubmitting && <ArrowRightIcon style={{ marginLeft: '4px' }} />}
              </span>
            }
            onClick={onSubmit}
            type="primary"
            loading={isSubmitting}
            disabled={isSubmitting}
          />
        )}
        
        {showNextButton && (
          <Button
            style={{ width: 320 }}
            label={
              <span className="flex items-center justify-center">
                下一步
                <ArrowRightIcon style={{ marginLeft: '4px' }} />
              </span>
            }
            onClick={onNext}
            type="primary"
            disabled={isSubmitting}
          />
        )}
      </div>
      
      <Text
        style={{
          fontSize: "14px",
          textDecoration: "underline",
          cursor: "pointer",
          color: "#9E9EA3"
        }}
        onClick={() => {
            window.open(`${window.location.origin}/financing/questions?q=${encodeURIComponent('平台API授权')}`, "_blank");        }}
      >
        如何授权及通过校验？
      </Text>
    </div>
  );
};

export default ActionButtons; 