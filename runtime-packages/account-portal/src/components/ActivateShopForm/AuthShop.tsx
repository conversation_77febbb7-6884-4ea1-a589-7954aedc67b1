import React, { lazy, useEffect, useState} from "react";
import "./AuthShop.scss";
import shopIcon from "@/assets/icons/sdg/shop_black.svg";
import { Form } from "antd";
import { useLocation, useNavigate } from 'react-router-dom';
import { getAuthURL, getOauthShopList, deleteAuthedShop, bindPSPtoShop } from "@fundpark/fp-api";
import { ModalStep } from "@/types/platforms/modalSteps";
import PlatformMainModal from "../Platforms";
import PspSelectionModal from "../Platforms/pspSelectionModal";
import SelectOrAddPaymentGateway from "../Platforms/selectOrAddPaymentGatewayModal";
import { IPingPongData, IPsp } from "@/types/platforms/psp";
import { usePaymentGatewayStore } from "@/store/platform/paymentGatewayStore";
import { IPlatform } from "@/types/platforms/platforms";
import { Modal } from '@/components/shared/Modal';
import SecondaryButton from '@/components/shared/SecondaryButton';
import { But<PERSON> } from '@/components/shared/Button';
import {usePlatformStore} from "@/store/platform/platformStore";
import {OauthPspItem} from "@fundpark/fp-api/types/platform/platform.ts";
import type { GetOauthShopListRes } from "@fundpark/fp-api";
import AppHelper from "@/utils/appHelper.ts";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import PingPongModal from "@/components/Platforms/pingpongModal";
import AuthConfirmationModal from "@/components/Platforms/authConfirmationModal";

const StatusBadge = lazy(() => import("@/components/shared/StatusBadge"));

interface AuthShopProps {
  addShopButton?: boolean;
  canDelete?: boolean;
  canEdit?:boolean;
  rows: any[];
  authedPlatforms: GetOauthShopListRes;
  updateRows: (updatedValue: any[]) => void;
};

export interface AuthErrorParams {
    errorMsg: string;
    width?: number;
    height?: number;
    contactMsg?:string;
}

const AuthShop: React.FC<AuthShopProps> = ({
    addShopButton=true,
    canDelete=true,
    canEdit=true,
    rows,
    authedPlatforms,
    updateRows
}) => {
    const [form] = Form.useForm();

    // todo: need communicate with backend, need psp verified status in response
    const [allRows, setRows] = useState<any[]>([rows]);
    const [modalStep, setModalStep] = useState<ModalStep>("none");
    const [selectedPlatform, setSelectedPlatform] = useState<IPlatform | null>(null);
    const [selectedPaymentGateway, setSelectedPaymentGateway] = useState<IPsp | null>(null);
    const [authUrl, setAuthUrl] = useState<string>("");
    const [authToken, setAuthToken] = useState<string | null>(null);
    const { payment_gateway, fetchPaymentGateway } = usePaymentGatewayStore();
    const [openPspModal, setOpenPspModal] = useState<boolean>(false);
    const [showConfirmDelete, setShowConfirmDelete] = useState(false);
    const [allAuthedPlatforms, setAuthedPlatforms] = useState(authedPlatforms);
    const { platforms, fetchPlatforms } = usePlatformStore();
    const [showWechatModal, setShowWechatModal] = useState(false);
    const [activeRowId, setActiveRowId] = useState(null);
    const [showPSPSelection, setShowPSPSelection] = useState(false);
    const [showPPModal, setShowPPModal] = useState(false);
    const [showAuthConfirm, setShowAuthConfirm] = useState(false);
    const location = useLocation();

    const handleShowConfirmDelete = (state: boolean) => {
        if (state) {
            setShowConfirmDelete(true);
        } else {
            setShowConfirmDelete(false);
        }
    };

    const handleDeletePsp = async (id: string) => {
        try{        
            setShowConfirmDelete(false);
            const res = await deleteAuthedShop(id);
            if (res.code === 0) {
                fetchData(); // need to fetch again for updated data
            } else {
                console.error("Delete shop error:", res.message);
            }
        } catch (err) {
            console.error(err);
            alert("Error fetching auth URL");
            return false;
        }
        console.log("handleDeletePsp");
    };

    const addNewShop = () => {
        setModalStep("platformSelection");
    };
    const fetchData = async () => {
        try {
            const res = await getOauthShopList({});
            if (res.code === 0) {
                // @ts-ignore
                setAuthedPlatforms(res.data);
                const updatedData = res.data.shop.map((shop, index) => ({
                    id: index + 1,
                    platform_id: shop.platform_id,
                    auth_status: shop.oauth_status,
                    credit_status: shop.credit_status,
                    platform: shop.platform,
                    shop_id: shop.seller_id,
                    platform_shop_id: shop.id,
                    psp: shop.choose_psp? `${shop.choose_psp.platform}: ID - ${shop.choose_psp.account_id}` : null,
                    psp_id: shop.choose_psp? shop.choose_psp.oauth_psp_id : null,
                    psp_verify: shop.choose_psp? shop.choose_psp.oauth_status : null,
                    can_delete: shop.can_delete,
                }))
                setRows(updatedData);
                updateRows(updatedData);
                console.log(allAuthedPlatforms);
            } else {
                console.error("Oauth list error:", res.message);
            }
        } catch (error) {
            console.error("Error fetching authorized platforms", error);
        }
    };

    const statusMapping = {
        0: "pending",
        1: "pass",
        2: "warning",
    };

    const getMappedStatus = (status: number) => {
        return statusMapping[status] || "Unknown";
    };

    const verificationMapping = {
        0: "校验中",
        1: "已通过",
        2: "不通过",
    };

    const getVerification = (value: number) => {
        return verificationMapping[value] || "Unknown";
    };


    const fetchUrlAndToken = async (
        platform: IPlatform | null,
        psp: IPsp | null,
        pingpongData: IPingPongData | null = null,
        shop_id: number
    ): Promise<boolean> => {
        try {
            const metaData: Record<string, any> = {
                scene: "psp",
                bind_ouath_shop_id: shop_id,
                ...(pingpongData || {})
            };

            const currentUrl = window.location.href;
            const res = await getAuthURL({
                platform_shop_id: platform?.id || null,
                platform_psp_id: psp?.id || null,
                redirect_url: currentUrl,
                meta_data: metaData
            });
            if (res.code !== 0) throw new Error("Auth URL fetch failed");
            setAuthUrl(res.data.oauth_url);
            setAuthToken(res.data.id_token);
            fetchPaymentGateway()

            if (psp?.is_callback != 1 ) {
                setShowPPModal(false);
                setShowAuthConfirm(true);
                window.open(res.data.oauth_url, "_blank");
            } else {
                window.location.href = res.data.oauth_url;
            }
            return true;
        } catch (err) {
            console.error(err);
            alert("Error fetching auth URL");
            return false;
        }
    };

    React.useEffect(() => {
        setRows(rows);
        setAuthedPlatforms(authedPlatforms);
    }, [rows, authedPlatforms]);

    useEffect(()=>{
        fetchPaymentGateway()
    },[])



    const handlePspComplete = async (psp: IPsp, shop_id: number) => {
        if (psp && psp.platform.toLowerCase() === "PingPong".toLowerCase()) {
            setOpenPspModal(false);
            setShowPPModal(true);
        } else {
            console.log('ready to bind new psp acc', psp);
            const res = await fetchUrlAndToken(null, psp, null, shop_id);
            if (res) {
                setModalStep('none');
                setShowPSPSelection(false);
            } else {
                console.error("Bind psp to shop error");
            }
        }
    }
    
    const handleSelectOrAddPaymentGatewayComplete = async (psp: OauthPspItem | null, shop_id: number) => {
        if (psp === null){
            await handlePspComplete(selectedPaymentGateway, shop_id);
        }else if (psp) { 
            await handleBindExistingPsp(psp, shop_id);
        }
    }
    
    const handleBindExistingPsp = async (selectedPsp: OauthPspItem, shop_id: number) => {
        console.log('selectedPlatform:' , selectedPlatform);
        // existing psp acc
        try{
            const res = await bindPSPtoShop({shop_id: shop_id, bind_psp_id: selectedPsp.id});
            if (res.code === 0) {
                setShowPSPSelection(false);
                AppHelper.msgApi.success("授权成功");
                setTimeout(() => {
                    window.location.reload();
                }, 2000);

            } else {
                console.error("Bind psp to shop error:", res.message);
            }
        } catch (error) {
            console.error("Error fetching authorized shop data", error);
        }
    };
    
    const checkPspAuthed = async (psp: IPsp, shop_id: number) => {
        setSelectedPaymentGateway(psp);
        const alreadyAuthed = authedPlatforms.psp.some(auth => auth.platform_id === psp.id);
        if (alreadyAuthed){
            setOpenPspModal(false);
            setShowPSPSelection(true);
        }else{
            await handlePspComplete(psp, shop_id);
        }
        fetchData();
    }

    const handlePingPongComplete = async (pingpongData: IPingPongData | null | undefined) => {
        if (!allRows) return;
        const targetPlatform = allRows.find((row) => row.platform_id === selectedPlatform?.id);
        if (selectedPlatform && selectedPaymentGateway && pingpongData) {
            await fetchUrlAndToken(null , selectedPaymentGateway, pingpongData, activeRowId || targetPlatform.platform_shop_id);
        }
    };

    return (
        <div className="auth-shop-section">
            
            <PlatformMainModal modalStep={modalStep} setModalStep={setModalStep} authedPlatforms={allAuthedPlatforms} redirectPath={window.location.href}/>
            <OurWechatModal
                open={showWechatModal}
                onClose={() => setShowWechatModal(false)}
                message={"有任何问题，欢迎联系我们～"}
                hasAlertIcon={true}
                textAlign='center'
                />
            <Form form={form} layout="vertical">
                <div className="section">
                    <div className="section-title-container">
                        <h4 className="section-title">授权店铺</h4>
                        {addShopButton === true && (
                            <button className="add-shop-btn" onClick={addNewShop}>
                            <img
                                src={shopIcon}
                                style={{ width: "20px", height: "20px", marginTop: "-4px", marginRight: "4px" }}
                                alt="shop"
                            />
                            添加店铺继续提额
                        </button>)}
                    </div>

                    <div className="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>平台</th>
                                    <th>店铺ID</th>
                                    <th>授权状态</th>
                                    <th>授信状态</th>
                                    <th>收款公司</th>
                                    <th>收款公司校验</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {allRows.map((row, index) => (
                                    <tr key={index}>
                                        <td>{row.id}</td>
                                        <td>{row.platform}</td>
                                        <td>{row.shop_id}</td>
                                        <td style={{color: row.auth_status === 1 ? '#282830' : '#DD4C4C'}}>{row.auth_status === 0? "校验中" : row.auth_status === 1 ? "有效" : "无效"}</td>
                                        <td style={{color: row.credit_status === 1 ? '#282830' : '#DD4C4C'}}>{row.credit_status === 1 ? "可授信" : "不可授信"}</td>
                                        <td> {row.psp} </td>
                                        <td>
                                            {row.psp_verify != null && <StatusBadge
                                                status={getMappedStatus(row.psp_verify)}
                                                label={getVerification(row.psp_verify)}
                                            />}
                                        </td>
                                        <td>
                                            <div className="button-container">
                                                {canEdit && (!row.psp || row.psp_verify===2) && (
                                                        <>
                                                            <button
                                                                className="edit-button"
                                                                onClick={() => {
                                                                    const selectedPlatformObj = platforms.find((p: IPsp) => p.id === row.platform_id);
                                                                    setActiveRowId(row.platform_shop_id);
                                                                    setSelectedPlatform(selectedPlatformObj? selectedPlatformObj : null);
                                                                    setOpenPspModal(true);
                                                                }}
                                                            >
                                                                编辑收款公司
                                                            </button>
                                                            {openPspModal&&<PspSelectionModal
                                                                open={openPspModal}
                                                                onClose={() => setOpenPspModal(false)}
                                                                onComplete={checkPspAuthed}
                                                                payment_gateway={payment_gateway}
                                                                platform_id={activeRowId || row.platform_shop_id}
                                                            />}
                                                            {showPSPSelection && <SelectOrAddPaymentGateway
                                                                open={showPSPSelection}
                                                                onClose={() => {
                                                                    setShowPSPSelection(false);
                                                                    setOpenPspModal(true);
                                                                }}
                                                                onComplete={handleSelectOrAddPaymentGatewayComplete}
                                                                paymentGateways={allAuthedPlatforms.psp}
                                                                selectedPSP={selectedPaymentGateway}
                                                                platform_id={activeRowId || row.platform_shop_id}
                                                            />}
                                                            {showPPModal && <PingPongModal
                                                                open={showPPModal}
                                                                onClose={() => setShowPPModal(false)}
                                                                onComplete={handlePingPongComplete}
                                                                payment_gateway={selectedPaymentGateway}
                                                                platform={selectedPlatform}
                                                            />}
                                                            {showAuthConfirm && <AuthConfirmationModal
                                                                open={showAuthConfirm}
                                                                onClose={() => setShowAuthConfirm(false)}
                                                                onComplete={async () => {
                                                                    setShowAuthConfirm(false);
                                                                    const currentPath = window.location.href;
                                                                    const newParam = `id_token=${authToken}`; 
                                                                    const newPath = `${currentPath}?${newParam}`;
                                                                    window.location.href = newPath;
                                                                }}
                                                                authUrl={authUrl}
                                                                isHandleAuth={true}
                                                                isOpenNewTab={(selectedPaymentGateway?.is_callback ?? selectedPlatform?.is_callback) !== 1}
                                                            />}
                                                            
                                                        </>
                                                    )}
                                                    {(!row.psp || row.psp_verify===2) && (rows.length > 1 && canDelete === true && row.can_delete == 1) 
                                                    && <div className="separator"></div>}
                                                    {(rows.length > 1 && canDelete === true && row.can_delete == 1) && (
                                                        <>
                                                            <button
                                                                className="delete-button"
                                                                onClick={() => {
                                                                    setActiveRowId(row.platform_shop_id);
                                                                    handleShowConfirmDelete(true);
                                                                }}
                                                            >
                                                                删除
                                                            </button>
                                                            <Modal
                                                                open={showConfirmDelete}
                                                                onClose={() => handleShowConfirmDelete(false)}
                                                                title="温馨提示"
                                                                >
                                                                <div>
                                                                    <p className="mb-4" style={{ textAlign: 'center' }}>
                                                                        您是否确认删除该店铺信息？
                                                                    </p>
                                                                    
                                                                    <div className="flex justify-center mt-10" style={{ gap: '16px' }}>
                                                                    <SecondaryButton
                                                                        label="取消"
                                                                        onClick={() => handleShowConfirmDelete(false)}
                                                                    />
                                                                    <Button
                                                                        type="primary"
                                                                        label="确定"
                                                                        onClick={() => {
                                                                            handleDeletePsp(activeRowId || row.platform_shop_id); // Use the active row ID
                                                                            handleShowConfirmDelete(false); // Close the modal
                                                                        }}
                                                                        style={{
                                                                        height: '40px',
                                                                        width: '88px'
                                                                        }}
                                                                    />
                                                                    </div>
                                                                    
                                                                </div>
                                                            </Modal>
                                                        </>
                                                    )}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </Form>
        </div>
    );
};

export default AuthShop;
