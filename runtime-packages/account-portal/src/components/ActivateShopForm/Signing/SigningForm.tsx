import React, { Suspense, useEffect, useRef } from 'react';
import { Spin } from 'antd';
import Stepper from '@/components/shared/Stepper';

// Import shared styles
import './styles.scss';
import '../styles.scss';

// Import components and hooks
import SigningSection from './SigningSection';
import ActionButtons from './ActionButtons';
import { useSigningForm } from './hooks';
import { ErrorModal, SubmitModal, InitiateSuccessModal } from './modals';

const SigningForm: React.FC<{ type: 'hook' | 'underwriting' }> = ({ type }) => {
  const signingFormRef = useRef<any>(null);
  const { state, actions } = useSigningForm(type);
  
  // Fetch signer information when component mounts
  useEffect(() => {
    actions.fetchSignerInfo();
  }, [actions.fetchSignerInfo]);

  // Define steps for the stepper component
  const steps = [
    {
      title: '平台授权信息',
      content: (<></>),
      action: null,
    },
    {
      title: '激活签约',
      content: (
        <Suspense fallback={<Spin size="large"/>} >
          <div style={{padding: '0  24px'}}>
          <SigningSection
            formRef={signingFormRef}
            signingPersons={state.signingPersons}
            onInitiateSign={() => actions.handleInitiateSign(signingFormRef)}
            onUrgeSign={actions.handleUrgeSign}
            onSubmitApplication={actions.handleSubmitApplication}
            onRefresh={actions.fetchSignerInfo}
            loading={state.isLoading}
            setIsLoading={actions.setIsLoading}
            currentStage={state.currentStage}
          />
          </div>
        </Suspense>
      ),
      action: (
        <ActionButtons
          currentStage={state.currentStage}
          isSubmitting={state.isSubmitting}
          isLoading={state.isLoading}
          onInitiateSign={() => actions.handleInitiateSign(signingFormRef)}
          onUrgeSign={actions.handleUrgeSign}
          onRefresh={actions.fetchSignerInfo}
          onSubmitApplication={actions.handleSubmitApplication}
          formRef={signingFormRef}
        />
      ),
    },
  ];

  return (
    <div className="signing-form-wrapper">
      <Stepper
        steps={steps}
        currentStep={1}
        borderTopRadiusRounded={false}
      />
      
      <ErrorModal
        open={state.showErrorModal}
        onClose={actions.handleCloseErrorModal}
        errorMessage={state.errorMessage}
      />
      
      <SubmitModal
        open={state.showSubmitModal}
        onClose={actions.handleConfirmSubmit}
        onConfirm={actions.handleConfirmSubmit}
      />
      
      <InitiateSuccessModal
        open={state.showInitiateSuccessModal}
        onClose={actions.handleCloseInitiateSuccessModal}
      />
    </div>
  );
};

export default SigningForm; 