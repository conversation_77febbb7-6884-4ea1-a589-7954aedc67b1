import { useState, useCallback } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { getSignerInfo, initiateSign, urgeSign } from '@fundpark/fp-api';
import { mapSignerInfoApiToUi } from '../../../mappers/drawdownForm';
import { SigningPerson, SigningStage } from './types';
import { usePositionTracking } from '@/hooks/usePositionTracking';
import { VerificationStatusCodes as ApiResponseCodes, ErrorMessages, SuccessMessages, Routes } from '@/constants/signing';

/**
 * Determines the current signing stage based on the status of signing persons
 * 
 * @param signingPersons Array of signing persons with their status
 * @returns The appropriate SigningStage
 */
export const determineSigningStage = (signingPersons: SigningPerson[]): SigningStage => {
  // Filter to only include persons who need to sign
  const personsWhoNeedToSign = signingPersons.filter(person => person.needSign === true);
  
  // No signing persons who need to sign yet
  if (personsWhoNeedToSign.length === 0) {
    return SigningStage.PENDING;
  }
  
  // Check if all persons who need to sign have signed
  const allSigned = personsWhoNeedToSign.every(person => person.status === 'signed');
  if (allSigned) {
    return SigningStage.SIGNED;
  }
  
  // Check if any person who needs to sign is in the signing process
  const anySigning = personsWhoNeedToSign.some(person => person.status === 'signing');
  const anyUnhappy = personsWhoNeedToSign.some(person => person.status === 'rejected' || person.status === 'expired' || person.status === 'withdrawn' || person.status === 'failed');
  if (anySigning || anyUnhappy) {
    return SigningStage.SIGNING;
  }
  
  // Default case: pending
  return SigningStage.PENDING;
};

/**
 * Custom hook to manage signing data and operations
 */
export const useSigningForm = (type: 'hook' | 'underwriting') => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [signingPersons, setSigningPersons] = useState<SigningPerson[]>([]);
  const [currentStage, setCurrentStage] = useState<SigningStage>(SigningStage.PENDING);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | React.ReactNode>("");
  const [showSubmitModal, setShowSubmitModal] = useState<boolean>(false);
  const [showInitiateSuccessModal, setShowInitiateSuccessModal] = useState<boolean>(false);
  const navigate = useNavigate();
  const { trackPosition } = usePositionTracking();

  /**
   * Fetch signer information from API and update component state
   * @returns Promise that resolves when the operation is complete
   */
  const fetchSignerInfo = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await getSignerInfo();
      if (response.code === 404003) {
        const route = type === 'hook' ? Routes.HOME_PAGE : Routes.HOME_PAGE_CAA;
        navigate(route);
        return;
      }
    //   const response = {
    //     "code": 0,
    //     "data": [
    //         {
    //             "id": 5,
    //             "company_id": 355,
    //             "seq": 1,
    //             "position": [
    //                 "董事",
    //                 "股东"
    //             ],
    //             "full_name_chn": "张大民",
    //             "full_name_eng": "Cheung Tai Man",
    //             "birth_date": "1968-1-1",
    //             "region_code": "1",
    //             "papers_type": "hkidcard",
    //             "phone_region_code": "+852",
    //             "phone_number": "92222222",
    //             "email": "<EMAIL>",
    //             "front_id_file_id": "",
    //             "front_id_file_name": "",
    //             "frontIDPreviewLink": "",
    //             "back_id_file_id": "",
    //             "back_id_file_name": "",
    //             "backIDPreviewLink": "",
    //             "front_file_id": 550,
    //             "front_file_name": "sfz2.png",
    //             "frontPreviewLink": "https://datahub-uat.s3.ap-southeast-1.amazonaws.com/papers/7/20250426-gl90mcBlQF.png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASAUIZ6MWW5SNROXZ%2F20250516%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20250516T025229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1800&X-Amz-Signature=1ff2c1471642517861a4e544b45f468e0f569684fb9dbe1f2dbaba65850d0da9",
    //             "back_file_id": 551,
    //             "back_file_name": "sfz.png",
    //             "backPreviewLink": "https://datahub-uat.s3.ap-southeast-1.amazonaws.com/papers/7/20250427-glfuBdnARl.png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASAUIZ6MWW5SNROXZ%2F20250516%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20250516T025229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1800&X-Amz-Signature=499d82160e4d744ad8a60a8cac248fbcdbe4b792b00d17e16405c9252d008cc5",
    //             "papers_number": "H1234567",
    //             "papers_name": "张大民",
    //             "papers_name_en": "Cheung Tai Man",
    //             "papers_data": {
    //                 "sex": "男M",
    //                 "nameCn": "张大民",
    //                 "nameEn": "Cheung Tai Man",
    //                 "idNumber": "H1234567",
    //                 "nameCode": "2621 2535 5174",
    //                 "birthDate": "01-01-1968",
    //                 "issuedCode": "***AZ",
    //                 "issuedDate": "15-09-03",
    //                 "firstIssuedDate": "(01-79)"
    //             },
    //             "counter_fraud_status": 0,
    //             "counter_fraud_time": null,
    //             "rpa_check_result": 1,
    //             "sign_record": {
    //                 "id": 13,
    //                 "company_id": 355,
    //                 "company_agreement_id": 9,
    //                 "member_id": 5,
    //                 "docs_num": 2,
    //                 "sign_status": 2,
    //                 "sign_date": null,
    //                 "status": 1,
    //                 "created_at": "2025-05-07T11:56:00.000000Z",
    //                 "updated_at": "2025-05-16T02:39:07.000000Z"
    //             },
    //             "company_agreement_detail_status": 1,
    //             "sign_status": 2
    //         },
    //         {
    //             "id": 6,
    //             "company_id": 355,
    //             "seq": 2,
    //             "position": [
    //                 "股东"
    //             ],
    //             "full_name_chn": "李四",
    //             "full_name_eng": "Li Si",
    //             "birth_date": "1968-1-1",
    //             "region_code": "1",
    //             "papers_type": "idcard",
    //             "phone_region_code": "+86",
    //             "phone_number": "18617261111",
    //             "email": "<EMAIL>",
    //             "front_id_file_id": 894,
    //             "front_id_file_name": "图像 copy 2.jpeg",
    //             "frontIDPreviewLink": "https://datahub-uat.s3.ap-southeast-1.amazonaws.com/XDJ/387/20250512-jKgijzGQNr.jpeg?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASAUIZ6MWW5SNROXZ%2F20250516%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20250516T025229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1800&X-Amz-Signature=17e9542b6dc64f8e6414a8d81a3062ad1a9d9006b38f10ef013b6448cbcfebe6",
    //             "back_id_file_id": "",
    //             "back_id_file_name": "",
    //             "backIDPreviewLink": "",
    //             "front_file_id": 550,
    //             "front_file_name": "sfz2.png",
    //             "frontPreviewLink": "https://datahub-uat.s3.ap-southeast-1.amazonaws.com/papers/7/20250426-gl90mcBlQF.png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASAUIZ6MWW5SNROXZ%2F20250516%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20250516T025229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1800&X-Amz-Signature=1ff2c1471642517861a4e544b45f468e0f569684fb9dbe1f2dbaba65850d0da9",
    //             "back_file_id": 551,
    //             "back_file_name": "sfz.png",
    //             "backPreviewLink": "https://datahub-uat.s3.ap-southeast-1.amazonaws.com/papers/7/20250427-glfuBdnARl.png?X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIASAUIZ6MWW5SNROXZ%2F20250516%2Fap-southeast-1%2Fs3%2Faws4_request&X-Amz-Date=20250516T025229Z&X-Amz-SignedHeaders=host&X-Amz-Expires=1800&X-Amz-Signature=499d82160e4d744ad8a60a8cac248fbcdbe4b792b00d17e16405c9252d008cc5",
    //             "papers_number": "310101198610203222",
    //             "papers_name": "李四",
    //             "papers_name_en": "Li Si",
    //             "papers_data": {
    //                 "sex": "男M",
    //                 "nameCn": "张大民",
    //                 "nameEn": "Cheung Tai Man",
    //                 "idNumber": "H1234567",
    //                 "nameCode": "2621 2535 5174",
    //                 "birthDate": "01-01-1968",
    //                 "issuedCode": "***AZ",
    //                 "issuedDate": "15-09-03",
    //                 "firstIssuedDate": "(01-79)"
    //             },
    //             "counter_fraud_status": 0,
    //             "counter_fraud_time": null,
    //             "rpa_check_result": 1,
    //             "sign_record": {
    //                 "id": 14,
    //                 "company_id": 355,
    //                 "company_agreement_id": 9,
    //                 "member_id": 6,
    //                 "docs_num": 2,
    //                 "sign_status": 2,
    //                 "sign_date": null,
    //                 "status": 1,
    //                 "created_at": "2025-05-07T11:56:00.000000Z",
    //                 "updated_at": "2025-05-16T02:39:07.000000Z"
    //             },
    //             "company_agreement_detail_status": 1,
    //             "sign_status": 2
    //         }
    //     ],
    //     "message": "success"
    // }
      
      // Use mapper to transform API data to UI format
      const transformedData = mapSignerInfoApiToUi(response);
      setSigningPersons(transformedData);
      
      // Determine and set current stage based on signing status
      setCurrentStage(determineSigningStage(transformedData));
    } catch (error) {
      console.error('Failed to fetch signer information:', error);
      message.error(ErrorMessages.FETCH_ERROR);
    } finally {
      setIsLoading(false);
    }
  }, [type]);

  /**
   * Handle showing and closing error modal
   */
  const handleCloseErrorModal = useCallback(() => {
    setShowErrorModal(false);
  }, []);

  /**
   * Handle closing initiate success modal
   */
  const handleCloseInitiateSuccessModal = useCallback(async () => {
    setShowInitiateSuccessModal(false);
    await fetchSignerInfo();
  }, [fetchSignerInfo]);

  /**
   * Initiate signing process
   * @param formRef Reference to the form for validation
   * @returns Promise that resolves when the sign initiation is complete
   */
  const handleInitiateSign = useCallback(async (formRef: React.MutableRefObject<any>): Promise<void> => {
    try {
      // Validate form first
      if (formRef.current) {
        try {
          await formRef.current.validateFields();

          // Trigger saveSignerInfo for all signing cards before initiating sign
          if (formRef.current.saveAllSignerInfo) {
            await formRef.current.saveAllSignerInfo();
          }
        } catch (validationError) {
          message.error(ErrorMessages.VALIDATION_ERROR);
          return;
        }
      }

      setIsSubmitting(true);

      const response = await initiateSign();

      if (response.code === ApiResponseCodes.SUCCESS) {
        // Show success modal instead of message
        setShowInitiateSuccessModal(true);
      } else {
        // Display specific error message based on response code
        let errorMsg = ErrorMessages.INITIATE_ERROR;

        if (response.code === ApiResponseCodes.SIGNER_INFO_MISMATCH) {
          errorMsg = ErrorMessages.INFO_MISMATCH;
        } else if (response.code === ApiResponseCodes.RPA_INFO_MISMATCH) {
          errorMsg = ErrorMessages.RPA_MISMATCH;
        } else if (response.code === ApiResponseCodes.ANTI_FRAUD_FAILED) {
          errorMsg = ErrorMessages.ANTI_FRAUD_FAILED;
        } else if (response.code === ApiResponseCodes.RPA_INFO_NOT_FOUND) {
          errorMsg = ErrorMessages.RPA_INFO_NOT_FOUND;
        } else if (response.code === ApiResponseCodes.COMPANY_NOT_FOUND) {
          errorMsg = ErrorMessages.COMPANY_NOT_FOUND;
        } else if (response.code === ApiResponseCodes.MEMBER_NOT_FOUND) {
          errorMsg = ErrorMessages.MEMBER_NOT_FOUND;
        } else if (response.code === ApiResponseCodes.MEMBER_NOT_FOUND_IN_CURRENT_COMPANY) {
          errorMsg = ErrorMessages.MEMBER_NOT_FOUND_IN_CURRENT_COMPANY;
        } else if (response.code === ApiResponseCodes.MEMBER_HAS_BEEN_SIGNED) {
          errorMsg = ErrorMessages.MEMBER_HAS_BEEN_SIGNED;
        } else if (response.code === ApiResponseCodes.FIND_MORE_THAN_ONE_CONNECTED_COMPANY) {
          errorMsg = ErrorMessages.FIND_MORE_THAN_ONE_CONNECTED_COMPANY;
        }

        // Set error message and show modal instead of using message.error
        setErrorMessage(errorMsg);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Failed to initiate signing process:', error);
      setErrorMessage(ErrorMessages.INITIATE_ERROR);
      setShowErrorModal(true);
    } finally {
      setIsSubmitting(false);
    }
  }, [fetchSignerInfo]);

  /**
   * Send signing reminders
   * @returns Promise that resolves when the reminder is sent
   */
  const handleUrgeSign = useCallback(async (): Promise<void> => {
    try {
      setIsSubmitting(true);

      const response = await urgeSign();

      if (response.code === ApiResponseCodes.SUCCESS) {
        message.success(SuccessMessages.URGE_SUCCESS);
      } else if (response.code === ApiResponseCodes.AGREEMENT_HAS_BEEN_SENT) {
        message.error(ErrorMessages.AGREEMENT_HAS_BEEN_SENT);
      } else if (response.code === ApiResponseCodes.AGREEMENT_GENERATING) {
        message.error(ErrorMessages.AGREEMENT_GENERATING);
      } else {
        message.error(ErrorMessages.URGE_ERROR);
      }
    } catch (error) {
      console.error('Failed to send signing reminder:', error);
      message.error(ErrorMessages.URGE_ERROR);
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  /**
   * Handle submit application after all signings are complete
   */
  const handleSubmitApplication = useCallback(async () => {
    await trackPosition({ path: `/credit/${type}/home` });
    setShowSubmitModal(true);
  }, [trackPosition, type]);

  /**
   * Confirm submission and navigate away
   */
  const handleConfirmSubmit = useCallback(() => {
    setShowSubmitModal(false);
    const route = type === 'hook' ? Routes.HOME_PAGE : Routes.HOME_PAGE_CAA;
    navigate(route);
  }, [navigate, type]);

  return {
    state: {
      isSubmitting,
      isLoading,
      signingPersons,
      currentStage,
      showErrorModal,
      errorMessage,
      showSubmitModal,
      showInitiateSuccessModal
    },
    actions: {
      setIsLoading,
      fetchSignerInfo,
      handleCloseErrorModal,
      handleInitiateSign,
      handleUrgeSign,
      handleSubmitApplication,
      handleConfirmSubmit,
      handleCloseInitiateSuccessModal
    }
  };
}; 