import { SignRecord } from "@fundpark/fp-api";

// SigningStatus type for the UI representation of sign status
export type SigningStatus = 'pending' | 'signing' | 'signed' | 'rejected' | 'expired' | 'withdrawn' | 'failed';

// Interface for signing person data for UI display
export interface SigningPerson {
  id: string;
  englishName?: string;
  chineseName?: string;
  role: string;
  status: SigningStatus;
  personId: string;
  nationality?: string;
  email?: string;
  mobilePhoneAreaCode?: string;
  mobilePhoneNumber?: string;
  papersType?: string;
  frontFileId?: number;
  frontPreviewLink?: string;
  backFileId?: number;
  backPreviewLink?: string;
  signRecords?: SignRecord[];
  frontFileName?: string;
  backFileName?: string;  
  needSign?: boolean;
  address?: string;
  extraFrontFileLink?: string;
  extraBackFileLink?: string;
  extraFrontFileId?: number;
  extraBackFileId?: number;
}

// Interface for form props to be passed to the SigningCard component
export interface SigningCardFormValues {
  email: string;
  phoneNumber: string;
  phoneAreaCode: string;
  idType: string;
  nationality: string;
  frontPhoto?: File;
  backPhoto?: File;
}

// Props interface for the SigningForm component
export interface SigningFormProps {
  // Add any props if needed
}

// Enum for representing the overall signing stage of the process
export enum SigningStage {
  PENDING = 'pending',    // All sign_status = 0
  SIGNING = 'signing',    // Contains some sign_status = 1
  SIGNED = 'signed',       // All sign_status = 2
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  WITHDRAWN = 'withdrawn',
  FAILED = 'failed'
}

// Props for the SigningSection component
export interface SigningSectionProps {
  formRef: React.MutableRefObject<any>;
  signingPersons?: SigningPerson[];
  onInitiateSign: () => Promise<void>;
  onUrgeSign: () => Promise<void>;
  onSubmitApplication?: () => void;
  onRefresh?: () => Promise<void>;
  loading?: boolean;
  currentStage: SigningStage;
  setIsLoading: (loading: boolean) => void;
} 