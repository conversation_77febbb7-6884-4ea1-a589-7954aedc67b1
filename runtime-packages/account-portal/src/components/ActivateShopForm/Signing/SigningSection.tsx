import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Form } from 'antd';
import InfoBanner from '@/components/shared/InfoBanner';
import SigningCard from './SigningCard';
import { SigningCardRef, SigningCardFormValues } from './SigningCardTypes';
import { SigningSectionProps, SigningStage } from './types';
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import './SigningSection.scss';

const SigningSection: React.FC<SigningSectionProps> = ({
  formRef,
  signingPersons = [],
  onInitiateSign,
  onUrgeSign,
  onSubmitApplication,
  onRefresh,
  loading,
  currentStage,
  setIsLoading
}) => {
  const [signingInfo, setSigningInfo] = useState<Record<string, SigningCardFormValues>>({});
  const [wechatModalOpen, setWechatModalOpen] = useState(false);

  // Refs to each SigningCard
  const cardRefs = useRef<Record<string, SigningCardRef>>({});

  // Determine if fields should be disabled based on current stage
  const fieldsDisabled = currentStage === SigningStage.SIGNING || currentStage === SigningStage.SIGNED;

  // If formRef is provided, assign our validation method to it
  useEffect(() => {
    if (formRef) {
      formRef.current = {
        cardRefs: cardRefs,
        validateFields: async () => {
          // Validate all SigningCard forms
          try {
            const cardPromises = Object.entries(cardRefs.current).map(async ([id, cardRef]) => {
              try {
                const values = await cardRef.validate();
                return { id, values, valid: true };
              } catch (error) {
                return { id, valid: false, error };
              }
            });

            const results = await Promise.all(cardPromises);
            const invalidCards = results.filter(result => !result.valid);

            if (invalidCards.length > 0) {
              // Find the first person with invalid data
              const firstInvalidPerson = signingPersons.find(
                person => invalidCards.some(card => card.id === person.id)
              );

              if (firstInvalidPerson) {
                const name = firstInvalidPerson.chineseName ||
                  firstInvalidPerson.englishName ||
                  `关联人 (${firstInvalidPerson.role})`;
              }

              throw new Error('Validation failed');
            }

            // Combine all values into one object
            const allValues = results.reduce((acc, result) => {
              if (result.valid && result.values) {
                acc[result.id] = result.values;
              }
              return acc;
            }, {} as Record<string, SigningCardFormValues>);

            return allValues;
          } catch (error) {
            throw error;
          }
        },

        // New method to save all signer info
        saveAllSignerInfo: async () => {
          // Check if there are any cards to process
          if (Object.keys(cardRefs.current).length === 0) {
            return;
          }

          // For each signing card, trigger the data collection and API call
          const promises = signingPersons.map(async (person) => {
            const cardRef = cardRefs.current[person.id];
            if (!cardRef) return;

            try {
              // Get all the form values
              const formValues = cardRef.form.getFieldsValue();

              // Get uploaded file values directly from the exposed uploadedFiles
              const uploadedFiles = cardRef.uploadedFiles || {};
              
              // Get all possible file IDs
              const idDocumentFront = uploadedFiles.idDocumentFront?.file_id || person.frontFileId;
              const idDocumentBack = uploadedFiles.idDocumentBack?.file_id || person.backFileId;
              const extraDocumentFront = uploadedFiles.extraDocumentFront?.file_id || person.extraFrontFileId;
              const extraDocumentBack = uploadedFiles.extraDocumentBack?.file_id || person.extraBackFileId;

              // Check required form fields (address is required for non-China)
              const isRequiredFieldsValid = formValues.nationality && 
                formValues.idType && 
                formValues.phoneCountryCode && 
                formValues.phoneNumber && 
                formValues.email &&
                (formValues.nationality === '2' || formValues.address); // Address required for non-China

              // Check if we have at least the basic required documents and form fields
              if (idDocumentFront && idDocumentBack && isRequiredFieldsValid) {
                // Prepare request data
                const requestData: any = {
                  region_code: formValues.nationality,
                  phone_region_code: formValues.phoneCountryCode,
                  phone_number: formValues.phoneNumber,
                  email: formValues.email
                };

                // Include address for non-China nationalities
                if (formValues.nationality !== '2' && formValues.address) {
                  requestData.address = formValues.address;
                }

                // Add file IDs
                if (idDocumentFront) {
                  requestData.front_id_file_id = idDocumentFront;
                }
                if (idDocumentBack) {
                  requestData.back_id_file_id = idDocumentBack;
                }
                if (extraDocumentFront) {
                  requestData.front_file_id = extraDocumentFront;
                }
                if (extraDocumentBack) {
                  requestData.back_file_id = extraDocumentBack;
                }

                // Include papers_type specifically for Hong Kong Travel Permit (港澳居民来往内地通行证)
                if (formValues.idType === 'hkIdWithTravel' && (extraDocumentFront || extraDocumentBack)) {
                  requestData.papers_type = "exitentrypermittomainland";
                }

                // Set loading state
                setIsLoading(true);

                // Import the saveSignerInfo function from API 
                const { saveSignerInfo } = await import('@fundpark/fp-api');

                // Call the API with the data
                return saveSignerInfo(person.id)(requestData)
                  .then((response) => {
                    // Handle specific error responses like in checkAndSaveSignerInfo
                    if (response && response.code === 1) {
                      // 未识别的情况 - Document not recognized
                      console.warn(`Document not recognized for person ${person.id}`);
                      return { success: false, code: 1, personId: person.id };
                    }

                    if (response && response.code === 200400) {
                      // 身份信息不匹配的情况 - Identity mismatch
                      console.warn(`Identity mismatch for person ${person.id}`);
                      return { success: false, code: 200400, personId: person.id };
                    }

                    return { success: true, personId: person.id };
                  })
                  .catch((error) => {
                    console.error(`Error saving signer info for ${person.id}:`, error);
                    return { success: false, error, personId: person.id };
                  });
              }
            } catch (error) {
              console.error(`Error preparing data for ${person.id}:`, error);
              return { success: false, error, personId: person.id };
            }
          });

          try {
            const results = await Promise.all(promises.filter(Boolean));
            return results;
          } finally {
            setIsLoading(false);
          }
        }
      };
    }
  }, [formRef, signingPersons, setIsLoading]);

  // Handle form change for each card
  const handleCardFormChange = (id: string) => (values: SigningCardFormValues) => {
    setSigningInfo(prev => ({
      ...prev,
      [id]: values
    }));
  };

  return (
    <div className="signing-section">
      <div className="section">
        <div className="section-title-container">
          <h4 className="section-title">线上签约</h4>
          <InfoBanner
            type="info"
            message="合同文件將发送到以下关联人的邮箱及手机号，请注意查收"
            width="581px"
          />
        </div>

        <div className="signing-cards-container">
          {signingPersons.length > 0 ? (
            signingPersons.map(person => (
              <SigningCard
                key={person.id}
                ref={ref => {
                  if (ref) {
                    cardRefs.current[person.id] = ref;
                  }
                }}
                englishName={person.englishName}
                chineseName={person.chineseName}
                role={person.role}
                status={person.status}
                personId={person.id}
                onFormChange={handleCardFormChange(person.id)}
                nationality={person.nationality}
                email={person.email}
                mobilePhoneAreaCode={person.mobilePhoneAreaCode}
                mobilePhoneNumber={person.mobilePhoneNumber}
                papersType={person.papersType}
                frontPreviewLink={person.frontPreviewLink}
                backPreviewLink={person.backPreviewLink}
                frontFileId={person.frontFileId}
                backFileId={person.backFileId}
                disabled={fieldsDisabled}
                frontFileName={person.frontFileName}
                backFileName={person.backFileName}
                setIsLoading={setIsLoading}
                needSign={person.needSign}
                address={person.address}
                extraFrontFileLink={person.extraFrontFileLink}
                extraBackFileLink={person.extraBackFileLink}
                extraFrontFileId={person.extraFrontFileId}
                extraBackFileId={person.extraBackFileId}
              />
            ))
          ) : (
            <div className="empty-state">加载中，请稍候...</div>
          )}
        </div>

        <div className="contact-info-text">
          如以上关联人信息不正确，请<span className="contact-link" onClick={(e) => { e.preventDefault(); setWechatModalOpen(true); }}>联系我们</span>更正？
        </div>

      </div>

      <OurWechatModal
        open={wechatModalOpen}
        onClose={() => setWechatModalOpen(false)}
        message="如需更正关联人信息，请联系我们的客服。"
      />
    </div>
  );
};

export default SigningSection; 