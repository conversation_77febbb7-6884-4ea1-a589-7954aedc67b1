import { ConfigType } from '@/constants/configTypes';

export type SigningStatus = 'pending' | 'signing' | 'signed' | 'rejected' | 'expired' | 'withdrawn' | 'failed';

export interface SigningCardFormValues {
  nationality?: string;
  idType?: string;
  phone?: string;
  email?: string;
  idDocumentFront?: string;
  idDocumentBack?: string;
}

export interface SigningCardRef {
  form: any;
  validate: () => Promise<SigningCardFormValues>;
  uploadedFiles: Record<string, UploadedFileInfo>;
}

// Interface for uploaded file information
export interface UploadedFileInfo {
  file_link: string;
  file_id: number;
  file_name: string;
}

export interface SigningCardProps {
  englishName?: string;
  chineseName?: string;
  role: string;
  status: SigningStatus;
  personId: string;
  onFormChange?: (values: SigningCardFormValues) => void;
  onUploadChange?: (field: string, value?: string, fileInfo?: UploadedFileInfo) => void;
  // Additional props from API data
  nationality?: string;
  email?: string;
  mobilePhoneAreaCode?: string;
  mobilePhoneNumber?: string;
  papersType?: string;
  frontPreviewLink?: string;
  backPreviewLink?: string;
  frontFileId?: number;
  backFileId?: number;
  frontFileName?: string;
  backFileName?: string;
  // Control props for form state
  disabled?: boolean;
  setIsLoading: (loading: boolean) => void;
  needSign?: boolean;
  address?: string;
  extraFrontFileLink?: string;
  extraBackFileLink?: string;
  extraFrontFileId?: number;
  extraBackFileId?: number;
}

// Document requirements for each ID type
export interface DocumentField {
  key: string;
  label: string;
  title: string;
  required: boolean;
}

export interface DocumentConfig {
  key: string;
  label: string;
  fields: DocumentField[];
}

export interface StatusConfig {
  label: string;
  backgroundColor: string;
  color: string;
}

export interface CountryOption {
  value: string;
  label: string;
} 