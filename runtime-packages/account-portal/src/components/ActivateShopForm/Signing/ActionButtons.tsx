import React, { useState } from 'react';
import { Col, Space } from 'antd';
import { But<PERSON> } from '@/components/shared/Button';
import SecondaryButton from '@/components/shared/SecondaryButton';
import TwoButtonModal from '@/components/shared/GenericModal';
import { SigningStage } from './types';
import { ButtonConfig } from '@/constants/signing';
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from '@/components/shared/tracking/constants';
import { useMatomoContext } from '@/contexts/MatomoContext';

interface ActionButtonsProps {
  currentStage: SigningStage;
  isSubmitting: boolean;
  isLoading: boolean;
  onInitiateSign: () => Promise<void>;
  onUrgeSign: () => Promise<void>;
  onRefresh: () => Promise<void>;
  onSubmitApplication: () => void;
  formRef?: React.MutableRefObject<any>;
}

/**
 * Component to render stage-appropriate action buttons
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  currentStage,
  isSubmitting,
  isLoading,
  onInitiateSign,
  onUrgeSign,
  onRefresh,
  onSubmitApplication,
  formRef
}) => {
  const { trackEvent } = useMatomoContext();
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Check if any signing person has Hong Kong ID type
  const hasHongKongIdType = () => {
    if (!formRef?.current) return false;
    
    try {
      // Get form values from all signing cards
      const cardRefs = formRef.current.cardRefs?.current || {};
      
      // Check each card for Hong Kong ID type
      for (const cardRef of Object.values(cardRefs)) {
        const formValues = (cardRef as any)?.form?.getFieldsValue();
        if (formValues?.idType === 'hkId') {
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Error checking ID types:', error);
      return false;
    }
  };

  const handleInitiateSignClick = () => {
    trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.INITIATE_SIGNING_BUTTON);
    
    if (hasHongKongIdType()) {
      setShowConfirmModal(true);
    } else {
      onInitiateSign();
    }
  };

  const handleConfirmModalClose = () => {
    setShowConfirmModal(false);
  };

  const handleConfirmModalComplete = () => {
    setShowConfirmModal(false);
    onInitiateSign();
  };

  switch (currentStage) {
    case SigningStage.PENDING:
      return (
        <>
          <Col>
            <Button
              type="primary"
              onClick={handleInitiateSignClick}
              loading={isSubmitting}
              disabled={isLoading}
              label="发送签约"
              style={{ width: ButtonConfig.PRIMARY_BUTTON_WIDTH }}
            />
          </Col>
          
          <TwoButtonModal
            open={showConfirmModal}
            onClose={handleConfirmModalClose}
            onComplete={handleConfirmModalComplete}
            title="温馨提示"
            content="补充上传【港澳居民来往内地通行证】线上签约更便捷哦～"
            completeBtnLabel="继续提交"
            isWidthAuto
            closeBtnLabel="返回补充"
          />
        </>
      );

    case SigningStage.SIGNING:
      return (
        <Col>
          <Space size={16}>
            <Button
              type="primary"
              onClick={onUrgeSign}
              loading={isSubmitting}
              disabled={isLoading}
              label="重新发送"
              style={{ width: ButtonConfig.ACTION_BUTTON_WIDTH }}
            />
            <SecondaryButton
              onClick={onRefresh}
              label="刷新页面"
              style={{ width: ButtonConfig.ACTION_BUTTON_WIDTH }}
            />
          </Space>
        </Col>
      );

    case SigningStage.SIGNED:
      return (
        <Col>
          <Button
            type="primary"
            onClick={onSubmitApplication}
            loading={isSubmitting}
            disabled={isLoading}
            label="提交申请"
            style={{ width: ButtonConfig.PRIMARY_BUTTON_WIDTH }}
          />
        </Col>
      );

    default:
      return null;
  }
};

export default ActionButtons; 