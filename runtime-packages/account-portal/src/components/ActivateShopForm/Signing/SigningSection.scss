.signing-section {
  width: 100%;
  padding: 24px 0;
  
  .section {
    
    .section-title-container {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-bottom: 24px;
      
      .section-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .section-spacing {
      margin-top: 2rem;
    }
    
    .form-row {
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      @media (max-width: 992px) { // smaller than lg
        display: block;
      }
    }
    
    .half-width-item {
      flex: 0 0 calc(50% - 15px);
      min-width: 0; // Prevents flex items from overflowing
      max-width: calc(50% - 15px); // Ensures exactly half width minus gap
      
      // Ensure inputs take full width of their container
      .ant-select, .ant-input, .ant-input-number {
        width: 100%;
      }
    }

    .full-width-item {
      flex: 0 0 100%;
      width: 100%;
    }
  }
  
  .signing-cards-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 24px;
  }
  
  .empty-state {
    width: 100%;
    padding: 16px;
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
  }
  
  .contact-info-text {
    margin-top: 8px;
    font-size: 14px;
  }
  
  .contact-link {
    color: #2463EB;
    text-decoration: underline;
    cursor: pointer;
  }
} 