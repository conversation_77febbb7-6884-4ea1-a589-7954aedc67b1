import React, { lazy, Suspense, useState, useCallback, useEffect } from 'react';
import { Col, Spin } from 'antd';
import { DrawdownFormProvider } from '@/contexts/DrawdownFormContext';
import { Button } from '@/components/shared/Button';
import Stepper from '@/components/shared/Stepper';
import ArrowRightIcon from '@/assets-new/icons/arrow-right-sm.svg?react';
import ArrowLeftIcon from '@/assets-new/icons/arrow-left-sm.svg?react';
import './styles.scss';
import { useLimitStore } from '@/store/limits';
import { useNavigate } from 'react-router-dom';
import { getUserCreditApplication, getOauthShopList, submitUserCreditApplication, GetOauthShopListRes} from "@fundpark/fp-api";
import { Modal } from '@/components/shared/Modal';
import SecondaryButton from '@/components/shared/SecondaryButton';
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import AlertIcon from '@/assets-new/icons/alert-icon.svg?react';
import {ModalStep} from "@/types/platforms/modalSteps";
import Text from "@/components/shared/Text/index.tsx";
import appHelper from "@/utils/appHelper";
import { usePositionTracking } from '@/hooks/usePositionTracking';
import {usePaymentGatewayStore} from "@/store/platform/paymentGatewayStore.tsx";
import ActionButtons from './ActionButtons';
import TwoButtonModal from '@/components/shared/GenericModal/index.tsx';

// Dynamically import the sections to avoid circular dependencies
const AuthShop = lazy(() => import('./AuthShop'));

const ActivateShopFormContent: React.FC = () => {
  const { trackPosition } = usePositionTracking();
  const [loading, setLoading] = useState(false);
  const [signRequired, setSignRequired] = useState(false);
  const navigate = useNavigate();
  const [rows, setRows] = useState<any[]>([]);
  const { creditApplication, hasLimitIncrease, creditLimit} = useLimitStore();
  const [authedPlatforms, setAuthedPlatforms] = useState<GetOauthShopListRes>({ shop: [], psp: [] });
  const [showWarning, setShowWarning] = useState(false);
  const [showGenericModal, setShowGenericModal] = useState(false);
  const [showExceedLimitModal, setShowExceedLimitModal] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [showWechatModal, setShowWechatModal] = useState(false);
  const [modalStep, setModalStep] = useState<ModalStep>("none");
  const [pspVerifying, setPspVerifying] = useState(false);
  const [pspAllVerified, setPspAllVerified] = useState(false);
  const [pspFailed, setPspFailed] = useState(false);

  // credit application conditions
  const preWml = creditApplication?.pre_wml ?? 0;
  const preLimit = creditApplication?.pre_limit ?? 0;
  const approvalLimit = creditApplication?.approval_limit ?? 0;
  const isExceeded10k = approvalLimit > 100000;
  const isExceededLimit = (creditApplication?.pre_wml ?? 0) > 100000;
  const isExceededApproval = (creditApplication?.pre_wml ?? 0) > creditLimit?.approval_limit;
  const previousBelow10K = creditLimit?.approval_limit < 100000;

  const notFirstOver10K = creditLimit?.approval_limit >= 100000 && preWml > 100000 && preLimit > 100000;
  const firstOver10K = creditLimit?.approval_limit < 100000 && preWml > 100000 && preLimit > 100000;
  const allPassed = rows.every(row => row.psp_verify === 1);
  const active_or_failed = (creditApplication?.type === 'limit_increase' && creditApplication?.status === 'active') || (creditApplication?.type === 'limit_increase' && creditApplication?.status === 'failed');
  const is_shop_not_allowed = creditApplication?.pre_limit == 0;
  // const isUnderLimit =
  //   creditApplication?.pre_wml != null &&
  //   creditLimit?.approval_limit != null &&
  //   creditApplication.pre_wml < creditLimit.approval_limit;

  // Scenarios
  const scenario1 = pspFailed
  const scenario2 = creditApplication?.type === "xdj" && creditApplication?.status === "active" && creditApplication?.pre_limit != null
  const scenario3 = creditApplication?.type === "limit_increase" && ["active", "failed"].includes(creditApplication?.status ?? "")
  const scenario4 = creditApplication?.type === "limit_increase" && ["pending"].includes(creditApplication?.status ?? "") && pspAllVerified && !isExceededLimit
  const scenario5 = creditApplication?.type === "limit_increase" && ["submitted", "pending"].includes(creditApplication?.status ?? "") 
  const scenario6 = creditApplication?.type === "xdj" && creditApplication?.status === "active" && creditApplication?.pre_limit === null
  const scenario7 = isExceededApproval && isExceededLimit && creditApplication?.status === 'pending' && !previousBelow10K;
  const scenario8 = isExceededApproval && isExceededLimit && creditApplication?.status === 'pending'  && previousBelow10K && pspAllVerified;
  const scenario9 = creditApplication?.stage === 'waiting_result' && creditApplication?.is_first === true && creditApplication?.status === 'submitted'
  const scenario10 = creditApplication?.type === 'limit_increase' && creditApplication?.status === 'failed'
  const scenario11 = !creditApplication?.pre_limit && !creditApplication?.pre_wml
  const scenario12 = creditApplication?.stage === 'finished' && creditApplication?.is_first === true && creditApplication?.status === 'active'
  const scenario13 = creditApplication?.status === 'submitted' && creditApplication?.type ==='limit_increase' && pspFailed

  const showAddButton = pspFailed || scenario13 || is_shop_not_allowed || active_or_failed || (scenario2 || ((scenario1 || scenario3 || scenario7 || notFirstOver10K) && creditApplication?.pre_limit != null && !firstOver10K)) ;
  const showBackButton = active_or_failed || (scenario1 || scenario2 || scenario3 || scenario5 || scenario6 || scenario7);
  const showNextButton = !is_shop_not_allowed && (scenario4 || scenario8 || creditApplication?.is_first || allPassed ) && !scenario9 && !scenario10 && !scenario11 && creditApplication?.type !== 'xdj' && !scenario12 && !notFirstOver10K;
  const showSubmitButton = pspFailed || is_shop_not_allowed || active_or_failed || ((scenario1 || scenario2 || scenario3 || scenario7 || notFirstOver10K ||scenario13) && creditApplication?.pre_limit != null);
  const canDelete = pspFailed || scenario2 || scenario3 || scenario7 || active_or_failed || is_shop_not_allowed ||scenario13;
  const canEdit = pspFailed || scenario2 || scenario3 || scenario7 || active_or_failed || is_shop_not_allowed || scenario13;

  
  // Regular workflow paths (XDJ Dashboard)
  const homePath = '/credit/hook/home';
  // xdj-specific paths
  const signingPath = '/credit/hook/activate-shop/signing';
  
  const {fetchPaymentGateway, clear} = usePaymentGatewayStore();


  const fetchOauthShopList = async () => {
    try {
          const res = await getOauthShopList({});
          if (res.code === 0) {
              setAuthedPlatforms(res.data);
              const shops = res.data.shop.map((shop, index) => ({
                  id: index + 1,
                  platform_id: shop.platform_id,
                  auth_status: shop.oauth_status,
                  credit_status: shop.credit_status,
                  platform: shop.platform,
                  shop_id: shop.seller_id,
                  platform_shop_id: shop.id,
                  psp: shop.choose_psp? `${shop.choose_psp.platform}: ID - ${shop.choose_psp.account_id}` : null,
                  psp_id: shop.choose_psp? shop.choose_psp.oauth_psp_id : null,
                  psp_verify: shop.choose_psp? shop.choose_psp.oauth_status : null,
                  can_delete: shop.can_delete,
              }))
              setRows(shops);
              setPspVerifying(shops.some(shop => shop.psp_verify === 0));
              setPspAllVerified(shops.every(shop => shop.psp_verify === 1));
              setPspFailed(shops.some(shop => shop.psp_verify === 2));
          } else {
              console.error("Oauth list error:", res.message);
          }
      } catch (error) {
          console.error("Error fetching authorized shop data", error);
      }
    
  }

  const fetchData = async () => {
    try {
      // Map form data to API format using the mapper
          const res = await getUserCreditApplication(null);
          if (res.code === 0) {
              // @ts-ignore
              setSignRequired(res.data.need_sign_contract);
          } else {
              console.error("credit application error:", res.message);
          }
        } catch (error) {
          console.error("Error fetching credit application", error);
    }

    clear();
    await fetchPaymentGateway();

   fetchOauthShopList()
  };

  const handleShowWechat = () => {
    setShowWarning(false);
    setShowWechatModal(true);
  };

  const handleBack = () => {
    navigate(homePath);
  };

  React.useEffect(() => {
      fetchData();
  }, []);
  
  const handleNext = useCallback(async () => {
      await trackPosition({ path: homePath });
      navigate(signingPath, {replace: true});
    }, [navigate]);
  
  const handleSubmit = useCallback(async () => {

    if (rows.find(row => row.psp_id === null)) {
      appHelper.msgApi.error('请编辑绑定收款公司');
      // setShowWarning(true);
      return;
    }

    const hasOccupied = rows.some(row => row.credit_status === 2);
    const hasFailed = rows.some(row => row.auth_status === 2);


    if (hasOccupied) {
      setShowWarning(true);
      setWarningMessage('检测到您的店铺/支付平台已被其他用户绑定授权了，建议您换一个店铺/支付平台继续申请');
    } else if (hasFailed) {
      setShowWarning(true);
      setWarningMessage('检测到您的店铺/支付平台授权失效，建议您重新授权或换一个店铺/支付平台继续申请');
    } else if (isExceeded10k && isExceededLimit) {
      setShowExceedLimitModal(true);
    } else {
      setLoading(true);
      try {
        const res = await submitUserCreditApplication();
        if (res.code === 0) {
          try {
            // Regular workflow navigation logic
            let targetPath = '/credit/hook/home';
            let targetPathName = 'XDJ Dashboard';

            if (scenario13){
                  fetchOauthShopList()
                  setLoading(false);
                  return
                }
            
            if (res.data.need_sign_contract) { 
              targetPath = '/credit/hook/activate-shop/signing';
              targetPathName = 'Sign to activate shop';
            }

            if (scenario3) {
              targetPath = '/credit/hook/home';
              targetPathName = 'XDJ Dashboard';
            }

            if(!res.data?.pre_wml && !res.data?.pre_limit){
                targetPath = '/credit/hook/home';
                targetPathName = 'XDJ Dashboard';
            }
            
            await trackPosition({ path: '/credit/hook/home' });
            navigate(targetPath, {replace: true});
          } catch (error) {
            console.error('API error when submitting business information:', error);
          } finally {
            setLoading(false);
          }
        } else if (res.code === 4012){
            setShowGenericModal(true);
        }else if (res.code === 4014){
            setShowExceedLimitModal(true)
        }else {
          setShowWarning(false);
          appHelper.msgApi.error(res.message);
          setWarningMessage(res.message);
        }
      } catch (error) {
        console.error('Validation failed:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [rows, trackPosition, navigate, scenario3]);

  
  const steps = [
    {
      title: '平台授权信息',
      content: (
        <Suspense fallback={<Spin size="large" />}>
          <Col>
            <AuthShop 
              addShopButton={showAddButton}
              rows={rows}
              authedPlatforms={authedPlatforms}
              canDelete={canDelete}
              canEdit={canEdit}
              updateRows={setRows}
            />
          </Col>
          <Modal
              open={showWarning}
              onClose={() => setShowWarning(false)}
              title="温馨提示"
              >
              <div>
                <div style={{ display: 'flex', alignItems: 'flex-start', textAlign: 'left'}}>
                <AlertIcon className="alert-icon" style={{ width: '40px', height: '30px' }}/>
                  <p className="mb-4" >
                  {warningMessage}
                  </p>
                  </div>
                  
                  <div className="flex justify-center mt-10" style={{ gap: '16px' }}>
                  <SecondaryButton
                      label="线上查询"
                      onClick={() => handleShowWechat()}
                  />
                  <Button
                      type="primary"
                      label="重新授权"
                      onClick={() => {
                        setShowWarning(false)
                        setModalStep('platformSelection')
                      }}
                      style={{
                      height: '40px',
                      width: '88px'
                      }}
                  />
                  </div>
                  
              </div>
          </Modal>
            <TwoButtonModal
                 open={showGenericModal}
                 onClose={() => setShowGenericModal(false)}
                 onComplete={() => setShowGenericModal(false)}
                 variant={"one_btn"}
                 content={"很抱歉，您当前授权的店铺/支付账户尚未达到涨额度要求，请添加新的店铺/支付账户重新申请～"}
                 completeBtnLabel={"确认"}
            />
            <TwoButtonModal
                open={showExceedLimitModal}
                onClose={() => setShowExceedLimitModal(false)}
                onComplete={() => {
                    setShowExceedLimitModal(false);
                    setShowWechatModal(true);
                }}
                content={
                    <>
                        尊敬的客户，您的专属客户经理将在30分钟内联系您，请注意接听来电。
                        <br/>
                        <br/>
                        其他问题请点击【联系客服】
                    </>
                }
            />
          <OurWechatModal
            open={showWechatModal}
            onClose={() => setShowWechatModal(false)}
            message={"有任何问题，欢迎联系我们～"}
            hasAlertIcon={true}
            textAlign='center'
          />
        </Suspense>
      ),
      action: (
        <ActionButtons
          showBackButton={showBackButton}
          showSubmitButton={showSubmitButton}
          showNextButton={showNextButton}
          isSubmitting={loading}
          onNext={handleNext}
          onBack={handleBack}
          onSubmit={handleSubmit}
        />
      ),
    },
    {
      title: '激活签约',
      content: (
        <></>
      ),
      action: null,
    },
  ];

  return (
    <div className="activate-shop-form-wrapper">
      <Stepper 
        steps={steps} 
        currentStep={0}
        borderTopRadiusRounded={false}
      />
    </div>
  );
};

// Wrapper component that provides the context
const ActivateShopForm: React.FC = () => {
  return (
    <DrawdownFormProvider>
      <ActivateShopFormContent />
    </DrawdownFormProvider>
  );
};

export default ActivateShopForm; 