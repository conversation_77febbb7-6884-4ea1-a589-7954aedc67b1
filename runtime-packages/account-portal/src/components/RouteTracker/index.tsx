import React from 'react';
import useRouteTracking from '@/hooks/useRouteTracking';

/**
 * RouteTracker Component
 * 
 * This is a utility component that automatically tracks route changes 
 * throughout the application. It uses the useRouteTracking hook to
 * detect and track page views and URL parameter changes.
 * 
 * It should be included once at the application's router level,
 * but ONLY after the RouterProvider is rendered.
 */
const RouteTracker: React.FC = () => {
  // Use the route tracking hook with default settings
  useRouteTracking();
  
  // This component doesn't render anything
  return null;
};

export default RouteTracker; 