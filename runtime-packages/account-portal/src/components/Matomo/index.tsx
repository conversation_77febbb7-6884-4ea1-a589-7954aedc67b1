import { useEffect } from 'react';
import matomoConfig from '@/config/matomo';

interface MatomoProps {
  siteId?: string;
  trackerUrl?: string;
  scriptUrl?: string;
  enableDebug?: boolean;
  forceDisable?: boolean;
}

/**
 * Matomo Component for tracking visitor data
 * 
 * This component adds Matomo tracking to your application.
 * It can be configured using the config/matomo.ts file.
 */
const Matomo: React.FC<MatomoProps> = ({
  siteId = matomoConfig.siteId,
  trackerUrl = matomoConfig.baseUrl,
  scriptUrl = matomoConfig.scriptUrl,
  enableDebug = matomoConfig.enableDebug,
  forceDisable = false
}) => {
  useEffect(() => {
    // Check for alternative configuration in localStorage
    let localConfig: any = null;
    try {
      const savedConfig = localStorage.getItem('matomoAltConfig');
      if (savedConfig) {
        localConfig = JSON.parse(savedConfig);
        if (enableDebug) {
          console.log('Matomo: Found alternative configuration in localStorage:', localConfig);
        }
      }
    } catch (error) {
      console.error('Matomo: Error reading localStorage configuration:', error);
    }

    // Use local config if available
    const effectiveSiteId = localConfig?.siteId || siteId;
    const effectiveTrackerUrl = localConfig?.baseUrl || trackerUrl;
    const effectiveScriptUrl = localConfig?.scriptUrl || scriptUrl;

    // Disable tracking if in development and disableInDevelopment is true
    const isDevelopment = process.env.NODE_ENV === 'development';
    if ((isDevelopment && matomoConfig.disableInDevelopment) || forceDisable) {
      if (enableDebug) console.log('Matomo: Tracking disabled in development mode or by force');
      return;
    }

    if (enableDebug) {
      console.log('Matomo: Initializing with configuration:');
      console.log('- siteId:', effectiveSiteId);
      console.log('- trackerUrl:', effectiveTrackerUrl);
      console.log('- scriptUrl:', effectiveScriptUrl);
    }

    // Check for existing Matomo script to prevent duplicate loading
    const existingScript = document.querySelector(`script[src="${effectiveScriptUrl}"]`);
    if (existingScript) {
      if (enableDebug) console.log('Matomo: Script already loaded, skipping initialization');
      return;
    }

    // Initialize Matomo
    window._paq = window._paq || [];
    
    // Tracker methods like "setCustomDimension" should be called before "trackPageView"
    window._paq.push(['trackPageView']);
    window._paq.push(['enableLinkTracking']);
    
    // Initialize tracker
    (function() {
      try {
        const u = effectiveTrackerUrl;
        if (enableDebug) console.log('Matomo: Setting tracker URL:', u + 'matomo.php');
        window._paq.push(['setTrackerUrl', u + 'matomo.php']);
        window._paq.push(['setSiteId', effectiveSiteId]);
        
        // Create script element and append to document
        const d = document;
        const g = d.createElement('script');
        const s = d.getElementsByTagName('script')[0];
        g.async = true;
        g.src = effectiveScriptUrl;
        g.onerror = function() {
          console.error('Matomo: Failed to load tracking script from:', g.src);
          
          // Try alternative URL format if the first one fails
          const alternativeUrl = `${u.replace(/\/$/, '')}/js/matomo.js`;
          if (enableDebug) console.log('Matomo: Trying alternative URL:', alternativeUrl);
          
          const altScript = d.createElement('script');
          altScript.async = true;
          altScript.src = alternativeUrl;
          altScript.onerror = function() {
            console.error('Matomo: Alternative script also failed to load from:', alternativeUrl);
          };
          altScript.onload = function() {
            if (enableDebug) console.log('Matomo: Alternative script loaded successfully:', alternativeUrl);
          };
          if (s && s.parentNode) {
            s.parentNode.insertBefore(altScript, s);
          } else {
            document.head.appendChild(altScript);
          }
        };
        g.onload = function() {
          if (enableDebug) console.log('Matomo: Script loaded successfully:', g.src);
        };
        if (s && s.parentNode) {
          if (enableDebug) console.log('Matomo: Inserting script into document');
          s.parentNode.insertBefore(g, s);
        } else {
          if (enableDebug) console.warn('Matomo: Could not find a script tag to insert before, appending to head');
          document.head.appendChild(g);
        }
      } catch (error) {
        console.error('Matomo: Error during initialization:', error);
      }
    })();

    // Clean up function
    return () => {
      if (enableDebug) console.log('Matomo: Component unmounting, cleaning up');
      
      // Remove the script element when the component unmounts
      const scriptElements = document.querySelectorAll('script[src*="matomo.js"]') as NodeListOf<HTMLScriptElement>;
      scriptElements.forEach(script => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
          if (enableDebug) console.log('Matomo: Removed script:', script.src);
        }
      });
    };
  }, [siteId, trackerUrl, scriptUrl, enableDebug, forceDisable]);

  return null; // This component doesn't render anything visible
};

export default Matomo;

// Add TypeScript declaration for window._paq
declare global {
  interface Window {
    _paq: any[];
  }
} 