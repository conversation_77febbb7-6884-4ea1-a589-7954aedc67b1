import React from "react";

/** 捕获异常的组件 */
export default class extends React.Component<
    any,
    {
        error?: Error;
    }
> {
    state = {
        error: undefined as Error | undefined
    };

    static getDerivedStateFromError(error: any) {
        console.log('getDerivedStateFromError', error);
        return { error };
    }
    private promiseRejectionHandler = (event: PromiseRejectionEvent) => {
        console.warn("promiseRejectionHandler", event);
    };
    componentDidCatch(error: Error) {
        console.log("ErrorBoundary error: ", error);
        this.setState({
            error
        });
    }
    componentDidMount() {
        window.addEventListener("unhandledrejection", this.promiseRejectionHandler);
    }
    componentWillUnmount() {
        window.removeEventListener("unhandledrejection", this.promiseRejectionHandler);
    }
    render() {
        const { error } = this.state;

        if (error) {
            return (
                <div style={{ color: "red" }}>
                    Component error:{" "}
                    <pre
                        dangerouslySetInnerHTML={{
                            __html: error.stack || "No Error Stack"
                        }}
                    />
                </div>
            );
        }
        return this.props.children;
    }
}
