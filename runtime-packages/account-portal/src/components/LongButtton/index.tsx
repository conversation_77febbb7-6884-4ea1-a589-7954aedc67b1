import React, {ReactNode} from "react";
import styles from "./index.module.scss";
import redPocketBtn from "@/assets-new/images/redPocketBtn.svg";
import bubbleDialog from "@/assets-new/images/bubbleDialog.svg";
import bubbleDialogLeft from "@/assets-new/images/bubbleDialogLeft.svg";
import longButton from "@/assets-new/images/long_button.svg";
import longButtonGrey from "@/assets-new/images/long_button_grey.svg"
import longButtonDisabled from "@/assets-new/images/long_button_disabled.svg"
import clockIcon from "@/assets-new/icons/common/clock.svg";

interface LongButtonProps {
    buttonTitle: string;
    actionTitle?: string;
    isShowHelpingText?: boolean;
    helpingText?: ReactNode | null;
    width?: string;
    height?: string;
    onClick?: () => void;
    type?: "dark" | "light" | "disabled" | string;
    helpingTextStyle?: "left" | "right";
}

const LongButton: React.FC<LongButtonProps> = ({
                                                   buttonTitle,
                                                   actionTitle,
                                                   isShowHelpingText = false,
                                                   helpingTextStyle = "right",
                                                   helpingText = null,
                                                   width = "243px",
                                                   height = "52px",
                                                   onClick,
                                                   type
                                               }) => {

    let buttonImage
    let buttonTextStyle
    const cursorStyle = type === "disabled" ? "not-allowed" : "pointer";
    const containerStyle: React.CSSProperties = {
        width,
        height,
        cursor: cursorStyle,

    };

    switch (type) {
        case "dark":
            buttonImage = <img src={longButtonGrey} alt="long button" className={styles.redPocketImage}/>
            buttonTextStyle = {color: 'white'};
            break;
        case "light":
            buttonImage = <img src={longButton} alt="long button" className={styles.redPocketImage}/>
            break;
        case "disabled":
            buttonImage = <img src={longButtonDisabled} alt="long button" className={styles.redPocketImage}/>
            buttonTextStyle = {color: '#FFFFFF'};
            break;
        default:
            buttonImage = <img src={longButton} alt="long button" className={styles.redPocketImage}/>
            buttonTextStyle = {color: '#000000'};
    }

    return (
        <div className={styles.redPocketContainer} style={containerStyle} onClick={onClick}>
            {buttonImage}
            <div className={styles.redPocketTitle} style={buttonTextStyle}>{buttonTitle}</div>
            {actionTitle && (
                <button className={styles.redPocketActionButton}>
                    <img src={redPocketBtn} alt="Action"/>
                    <div className={styles.redPocketActionButtonText}>{actionTitle}</div>
                </button>
            )}
            {helpingTextStyle === 'right' && isShowHelpingText && helpingText && (
                <div>
                    <div className={styles.redPocketHelpingContainer}>
                        <img src={bubbleDialog} alt="Action"/>
                    </div>
                    <div className={styles.redPocketHelpingText}>
                        <span>
                            <img src={clockIcon} alt=""/>
                        </span>
                        {helpingText}
                    </div>
                </div>
            )}
            {helpingTextStyle === 'left' && isShowHelpingText && helpingText && (
                <div>
                    <div className={styles.redPocketHelpingContainerLeft}>
                        <img src={bubbleDialogLeft} alt="Action"/>
                    </div>
                    <div className={styles.redPocketHelpingTextLeft}>
                        <span>
                            <img src={clockIcon} alt=""/>
                        </span>
                        {helpingText}
                    </div>
                </div>
            )}
        </div>
    );
};

export default LongButton;
