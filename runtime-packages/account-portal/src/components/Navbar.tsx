import {<PERSON><PERSON>, But<PERSON>, Drawer, Dropdown, Layout} from "antd";
import {MenuOutlined, SettingOutlined, UserOutlined} from "@ant-design/icons";
import {useLocation, useNavigate} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {useCommonStore} from "@/store/common";
import appHelper from "@/utils/appHelper";

import FundParkLogo from "@/assets/icons/fundpark-logo.svg?react";
import {useState} from "react";
import {THEME_CONSTANTS} from "./common";
import ChangeCompany from "@/components/company/ChangeCompany";
import styles from "./styles.module.scss";

import LogoutDefault from "@/assets/icons/navbar-icon/logout-default.svg?react";
import LogoutActive from "@/assets/icons/navbar-icon/logout-active.svg?react";

import CompanyUserIcon from "@/assets/icons/company-user-icon.svg?react";

import {GetUserRedirectToTABFToken} from "@fundpark/fp-api";

const {Header} = Layout;


const RotatingTriangle: React.FC<{ isOpen: boolean }> = ({isOpen}) => (
    <svg
        width="8"
        height="6"
        viewBox="0 0 8 6"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        style={{
            transform: isOpen ? "rotate(180deg)" : "rotate(0deg)",
            transition: "transform 0.2s ease-in-out"
        }}
    >
        <path d="M4 6L0 0L8 0L4 6Z"/>
    </svg>
);

const Navbar: React.FC = () => {
    const {userInfo} = useCommonStore();

    const location = useLocation();
    const {t} = useTranslation();
    const [visibleChangeCompanyModal, setVisibleChangeCompanyModal] = useState(false);
    const [openUserProfile, setOpenUserProfile] = useState(false);
    const navigate = useNavigate();
    const user = useCommonStore((s) => s.userInfo);

    const allMenuDefs = [
        {
            key: "repaymentManagement",
            label: "还款管理",
            visible: userInfo?.is_activated,
        },
    ];

    const menuItems = allMenuDefs
        .filter(item => item.visible)
        .map(({visible, ...rest}) => rest);

    const handleLogout = async () => {
        try {
            if (window.location.href.includes('/free-reports')) {
                if (user && user.register_source === 'small_shop_financing') {
                    navigate('/credit/hook');
                } else if (user && user.register_source === 'revenue_credit_automation') {
                    navigate('/credit/underwriting');
                }
            }
            appHelper.clearAccessInfo();
        } catch (error) {
            console.error("Logout failed:", error);
        }
    };

    const handleMenuClick = async (key: string) => {
        if (key === 'repaymentManagement') {
            if (!userInfo) return;
            const res = await GetUserRedirectToTABFToken({id: userInfo.id});
            if (res.code === 0 && res.data && res.data.links) {
                window.open(res.data.links, "_blank");
            } else {
                appHelper.msgApi.error("error in calling redirect url");
            }
        }
    };

    const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

    const onOpenUserProfileChange = (open: boolean) => {
        setOpenUserProfile(open);
    };

    return (
        <Header
            className="h-16 sticky top-0 z-50"
            style={{lineHeight: "initial", height: "60px", background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR}}
        >
            <div className="mx-auto px-[100px] h-full">
                <div className="flex items-center justify-between h-full">
                    {/* Left section - Logo and Menu */}
                    <div className="flex items-center h-full">
                        <div className="flex items-center h-full mr-8">
                            <FundParkLogo className="w-[162.35px] h-[24px]"/>
                        </div>
                    </div>

                    {/* Mobile Menu Button */}
                    <Button
                        type="text"
                        icon={<MenuOutlined/>}
                        className="lg:hidden text-white hover:bg-transparent"
                        onClick={() => setMobileMenuVisible(!mobileMenuVisible)}
                    />

                    {/* Desktop Menu */}
                    <div className="hidden lg:flex items-center h-full space-x-6">
                        {menuItems.map(item =>
                            (
                                <Button
                                    key={item.key}
                                    type="text"
                                    className={`h-full rounded-none ${styles.navButton} ${
                                        location.pathname.includes(item.key)
                                            ? styles.activeNavButton
                                            : styles.defaultNavButton
                                    }`}
                                    onClick={() => handleMenuClick(`${item.key}`)}
                                >
                                    {item.label}
                                </Button>
                            )
                        )}
                        <Dropdown
                            className={`${styles.navButton} `}
                            onOpenChange={onOpenUserProfileChange}
                            open={openUserProfile}
                            overlayStyle={{ minWidth: 0 }}
                            placement="bottomRight"
                            dropdownRender={() => (
                                <div className="shadow-[0px_0px_16px_0px_rgba(0,0,0,0.2)] bg-white rounded-[12px] ">
                                    <div
                                        className="flex items-center justify-end h-[60px] px-5 py-3">
                                        <Button
                                            className="h-[36px] w-[101px] rounded-[48px] text-black-600 flex items-center gap-2 justify-center py-4 border-none group transition-colors duration-200 hover:!bg-[#F6F7FA] shadow-none"
                                            style={{backgroundColor: "transparent"}}
                                            onClick={handleLogout}
                                        >
                                            <span className="text-[14px] text-[#6E6E75] group-hover:text-[#0C1231]">
                                                {t("layout.topbar.logout")}
                                            </span>
                                            <div className="flex items-center w-[24px] h-[24px] justify-center">
                                                <div className="w-[24px] h-[24px]">
                                                    <span className="block group-hover:hidden">
                                                        <LogoutDefault/>
                                                    </span>
                                                    <span className="hidden group-hover:block">
                                                        <LogoutActive/>
                                                    </span>
                                                </div>
                                            </div>
                                        </Button>
                                    </div>
                                </div>
                            )}
                        >
                            <Button
                                type="text"
                                className="flex items-center text-white hover:bg-transparent h-full rounded-none border-x border-x-[#0C1231]"
                            >
                                <div
                                    className="flex items-center justify-center w-[30px] h-[30px] rounded-[10px] bg-[#F6F7FA]">
                                    <CompanyUserIcon className="w-[12.5px] h-[14.8px]"/>

                                </div>
                                <span>{userInfo?.email}</span>
                                <RotatingTriangle isOpen={openUserProfile}/>
                            </Button>
                        </Dropdown>
                    </div>

                    {/* Mobile Menu Drawer */}
                    <Drawer
                        placement="right"
                        open={mobileMenuVisible}
                        onClose={() => setMobileMenuVisible(false)}
                        className="lg:hidden"
                        width={280}
                        styles={{
                            body: {
                                padding: 0
                            }
                        }}
                    >
                        {/* User Profile Section at Top */}
                        <div className="px-4 py-6 border-b bg-gray-50">
                            <div className="flex flex-col items-center justify-center mb-2">
                                <Avatar size={64} icon={<UserOutlined/>}/>
                                <div className="mt-3 text-center">
                                    <span className="text-sm text-gray-600">{userInfo?.email}</span>
                                </div>
                            </div>
                        </div>


                        {/* Bottom Actions */}
                        <div className="absolute bottom-0 left-0 right-0 border-t bg-white">
                            <div className="px-4 py-3 flex items-center justify-around">
                                <Button type="text" danger onClick={handleLogout} className="font-medium">
                                    {t("navbar.logout")}
                                </Button>
                            </div>
                        </div>
                    </Drawer>
                </div>
            </div>
            <ChangeCompany visible={visibleChangeCompanyModal} onClose={() => setVisibleChangeCompanyModal(false)}/>
        </Header>
    );
};

export default Navbar;
