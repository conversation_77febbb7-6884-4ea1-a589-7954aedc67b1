import { Link } from "react-router-dom";

export interface BreadcrumbItem {
    label: React.ReactNode;
    url?: string;
}

const Breadcrumb: React.FC<{ className?: string; style?: React.CSSProperties; list?: BreadcrumbItem[] }> = ({
    className,
    style,
    list
}) => {
    if (!list || list.length === 0) {
        return null;
    }
    return (
        <div
            className={`bg-white sticky top-[64px] z-[100] py-8 flex items-center text-[14px]${className ? " " + className : ""}`}
            style={style}
        >
            {list.map((item, index) => (
                <span key={index}>
                    {index > 0 && <span className="text-gray-500 mx-2">|</span>}
                    {index === list.length - 1 ? (
                        <span className="text-black-500 font-semibold">{item.label}</span>
                    ) : item.url ? (
                        <Link to={item.url} className="text-gray-500 hover:text-gray-900">
                            {item.label}
                        </Link>
                    ) : (
                        <span className="text-gray-500">{item.label}</span>
                    )}
                </span>
            ))}
        </div>
    );
};

export default Breadcrumb;
