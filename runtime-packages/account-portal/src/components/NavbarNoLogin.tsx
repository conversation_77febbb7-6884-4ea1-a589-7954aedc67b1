import {But<PERSON>, Drawer, Layout} from "antd";
import {MenuOutlined} from "@ant-design/icons";
import {useLocation, useNavigate} from "react-router-dom";
import {SendResetPasswordEmail} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper";

import FundParkLogo from "@/assets/icons/fundpark-logo.svg?react";
import {useEffect, useState} from "react";
import {THEME_CONSTANTS} from "./common";
import ChangeCompany from "@/components/company/ChangeCompany";

import AuthModal from "@/components/common/Modal/AuthModal.tsx";
import LoginMethodSwitcher from "@/components/LoginMethodSwitcher";
import Login from "@/pages/login/index.tsx";
import Signup from "@/pages/signup/index.tsx";
import ForgetPassword from "@/pages/forgetPassword/index.tsx";

import CompanyUserIcon from "@/assets/icons/company-user-icon.svg?react";
import {useGeneralStore} from "@/store/general";
import TwoButtonModal from "@/components/shared/GenericModal";
import {useMatomoContext} from "@/contexts/MatomoContext";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants";

const {Header} = Layout;

const NavbarNotLogin: React.FC = () => {
    const [visibleChangeCompanyModal, setVisibleChangeCompanyModal] = useState(false);
    const [resetEmail, setResetEmail] = useState<string | null>(null);
    const [showResetConfirm, setShowResetConfirm] = useState(false);
    const {search, pathname} = useLocation();
    const navigate = useNavigate();

    const {trackEvent} = useMatomoContext();

    const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

    const handleAuthModalClose = () => {
        setAuthModalOpen(false);
    };

    const {authModalOpen, setAuthModalOpen, loginMethod, setLoginMethod} = useGeneralStore();

    const params = new URLSearchParams(search);
    const emailParam = params.get("email") || undefined;
    const signUpParam = params.get("signup") || undefined;
    const initialValues = emailParam ? {email: emailParam} : {};

    useEffect(() => {
        if (emailParam) {
            setLoginMethod("login");
            setAuthModalOpen(true);

            const paramsCopy = new URLSearchParams(search);
            paramsCopy.delete("email");
            const newSearch = paramsCopy.toString();
            navigate(
                {
                    pathname,
                    search: newSearch ? `?${newSearch}` : "",
                },
                {replace: true}
            );
        } else if (signUpParam) {
            setLoginMethod("signup");
            setAuthModalOpen(true);

            const paramsCopy = new URLSearchParams(search);
            paramsCopy.delete("signup");
            navigate(
                {
                    pathname,
                    search: "",
                },
                {replace: true}
            );
        }
        // Only re‐run if `emailParam` (or path) changes
    }, [
        emailParam,
        pathname,
        search,
        setLoginMethod,
        setAuthModalOpen,
        navigate,
    ]);

    return (
        <>
            <AuthModal open={authModalOpen} onClose={handleAuthModalClose}>
                {loginMethod === "login" && (
                    <>
                        <LoginMethodSwitcher loginMethod={loginMethod} onMethodChange={setLoginMethod}/>
                        <Login onForgotPassword={() => setLoginMethod("forgot")} initialValues={initialValues}
                               key="login"/>
                    </>
                )}

                {loginMethod === "signup" && (
                    <>
                        <LoginMethodSwitcher loginMethod={loginMethod} onMethodChange={setLoginMethod}/>
                        <Signup key="signup"/>
                    </>
                )}

                {loginMethod === "forgot" && (
                    <ForgetPassword
                        onBack={() => setLoginMethod("login")}
                        onResetSent={email => {
                            setAuthModalOpen(false);
                            setResetEmail(email);
                            setShowResetConfirm(true);
                            setLoginMethod("login");
                        }}
                    />
                )}
            </AuthModal>
            <TwoButtonModal
                open={showResetConfirm}
                title="忘记密码"
                completeBtnLabel="重新发送"
                isShowCircle={false}
                content={`我们已发电子邮件到 ${resetEmail}。如数分钟后未查收到邮件，请您查看垃圾邮件。如仍未收到，请按重发邮件。`}
                onClose={() => {
                    setShowResetConfirm(false);
                    setResetEmail(null);
                }}
                onComplete={async () => {
                    if (!resetEmail) return;
                    try {
                        const res = await SendResetPasswordEmail({
                            email: resetEmail,
                            host_name: "ACCOUNT_PORTAL_HOST",
                            reset_page: "reset-password"
                        });
                        if (res.code === 0) {
                            appHelper.msgApi.success("重设密码邮件发送成功");
                        } else {
                            appHelper.msgApi.error("重设密码邮件发送失败");
                        }
                    } catch (e) {
                        console.error("Resend failed:", e);
                    }
                    setShowResetConfirm(false);
                }}
            />
            <Header
                className="h-16 sticky top-0 z-50"
                style={{lineHeight: "60px", height: "60px", background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR}}
            >
                <div className="mx-auto px-[100px] h-full">
                    <div className="flex items-center justify-between h-full">
                        {/* Left section - Logo and Menu */}
                        <div className="flex items-center h-full">
                            <div className="flex items-center h-full mr-8">
                                <FundParkLogo className="w-[162.35px] h-[24px]"/>
                            </div>
                        </div>

                        {/* Mobile Menu Button */}
                        <Button
                            type="text"
                            icon={<MenuOutlined/>}
                            className="lg:hidden text-white hover:bg-transparent"
                            onClick={() => setMobileMenuVisible(!mobileMenuVisible)}
                        />

                        {/* Desktop Menu */}
                        <div className="hidden lg:flex items-center h-full space-x-6">
                            <Button
                                type="text"
                                className="flex items-center hover:bg-transparent h-full rounded-none"
                                onClick={() => {
                                    trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.ENTER_LOGIN_PAGE);
                                    setLoginMethod('login')
                                    setAuthModalOpen(true);
                                }}
                            >
                                <div
                                    className="flex items-center justify-center w-[30px] h-[30px] rounded-[10px] bg-[#F6F7FA]">
                                    <CompanyUserIcon className="w-[12.5px] h-[14.8px]"/>
                                </div>
                                <span className="max-w-[120px] truncate text-white">登录</span>
                            </Button>
                        </div>

                        {/* Mobile Menu Drawer */}
                        <Drawer
                            placement="right"
                            open={mobileMenuVisible}
                            onClose={() => setMobileMenuVisible(false)}
                            className="lg:hidden"
                            width={280}
                            styles={{
                                body: {
                                    padding: 0
                                }
                            }}
                        >
                            {/* Login Section */}
                            <div className="p-6 border-b border-gray-100">
                                <Button
                                    type="text"
                                    onClick={() => {
                                        setAuthModalOpen(true);
                                        setMobileMenuVisible(false);
                                    }}
                                    className="w-full flex items-center justify-start gap-3 p-4 hover:bg-gray-50 rounded-lg"
                                >
                                    <div
                                        className="flex items-center justify-center w-[40px] h-[40px] rounded-[12px] bg-[#F6F7FA]">
                                        <CompanyUserIcon className="w-[16px] h-[18px]"/>
                                    </div>
                                    <span className="text-base font-medium text-gray-800">Login</span>
                                </Button>
                            </div>
                        </Drawer>
                    </div>
                </div>
                <ChangeCompany
                    visible={visibleChangeCompanyModal}
                    onClose={() => setVisibleChangeCompanyModal(false)}
                />
            </Header>
        </>
    );
};

export default NavbarNotLogin;
