.drawdown-form-card {
  // margin-top: 20px;
  // Fix for card width discrepancy
  border: none;
  overflow: hidden;
  width: 100%;
  
  .ant-card-head {
    background-color: #f9f9f9;
    padding: 0;
    margin: 0;
    width: 100%;
    
    .drawdown-form-header {
      width: 100%;
      padding: 0;
      margin: 0;
      
      // Override default styling for FormStepper when used as header
      .stepper-container {
        margin: 0;
        padding: 0;
        width: 100%;
        
        .stepper {
          display: flex;
          width: 100%;
          margin: 0;
          padding: 0;
          @media (max-width: 576px) { // smaller than sm
            display: block;
          }
        }
      }
    }
  }
  
  // Fix for the header width discrepancy
  .ant-card-head-wrapper {
    width: 100%;
    padding: 0;
    margin: 0;
  }
  
  .ant-card-head-title {
    padding: 0;
    margin: 0;
    width: 100%;
  }
  
  .ant-card-body {
    // Remove default padding and let the component control it
    padding: 0 !important;
  }
  
  .drawdown-form-content {
    min-height: 300px;
    padding: 0;
  }
  
  .drawdown-form-actions {
    margin-top: 20px;
    padding: 10px 0;
    // border-top: 1px solid #f0f0f0;
  }
  
  .business-info-section,
  .drawdown-account-section,
  .signing-section {
    padding-left: 24px;
    padding-right: 24px;
    margin: 0 auto;
  }
  
  // Make form sections more user-friendly
  .ant-form-item {
    margin-bottom: 24px;
    
    .ant-form-item-label {
      padding-bottom: 8px;
      
      label {
        font-weight: 500;
      }
    }
  }
  
  // Style for loading spinner
  .ant-spin {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
} 