import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/shared/Button";
import Stepper from "@/components/shared/Stepper";
import DrawdownAccountSection from "@/components/DrawdownForm/secondDrawdown/DrawdownAccountSection";
import ArrowRightIcon from "@/assets-new/icons/arrow-right-sm.svg?react";
import { UnsupportedRegionModal } from "../UnsupportedRegionModal";
import { getDebitAccount, submitDebitAccount } from "@fundpark/fp-api";
import { mapDebitAccountApiToUi, mapDebitAccountFormToApi } from "@/mappers/drawdownForm";
import { DrawdownAccountFormData } from "./types";
import "./DrawdownAccountForm.scss";
import "../styles.scss";
import { useNavigate } from "react-router-dom";
import { Checkbox } from "antd";
import { useLimitStore } from "@/store/limits";

const DrawdownAccountForm: React.FC = () => {
    const navigate = useNavigate();
    const [formData, setFormData] = useState<DrawdownAccountFormData>({
        countryRegion: "",
        drawdownCurrency: "USD",
        drawdownAmount: "",
        bankAccountName: "",
        bankAccountNumber: "",
        bank: "",
        swiftCode: "",
        bankAddress: ""
    });
    const [loading, setLoading] = useState(false);
    const [dataFetched, setDataFetched] = useState(false);
    const [confirmCheckbox, setConfirmCheckbox] = useState(false);
    const formRef = useRef<any>(null);
    const [showUnsupportedRegionModal, setShowUnsupportedRegionModal] = useState(false);
    const { fetchUserLimits } = useLimitStore();


    useEffect(async ()=>{
        await fetchUserLimits(true);
    },[])

    const limit = useLimitStore().creditLimit?.available_limit;


    // Fetch debit account data on component mount
    useEffect(() => {
        const fetchDebitAccount = async () => {
            if (dataFetched) return;

            try {
                setLoading(true);
                console.log("Fetching debit account data...");
                const response = await getDebitAccount();
                console.log("API Response:", response);

                if (response.status === 0 && response.data) {
                    console.log("Debit account data:", response.data);

                    // Map API data to UI format using the mapper
                    const uiData = mapDebitAccountApiToUi(response.data);
                    console.log("Mapped UI data:", uiData);

                    // Force form to update with new values
                    setTimeout(() => {
                        setFormData(prevData => {
                            const newData = {
                                ...prevData,
                                ...uiData
                            };
                            console.log("New form data:", newData);

                            // If form is already mounted, set its values directly
                            if (formRef.current) {
                                console.log("Setting form fields directly");
                                formRef.current.setFieldsValue(newData);
                            }

                            return newData;
                        });
                    }, 0);

                    setDataFetched(true);
                }
            } catch (error) {
                console.error("Failed to fetch debit account information:", error);
            } finally {
                setLoading(false);
            }
        };

        // Only fetch if we don't have initial values
        if (!dataFetched) {
            fetchDebitAccount();
        } else {
            setDataFetched(true);
        }
    }, []); // Empty dependency array - only run on mount

    // Debug: Log formData on change
    useEffect(() => {
        console.log("Current form data:", formData);

        // If form ref exists, try to set values directly
        if (formRef.current && Object.keys(formData).length > 0) {
            console.log("Setting form fields on formData change");
            formRef.current.setFieldsValue(formData);
        }
    }, [formData]);

    const handleChange = (values: Partial<DrawdownAccountFormData>) => {
        console.log("Form values changed:", values);
        setFormData({
            ...formData,
            ...values
        });
    };

    const handleNext = async () => {
        if (formRef.current) {
            try {
                await formRef.current.validateFields();

                try {
                    setLoading(true);

                    // Map form data to API format using the mapper
                    const apiData = mapDebitAccountFormToApi(formData);
                    console.log("Submitting data to API:", apiData);

                    // Submit data to the API
                    const response = await submitDebitAccount(apiData);
                    console.log("API response:", response);

                    if (response.status === 0) {
                        navigate("/credit/underwriting/register/business-info");
                    } else if (response.status === 4012) {
                        // Status 1 means the business registration number has been registered
                        // Set the error in the form field
                        if (formRef.current) {
                            formRef.current.setFields([
                                {
                                    name: "businessRegistrationNumber",
                                    errors: ["该公司已注册"]
                                }
                            ]);
                        }
                    } else if (response.status === 4020) {
                        if (formRef.current) {
                            formRef.current.setFields([
                                {
                                    name: "swiftCode",
                                    errors: ["SWIFT代码有误"]
                                }
                            ]);
                        }
                    } else {
                        console.error("Failed to submit debit account information:", response.message);
                    }
                } catch (error) {
                    console.error("API error when submitting debit account:", error);
                } finally {
                    setLoading(false);
                }
            } catch (error) {
                console.error("Validation failed:", error);
            }
        }
    };

    const handleCheckboxChange = (e: any) => {
        setConfirmCheckbox(e.target.checked);
    };

    // Define steps for the stepper
    const steps = [
        {
            title: "用款信息",
            content: <DrawdownAccountSection formRef={formRef} initialValues={formData} onChange={handleChange} limit={limit} />,
            action: (
                <div className="flex flex-col items-center">
                    <div className="mb-6 flex justify-center">
                        <Checkbox
                            checked={confirmCheckbox}
                            onChange={handleCheckboxChange}
                            className="confirm-checkbox"
                        >
                            本公司确认以上资料信息正确
                        </Checkbox>
                    </div>

                    <div>
                        <Button
                            type="primary"
                            onClick={() => navigate('/')}
                            style={{ width: 160 ,backgroundColor:"white"}}
                            loading={loading}
                            label={
                                <span>
                                    <ArrowRightIcon style={{ transform: "rotate(180deg)"  }} />
                                    返回
                                </span>
                            }
                        >
                        </Button>

                        <Button
                            type="primary"
                            onClick={handleNext}
                            style={{ width: 160 }}
                            loading={loading}
                            disabled={!confirmCheckbox}
                            label={
                                <span>
                                    下一步
                                </span>
                            }
                        >
                        </Button>
                    </div>
                </div>
            )
        },
        {
            title: "放款",
            content: <></>,
            action: null
        }
    ];

    return (
        <div className="drawdown-account-form">
            <Stepper steps={steps} currentStep={0} borderTopRadiusRounded={false} />
            <UnsupportedRegionModal
                open={showUnsupportedRegionModal}
                onClose={() => setShowUnsupportedRegionModal(false)}
            />
        </div>
    );
};

export default DrawdownAccountForm;
