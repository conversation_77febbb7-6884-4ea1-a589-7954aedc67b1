import React, { useEffect, useState, MutableRefObject } from "react";
import { Form, Select, Input, InputNumber, Tooltip } from "antd";
import { useConfig } from "@/contexts/ConfigContext";
import "./DrawdownAccountSection.scss";
import CurrencyAmountInput from "../../shared/CurrencyAmountInput";
import InfoBanner from "../../shared/InfoBanner";
import { LegacyCountryCode, COUNTRY_ID_TO_LEGACY_CODE } from "@/constants/configTypes";
import ConfigDataSelect from "../../shared/ConfigDataSelect";
import { ConfigType } from "@/constants/configTypes";
import { DrawdownAccountFormData } from "./types";

const { Option } = Select;

interface DrawdownAccountSectionProps {
    formRef?: MutableRefObject<any>;
    initialValues?: Partial<DrawdownAccountFormData>;
    onChange?: (values: Partial<DrawdownAccountFormData>) => void;
}

function formatNumber(number: number) {
    return number.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

const interestCalculator = (interest_rate, amount, sofrRate, tenorDay) => {
    var interest = amount * (((Number(interest_rate) + sofrRate) / 100.0 / 360) * tenorDay);
    return interest;
};

const CAADrawdownAccountSection: React.FC<DrawdownAccountSectionProps> = ({ formRef, initialValues = {}, onChange }) => {
    const { loading } = useConfig();
    const [form] = Form.useForm();
    const [formData, setFormData] = useState(initialValues);
    const [selectedCountry, setSelectedCountry] = useState<string | undefined>(
        initialValues.countryRegion ? COUNTRY_ID_TO_LEGACY_CODE[initialValues.countryRegion] : undefined
    );
    const [showExpectedResult, setShowExpectedResult] = useState(false);

    const [expectedInterest, setExpectedInterest] = useState(0);
    // let redpocketWaive =2000
    const redpocketAmount = 2000
    const [redpocketWaive, setRedpocketWaive] = useState(redpocketAmount);


    useEffect(() => {
        console.log(redpocketWaive,'redpocketWaiveredpocketWaive')
    },[redpocketWaive])

    useEffect(() => {
        try {
         let newExpectedInterest = interestCalculator(11, formData.drawdownAmount, 0, 80);
      
         if (redpocketAmount >= newExpectedInterest) {
          setRedpocketWaive(newExpectedInterest);
         }else{
            setRedpocketWaive(redpocketAmount)
         }
         console.log('redpocketWaive', redpocketWaive);

      
         setExpectedInterest(newExpectedInterest);
        } catch (error) {
         console.error("repaymentCalculator error:", error);
        }
      
        setShowExpectedResult(formData.drawdownAmount >= 1000);
       }, [formData.drawdownAmount]);

    // Set form reference if provided
    useEffect(() => {
        if (formRef) {
            formRef.current = form;
        }
    }, [form, formRef]);

    // Update form values when initialValues changes
    useEffect(() => {
        console.log("initialValues changed in DrawdownAccountSection:", initialValues);
        if (Object.keys(initialValues).length > 0) {
            // Reset fields and set values
            form.setFieldsValue(initialValues);

            // Update selectedCountry if countryRegion exists
            if (initialValues.countryRegion) {
                const legacyCode = COUNTRY_ID_TO_LEGACY_CODE[initialValues.countryRegion];
                setSelectedCountry(legacyCode);
            }

            // Update local form data
            setFormData(initialValues);
        }
    }, [initialValues, form]);

    const currencyOptions = [{ value: "USD", label: "USD" }];

    const handleCountryChange = (value: string) => {
        setSelectedCountry(value);
    };

    // Function to convert API key_id back to legacy codes for internal logic
    const getLegacyCountryCode = (keyId: string): string => {
        return COUNTRY_ID_TO_LEGACY_CODE[keyId] || keyId;
    };

    const handleFormChange = (changedValues: any, allValues: any) => {
        if (changedValues.countryRegion) {
            const legacyCode = getLegacyCountryCode(changedValues.countryRegion);
            handleCountryChange(legacyCode);
        }

        setFormData(allValues);

        if (onChange) {
            onChange(allValues);
        }
    };

    return (
        <div className="drawdown-account-section">
            <Form form={form} layout="vertical" initialValues={initialValues} onValuesChange={handleFormChange}>
                <div className="section section-spacing">
                    <div className="section-title-container">
                        <h4 className="section-title">支用信息</h4>
                        <InfoBanner
                            type="notice"
                            message="请确保以下同名银行账户 (或空中云汇、连连支付、寻汇的同名虚拟香港银行账户）能接收美金转账"
                            width="auto"
                        />
                    </div>
                    <div className="form-row">
                        <div className="drawdown-amount-container">
                            <CurrencyAmountInput
                                currencyName="drawdownCurrency"
                                amountName="drawdownAmount"
                                currencyOptions={currencyOptions}
                                currencyRules={[{ required: true, message: "请选择币种" }]}
                                amountRules={[
                                    { required: true, message: "请输入支用金额" },
                                    { type: "number", min: 1000, message: "最低支用金額：USD 1,000.00" },
                                    {
                                        pattern: /^\d+(\.\d{0,2})?$/,
                                        message: "只能输入两位小数"
                                    }
                                ]}
                                amountPlaceholder="最小支用金额：USD1,000.00"
                                label="支用金额"
                                className="half-width-item"
                            />
                            <div className="interest-rate-info">
                                <div className="interest-rate-text">
                                    日利率约0.039% (借1万用1天约3.9美元) 年利息： SOFR+10% 贷款限期：90天
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="form-row">
                        <Form.Item
                            name="bank"
                            label="收款银行账户"
                            rules={[{ required: true, message: "请选择银行名称" }]}
                            className="half-width-item"
                        >
                            <ConfigDataSelect
                                showSearch
                                type={ConfigType.BANK_ACCOUNT}
                                placeholder="请选择"
                                disabled={loading}
                            />
                        </Form.Item>
                    </div>

                    <div className="form-row">
                        <Form.Item name="refNumber" label="参考号码（选填）" className="half-width-item">
                            <Input placeholder="请输入" />
                        </Form.Item>
                    </div>
                </div>
            </Form>

            <div>
                <span style={{ fontSize: "16px", fontWeight: "600" }}>還款計算機</span>
                <div
                    style={{
                        padding: "20px",
                        display: "flex",
                        alignItems: "center",
                        height: "68px",
                        backgroundColor: "#F6F7FA",
                        borderRadius: "20px",
                        marginTop: "24px",
                        marginBottom: "24px",
                        fontSize: "20px",
                        fontWeight: "400"
                    }}
                >
                    <span style={{ color: "black", fontSize: "16px" }}>预计截至到期日还款：</span>

                    {showExpectedResult && (
                        <>
                            <div style={{ color: "#2463EB" }}>
                                {" "}
                                <span>本金 $</span>
                                <span>{formatNumber(formData.drawdownAmount)}</span>
                            </div>

                            <span>+</span>
                            <div style={{ color: "#BF79DF" }}>
                                {" "}
                                <span>利息 $</span>
                                <span>{formatNumber(expectedInterest)}</span>
                            </div>

                            <span>-</span>
                            <div style={{ color: "#DD4C4C" }}>
                                {" "}
                                <span>红包抵扣 $</span>
                                <span>{formatNumber(redpocketWaive)}</span>
                            </div>

                            <span>=</span>
                            <span>{formatNumber(formData.drawdownAmount + expectedInterest - redpocketWaive)}</span>
                        </>
                    )}
                </div>
                <div style={{ color: "#6E6E75" }}>
                    上述计算结果只供参考。丰泊国际并不会为该等资料的准确性，可靠性及完整性和/或针对某特定用途的适用性作出任何保证或及陈述。
                    是次支用申请以丰泊国际本公司最终评估和批准审核为准，贷款确认函之条款及细则均适用于本次支用申请。
                </div>
                <div style={{ color: "#6E6E75", marginTop: "24px", marginBottom: "24px" }}>
                    适用于利息按照有抵押隔夜融资利率（SOFR）：
                    <br />
                    利息按照有抵押隔夜融资利率（SOFR）（由纽约联邦储备银行管理和发表的有抵押隔夜融资利率（newyorkfed.org））以单利形式依照每年360天为计算基础根据实际天数每天累计，并基于五
                    (5) 个工作日的回溯期相关利率。
                    <br />
                    具体而言，SOFR并非预先指定或固定的利率，而每天累计的利息是基于每天获取的SOFR利率，而非平均計算的SOFR利率。针对回溯期以外的利息是基于不能知悉SOFR的情况下计算出来，相关计算结果会视为粗略估算和近似计算。
                </div>
            </div>
        </div>
    );
};

export default CAADrawdownAccountSection;
