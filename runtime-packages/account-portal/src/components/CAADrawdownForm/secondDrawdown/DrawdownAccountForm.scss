.drawdown-account-form {
  width: 100%;
  // max-width: 900px;
  margin: 0 auto;
  
  .review-section {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 4px;
    
    h3 {
      margin-bottom: 20px;
      font-weight: 500;
    }
    
    pre {
      background-color: #fff;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #e8e8e8;
      overflow: auto;
    }
  }
  
  .action-buttons {
    display: flex;
    justify-content: center;
  }
  
  .confirm-checkbox {
    font-weight: 400;
    font-size: 14px;
    display: flex;
    align-items: center;
    
    .ant-checkbox + span {
      padding-left: 8px;
      padding-right: 8px;
      color: #6E6E75;
      font-weight: 400;
      font-size: 14px;
    }
    
    &.ant-checkbox-wrapper-checked {
      .ant-checkbox + span {
        color: #282830;
      }
    }
  }
} 