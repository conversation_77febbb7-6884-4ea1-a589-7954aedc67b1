import React, { useRef } from 'react';
import { Button } from '@/components/shared/Button';
import Stepper from '@/components/shared/Stepper';
import DrawdownAccountSection from './DrawdownAccountSection';
import ArrowRightIcon from '@/assets-new/icons/arrow-right-sm.svg?react';
import { UnsupportedRegionModal } from '../UnsupportedRegionModal';
import { CountryId } from '@/constants/configTypes';
import './DrawdownAccountForm.scss';
import '../styles.scss'
import { useNavigate } from 'react-router-dom';
import { useMatomoContext } from "@/contexts/MatomoContext.tsx";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import { useCAADrawdownAccountStore } from '@/store/caaDrawdownAccount';
import { usePositionTracking } from '@/hooks/usePositionTracking';
import { POSITION_STATUS } from '@/constants/position-status';

const DrawdownAccountForm: React.FC = () => {
  const { trackPosition } = usePositionTracking();
  const navigate = useNavigate();
  const formRef = useRef<any>(null);
  const { trackEvent, isEnabled } = useMatomoContext();

  const {
    formData,
    isLoading,
    isInitialHongKong,
    showUnsupportedRegionModal,
    setFormData,
    setShowUnsupportedRegionModal,
    fetchDebitAccount,
    submitDebitAccount
  } = useCAADrawdownAccountStore();

  // Fetch debit account data on component mount
  React.useEffect(() => {
    fetchDebitAccount();
  }, [fetchDebitAccount]);

  const handleChange = (values: Partial<typeof formData>) => {
    setFormData(values);
  };

  const handleNext = async () => {
    if (formRef.current) {
      try {
        await formRef.current.validateFields();
        if (isEnabled) {
          trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.FINISH_FILLING_KYC); 
        }
        // Check if region is China
        if (formData.countryRegion === CountryId.CHINA) {
          setShowUnsupportedRegionModal(true);
          return;
        }

        const values = await formRef.current.validateFields();
        const result = await submitDebitAccount(values);
        
        if (result.success) {
          await trackPosition({ path: "/credit/underwriting/register/business-info", status: POSITION_STATUS.CAA_DRAWDOWN_ACCOUNT_SUBMITTED });
          navigate('/credit/underwriting/register/business-info');
        } else if (result.status === 4012) {
          // Status 4012 means the business registration number has been registered
          if (formRef.current) {
            formRef.current.setFields([
              {
                name: 'businessRegistrationNumber',
                errors: ['该公司已注册']
              }
            ]);
          }
        } else if (result.status === 4020) {
          if (formRef.current) {
            formRef.current.setFields([
              {
                name: 'swiftCode',
                errors: ['SWIFT代码有误']
              }
            ]);
          }
        } else {
          console.error('Failed to submit debit account information:', result.error);
        }
      } catch (error) {
        console.error('Validation failed:', error);
      }
    }
  };

  // Define steps for the stepper
  const steps = [
    {
      title: '用款账户',
      content: (
        <DrawdownAccountSection
          formRef={formRef}
          initialValues={formData}
          onChange={handleChange}
          isInitialHongKong={isInitialHongKong}
        />
      ),
      action: (
        <div className="flex flex-col items-center">
          <Button
            type="primary"
            onClick={handleNext}
            style={{ width: 320 }}
            loading={isLoading}
            label={
              <span className="flex items-center justify-center">
                下一步
                <ArrowRightIcon style={{ marginLeft: '4px' }} />
              </span>
            }
          >
            下一步
          </Button>
        </div>
      )
    },
    {
      title: '平台授权与信息确认',
      content: (<></>),
      action: null,
    },
    {
      title: '签约放款',
      content: (<></>),
      action: null,
    },
  ];

  return (
    <div className="drawdown-account-form">
      <Stepper
        steps={steps}
        currentStep={0}
        borderTopRadiusRounded={false}
      />
      <UnsupportedRegionModal
        open={showUnsupportedRegionModal}
        onClose={() => setShowUnsupportedRegionModal(false)}
      />
    </div>
  );
};

export default DrawdownAccountForm; 