import { DocumentConfig, StatusConfig, SigningStatus } from './SigningCardTypes';
import { ConfigType } from '@/constants/configTypes';

// Document requirements for each ID type
export const idTypeDocuments: Record<string, DocumentConfig> = {
  cnId: {
    key: 'cnId',
    label: '中国居民身份证',
    fields: [
      { key: 'idDocumentFront', label: '中国居民身份证（正面）', title: '中国居民身份证（正面）', required: true },
      { key: 'idDocumentBack', label: '中国居民身份证（反面）', title: '中国居民身份证（反面）', required: true }
    ]
  },
  hkId: {
    key: 'hkId',
    label: '香港身份证',
    fields: [
      { key: 'idDocumentFront', label: '香港身份证（正面）', title: '香港身份证（正面）', required: true },
      { key: 'idDocumentBack', label: '香港身份证（反面）', title: '香港身份证（反面）', required: true }
    ]
  },
  hkIdWithTravel: {
    key: 'hkIdWithTravel',
    label: '香港身份证&港澳居民来往内地通行证',
    fields: [
      { key: 'idDocumentFront', label: '香港身份证（正面）', title: '香港身份证（正面）', required: true },
      { key: 'idDocumentBack', label: '香港身份证（反面）', title: '香港身份证（反面）', required: true },
      { key: 'extraDocumentFront', label: '港澳居民来往内地通行证（正面）', title: '港澳居民来往内地通行证（正面）', required: true },
      { key: 'extraDocumentBack', label: '港澳居民来往内地通行证（反面）', title: '港澳居民来往内地通行证（反面）', required: true }
    ]
  },
  passport: {
    key: 'passport',
    label: '护照',
    fields: [
      { key: 'idDocumentFront', label: '护照（正面）', title: '护照（正面）', required: true },
      { key: 'idDocumentBack', label: '护照（反面）', title: '护照（反面）', required: true }
    ]
  }
};

// Map nationality key_id to ID type (for auto-selection)
export const getNationalityIdType = (nationalityId: string, papers_type?: string): string => {
  switch (nationalityId) {
    case '2': // China
      return 'cnId';
    case '1': // Hong Kong - will be manually selected
      if (papers_type === 'hkidcard') {
        return 'hkId'
      }
      return 'hkIdWithTravel'
    default:  // All others
      return 'passport';
  }
};

// Check if nationality allows manual ID type selection
export const isManualIdTypeSelection = (nationalityId: string): boolean => {
  return nationalityId === '1'; // Hong Kong
};

// Get available ID type options for a nationality
export const getIdTypeOptions = (nationalityId: string) => {
  switch (nationalityId) {
    case '1': // Hong Kong
      return [
        { value: 'hkId', label: '香港身份证' },
        { value: 'hkIdWithTravel', label: '香港身份证&港澳居民来往内地通行证' }
      ];
    case '2': // China
      return [{ value: 'cnId', label: '中国居民身份证' }];
    default: // All others
      return [{ value: 'passport', label: '护照' }];
  }
};

export const getStatusConfig = (status: SigningStatus): StatusConfig => {
  switch (status) {
    case 'pending':
      return {
        label: '未签约',
        backgroundColor: '#A6A2B5',
        color: '#FFFFFF'
      };
    case 'signing':
      return {
        label: '签约中',
        backgroundColor: '#8F8BA3',
        color: '#FFFFFF'
      };
    case 'signed':
      return {
        label: '已签署',
        backgroundColor: '#40B16E',
        color: '#FFFFFF'
      };
    case 'rejected':
      return {
        label: '已拒绝',
        backgroundColor: '#FF0000',
        color: '#FFFFFF'
      };
    case 'expired':
      return {
        label: '已过期',
        backgroundColor: '#FF0000',
        color: '#FFFFFF'
      };
    case 'withdrawn':
      return {
        label: '已撤回',
        backgroundColor: '#FF0000',
        color: '#FFFFFF'
      };
    case 'failed':
      return {
        label: '已失败',
        backgroundColor: '#FF0000',
        color: '#FFFFFF'
      };
    default:
      return {
        label: '未知',
        backgroundColor: '#f5f5f5',
        color: '#595959'
      };
  }
};

// Helper function to get country options from config
export const getCountryOptions = (companyConfig: any) => {
  if (!companyConfig || !companyConfig[ConfigType.COUNTRY_REGION]) {
    return [];
  }

  return companyConfig[ConfigType.COUNTRY_REGION].map((country: any) => ({
    value: country.key_id.toString(),
    label: country.name_chi
  }));
};

// Helper function to get phone country code options from config
export const getPhoneCountryOptions = (companyConfig: any) => {
  if (!companyConfig || !companyConfig[ConfigType.MOBILE_AREA_CODE]) {
    return [];
  }

  return companyConfig[ConfigType.MOBILE_AREA_CODE].map((areaCode: any) => ({
    value: areaCode.name,
    label: areaCode.name_chi
  }));
}; 