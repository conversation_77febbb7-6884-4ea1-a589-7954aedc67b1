import React from 'react';
import Modal from '@/components/common/Modal';
import SecondaryButton from '@/components/shared/SecondaryButton';
import { Button } from '@/components/shared/Button';
import AlertIcon from '@/assets-new/icons/alert-icon.svg?react';
import SuccessIcon from '@/assets-new/icons/success-icon.svg?react';
import { ModalConfig, ButtonConfig } from '@/constants/signing';


/**
 * Error modal for displaying error messages
 */
export interface ErrorModalProps {
  open: boolean;
  onClose: () => void;
  errorMessage: string | React.ReactNode;
}

export const ErrorModal: React.FC<ErrorModalProps> = ({ 
  open, 
  onClose, 
  errorMessage 
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title="温馨提示"
      width={ModalConfig.ERROR_MODAL_WIDTH}
    >
      <div className="flex flex-col items-center">
        <p className="text-center flex items-center justify-center">
          <AlertIcon className="mr-2" />
          {errorMessage}
        </p>
      </div>
      <div className="flex justify-center mt-8">
        <SecondaryButton
          label="返回检查"
          onClick={onClose}
        />
      </div>
    </Modal>
  );
};

/**
 * Submit success modal
 */
export interface SubmitModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const SubmitModal: React.FC<SubmitModalProps> = ({ 
  open, 
  onClose,
  onConfirm
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title="提交成功提醒"
      width={ModalConfig.SUBMIT_MODAL_WIDTH}
    >
      <div className="flex flex-col items-center">
        <SuccessIcon width={80} height={80} className="text-green-500 mb-1" />
        <p className="text-center text-sm font-semibold mb-2">您的用款申请已提交成功！</p>
        <p className="text-center text-sm font-normal">请留意手机短信, 预计在<span className="font-bold">30</span>分钟内收到放款通知</p>
      </div>
      <div className="flex justify-center mt-10">
        <Button
          label="确定"
          onClick={onConfirm}
          style={{
            height: ButtonConfig.CONFIRM_BUTTON_HEIGHT,
            width: ButtonConfig.CONFIRM_BUTTON_WIDTH,
          }}
        />
      </div>
    </Modal>
  );
};

/**
 * Initiate sign success modal
 */
export interface InitiateSuccessModalProps {
  open: boolean;
  onClose: () => void;
}

export const InitiateSuccessModal: React.FC<InitiateSuccessModalProps> = ({ 
  open, 
  onClose
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title="温馨提示"
      width={ModalConfig.INITIATE_SUCCESS_MODAL_WIDTH}
    >
      <div className="flex flex-col items-center">
        <p className="text-center text-sm font-normal">签约合同已发送给相关人，请留意手机和短信，并完成签约。</p>
      </div>
      <div className="flex justify-center mt-8">
        <Button
          type="primary"
          label="确定"
          onClick={onClose}
          style={{
            height: ButtonConfig.CONFIRM_BUTTON_HEIGHT,
            width: ButtonConfig.CONFIRM_BUTTON_WIDTH,
          }}
        />
      </div>
    </Modal>
  );
}; 