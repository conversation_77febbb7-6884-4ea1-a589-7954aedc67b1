import React, { useRef, lazy, Suspense, useState } from 'react';
import { Button } from '@/components/shared/Button';
import Stepper from '@/components/shared/Stepper';
import BusinessInfoSection from './BusinessInfoSection';
import ArrowRightIcon from '@/assets-new/icons/arrow-right-sm.svg?react';
import { Checkbox, Spin, Col } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import './BusinessInfoForm.scss';
import '../styles.scss';
import { useNavigate } from 'react-router-dom';
import { useCAABusinessInfoStore } from '@/store/caaBusinessInfo';
import {getOauthShopList, sync_auth_shop_status} from "@fundpark/fp-api";
import type { GetOauthShopListRes } from "@fundpark/fp-api";
import { usePositionTracking } from '@/hooks/usePositionTracking';
import { POSITION_STATUS } from '@/constants/position-status';
import appHelper from "@/utils/appHelper";
import { useMatomoContext } from '@/contexts/MatomoContext';
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from '@/components/shared/tracking/constants';
import { MATOMO_CONFIG } from '@/utils/matomoConfig';

// Dynamically import AuthShop
const AuthShop = lazy(() => import('@/components/ActivateShopForm/AuthShop'));

const BusinessInfoForm: React.FC = () => {
  const { trackPosition } = usePositionTracking();
  const { trackEvent, trackEventWithDimensions } = useMatomoContext();
  const navigate = useNavigate();
  const formRef = useRef<any>(null);
  const [rows, setRows] = useState<any[]>([]);
  const [authedPlatforms, setAuthedPlatforms] = useState<GetOauthShopListRes>({ shop: [], psp: [], ouath_change: 0 });

  const {
    formData,
    isLoading,
    isConfirmed,
    currentSubStep,
    setFormData,
    setIsConfirmed,
    setCurrentSubStep,
    fetchBusinessInfo,
    submitBusinessInfo
  } = useCAABusinessInfoStore();

  const fetchData = async () => {
    try {
      const res = await getOauthShopList({});
      if (res.code === 0) {
        setAuthedPlatforms(res.data);
        setRows(
          res.data.shop.map((shop, index) => ({
            id: index + 1,
            platform_id: shop.platform_id,
            auth_status: shop.oauth_status,
            credit_status: shop.credit_status,
            platform: shop.platform,
            shop_id: shop.seller_id,
            platform_shop_id: shop.id,
            psp: shop.choose_psp ? `${shop.choose_psp.platform}: ID - ${shop.choose_psp.account_id}` : null,
            psp_id: shop.choose_psp ? shop.choose_psp.oauth_psp_id : null,
            psp_verify: shop.choose_psp ? shop.choose_psp.oauth_status : null,
            can_delete: shop.can_delete,
          }))
        );
      } else {
        console.error("Oauth list error:", res.message);
      }
    } catch (error) {
      console.error("Error fetching authorized shop data", error);
    }
  };

  // Track initial position on component mount
  React.useEffect(() => {
    fetchBusinessInfo();
    fetchData();
  }, [fetchBusinessInfo]);

  const handleChange = (values: Partial<typeof formData>) => {
    setFormData(values);
  };

  const handleCheckboxChange = (e: CheckboxChangeEvent) => {
    const isChecked = e.target.checked;
    setIsConfirmed(isChecked);
    
    // Track checkbox interaction
    trackEventWithDimensions({
      category: TRACKING_CATEGORIES.FORM,
      action: TRACKING_ACTIONS.SELECT,
      name: TRACKING_EVENTS.CHECK_BUSINESS_INFO_CONFIRMATION,
      customDimensions: {
        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: isChecked ? 'confirmed' : 'unconfirmed'
      }
    });
  };

  const handleNextSubStep = async () => {
    if (rows.find(row => row.psp_id === null)) {
      appHelper.msgApi.error('请编辑绑定收款公司');
      return;
    }
    
    // Track next step button click
    trackEvent(
      TRACKING_CATEGORIES.BUTTON,
      TRACKING_ACTIONS.CLICK,
      TRACKING_EVENTS.CLICK_BUSINESS_INFO_SUBMIT
    );

    await sync_auth_shop_status();
    setCurrentSubStep(1);
  };

  const handleNext = async () => {
    if (formRef.current) {
      try {
        await formRef.current.validateFields();
        
        // Track submit button click
        trackEvent(
          TRACKING_CATEGORIES.BUTTON,
          TRACKING_ACTIONS.CLICK,
          TRACKING_EVENTS.CLICK_BUSINESS_INFO_SUBMIT
        );
        
        const result = await submitBusinessInfo();

        if (result.success) {
          await trackPosition({ path: "/credit/underwriting/register/signing/loading", status: POSITION_STATUS.CAA_BUSINESS_INFO_SUBMITTED });
          navigate('/credit/underwriting/register/signing/loading');
        } else {
          console.error('Failed to submit business information:', result.error);
        }
      } catch (error) {
        console.error('Validation failed:', error);
      }
    }
  };

  // Define steps for the stepper
  const steps = [
    {
      title: '用款账户',
      content: (<></>),
      action: null,
    },
    {
      title: '平台授权与信息确认',
      content: currentSubStep === 0 ? (
        <Suspense fallback={<Spin size="large" />}>
          <Col className="px-6">
            <AuthShop
              addShopButton={false}
              canDelete={false}
              rows={rows}
              authedPlatforms={authedPlatforms}
            />
          </Col>
        </Suspense>
      ) : (
        <BusinessInfoSection
          formRef={formRef}
          initialValues={formData}
          onChange={handleChange}
        />
      ),
      action: currentSubStep === 0 ? (
        <div className="flex flex-col items-center">
          <Button
            type="primary"
            onClick={handleNextSubStep}
            style={{ width: 320 }}
            label={
              <span className="flex items-center justify-center">
                下一步
                <ArrowRightIcon style={{ marginLeft: '4px' }} />
              </span>
            }
          >
            下一步
          </Button>
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <div className="mb-6 flex justify-center">
            <Checkbox
              checked={isConfirmed}
              onChange={handleCheckboxChange}
              className="confirm-checkbox"
            >
              本公司确认以上资料信息正确
            </Checkbox>
          </div>
          <Button
            type="primary"
            onClick={handleNext}
            style={{ width: 320 }}
            loading={isLoading}
            disabled={!isConfirmed}
            label={
              <span className="flex items-center justify-center">
                确认以上信息
                <ArrowRightIcon style={{ marginLeft: '4px' }} />
              </span>
            }
          >
            确认以上信息
          </Button>
        </div>
      )
    },
    {
      title: '签约放款',
      content: (<></>),
      action: null,
    },
  ];

  return (
    <div className="business-info-form">
      <Stepper
        steps={steps}
        currentStep={1}
        borderTopRadiusRounded={false}
      />
    </div>
  );
};

export default BusinessInfoForm; 