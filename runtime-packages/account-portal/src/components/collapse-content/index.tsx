import initCollapseMotion from "antd/es/_util/motion";
import CSSMotion, { CSSMotionProps } from "rc-motion";

interface CollapseContentProps extends CSSMotionProps {}

const CollapseContent: React.FC<CollapseContentProps> = props => {
    return (
        <CSSMotion
            {...initCollapseMotion("ant")}
            motionAppear={false}
            leavedClassName="ant-content-hidden"
            {...props}
        ></CSSMotion>
    );
};

export default CollapseContent;
