import NoticeIcon from "@/assets-new/icons/common/notice_icon.svg";
import ErrorNoticeIcon from "@/assets-new/icons/common/red_notice.svg";
import NoticeLayout from "@/layouts/NoticeLayout";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import React, { useEffect, useState, useRef } from 'react';
import { Modal } from '@/components/shared/Modal';
import { SecondaryButton } from '@/components/shared/SecondaryButton';
import {CompanyNotification} from '@fundpark/fp-api/types/notifications.ts'
import './styles.scss';
import {WechatImage} from "@/components/WechatImage.t.tsx";
import { AuthGuideModal } from "../AuthGuideModal";

interface LoanNoticeBannerProps {
    notices: CompanyNotification[];
}

const LoanNoticeBanner: React.FC<LoanNoticeBannerProps> = ({ notices }) => {
    const [maxLength, setMaxLength] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const [showWechatModal, setShowWechatModal] = useState(false);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [needDetails, setNeedDetails] = useState(false);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [modifiedText, setModifiedText] = useState(notices[currentIndex].content);
    const [parsedText, setParsedText] = useState(notices[currentIndex].content);
    const [isAutoScroll, setIsAutoScroll] = useState(true);
    const [title,setTitle] = useState(notices[currentIndex].title)
    const [link,setLink] = useState(notices[currentIndex].link)
    const [code,setCode] = useState(notices[currentIndex].code)


    // Function to truncate text
    const truncateText = (text: string, maxLength: number) => {
        let result = text;
        if (text.length > maxLength) { 
            result = text.slice(0, maxLength) + '...' ;
        } ;
        return result; 
    };

    // Function to update maxLength based on container width
    const updateMaxLength = () => {
        if (containerRef.current) {
            const divisor = notices[currentIndex].level === 'error'? 15: 20;
            const parentWidth = containerRef.current.parentElement?.clientWidth|| 0; // Get the width of the parent
            const availableWidth = parentWidth * 0.65;
            const newMaxLength = Math.floor(availableWidth / divisor); // Adjust for average character width
            setMaxLength(newMaxLength);
        }
    };

    // Check the text length and update needDetails state
    useEffect(() => {
        const descText: string = notices[currentIndex].content;
        const parsedText = descText.includes('\\n') ? JSON.parse(`"${descText}"`) : descText;
        const modifiedText = parsedText.replace(/[\r\n]+/g, ' '); // Replace newlines with a space
        const newTitle = notices[currentIndex].title
        const newLink = notices[currentIndex].link
        const newCode = notices[currentIndex].code
        if (( newLink === 'shop_list') ||(modifiedText && modifiedText.length > maxLength)) {
            setNeedDetails(true);
        } else {
            setNeedDetails(false);
        };

        setModifiedText(modifiedText);
        setParsedText(parsedText);
        setTitle(newTitle)
        setLink(newLink)
        setCode(newCode)
    }, [modifiedText, maxLength, currentIndex,title,link,code]);

    // Set initial maxLength and handle resize
    useEffect(() => {
        updateMaxLength(); // Set initial value
        window.addEventListener('resize', updateMaxLength); // Update on resize

        // Cleanup event listener on unmount
        return () => {
            window.removeEventListener('resize', updateMaxLength);
        };
    }, []);

    useEffect(() => {
        let interval: NodeJS.Timeout | undefined;

        if (isAutoScroll) {
            interval = setInterval(() => {
                setCurrentIndex((prevIndex) => (prevIndex + 1) % notices.length);
            }, 5000); // Change every 5 seconds
        }

        return () => {
            if (interval) clearInterval(interval); // Cleanup on unmount
        };
    }, [isAutoScroll, notices.length]);

    const handleShowDetails = (state: boolean) => {
        if (state) {
            setIsAutoScroll(false);
            setShowDetailsModal(true);
        } else {
            setIsAutoScroll(true);
            setShowDetailsModal(false);
        }
    };

    const handleShowWechatModal = (state: boolean) => {
        if (state) {
            setIsAutoScroll(false);
            setShowWechatModal(true);
        } else {
            setIsAutoScroll(true);
            setShowWechatModal(false);
        }
    };
    

    const selectedModal =() => {
        if(link==='shop_list' && (code === "4016" || code === "4017")){
            return (
                <AuthGuideModal 
                    open={showDetailsModal} 
                    canClose={true}
                    onClose={() => handleShowDetails(false)}
                    notificationId={notices[currentIndex].id}/>
            )
        }else{
            return(
                    <Modal
                        open={showDetailsModal}
                        onClose={() => handleShowDetails(false)}
                        title="详情"
                        >
                        <div>
                            <p className="mb-4" style={{ textAlign: 'left' }}>
                                {parsedText.split('\n').map((line, index) => (
                                    <span key={index}>{line}<br /></span>
                                ))}
                                
                            </p>
                            <p className={`mb-4 flex items-center justify-center`}>您可以试试联系我们～</p>
                            <div className="flex justify-center">
                                <WechatImage
                                    style={{ width: '140px', height: '142px'}}
                                />
                            </div>
                            <div className="flex justify-center mt-10">
                            <SecondaryButton
                                label="关闭"
                                onClick={() => handleShowDetails(false)}
                            />
                            </div>
                        </div>
                    </Modal>
                    
            )
        }
    }

    return (
        <NoticeLayout>
            <div className="loan-notice-banner" style={{ background: notices[currentIndex].level === 'error'? '#FCEDED' : '#E9EFFD' , alignItems: "center", display: 'flex', justifyContent: 'left'}}>
                <div 
                    key={currentIndex}
                    className='notice'
                    ref={containerRef} // Reference to the inner container
                    style={{ margin: "0 20%", height: "30px", display: 'flex', alignItems: 'center'}}
                >
                    <img src={notices[currentIndex].level === 'error'? ErrorNoticeIcon : NoticeIcon} alt="" style={{ marginRight: '8px' }} />
                    <span style={{ color: notices[currentIndex].level === 'error'? '#DD4C4C' : '#2463EB', fontSize: '14px' }}>
                        {modifiedText ? truncateText(modifiedText, maxLength) : ''}
                    </span>
                    {needDetails && (<span 
                        onClick={() => handleShowDetails(true)}
                        style={{ 
                            marginLeft: '2px', 
                            color: notices[currentIndex].level === 'error'? '#DD4C4C' : '#2463EB',
                            cursor: 'pointer',
                        }}
                    >
                        【查看详情】
                    </span>) }

                    {
                        selectedModal()
                    }
                    
                    {(notices[currentIndex].level === 'error' && link !='shop_list') && (<span 
                        onClick={() => handleShowWechatModal(true)} // Call the onClick function when clicked
                        style={{ 
                            marginLeft: '2px', 
                            color: notices[currentIndex].level === 'error'? '#DD4C4C' : '#2463EB',
                            cursor: 'pointer',
                            width: '100px'
                        }}
                    >
                        【联系客服】
                    </span>)}
                    <OurWechatModal
                        open={showWechatModal}
                        onClose={() => handleShowWechatModal(false)}
                        message={"有任何问题，欢迎联系我们～"}
                        hasAlertIcon={true}
                        textAlign="center"
                    />
                </div>
            </div>
        </NoticeLayout>
    );
};

export default LoanNoticeBanner;