@use "@/assets/styles/variables.module.scss" as *;

.languageSelector {
    position: relative;
    display: inline-block;
    // width: 124px;
    height: 40px;

    :global(.language-icon) {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.88);
        transition: all 0.3s;
    }

    :global(.select) {
        width: 100%;
        height: 100%;
    }

    :global(.ant-select-selector) {
        height: 40px !important;
        display: flex;
        align-items: center;
        padding: 0 16px !important;
        box-shadow: none !important;
        border-radius: 9999px !important;
        background-color: #f5f5f5 !important;
        border: none !important;
    }

    :global(.ant-select-selection-item) {
        padding-left: 24px !important;
        transition: all 0.3s;
        font-size: 14px;
        color: black !important;
        display: flex !important;
        align-items: center;
    }

    :global(.ant-select-arrow) {
        right: 12px;
        color: black !important;
    }

    :global(.anticon) {
        transition: all 0.3s !important;
    }

    &:hover {
        :global {
            .language-icon, .ant-select-selection-item, .anticon {
                color: $fp-primary-color !important;
            }
        }
    }

    &:active {
        :global(.ant-select-selector) {
            background-color: #ECEFF6 !important;
        }
    }
    
    &.opened {
        :global {
            .language-icon, .ant-select-selection-item, .anticon {
                color: $fp-primary-color !important;
            }
        }
    }
}

:global(.ant-select-dropdown) {
    :global(.ant-select-item-option-selected) {
        background-color: rgba(32, 23, 71, 0.08) !important;
        font-weight: 600 !important;
    }
}
