import { Select } from "antd";
import LanguageIcon from "@/assets/icons/language-icon.svg?react";
import { useTranslation } from "react-i18next";
import type { Locale } from "@fundpark/ui-utils";
import { useSettingsStore } from "@/store/settings";
import styles from "./index.module.scss";
import ArrowDown from '@/assets/icons/arrow-down-2.svg?react';
import ArrowUp from '@/assets/icons/arrow-up-2.svg?react';
import ArrowDownDark from '@/assets/icons/arrow-down-dark-2.svg?react';
import { useState } from "react";
import classNames from "classnames";

export const LanguageSelector: React.FC = () => {
    const { i18n, t } = useTranslation();
    const { locale, setLocale } = useSettingsStore();
    const [isHovered, setIsHovered] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    const langOptions: { label: string; value: Locale }[] = [
        { value: "en_US", label: t("layout.enUS") },
        { value: "zh_HK", label: t("layout.zhHK") },
        { value: "zh_CN", label: t("layout.zhCN") }
    ];

    const handleLanguageChange = (value: Locale) => {
        setLocale(value);
        i18n.changeLanguage(value);
    };

    const getSuffixIcon = () => {
        if (isOpen) return <ArrowUp />;
        if (isHovered) return <ArrowDown />;
        return <ArrowDownDark />;
    };

    return (
        <div className={classNames(styles.languageSelector, { [styles.opened]: isOpen })}>
            <LanguageIcon className="language-icon" />
            <Select
                value={locale}
                onChange={handleLanguageChange}
                className="select"
                suffixIcon={getSuffixIcon()}
                options={langOptions}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                onDropdownVisibleChange={(open) => setIsOpen(open)}
            />
        </div>
    );
};
