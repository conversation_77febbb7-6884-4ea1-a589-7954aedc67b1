body {
    font-family: 'Source Sans Pro', sans-serif; /* Apply the font to the body */
}

.tabs-container {
    display: flex;
    flex-direction: column;
    align-items: stretch; /* Ensure the container stretches */
}

.tabs {
    display: flex; /* Horizontal layout for tabs */
    position: relative; /* For positioning the active tab underline */
}

.tab {
    flex: 1; 
    cursor: pointer; /* Change cursor to pointer on hover */
    padding: 10px 15px; /* Space around the text */
    color: #6E6E75; /* Text color */
    text-align: center; /* Center text */
    border: none; /* No border */
    background: none; /* No background */
    font-size: 16px;
    font-weight: 600;
}

.tab.active {
    position: relative; /* For positioning the active tab underline */
    font-weight: 700;
    color: #201747;
}

.tab.active::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -5px; /* Adjust position above the bottom line */
    height: 3px; /* Thickness of the active tab underline */
    background-color: #201747; /* Color of the active line */
    
}

.bottom-line {
    width: 100%; /* Full width for the bottom line */
    height: 1.5px; /* Thickness of the bottom line */
    background-color: #201747; /* Color of the bottom line */
    margin-top: 5px; /* Space above the bottom line */
    margin-bottom: 30px;
}
.content {
    display: block;
    flex-direction: column; /* Stack content blocks vertically */
}

.faq {
    display:block;
    align-items: center; /* Align items horizontally within message */
    padding: 10px;
    margin: 0px 0;
    border-radius: 4px;
    width: 940px;
}

.faq-title {
    color: #282830;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 16px;
    position: relative;
    font-weight: 600;
    line-height: 16px;
}

.faq-content {
    color: #6E6E75;
    font-size: 14px;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    flex-direction: column;
    gap: 16px;
    line-height: 22px;
}
.faq-content ul {
    margin: 0;
}

.faq-content-link {
    color: #2463EB;
    font-size: 14px;
    line-height: 14px;
    text-decoration: underline;
    cursor: pointer; /* Change cursor to pointer on hover */
}

.faq-divider {
    height: 2px;
    background-color: #dddadae8; /* Grey line */
    margin-top: 10px;
    margin: 10px;
    flex-basis: 100%; /* Ensure the divider takes the full width */
}