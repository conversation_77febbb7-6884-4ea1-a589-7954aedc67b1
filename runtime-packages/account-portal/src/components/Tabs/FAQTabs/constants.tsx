import i18n from "@/i18n";
import {FAQProducts } from "./index";
import {Button, Drawer, Layout} from "antd";
import paymentCompanyGuideline from "@/assets-new/pdfs/payment-company-guideline-v2.pdf"
import shopAuthorizationGuideline from "@/assets-new/pdfs/shop-authorization-guideline.pdf"

// Define the function to get tab data
export const getByProductsFAQ = (): FAQProducts => {
    return {
        revenueFinancing: {
            '产品介绍': {
                '可支持平台有哪些？': '支持电商平台有Amazon、Walmart、eBay、AliExpress、Shopee、Shopify、得物、抖音、快手、淘分销、京东、天猫国际、拼多多、小红书、Cdiscount、MercadoLibre、TikTok、Temu、Lazada、kaufland、Otto、Shein、Wayfair、Manomano、Conforama、Kiwi、Traveloka、PKFare、Naver等。', 
                '什么是丰收融？': (
                    <>
                        丰收融根据商家在电商平台的交易数据，为商家提供“爽”“快”“大”“方”的授信资金。<br/>
                        🌟爽：用得爽<br/>
                        <ul>
                        <li>线上审批，无房产抵押；</li>
                        <li>随借随还，按日计息。</li>
                        </ul>
                        🌟快：时效快<br/>
                        <ul>
                        <li>3分钟申请+过审，店铺数据授权即可30分钟出额；</li>
                        <li>当日放款，还款后额度自动释放，可继续借款。</li>
                        </ul>
                        🌟大：额度大<br/>
                        <ul>
                        <li>高至月GMV12倍授信额度；</li>
                        <li>授信12个月有效。</li>
                        </ul>
                        🌟方：方方面面更懂你<br/>
                        <ul>
                        <li>限时领取免费行业报告，支持店铺经营发展；</li>
                        <li>全场景全链路支持，支持货物质押、人工提额等。</li>
                        </ul>
                    </>
                ),
                '什么是7天免息产品？': '市场首创跨境电商专享小额支持金，有店即可获取🧧2000美元支持金\n🌟3分钟申请+过审，首7天免利息\n🌟不查征信，不做锁定，不看担保，不用质押\n🌟有店必有钱\u002A，店铺越多，额度越高，最高可用1000万美金\n*无最低流水要求，需通过反欺诈审核',
                '怎么申请丰收融？': '1、入驻出口及进口跨境电商平台，授权6个月以上的电商销售记录，或授权12个月以上的支付平台流水记录 \n2、申请店铺至少有3个月以上交易记录。 \n3、过去一年月平均销售1000美金或以上（最低3个月） ',
                '怎么申请7天免息产品？': '授权您跨境店铺或支付公司的数据信息，即可用款，无需提供担保或抵押、不做锁定\u002A；\n后续更可提额至最高1000万美金。\n*无最低流水要求，需通过反欺诈审核'
            },
            '计息方式': {
                '丰收融利息是多少？': '年化利息率低至8%，按日计息，随借随还 。', 
                '7天免息产品利息是多少？': '首次7天免息使用，第二次之后正常计息，随借随还',
                '提额后利息是多少？': '额度越高，利息越低，最低可低至年化8%。'
            },
            '借款周期': {
                '丰收融借款周期是多久？': '贷款周期为90天。', 
                '7天免息产品借款周期是多久？': '首次7天免息使用，之后正常计息，随借随还。'
            },
            '还款方式': {
                '还款方式是什么？': '随借随还，按日计息。 \n点击还款管理，登陆系统后台，点击对应借款单号进行还款。 ', 
                '节假日可以还款吗？': '节假日可以还款，但由于节假日银行不工作，成功还款日将在下一个工作日。 \n请注意合理资金运营，杜绝逾期记录风险～',
                '最低还款要求是什么？': '随借随还，按日计息，提前还款无罚息。',
                '还款账户是什么？': '点击右上角还款管理，登陆系统后台，点击对应借款单号进行还款，即可查阅还款账户～'
            },
            '平台API授权': {
                '支持API授权的平台有哪些？': (
                    <>
                        <p>支持API功能授权的电商平台有Amazon、Walmart、eBay。</p>
                        <a type="text" className="faq-content-link" href={shopAuthorizationGuideline} target="_blank">查看指引→</a>
                    </>
                ),
                '可授权支付公司平台有哪些？': (
                    <>
                        <p>支持API功能授权的支付公司平台有LianLian、PingPong、Airwallex、Payoneer。</p>
                        <a type="text" className="faq-content-link" href={paymentCompanyGuideline} target="_blank">查看指引→</a>
                    </>
                ),
            },
            '其他问题': {
                '额度怎么计算？': '最高可贷1000万美金额度，根据月实际流水的2-12倍授信可贷金额。', 
                '回款账户要求是什么？': '需要绑定指定的第三方公司跨境支付公司的回款账户，目前接受Airwallex、LianLian、Payoneer、Sunrate、PingPong等支付公司。',
                '逾期利息是多少？': '逾期双倍利息，请注意合理安排资金用款哟～',
                '上征信吗？': '不上征信。',
                '公司主体要求是什么？': '有跨境电商平台即可获取额度，有香港公司主体即可放款。\n申请香港公司主体请联系人工客服，3天内完成申请注册',
                '店铺准入要求是什么？': '电商平台/支付公司/OTA平台正常交易记录满足 3 个月以上即可',
            },
    
        },
        inventoryFinancing: {
            'Tab 1': {
                'Question 1': 'Message 1A for Key 1', 
                'Question 2': 'Message 1B for Key 1'
            },
            'Tab 2': {
                'Question 1': 'Message 1A for Key 1', 
                'Question 2': 'Message 1B for Key 1'
            },
            'Tab 3': {
                'Question 1': 'Message 1A for Key 1', 
                'Question 2': 'Message 1B for Key 1'
            },
        },
    };
};