import React, { useState, FC, PropsWithChildren } from "react";
import { getByProductsFAQ } from "./constants";
import "./styles.scss";

// Define the interfaces
export interface TabContent {
    [question: string]: string | React.ReactNode; // Maps tab names to an array of messages
}

export interface TabItems {       
    [tab: string]: TabContent;     // Content associated with each tab
}

export interface FAQProducts {
    [product: string]: TabItems; // Maps keys to TabItems
}

interface FAQTabsProps {
    keyName: string;
    questionTab: string;
}

const FAQTabs : FC<PropsWithChildren<FAQTabsProps>> = ({ keyName,  questionTab}) => {
    const tabData: FAQProducts = getByProductsFAQ();
    const tabItems = tabData[keyName]; // Get the TabItems for the specified key
    const [activeTab, setActiveTab] = useState(questionTab === ''? Object.keys(tabItems)[0]:questionTab); // Set the first tab as active

    React.useEffect(() => {
        console.log('questionTab: ', typeof questionTab);
        console.log('activeTab: ', activeTab);
      }, [questionTab]);

    return (
        <div>
            <div className="tabs-container">
                <div className="tabs">
                    {Object.keys(tabItems).map((tab) => (
                        <div
                            key={tab}
                            className={`tab ${activeTab === tab ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab)}
                        >
                            {tab}
                        </div>
                    ))}
                </div>
                <div className="bottom-line"></div>
            </div>
            <div className="content">
                {Object.entries(tabItems[activeTab]).map(([question, answer]) => (
                    <FAQItems question = {question} answer = {answer} />
                ))}
            </div>
        </div>
    );
};


const FAQItems = ({ question, answer }: { question: string; answer: string}) => {
    return (
        <>
            <div className="faq">
                <div className="faq-title">{question}</div>
                <div className="faq-content">
                    {typeof answer === "string" ? (
                        answer.split("\n").map((line, index) => (
                            <React.Fragment key={index}>
                                {line}
                                {index < answer.split("\n").length - 1 && <br />}
                            </React.Fragment>
                        ))
                    ) : (
                        answer // Render ReactNode directly
                    )}
                </div>
            </div>
            <div className="faq-divider"></div>
        </>
    );
}

export default FAQTabs;
