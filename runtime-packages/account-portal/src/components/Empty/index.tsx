import { useTranslation } from "react-i18next";
import EmptyImg from "@/assets/images/default-empty/no-data.svg?react";

const Empty: React.FC<{ className?: string; style?: React.CSSProperties; text?: string }> = ({
    className,
    style,
    text
}) => {
    const { t } = useTranslation();

    return (
        <div className={`w-full flex flex-col items-center${className ? " " + className : ""}`} style={style}>
            <EmptyImg />
            <div className="text-[18px] leading-[27px] font-[600] mt-[24px]">{text || t("common.nodata")}</div>
        </div>
    );
};

export default Empty;
