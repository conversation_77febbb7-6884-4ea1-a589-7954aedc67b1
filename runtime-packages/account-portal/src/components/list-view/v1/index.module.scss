@use "@/assets/styles/variables.module.scss" as *;

.list-search {
    margin: 24px 0;
    overflow: hidden;
    &-btns {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 16px;
		button {
			gap: 0;
		}
    }
    :global {
        .ant-form-item {
            margin-bottom: 0;
        }
    }
	&-collapse-btn {
		font-weight: 600;
		color: $fp-accent-color;
	}
}

.list-card-view {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    &-wrap {
        overflow: hidden;
        border-radius: 20px;
        padding: 28px 24px 24px;
        background-color: #f6f7fa;
    }
    &-empty {
        margin: 120px 0;
    }
    &-item {
        overflow: hidden;
        width: 380px;
        height: 246px;
        border-radius: 20px;
        background-color: #fff;
        border: 1px solid $fp-gray-border-color;
        transition: all 0.3s;
        &:hover {
            border-color: $fp-accent-color;
        }
    }
}

.list-table-view {
    overflow: hidden;
    border-radius: 16px;
    border: 1px solid rgba(32, 23, 71, 0.1);
    :global {
        .ant-table-wrapper {
            .ant-table-container table > thead > tr:first-child > *:first-child,
            .ant-table-container table > thead > tr:first-child > *:last-child {
                border-radius: 0;
            }
        }
    }
}