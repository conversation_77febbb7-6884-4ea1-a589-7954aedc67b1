import { useImperativeHandle, useState, useEffect, useRef, useMemo } from "react";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { Form, Row, Col, Input, Select, DatePicker, Table, Typography, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import type { TableProps, TableColumnType } from "antd";
import CustomButton from "@/components/custom/button";
import ArrowDownIcon from "@/assets/icons/arrow-down3.svg?react";
import CustomPagination from "@/components/pagination";
import Empty from "@/components/Empty/V2";
import eventBus from "@/event";
import styles from "./index.module.scss";

const { Text } = Typography;

export interface IColumns<T> extends TableColumnType<T> {
    searchTitle?: React.ReactNode;
    valueType?: "input" | "index" | "select" | "dateTimeRange";
    initialValue?: any;
    fieldProps?: any;
    hideInTable?: true;
    search?: false;
    filter?: boolean;
    colSize?: number;
    valueEnum?: { label: string; value: any }[];
    dateTimeToTimeZone?: boolean;
    dateTimeRequestProp?: string[];
    renderFormItem?: (config: IColumns<T>, props: any) => React.ReactNode;
}

export interface FilterTableRefProps<T> {
    reload: (firstPage?: boolean) => void;
    reset: () => void;
    getFieldValues: () => T;
    setFieldValue: (fields: { name: string; value: any }[]) => void;
}

interface IProps<T, Q> extends Omit<TableProps<T>, "columns"> {
    formName?: string;
    className?: string;
    style?: React.CSSProperties;
    actionRef?: React.RefObject<FilterTableRefProps<Q>>;
    /** 初始化时是否请求数据，默认：true */
    manualRequest?: boolean;
    /** 轮询请求间隔，ms */
    polling?: number;
    /** 刷新事件 */
    refreshEvent?: string;
    /** 主键属性, 默认: id */
    rowKey?: string;
    /** 每页条数, 默认: 10 */
    pageSize?: number;
    onSearch?: (val: Q) => void;
    onReset?: () => void;
    columns?: IColumns<T>[];
    request?: (params: Q & { pageNum: number; pageSize: number }) => Promise<{ list: T[]; total: number }>;
    toolbarAction?: React.ReactNode;
    cardView?: boolean;
    cardRender?: (data: T, index: number) => React.ReactNode;
}

const ListView = <T extends Record<string, any>, Q = Record<string, any>>(props: IProps<T, Q>) => {
    const {
        formName,
        className,
        style,
        manualRequest,
        onSearch,
        onReset,
        columns,
        request,
        polling,
        actionRef,
        toolbarAction,
        refreshEvent,
        rowKey = "id",
        pageSize = 10,
        cardView = true,
        cardRender,
        ...tableProps
    } = props;
    const { t } = useTranslation();
    const [formRef] = Form.useForm<Q>();
    const [loading, setLoading] = useState(false);
    const [expand, setExpand] = useState(false);
    const [tableData, setTableData] = useState({
        total: 0,
        list: [] as T[]
    });
    const baseRef = useRef({
        destroy: false,
        timer: undefined as NodeJS.Timeout | undefined,
        reqId: 0,
        pageNum: 1,
        pageSize: pageSize || 10
    });

    useImperativeHandle(
        actionRef,
        () => ({
            reload(firstPage) {
                if (firstPage) {
                    baseRef.current.pageNum = 1;
                }
                toRequest(true);
            },
            reset() {
                baseRef.current.pageNum = 1;
                formRef.resetFields();
                toRequest(true);
            },
            getFieldValues() {
                return formRef.getFieldsValue();
            },
            setFieldValue(fields) {
                formRef.setFields(fields as any);
            }
        }),
        [actionRef]
    );

    useEffect(() => {
        baseRef.current.destroy = false;
        !manualRequest && toRequest(true);

        let unlistenRefreshEvent: (() => void) | undefined;
        if (refreshEvent) {
            unlistenRefreshEvent = eventBus.on(refreshEvent as any, (reset?: boolean) => {
                if (reset) {
                    baseRef.current.pageNum = 1;
                    formRef.resetFields();
                }
                toRequest(true);
            });
        }

        return () => {
            baseRef.current.destroy = true;
            baseRef.current.timer && clearTimeout(baseRef.current.timer);
            unlistenRefreshEvent?.();
        };
    }, []);

    const { searchCols, tableCols } = useMemo(() => {
        const searchCols: React.ReactNode[] = [];
        const tableCols: (TableColumnType<T> & { colId: number; filter?: boolean })[] = [];
        const span = expand ? 6 : 5;

        let searchColIndex = 0;
        columns?.forEach((col, index) => {
            const {
                searchTitle,
                valueType,
                initialValue,
                fieldProps,
                hideInTable,
                search,
                colSize,
                valueEnum,
                dateTimeToTimeZone,
                dateTimeRequestProp,
                renderFormItem,
                ...other
            } = col;
            const isIndex = valueType === "index";

            if (col.dataIndex !== undefined && search !== false) {
                let ele: React.ReactNode = null;
                if (renderFormItem) {
                    ele = renderFormItem(col, { ...fieldProps });
                } else {
                    switch (valueType) {
                        case "select":
                            ele = (
                                <Select
                                    allowClear
                                    placeholder={searchTitle || other.title}
                                    options={valueEnum}
                                    {...fieldProps}
                                />
                            );
                            break;
                        case "dateTimeRange":
                            ele = (
                                <DatePicker.RangePicker
                                    presets={[
                                        {
                                            label: t("filterTable.today"),
                                            value: [dayjs().startOf("day"), dayjs().endOf("day")]
                                        },
                                        {
                                            label: t("filterTable.lastweek"),
                                            value: [dayjs().subtract(1, "week").startOf("day"), dayjs().endOf("day")]
                                        },
                                        {
                                            label: t("filterTable.lastmonth"),
                                            value: [dayjs().subtract(1, "month").startOf("day"), dayjs().endOf("day")]
                                        }
                                    ]}
                                    showTime
                                    allowClear
                                    prefix={searchTitle || other.title}
                                    // placeholder={[t("filterTable.startDate"), t("filterTable.endDate")]}
                                    onChange={(dates, dateStrings) => {
                                        if (dateTimeRequestProp) {
                                            let startTime;
                                            let endTime;
                                            if (dateTimeToTimeZone) {
                                                startTime = dates && dates[0] ? dates[0].valueOf() : undefined;
                                                endTime = dates && dates[1] ? dates[1].valueOf() : undefined;
                                            } else {
                                                startTime = dateStrings[0] || undefined;
                                                endTime = dateStrings[1] || undefined;
                                            }
                                            formRef.setFields([
                                                {
                                                    name: dateTimeRequestProp[0] as any,
                                                    value: startTime
                                                },
                                                {
                                                    name: dateTimeRequestProp[1] as any,
                                                    value: endTime
                                                }
                                            ]);
                                        }
                                    }}
                                    {...fieldProps}
                                />
                            );
                            break;
                        default:
                            ele = <Input placeholder={searchTitle || other.title} allowClear {...fieldProps} />;
                            break;
                    }
                }
                const vnode = (
                    <Col
                        key={`${col.dataIndex || ""}_${col.valueType || 0}_${index}`}
                        span={colSize ? colSize * span : span}
                        flex={expand ? undefined : "auto"}
                        className={expand ? undefined : searchColIndex++ > 3 ? "hidden" : undefined}
                    >
                        <Form.Item name={col.dataIndex as string} initialValue={initialValue}>
                            {ele}
                        </Form.Item>
                    </Col>
                );
                searchCols.push(vnode);
            }

            if (!hideInTable) {
                tableCols.push({
                    ...other,
                    colId: index,
                    width: isIndex ? 80 : other.width,
                    title: getColTitle(col),
                    render:
                        other.render ||
                        ((text, _record, index) => {
                            if (isIndex) {
                                return index + 1;
                            }
                            if (valueEnum) {
                                const opt = valueEnum.find(i => i.value === text);
                                if (opt) {
                                    text = opt.label;
                                }
                            }
                            return <Text ellipsis={{ tooltip: text }}>{text}</Text>;
                        })
                });
            }
        });

        return {
            searchCols,
            tableCols
        };
    }, [columns, expand]);

    const isShowExpand = searchCols.length > 5;

    function getColTitle(col: IColumns<T>) {
        const isIndex = col.valueType === "index";
        return isIndex ? col.title || "SN" : col.title;
    }

    const toRequest = async (useLoad?: boolean) => {
        if (!request) return;

        const baseData = baseRef.current;
        baseData.timer && clearTimeout(baseData.timer);
        baseData.timer = undefined;
        const reqId = Date.now();
        baseData.reqId = reqId;

        const search: any = {};
        const values = formRef.getFieldsValue(true);

        if (columns) {
            for (const item of columns) {
                if (item.search !== false && item.dataIndex) {
                    if (item.dateTimeRequestProp) {
                        const [start, end] = item.dateTimeRequestProp;
                        search[start] = values[start];
                        search[end] = values[end];
                    } else {
                        search[item.dataIndex as string] = values[item.dataIndex];
                    }
                }
            }
        }

        useLoad && setLoading(true);

        try {
            const res = await request({
                ...search,
                pageNum: baseData.pageNum,
                pageSize: baseData.pageSize
            });
            if (!baseData.destroy && reqId === baseData.reqId && res) {
                setTableData({
                    list: res.list,
                    total: Number(res.total)
                });
            }
        } catch (error) {
            if (!baseData.destroy && reqId === baseData.reqId) {
                setTableData({ list: [], total: 0 });
            }
            throw error;
        } finally {
            if (!baseData.destroy && reqId === baseData.reqId) {
                useLoad && setLoading(false);
                if (polling) {
                    baseData.timer = setTimeout(() => {
                        toRequest();
                    }, polling);
                }
            }
        }
    };

    const formSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        onSearch?.(formRef.getFieldsValue());
        baseRef.current.pageNum = 1;
        toRequest(true);
    };

    const formReset = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        onReset?.();
        baseRef.current.pageNum = 1;
        toRequest(true);
    };

    const paginationNode = (
        <CustomPagination
            total={tableData.total}
            current={baseRef.current.pageNum}
            pageSize={baseRef.current.pageSize}
            onChange={(pageNum, pageSize) => {
                baseRef.current.pageNum = baseRef.current.pageSize !== pageSize ? 1 : pageNum;
                baseRef.current.pageSize = pageSize;
                setTableData(pre => ({ ...pre }));
                toRequest(true);
            }}
        />
    );

    return (
        <div className={className} style={style}>
            <div className={styles.listSearch}>
                <Form
                    name={formName}
                    layout="vertical"
                    autoComplete="off"
                    form={formRef}
                    size="large"
                    onSubmitCapture={formSubmit}
                    onReset={formReset}
                >
                    <Row gutter={[16, 16]} wrap>
                        {searchCols}
                        <Col flex={expand ? "auto" : 1}>
                            <Form.Item>
                                <div className={styles.listSearchBtns}>
                                    <CustomButton htmlType="reset" disabled={loading}>
                                        {t("filterTable.reset")}
                                    </CustomButton>
                                    <Spin spinning={loading} indicator={<LoadingOutlined className="text-white" />}>
                                        <CustomButton type="primary" htmlType="submit" className="font-semibold">
                                            {t("filterTable.search")}
                                        </CustomButton>
                                    </Spin>
                                    {isShowExpand && (
                                        <CustomButton
                                            type="link"
                                            className={styles.listSearchCollapseBtn}
                                            onClick={() => setExpand(pre => !pre)}
                                        >
                                            {expand ? t("filterTable.collapse") : t("filterTable.expand")}
                                            <ArrowDownIcon
                                                style={{ fontSize: 18 }}
                                                className={expand ? "rotate-180" : undefined}
                                            />
                                        </CustomButton>
                                    )}
                                </div>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </div>
            <Spin spinning={loading} indicator={<LoadingOutlined style={{ color: "#fff" }} />}>
                {cardView ? (
                    <>
                        <CardTable dataSource={tableData.list} rowKey={rowKey} render={cardRender} />
                        {paginationNode}
                    </>
                ) : (
                    <div className={styles.listTableView}>
                        <Table<T>
                            {...tableProps}
                            rowKey={rowKey}
                            dataSource={tableData.list}
                            columns={tableCols}
                            locale={{
                                emptyText: <Empty />
                            }}
                            pagination={false}
                        />
                        {paginationNode}
                    </div>
                )}
            </Spin>
        </div>
    );
};

const CardTable: React.FC<{
    dataSource?: any[];
    rowKey: string;
    render?: (data: any, index: number) => React.ReactNode;
}> = ({ dataSource, rowKey, render }) => {
    return (
        <div className={styles.listCardViewWrap}>
            {!dataSource || (dataSource.length === 0 && <Empty className={styles.listCardViewEmpty} />)}
            <div className={styles.listCardView}>
                {dataSource &&
                    render &&
                    dataSource.map((item, index) => (
                        <div key={item[rowKey]} className={styles.listCardViewItem}>
                            {render(item, index)}
                        </div>
                    ))}
            </div>
        </div>
    );
};

export default ListView;
