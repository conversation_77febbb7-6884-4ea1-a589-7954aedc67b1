@use "@/assets/styles/variables.module.scss" as *;

:global {
    .ant-drawer {
        .ant-drawer-header {
            padding: 40px;
            height: 100px;
            margin: 0;
            border-radius: 24px 24px 0 0;
            border: none;
            position: relative;
            background: linear-gradient(
                180deg,
                rgba(101, 198, 195, 0.4) 4.08%,
                rgba(208, 240, 239, 0.4) 48.3%,
                rgba(255, 255, 255, 0.4) 100%
            );
        }
        .ant-drawer-close {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            font-weight: 600;
			margin: 0;
            color: $fp-text2-color;
        }
        .ant-drawer-title {
            font-size: 20px;
            line-height: 24px;
            font-weight: 900;
            text-align: center;
            color: $fp-text-color;
        }
        .ant-drawer-content {
            border-radius: 24px 24px 0 0;
        }
        .ant-drawer-body {
            padding: 0 10px;
            line-height: 20px;
            font-weight: 400;
            color: $fp-text-color;
        }
    }
}

.custom-drawer {
    &-body {
        padding: 0 90px 40px;
        overflow-y: auto;
    }
}
