import { Drawer } from "antd";
import cls from "classnames";
import type { DrawerProps } from "antd";
import styles from "./index.module.scss";

interface CustomDrawerProps extends DrawerProps {
    contentClassName?: string;
}

const CustomDrawer: React.FC<CustomDrawerProps> = ({ contentClassName, children, ...antdProps }) => {
    return (
        <Drawer closable {...antdProps}>
            <div className={cls(styles.customDrawerBody, contentClassName)}>{children}</div>
        </Drawer>
    );
};

export default CustomDrawer;
