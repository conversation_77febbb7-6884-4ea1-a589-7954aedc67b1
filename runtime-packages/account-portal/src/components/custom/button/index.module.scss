@use "@/assets/styles/variables.module.scss" as *;

.round {
    padding: 10px 16px;
    border-radius: 60px;
}

.plain {
	background: #fff;
    color: $fp-accent-color;
    border: 1px solid $fp-accent-color;
}

.cp-btn-primary2 {
    color: #fff;
    background: $fp-primary-color;
    box-shadow: none;

    & > svg {
        font-size: 20px;
        margin-right: 4px;
    }

    &:not(:disabled):not(.ant-btn-disabled):hover {
        background: #83d6d4;
    }
    &:not(:disabled):not(.ant-btn-disabled):active {
        background: #50a3a1;
    }
}

.cp-link-btn {
    font-size: 14px;
    line-height: 20px;
    padding: 0;
    border-radius: 0;
    color: $fp-secondary-color;
    &:not(:disabled):not(.ant-btn-disabled):hover {
        color: #5082ef;
		text-decoration: underline;
    }
    &:not(:disabled):not(.ant-btn-disabled):active {
        color: #1945a4;
		text-decoration: underline;
    }
}