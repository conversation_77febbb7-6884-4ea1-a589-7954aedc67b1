import cls from "classnames";
import { But<PERSON> } from "antd";
import type { ButtonProps } from "antd";
import styles from "./index.module.scss";

interface CustomButtonProps extends ButtonProps {
    plain?: boolean;
    round?: boolean;
    customPrimary?: boolean;
}

const CustomButton: React.FC<CustomButtonProps> = ({ round, plain, customPrimary, ...antdBtnProps }) => {
    let btnClassName;
    switch (antdBtnProps.type) {
        case "primary":
            btnClassName = customPrimary ? styles.cpBtnPrimary2 : undefined;
            break;
        case "link":
            btnClassName = styles.cpLinkBtn;
            break;
    }
    return (
        <Button
            {...antdBtnProps}
            className={cls(antdBtnProps.className, { [styles.round]: round, [styles.plain]: plain }, btnClassName)}
        />
    );
};

export default CustomButton;
