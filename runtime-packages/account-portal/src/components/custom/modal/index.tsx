import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import cls from "classnames";
import { Modal } from "antd";
import type { ModalProps } from "antd";
import CustomButton from "../button";
import styles from "./index.module.scss";

interface CustomModalProps extends ModalProps {
    extraHeight?: number;
    bodyScroll?: boolean;
    onOk?: (e: React.MouseEvent<HTMLButtonElement>) => void | Promise<void>;
}

const CloseIcon: React.FC<{ className?: string; style?: React.CSSProperties }> = ({ className, style }) => (
    <svg
        className={className}
        style={style}
        width="1em"
        height="1em"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <circle cx="16" cy="16" r="16" fill="none" fillOpacity="0.4" />
        <path
            d="M21.6004 10.3984L10.4004 21.5984M21.6004 21.5984L10.4004 10.3984"
            stroke="currentColor"
            strokeWidth="1.6"
            strokeLinecap="round"
        />
    </svg>
);

const WarnIcon: React.FC<{ className?: string; style?: React.CSSProperties }> = ({ className, style }) => (
    <svg
        className={className}
        style={style}
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M12 12V7.5M12 15.3354V15.375M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
            stroke={styles.fpWarningColor}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

const CustomModal: React.FC<CustomModalProps> = ({ title, children, extraHeight, bodyScroll, ...antdProps }) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(false);

    const onConfirm = (e: React.MouseEvent<HTMLButtonElement>) => {
        if (typeof antdProps.onOk === "function") {
            const result = antdProps.onOk(e);
            if (result instanceof Promise) {
                setLoading(true);
                result.finally(() => {
                    setLoading(false);
                });
            }
        }
    };

    return (
        <Modal
            closeIcon={<CloseIcon style={{ fontSize: 32 }} />}
            {...antdProps}
            className={cls(styles.cpCustomModal, antdProps.className)}
            centered
            title={title}
            footer={
                antdProps.footer !== undefined ? (
                    antdProps.footer
                ) : (
                    <>
                        <CustomButton round disabled={loading} onClick={antdProps.onCancel}>
                            {antdProps.cancelText || t("common.cancel")}
                        </CustomButton>
                        <CustomButton round loading={loading} type="primary" onClick={onConfirm}>
                            {antdProps.okText || t("common.confirm")}
                        </CustomButton>
                    </>
                )
            }
        >
            {bodyScroll === false ? (
                <div className={styles.cpCustomModalBody}>{children}</div>
            ) : (
                <div
                    className={`${styles.cpCustomModalBody} overflow-y-auto`}
                    style={{ maxHeight: `calc(80vh - ${224 + (extraHeight || 0)}px)` }}
                >
                    {children}
                </div>
            )}
        </Modal>
    );
};

interface CustomModalApi {
    confirm: (options: {
        type?: "warn";
        width?: number | string;
        title?: React.ReactNode;
        /** 自定义内容，自动水平垂直居中，适合纯文本 */
        content?: React.ReactNode;
        /** 自定义内容，优先级比content高 */
        children?: React.ReactNode;
        contentClassName?: string;
        maskClosable?: boolean;
        hideCancel?: boolean;
        okText?: React.ReactNode;
        cancelText?: React.ReactNode;
        onCancel?: () => void;
        onOk?: () => any | Promise<any>;
        footer?: React.ReactNode;
    }) => void;
}

export function useModal(): [CustomModalApi, React.ReactNode] {
    const { t } = useTranslation();
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [context, setContext] = useState<Parameters<CustomModalApi["confirm"]>[0]>({});

    useEffect(() => {
        if (!open) {
            if (context.onCancel || context.onOk) {
                setContext(pre => {
                    return {
                        ...pre,
                        onCancel: undefined,
                        onOk: undefined
                    };
                });
            }
        }
    }, [open]);

    const confirm = (options: typeof context) => {
        setContext(options);
        setOpen(true);
    };

    const onCancel = () => {
        if (typeof context.onCancel === "function") {
            context.onCancel();
        }
        setOpen(false);
    };

    const onConfirm = () => {
        if (typeof context.onOk === "function") {
            const result = context.onOk();
            if (result instanceof Promise) {
                setLoading(true);
                result
                    .then(() => {
                        onCancel();
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            } else {
                onCancel();
            }
        }
    };

    return [
        {
            confirm
        },
        <CustomModal
            width={context.width}
            open={open}
            onCancel={onCancel}
            title={context.title}
            maskClosable={context.maskClosable ?? false}
            footer={
                context.footer || (
                    <>
                        {context.hideCancel !== true && (
                            <CustomButton round disabled={loading} onClick={onCancel}>
                                {context.cancelText || t("common.cancel")}
                            </CustomButton>
                        )}
                        <CustomButton round loading={loading} type="primary" onClick={onConfirm}>
                            {context.okText || t("common.confirm")}
                        </CustomButton>
                    </>
                )
            }
        >
            {context.children || (
                <div className={cls("flex justify-center items-center", context.contentClassName)}>
                    {context.type === "warn" && <WarnIcon style={{ fontSize: 24, marginRight: 8 }} />} {context.content}
                </div>
            )}
        </CustomModal>
    ];
}

export default CustomModal;
