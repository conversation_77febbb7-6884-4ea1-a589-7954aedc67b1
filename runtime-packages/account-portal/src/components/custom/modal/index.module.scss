@use "@/assets/styles/variables.module.scss" as *;

.cp-custom-modal {
	&-body {
		padding: 0 40px;
	}
    :global {
        .ant-modal-header {
            padding: 40px;
            margin: 0;
            border-radius: 24px 24px 0 0;
            background: linear-gradient(
                180deg,
                rgba(101, 198, 195, 0.4) 4.08%,
                rgba(208, 240, 239, 0.4) 48.3%,
                rgba(255, 255, 255, 0.4) 100%
            );
        }
        .ant-modal-close {
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            font-weight: 600;
            color: $fp-text2-color;
        }
        .ant-modal-title {
            font-size: 20px;
            line-height: 24px;
            font-weight: 900;
            text-align: center;
            color: $fp-text-color;
        }
        .ant-modal-content {
            padding: 0;
            border-radius: 24px;
        }
        .ant-modal-body {
			line-height: 20px;
			font-weight: 400;
			color: $fp-text-color;
        }
        .ant-modal-footer {
            margin: 0;
            padding: 40px;
            display: flex;
			align-items: center;
			justify-content: center;
			text-align: center;
			gap: 16px;
            > .ant-btn {
                margin: 0;
            }
        }
    }
}
