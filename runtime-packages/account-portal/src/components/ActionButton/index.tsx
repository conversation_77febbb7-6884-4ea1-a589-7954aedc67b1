import { Button } from "antd";
import { THEME_CONSTANTS } from "../common";

const ActionButton = ({
    onClick,
    loading,
    label,
    type = "primary"
}: {
    onClick: () => void;
    loading?: boolean;
    label: string;
    type?: "primary" | "default";
}) => {

    const className = type === "primary" ? "w-[88px] h-[40px] rounded-[40px] bg-[#FF4D4F] text-white border-none hover:opacity-80 hover:!bg-[#FF7875] hover:!text-white transition-all" : "w-[88px] h-[40px] rounded-[40px] border border-[#B2B5BF] hover:border-black hover:text-black hover:bg-[#F5F5F5] transition-all";

    const style = type === "primary" ? {
        background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR
    } : {};

    return (
        <Button
            loading={loading}
            className={className}
            style={style}
            onClick={() => onClick()}
        >
            {label}
        </Button>
    );
};

export default ActionButton;
