import { useEffect, useReducer, useState } from "react";
import { useNavigate, useLocation, Navigate } from "react-router-dom";
import { request, GetUserInfo, GetUserCompany, UpdateUserInfo } from "@fundpark/fp-api";
import { ErrorCode } from "@fundpark/fp-api/error-code";
import { useCommonStore } from "@/store/common.ts";
import { MissingInfoModal } from "@/components/myProfile/MissingInfoModal";
import type { UpdateUserInfoReq } from "@fundpark/fp-api/types/user.ts";
import { KYCStatus } from "@/types/company/enum";
import appHelper from "@/utils/appHelper";

interface AuthInfo {
    loading: boolean;
    isAuth: boolean;
    error: boolean;
}

function authReducer(state: AuthInfo, payload: Partial<AuthInfo>): AuthInfo {
    return {
        ...state,
        ...payload
    };
}

const Auth: React.FC<{
	children?: React.ReactNode;
	/** 登录态有效下初始化完成 */
	onAliveReady?: () => void;
}> = props => {
    const { children, onAliveReady } = props;
    const navigate = useNavigate();
    const location = useLocation();
    const [authInfo, setAuthInfo] = useReducer(authReducer, {
        loading: true,
        isAuth: false,
        error: false
    });
    const [showMissingInfoModal, setShowMissingInfoModal] = useState(false);
    const [missingEmail, setMissingEmail] = useState(false);
    const [missingMobile, setMissingMobile] = useState(false);

    useEffect(() => {
		appHelper.showLoading();
		
        let expireToLogin = false;
        const unsubscribe = request.onResponse(res => {
            if (res.code === ErrorCode.$401 && !expireToLogin) {
                expireToLogin = true;
                navigate("/login", { replace: true });
            }
        });

        const { accessInfo } = useCommonStore.getState();
        if (!accessInfo || !accessInfo.accessToken) {
            setAuthInfo({ loading: false, isAuth: false });
            navigate("/login", { replace: true });
            return;
        }

        let needClearHistoryState = false;
        // Check for missing info
        if (location.state?.alertMissingMobile) {
            needClearHistoryState = true;
            setMissingMobile(true);
            setShowMissingInfoModal(true);
        }

        if (location.state?.alertMissingEmail) {
            needClearHistoryState = true;
            setMissingEmail(true);
            setShowMissingInfoModal(true);
        }

        if (location.pathname === "/") {
            needClearHistoryState = false;
            navigate("/home", { replace: true });
        }

        if (needClearHistoryState) {
            navigate(location.pathname + location.search, { replace: true });
        }

        const initDefaultCompany = async () => {
            const store = useCommonStore.getState();
            const currentCompanyId = store.initCurrentCompanyId();

            const res = await GetUserCompany({ userId: store.userInfo.userId });
            if (res.success) {
                const list = (res.data || []).filter(item => item.kycStatus === KYCStatus.Approved);
                // 如果当前company查找为空，取默认公司
                const company =
                    (currentCompanyId ? list.find(item => item.companyId === currentCompanyId) : null) ||
                    list.find(item => item.isDefault);
                store.setCurrentCompany(company || null);
            }
        };

        GetUserInfo()
            .then(async res => {
                if (res.success) {
                    const store = useCommonStore.getState();
                    store.update({
                        userInfo: res.data
                    });

                    await initDefaultCompany();

					onAliveReady?.();

                    setAuthInfo({ loading: false, isAuth: true });
                } else {
                    setAuthInfo({ loading: false, isAuth: false, error: true });
                }
            })
            .catch(err => {
                console.error("err", err);
                setAuthInfo({ loading: false, isAuth: false, error: true });
            }).finally(() => {
				appHelper.hideLoading();
			});

        return () => {
            unsubscribe();
        };
    }, []);

	useEffect(() => {
		document.documentElement.scrollTop = 0;
	}, [location.pathname]);

    const handleMissingInfoSubmit = async (values: UpdateUserInfoReq) => {
        try {
            const { userInfo } = useCommonStore.getState();
            const type: ("email" | "mobile")[] = [];
            const updateData: UpdateUserInfoReq = {
                id: userInfo.id,
                type: type
            };

            if (missingEmail && values.email) {
                updateData.email = values.email;
                updateData.emailCode = values.emailCode;
                type.push("email");
            }

            if (missingMobile && values.mobilePhoneNumber) {
                updateData.mobilePhoneNumber = values.mobilePhoneNumber;
                updateData.mobilePhoneAreaCode = values.mobilePhoneAreaCode;
                updateData.phoneCode = values.phoneCode;
                type.push("mobile");
            }

            const res = await UpdateUserInfo(updateData);
            if (res.success) {
                // Update the store with new user info
                useCommonStore.getState().update({
                    userInfo: {
                        ...userInfo,
                        ...(missingEmail ? { email: values.email } : {}),
                        ...(missingMobile
                            ? {
                                  mobilePhoneNumber: values.mobilePhoneNumber,
                                  mobilePhoneAreaCode: values.mobilePhoneAreaCode
                              }
                            : {})
                    }
                });
                setShowMissingInfoModal(false);
				return true;
            }
        } catch (error) {
            console.error("Update user info error:", error);
        }
		return false;
    };

    if (authInfo.loading) {
        return null;
    }

    if (authInfo.error || !authInfo.isAuth) {
        return <Navigate to="/login" />;
    }

    return (
        <>
            {children}
            <MissingInfoModal
                open={showMissingInfoModal}
                onClose={() => setShowMissingInfoModal(false)}
                onSubmit={handleMissingInfoSubmit}
                missingEmail={missingEmail}
                missingMobile={missingMobile}
            />
        </>
    );
};

export default Auth;
