.carousel {
    border-radius: 16px;
    overflow: hidden;
    will-change: width, height;

    :global {
        .slick-dots-bottom {
            bottom: 16px;
        }

        .slick-dots {
            display: flex !important;
            justify-content: center;
            width: fit-content !important;
            margin: 0 auto;
            padding: 4px !important;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 40px;

            li {
                height: 12px !important;
                margin-right: 4px !important;
                transition: all 0.3s ease;

                button {
                    width: 8px !important;
                    height: 8px !important;
                    border-radius: 40px !important;
                    background: rgba(255, 255, 255, 0.5) !important;
                    opacity: 1;
                }

                &.slick-active {
                    margin-right: 20px !important;

                    button {
                        width: 36px !important;
                        height: 8px !important;
                        border-radius: 40px !important;
                        background: white !important;
                    }
                }
            }
        }

        .slick-slide, .slick-slide > div {
            height: 100%;
            will-change: transform;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        .slick-track {
            display: flex;
            align-items: center;
            will-change: transform;
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }
    }
}

.carouselSlide {
    height: 100%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding: 0 48px;
    color: #fff;
    text-align: center;
    will-change: transform, width, height;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.imageContainer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    will-change: width, height;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.carouselImage {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
    will-change: width, height, transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}
