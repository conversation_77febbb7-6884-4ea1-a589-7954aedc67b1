import React, { useEffect, useState } from "react";
import { Carousel } from "antd";
import styles from "./index.module.scss";
import { THEME_CONSTANTS } from "../common";
import { MarketingCarouselProps } from "@/types/components/MarketingCarousel/carousel.types";

export const MarketingCarousel: React.FC<MarketingCarouselProps> = ({ items }) => {
    const [key, setKey] = useState(0);
    const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 0);

    // Helper function to get image style based on window width
    const getImageStyle = () => {
        if (windowWidth >= 1300) {
            return {
                transform: 'translateZ(0)',
                // width: '540px',
                maxWidth: '540px',
                // minWidth: '400px',
                flexGrow: 0,
                flexShrink: 0,
            };
        } else {
            return {
                transform: 'translateZ(0)',
                maxWidth: '100%',
                // minWidth: '400px',
            };
        }
    };
    
    // Helper function to get container style based on window width
    const getContainerStyle = () => {
        if (windowWidth >= 1300) {
            return {
                // width: '540px',
                minWidth: '400px',
                maxWidth: '540px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
            };
        } else {
            return {
                minWidth: '400px',
            };
        }
    };
    
    // Helper function to render the image based on its type
    const renderImage = (image: any, alt: string) => {
        if (React.isValidElement(image)) {
            return image;
        }
        
        if (typeof image === 'object' && image.default) {
            return (
                <div className={styles.imageContainer} style={getContainerStyle()}>
                    <img 
                        src={image.default} 
                        alt={alt} 
                        className={styles.carouselImage}
                        loading="eager"
                        style={getImageStyle()}
                    />
                </div>
            );
        }
        
        return (
            <div className={styles.imageContainer} style={getContainerStyle()}>
                <img 
                    src={image} 
                    alt={alt} 
                    className={styles.carouselImage}
                    loading="eager"
                    style={getImageStyle()}
                />
            </div>
        );
    };

    // Force re-render on window resize
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth);
            setKey(prevKey => prevKey + 1);
        };

        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    return (
        <div
            key={key}
            className="flex flex-col relative overflow-hidden rounded-[16px] lg:rounded-[32px] h-full w-full"
            style={{ 
                background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR
            }}
        >
            <Carousel
                autoplay
                className={styles.carousel}
                dots={{ className: styles.carouselDots }}
            >
                {items.map((item, index) => (
                    <div key={index} className={styles.carouselSlide}>
                        <div className="p-6 lg:p-8 w-full h-full flex items-center justify-center">
                            {renderImage(item.image, item.title)}
                        </div>
                    </div>
                ))}
            </Carousel>
        </div>
    );
};
