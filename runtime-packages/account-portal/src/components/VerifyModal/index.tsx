import { useState } from "react";
import { Button, Password } from "../shared";
import { Modal } from "@/components/common/Modal";
import { Login } from "@fundpark/fp-api";
import { useCommonStore } from "@/store/common";

const VerifyModal = (props: VerifyModalProps) => {
    const { isModalOpen, onClose, handleSuccess } = props;
    const [isLoading, setIsLoading] = useState(false);

    const userInfo = useCommonStore(state => state.userInfo);
    const [warningText, setWarningText] = useState(false);
    const email = userInfo?.email;

    const [password, setPassword] = useState("");

    const handlePasswordChange = e => {
        setPassword(e.target.value);
        // Do something with the password value in the parent component
    };

    const verifyByPassword = async () => {
        // console.log("userInfo",userInfo)
        setIsLoading(true); // Disable the button

        let apiData = {
            email: email,
            password: password
        };
        // console.log(apiData, "apidata");
        // console.log(userInfo);

        const response = await Login(apiData);
        if (response.code == 0) {
            handleSuccess();
        } else {
            // console.log(response)
            setWarningText(true);
            setIsLoading(false)
        }

        //post to login
    };

    return (
        <div>
            <Modal open={isModalOpen} onClose={onClose}>
                <div>
                    <div style={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
                        {" "}
                        <span style={{ fontSize: "24px" }}>验证密码</span>
                        {warningText && <span style={{ color: "red" }}>抱歉，您输入的密码不正确</span>}
                    </div>
                    <div style={{ marginBottom: "10px" }}>密码</div>
                    <Password style={{ marginBottom: "50px" }} onChange={handlePasswordChange}></Password>

                    <Button label={<span>验证</span>} onClick={verifyByPassword} disabled={isLoading}></Button>
                </div>
            </Modal>
        </div>
    );
};

export default VerifyModal;
