import React from 'react';
import { ApplicationRecordRowProps } from './types';
import styles from './index.module.scss';
import classNames from 'classnames';

const ApplicationRecordRow: React.FC<ApplicationRecordRowProps> = ({
  applicationRecord,
  onViewDetails
}) => {
  const {
    approvalId,
    approvalName,
    productName,
    status,
    createdTime
  } = applicationRecord;

  const getStatusClassName = () => {
    switch (status) {
      case 'In Progress':
        return styles.inProgress;
      case 'Approved':
        return styles.approved;
      case 'Rejected':
        return styles.rejected;
      case 'Withdraw':
        return styles.withdraw;
      default:
        return '';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <tr className={styles.applicationRecordRow}>
      <td>{approvalId}</td>
      <td>{approvalName}</td>
      <td>{productName}</td>
      <td>
        <div className={classNames(styles.statusBadge, getStatusClassName())}>{status}</div>
      </td>
      <td>{formatDate(createdTime)}</td>
      <td>
        <a
          href="#"
          className={styles.viewDetailsLink}
          onClick={(e) => {
            e.preventDefault();
            onViewDetails(approvalId);
          }}
        >
          Detail
        </a>
      </td>
    </tr>
  );
};

export default ApplicationRecordRow; 