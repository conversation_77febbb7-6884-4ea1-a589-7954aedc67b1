import React from 'react';
import { ApplicationRecordDetailProps } from './types';
import { CustomButton } from '@/components/custom';
import classNames from 'classnames';
import styles from './index.module.scss';

const ApplicationRecordDetail: React.FC<ApplicationRecordDetailProps> = ({
  applicationId,
  onBack,
  isLoading,
  applicationDetail
}) => {
  const getStatusClassName = (status?: string) => {
    if (!status) return '';
    
    switch (status) {
      case 'In Progress':
        return styles.inProgress;
      case 'Approved':
        return styles.approved;
      case 'Rejected':
        return styles.rejected;
      case 'Withdraw':
        return styles.withdraw;
      default:
        return '';
    }
  };

  const formatCurrency = (amount?: number) => {
    if (amount === undefined) return '-';
    
    return `USD ${amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className={styles.detailCard} style={{ textAlign: 'center', padding: '100px 0' }}>
        Loading application details...
      </div>
    );
  }

  if (!applicationDetail) {
    return (
      <div>
        <div className={styles.detailHeader}>
          <h2 className={styles.detailTitle}>Application Record Details</h2>
          <CustomButton onClick={onBack}>Back to List</CustomButton>
        </div>
        <div className={styles.detailCard} style={{ textAlign: 'center', padding: '50px 0' }}>
          No details found for application ID: {applicationId}
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className={styles.detailHeader}>
        <h2 className={styles.detailTitle}>Application Record Details</h2>
        <CustomButton onClick={onBack}>Back to List</CustomButton>
      </div>

      {/* Basic Information */}
      <div className={styles.detailCard}>
        <div className={styles.detailSection}>
          <h3 className={styles.detailSectionTitle}>Basic Information</h3>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Application ID</div>
            <div className={styles.detailValue}>{applicationDetail.applicationId}</div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Type</div>
            <div className={styles.detailValue}>{applicationDetail.applicationType}</div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Status</div>
            <div className={styles.detailValue}>
              <div className={classNames(styles.statusBadge, getStatusClassName(applicationDetail.status))}>
                {applicationDetail.status}
              </div>
            </div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Applicant</div>
            <div className={styles.detailValue}>{applicationDetail.applicantName}</div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Amount</div>
            <div className={styles.detailValue}>{formatCurrency(applicationDetail.amount)}</div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Submitted Date</div>
            <div className={styles.detailValue}>{formatDate(applicationDetail.submittedDate)}</div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Last Updated</div>
            <div className={styles.detailValue}>{formatDate(applicationDetail.lastUpdatedDate)}</div>
          </div>
          <div className={styles.detailRow}>
            <div className={styles.detailLabel}>Description</div>
            <div className={styles.detailValue}>{applicationDetail.description || '-'}</div>
          </div>
        </div>
      </div>

      {/* Documents */}
      <div className={styles.detailCard}>
        <div className={styles.detailSection}>
          <h3 className={styles.detailSectionTitle}>Documents</h3>
          {applicationDetail.documents.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              No documents available
            </div>
          ) : (
            applicationDetail.documents.map(document => (
              <div key={document.id} className={styles.documentItem}>
                <div className={styles.documentIcon}>
                  📄
                </div>
                <div className={styles.documentInfo}>
                  <div className={styles.documentName}>{document.name}</div>
                  <div className={styles.documentMeta}>
                    {document.type} • Uploaded on {formatDate(document.uploadedDate)}
                  </div>
                </div>
                <a 
                  href={document.downloadUrl} 
                  className={styles.documentAction}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Download
                </a>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Timeline */}
      <div className={styles.detailCard}>
        <div className={styles.detailSection}>
          <h3 className={styles.detailSectionTitle}>Timeline</h3>
          {applicationDetail.timeline.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              No timeline events available
            </div>
          ) : (
            applicationDetail.timeline.map(event => (
              <div key={event.id} className={styles.documentItem}>
                <div className={styles.documentInfo}>
                  <div className={styles.documentName}>{event.action}</div>
                  <div className={styles.documentMeta}>
                    {formatDate(event.timestamp)} • {event.actor}
                  </div>
                  {event.details && (
                    <div style={{ marginTop: '8px' }}>{event.details}</div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Comments */}
      <div className={styles.detailCard}>
        <div className={styles.detailSection}>
          <h3 className={styles.detailSectionTitle}>Comments</h3>
          {applicationDetail.comments.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              No comments available
            </div>
          ) : (
            applicationDetail.comments.map(comment => (
              <div key={comment.id} className={styles.documentItem}>
                <div className={styles.documentInfo}>
                  <div className={styles.documentName}>{comment.author}</div>
                  <div className={styles.documentMeta}>
                    {formatDate(comment.timestamp)}
                  </div>
                  <div style={{ marginTop: '8px' }}>{comment.content}</div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ApplicationRecordDetail; 