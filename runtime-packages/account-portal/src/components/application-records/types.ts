export type ApplicationStatus = 'In Progress' | 'Approved' | 'Rejected' | 'Withdraw';

export interface ApplicationRecord {
  id: string;
  approvalId: string;
  approvalName: string;
  productName: string;
  status: ApplicationStatus;
  createdTime: string;
}

export interface ApplicationRecordsTableProps {
  applicationRecords: ApplicationRecord[];
  isLoading: boolean;
  onViewDetails: (applicationId: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  rowsPerPage: number;
  onRowsPerPageChange: (rows: number) => void;
}

export interface ApplicationRecordRowProps {
  applicationRecord: ApplicationRecord;
  onViewDetails: (applicationId: string) => void;
}

export interface ApplicationSearchFormProps {
  onSearch: (values: ApplicationSearchParams) => void;
  onReset: () => void;
  isLoading: boolean;
}

export interface ApplicationSearchParams {
  applicationId?: string;
  applicationType?: string;
  status?: ApplicationStatus;
  applicantName?: string;
  submittedDateRange?: [string, string];
}

export interface ApplicationRecordDetailProps {
  applicationId: string;
  onBack: () => void;
  isLoading: boolean;
  applicationDetail?: ApplicationRecordDetail;
}

export interface ApplicationRecordDetail extends ApplicationRecord {
  description: string;
  documents: ApplicationDocument[];
  timeline: ApplicationTimeline[];
  comments: ApplicationComment[];
  applicationId: string;
  applicationType: string;
  applicantName: string;
  amount: number;
  submittedDate: string;
  lastUpdatedDate: string;
}

export interface ApplicationDocument {
  id: string;
  name: string;
  type: string;
  uploadedDate: string;
  downloadUrl: string;
}

export interface ApplicationTimeline {
  id: string;
  action: string;
  timestamp: string;
  actor: string;
  details?: string;
}

export interface ApplicationComment {
  id: string;
  author: string;
  content: string;
  timestamp: string;
}

// Types for new components
export interface RevenueFacilityInfoProps {
  facilityLimit?: number;
  watermark?: number;
  outstanding?: number;
  pendingDrawdown?: number;
  frozenLimit?: number;
  availableFunding?: number;
  productCode?: string;
  productType?: string;
  facilityId?: string;
  effectiveDate?: string;
  nextReviewDate?: string;
  currency?: string;
  isLoading?: boolean;
  status?: string;
}

export interface LoanDetailsProps {
  loanDetailsList?: LoanDetailItem[];
  isLoading?: boolean;
}

export interface LoanDetailItem {
  tabKey: string;
  tabLabel: string;
  loanStructureId?: string;
  loanType?: string;
  maximumOutstanding?: number;
  maximumTenor?: string;
  minimumInterestTenor?: string;
  gracePeriod?: string;
  repaymentMethod?: string;
  repaymentAllocationRule?: string;
  minimumRepaymentAmount?: string;
  overdueInterestEnabled?: boolean;
  overdueInterestMultiplier?: string;
  interestPaymentTiming?: string;
  interestRateInfo?: InterestRateInfo[];
  currency?: string;
}

export interface InterestRateInfo {
  interestRateType: 'Fixed' | 'Floating' | 'Drawdown Floating';
  currency: string;
  benchmark?: string;
  interestRate: string;
  accrualBasis: string;
} 