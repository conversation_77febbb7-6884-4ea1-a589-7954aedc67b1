// Table styles
.tableContainer {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.applicationRecordsTable {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
  }
  
  th {
    background-color: #fafafa;
    font-weight: 500;
    color: #333;
  }
  
  tr:hover {
    background-color: #fafafa;
  }
}

.applicationRecordRow {
  transition: background-color 0.2s;
}

// Status badges
.statusBadge {
  position: absolute;
  top: 0;
  left: 0;
  padding: 6px 16px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  border-radius: 0 100px 100px 0;
  background: #d3e0fb;
  color: #1250C4;
}

.pending {
  background-color: #fffbe6;
  color: #d48806;
}

.approved {
  background-color: #f6ffed;
  color: #52c41a;
}

.rejected {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.inProgress {
  background-color: #e6f7ff;
  color: #1890ff;
}

.completed {
  background-color: #f0f5ff;
  color: #2f54eb;
}

// Pagination
.paginationWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pageInfo {
  color: #666;
}

// Filter styles - similar to ShopList.tsx
.filterWrapper {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.filterForm {
  padding: 24px 24px 12px;
}

.filterFormContent {
  margin-bottom: 12px;
}

.filterRow {
  width: 100%;
}

.filterLabel {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
}

.filterActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.expandButton {
  color: #1890ff;
}

.actionButtons {
  display: flex;
  gap: 8px;
}

.resetButton {
  margin-right: 8px;
}

// Detail view
.detailHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.detailTitle {
  font-size: 20px;
  font-weight: 500;
}

.detailCard {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.detailSection {
  margin-bottom: 32px;
}

.detailSectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.detailRow {
  display: flex;
  border-bottom: 1px solid #DFE1E5;
  padding: 16px 0;
  flex-wrap: wrap;
  gap: 90px;
}

.detailLabel {
  width: 180px;
  color: #666;
}

.detailValue {
  flex: 1;
  font-weight: 500;
}

// Action buttons
.viewDetailsLink {
  color: #1890ff;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

// Documents section
.documentItem {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.documentIcon {
  margin-right: 12px;
}

.documentInfo {
  flex: 1;
}

.documentName {
  font-weight: 500;
  margin-bottom: 4px;
}

.documentMeta {
  font-size: 12px;
  color: #666;
}

.documentAction {
  margin-left: 12px;
}

// RevenueFacilityInfo and LoanDetails styles
.facilityCard {
  margin-bottom: 24px;
  position: relative;
  // padding: 16px;
  width: 380px;
  border-radius: 20px;
  background-color: #F6F7FA;
  overflow: hidden;
}

.loanDetailsCard {
  margin-bottom: 24px;
  border-radius: 20px;
  background-color: #FFFFFF;
  width: 835px;
  border: 1px solid #DDDFE6;
}

.facilityHeader {
  margin-bottom: 20px;
  margin-top: 35px;
  text-align: center;
  
  span {
    font-family: 'Poppins', sans-serif;
    font-size: 18px;
    font-weight: 800;
    margin-bottom: 4px;
  }
}

.facilityTitle {
  font-size: 18px !important;
  font-weight: 800 !important;
  margin-bottom: 4px;
}

.currencyLabel {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 400;
  position: absolute;
  top: 16px;
  right: 16px;
}

.availableLimitSection {
  margin-bottom: 12px;
  text-align: center;
  padding: 20px 0;
}

.progressContainer {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  max-width: 280px;
  position: relative;
  
  :global {
    .ant-progress-text {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .ant-progress-inner {
      background-color: transparent;
    }
    
    .ant-progress-circle-trail {
      stroke-width: 6px;
    }
    
    .ant-progress-circle-path {
      stroke-width: 6px;
    }
  }
}

.progressContent {
  position: absolute;
  width: 100%;
  text-align: center;
  top: 90%;
  left: 50%;
  transform: translate(-50%, -70%);
}

.availableLimitValue {
  font-size: 20px;
  font-weight: 900;
  font-family: 'Poppins', sans-serif;
  color: #000000;
  line-height: 26px;
  margin-bottom: 3px;
}

.availableLimitLabel {
  font-size: 16px;
  font-weight: 400;
  color: #282830;
  line-height: 22px;
}

.detailItem {
  margin-bottom: 12px;
}

.detailLabel {
  font-size: 14px;
  color: #6E6E75;
  margin-bottom: 4px;
  font-weight: 400;
}

.detailValue {
  font-size: 16px;
  font-weight: 500;
}

.productInfoSection {
  margin-top: 40px;

  .productInfoTitle {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
}

.footnote {
  margin-top: 16px;
  font-size: 12px;
  color: #8c8c8c;
  // font-style: italic;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80px 0;
}

// Loan Details styles
.loanTabs {
  :global {
    .ant-tabs-nav {
      margin-bottom: 8px !important;
    }
    
    .ant-tabs-nav-list {
      background: #EFF0F6;
      border-radius: 10px;
      padding: 6px;
      width: 272px;
      display: flex;
      justify-content: space-between;
    }
    
    .ant-tabs-tab {
      padding: 8px 16px;
      border-radius: 10px !important;
      margin: 0;
      transition: all 0.3s;
      border: none;
      background: #EFF0F6 !important;
      color: #282830;
      font-weight: 600;
      display: flex;
      justify-content: center;
      width: 50%;
      text-align: center;

      &.ant-tabs-tab-active {
        font-weight: 600;
        background: #FFFFFF !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        color: #282830;
        display: flex;
        justify-content: center;
        width: 50%;
        text-align: center;
      }
    }
    
    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active, 
    .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab-active {
      color: #000000;
      background: #FFFFFF;
    }
    
    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab, 
    .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
      border: none;
    }
    
    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab, 
    .ant-tabs-card.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab, 
    .ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab, 
    .ant-tabs-card.ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
      margin-left: 4px;
    }
    
    .ant-tabs-nav::before {
      display: none !important;
    }
  }
}

.tabContent {
  padding: 8px;
}

// Interest Rate Table Styles
.interestRateTable {
  margin-top: 20px;
  
  :global {
    .ant-table-bordered .ant-table-thead > tr > th {
      font-weight: 600;
      font-size: 14px;
      color: #282830;
      padding: 16px 12px;
    }
    
    .ant-table-bordered .ant-table-tbody > tr > td {
      padding: 12px;
      border-right: 1px solid #f0f0f0;
      font-size: 14px;
      color: #282830;
      font-weight: 400;
    }
    
    .ant-table-bordered .ant-table-tbody > tr:hover > td {
      background: transparent;
    }
    
    .ant-table {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      overflow: hidden;
    }
  }
}

.interestRateSectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
}

.groupedInterestTable {
  :global {
    .ant-table-container {
      border-radius: 8px;
      overflow: hidden;
    }
    
    .ant-table-cell {
      vertical-align: middle;
    }
    
    .ant-table-thead > tr > th:first-child {
      min-width: 150px;
    }
  }
}

// New loan details table styles
.loanDetailsTable {
  width: 100%;
  // padding: 16px 24px;
  display: flex;
  flex-direction: column;
}

.detailRow {
  display: flex;
  border-bottom: 1px solid #DFE1E5;
  padding: 16px 0;
  flex-wrap: wrap;
  gap: 90px;
}

.detailCell {
  width: 140px;
  height: 44px;
  padding-right: 16px;
}

.detailLabel {
  font-size: 14px;
  color: #6E6E75;
  margin-bottom: 4px;
  font-weight: 400;
}

.detailValue {
  font-size: 14px;
  font-weight: 600;
  color: #282830;
}

.loanTabs {
  :global(.ant-tabs-nav) {
    margin-bottom: 24px;
  }
}

.tabContent {
  padding: 0;
} 