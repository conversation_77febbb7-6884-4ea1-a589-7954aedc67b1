import React, { useState } from 'react';
import { Card, Tabs, Table, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { LoanDetailsProps, LoanDetailItem, InterestRateInfo } from './types';
import styles from './index.module.scss';

const LoanDetails: React.FC<LoanDetailsProps> = ({
  loanDetailsList = [],
  isLoading = false
}) => {
  const [activeTab, setActiveTab] = useState('');
  const { t } = useTranslation();
  
  const formatCurrency = (value?: number) => {
    if (value === undefined) return '-';
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  if (isLoading) {
    return (
      <Card className={styles.loanDetailsCard} style={{ height: '873px' }}>
        <div className={styles.loadingContainer}>
          <Spin />
        </div>
      </Card>
    );
  }

  // If no loan details available
  if (loanDetailsList.length === 0) {
    return (
      <Card className={styles.loanDetailsCard} style={{ height: '873px' }}>
        <div className={styles.loadingContainer}>
          <div>{t('facility.loanDetails.noData')}</div>
        </div>
      </Card>
    );
  }

  const renderLoanDetailsTable = (loanDetail: LoanDetailItem) => {
    const detailItems = [
      { label: t('facility.loanDetails.loanStructureId'), value: loanDetail.loanStructureId || '-' },
      { label: t('facility.loanDetails.loanType'), value: loanDetail.loanType || '-' },
      { label: t('facility.loanDetails.maximumOutstanding'), value: loanDetail.maximumOutstanding ? formatCurrency(loanDetail.maximumOutstanding) : '-' },
      { label: t('facility.loanDetails.maximumTenor'), value: loanDetail.maximumTenor || '-' },
      { label: t('facility.loanDetails.minimumInterestTenor'), value: loanDetail.minimumInterestTenor || '-' },
      { label: t('facility.loanDetails.gracePeriod'), value: loanDetail.gracePeriod || '-' },
      { label: t('facility.loanDetails.repaymentMethod'), value: loanDetail.repaymentMethod || '-' },
      { label: t('facility.loanDetails.repaymentAllocationRule'), value: loanDetail.repaymentAllocationRule || '-' },
      { label: t('facility.loanDetails.minimumRepaymentAmount'), value: loanDetail.minimumRepaymentAmount || '-' },
      { label: t('facility.loanDetails.overdueInterestEnabled'), value: loanDetail.overdueInterestEnabled ? t('facility.loanDetails.yes') : t('facility.loanDetails.no') },
      { label: t('facility.loanDetails.overdueInterestMultiplier'), value: loanDetail.overdueInterestMultiplier || '-' },
      { label: t('facility.loanDetails.interestPaymentTiming'), value: loanDetail.interestPaymentTiming || '-' }
    ];

    // Create rows with 3 items per row
    const rows = [];
    for (let i = 0; i < detailItems.length; i += 3) {
      rows.push(detailItems.slice(i, i + 3));
    }

    return (
      <div className={styles.loanDetailsTable}>
        {rows.map((row, rowIndex) => (
          <div key={`row-${rowIndex}`} className={styles.detailRow}>
            {row.map((item, itemIndex) => (
              <div key={`item-${rowIndex}-${itemIndex}`} className={styles.detailCell}>
                <div className={styles.detailLabel}>{item.label}</div>
                <div className={styles.detailValue}>{item.value}</div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  };

  const renderInterestRateTable = (interestRateInfo: InterestRateInfo[] = []) => {
    if (!interestRateInfo || interestRateInfo.length === 0) {
      return null;
    }
    
    // Define the order of interest rate types
    const rateTypeOrder = {
      'Fixed': 1,
      'Floating': 2,
      'Drawdown Floating': 3
    };
    
    // Sort data by interest rate type according to the defined order
    const sortedData = [...interestRateInfo].sort((a, b) => {
      const orderA = rateTypeOrder[a.interestRateType] || 999;
      const orderB = rateTypeOrder[b.interestRateType] || 999;
      return orderA - orderB;
    });
    
    // Group data by interest rate type to calculate rowSpan
    const groupByType: Record<string, InterestRateInfo[]> = {};
    sortedData.forEach(item => {
      if (!groupByType[item.interestRateType]) {
        groupByType[item.interestRateType] = [];
      }
      groupByType[item.interestRateType].push(item);
    });
    
    // Create render columns with rowSpan logic
    const interestRateColumns = [
      {
        title: t('facility.interestRate.interestRateType'),
        dataIndex: 'interestRateType',
        key: 'interestRateType',
        width: 157.4,
        render: (text: string, record: InterestRateInfo, index: number) => {
          // Calculate rowSpan for this cell
          const currentType = record.interestRateType;
          const prevType = index > 0 ? sortedData[index - 1].interestRateType : null;
          
          // First row of this type gets rowSpan equal to the count of this type
          if (currentType !== prevType) {
            return {
              children: <span style={{ fontWeight: 600 }}>{text}</span>,
              props: {
                rowSpan: groupByType[currentType].length
              }
            };
          }
          
          // Other rows of the same type get rowSpan=0 (hidden)
          return {
            children: <span style={{ fontWeight: 600 }}>{text}</span>,
            props: {
              rowSpan: 0
            }
          };
        }
      },
      {
        title: t('facility.interestRate.drawdownCurrency'),
        dataIndex: 'currency',
        key: 'currency',
        width: 157.4,
      },
      {
        title: t('facility.interestRate.benchmarkRate'),
        dataIndex: 'benchmark',
        key: 'benchmark',
        width: 157.4,
        render: (text: string) => text || '-'
      },
      {
        title: t('facility.interestRate.interestRate'),
        dataIndex: 'interestRate',
        key: 'interestRate',
        width: 157.4,
      },
      {
        title: t('facility.interestRate.accrualBasis'),
        dataIndex: 'accrualBasis',
        key: 'accrualBasis',
        width: 157.4,
      },
    ];

    return (
      <div className={styles.interestRateTable}>
        <Table 
          dataSource={sortedData} 
          columns={interestRateColumns} 
          pagination={false}
          rowKey={(record, index) => `${record.interestRateType}-${record.currency}-${index}`}
          bordered
          className={styles.groupedInterestTable}
        />
      </div>
    );
  };

  // Initialize activeTab to the first tab's key if not set
  if (!activeTab && loanDetailsList.length > 0) {
    setActiveTab(loanDetailsList[0].tabKey);
  }

  // Generate tab items dynamically from loanDetailsList
  const items = loanDetailsList.map(loanDetail => ({
    key: loanDetail.tabKey,
    label: loanDetail.tabLabel,
    children: (
      <div className={styles.tabContent}>
        {renderLoanDetailsTable(loanDetail)}
        {renderInterestRateTable(loanDetail.interestRateInfo)}
      </div>
    ),
  }));

  return (
    <Card className={styles.loanDetailsCard} style={{ overflow: 'auto', height: '873px' }}>
      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab} 
        items={items}
        className={styles.loanTabs}
        tabBarStyle={{ marginBottom: 24 }}
        type="card"
        tabBarGutter={0}
      />
    </Card>
  );
};

export default LoanDetails; 