import React from 'react';
import { Card, Row, Col, Spin, Progress } from 'antd';
import { useTranslation } from 'react-i18next';
import { RevenueFacilityInfoProps } from './types';
import styles from './index.module.scss';

const RevenueFacilityInfo: React.FC<RevenueFacilityInfoProps> = ({
  facilityLimit,
  watermark,
  outstanding,
  pendingDrawdown,
  frozenLimit,
  availableFunding,
  productCode,
  productType,
  facilityId,
  effectiveDate,
  nextReviewDate,
  currency,
  isLoading = false,
  status = ''
}) => {
  const { t } = useTranslation();

  const formatCurrency = (value?: number) => {
    if (value === undefined) return '-';
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  if (isLoading) {
    return (
      <Card className={styles.facilityCard} style={{ height: '873px' }}>
        <div className={styles.loadingContainer}>
          <Spin />
        </div>
      </Card>
    );
  }

  // Calculate progress percentage (available funding as percentage of facility limit)
  const totalLimit = facilityLimit || 0;
  const availableLimit = availableFunding || 0;
  const progressPercentage = totalLimit > 0 
    ? Math.min(Math.round((availableLimit / totalLimit) * 100), 100)
    : 0;

  // Format the available limit for display
  const formattedAvailableLimit = formatCurrency(availableLimit);

  // Common styles
  const labelStyle = {
    fontSize: '14px',
    fontWeight: 400,
    color: '#6E6E75'
  };
  
  const valueStyle = {
    fontSize: '14px',
    fontWeight: 700
  };
  
  const rowStyle = {
    borderBottom: '1px solid #DFE1E5',
  };

  const detailItemStyle = {
    paddingTop: '12px',
    paddingBottom: '0'
  };

  // Status badge styles based on status
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'activated':
        return {
          label: t('facility.facilityInfo.statusLabels.active'),
          backgroundColor: '#40B16E',
          color: '#FFFFFF'
        };
      case 'expired':
        return {
          label: t('facility.facilityInfo.statusLabels.expired'),
          backgroundColor: '#E6FFF8',
          color: '#00A37A'
        };
      case 'closed':
        return {
          label: t('facility.facilityInfo.statusLabels.closed'),
          backgroundColor: '#F5F5F5',
          color: '#595959'
        };
      default:
        return {
          label: '',
          backgroundColor: '#E6FFF8',
          color: '#00A37A'
        };
    }
  };

  const statusConfig = getStatusConfig(status);

  return (
    <Card className={styles.facilityCard} style={{ overflow: 'auto', height: '873px' }}>
      <div className={styles.statusBadge} style={{
        backgroundColor: statusConfig.backgroundColor,
        color: statusConfig.color
      }}>
        {statusConfig.label}
      </div>
      <div className={styles.currencyLabel}>{t('facility.facilityInfo.currency')}: {currency}</div>
      
      <div className={styles.facilityHeader}>
        <span className={styles.facilityTitle}>{productType}</span>
      </div>
      
      <div className={styles.availableLimitSection}>
        <div className={styles.progressContainer} style={{ height: '125px', overflow: 'visible' }}>
          <Progress 
            type="dashboard" 
            percent={progressPercentage}
			size={250}
            strokeColor="#64CCC9"
            trailColor="#2017470A"
            strokeWidth={8}
            gapDegree={180}
            format={() => null}
            style={{ position: 'absolute' }}
          />
          <div className={styles.progressContent}>
            <div className={styles.availableLimitValue}>
              {formattedAvailableLimit}
            </div>
            <div className={styles.availableLimitLabel}>
              {t('facility.facilityInfo.availableLimit')}
            </div>
          </div>
        </div>
      </div>
      
      <div>
        {/* First row */}
        <Row style={rowStyle}>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.facilityInfo.facilityLimit')}</div>
              <div className={styles.detailValue} style={valueStyle}>{formatCurrency(facilityLimit)}</div>
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.facilityInfo.watermark')}</div>
              <div className={styles.detailValue} style={{...valueStyle, color: '#64CCC9'}}>{formatCurrency(watermark)}</div>
            </div>
          </Col>
        </Row>
        
        {/* Second row */}
        <Row style={rowStyle}>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.facilityInfo.outstanding')}</div>
              <div className={styles.detailValue} style={{...valueStyle, color: '#FF5E5E'}}>{formatCurrency(outstanding)}</div>
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.facilityInfo.pendingDrawdown')}</div>
              <div className={styles.detailValue} style={{...valueStyle, color: '#3F83F8'}}>{formatCurrency(pendingDrawdown)}</div>
            </div>
          </Col>
        </Row>
        
        {/* Third row */}
        <Row style={rowStyle}>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.facilityInfo.frozenLimit')}</div>
              <div className={styles.detailValue} style={valueStyle}>{formatCurrency(frozenLimit)}</div>
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.facilityInfo.availableFunding')}</div>
              <div className={styles.detailValue} style={valueStyle}>{formatCurrency(availableFunding)}</div>
            </div>
          </Col>
        </Row>
      </div>
      
      <div className={styles.footnote} style={{ fontSize: '14px', fontWeight: 400, color: '#9E9EA3', marginTop: '16px' }}>
        {t('facility.facilityInfo.availableBalanceNote')}
      </div>

      <div className={styles.productInfoSection}>
        <span className={styles.productInfoTitle}>{t('facility.productInfo.title')}</span>
        {/* Product info first row */}
        <Row style={rowStyle}>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.productInfo.productCode')}</div>
              <div className={styles.detailValue} style={valueStyle}>{productCode || '-'}</div>
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.productInfo.productType')}</div>
              <div className={styles.detailValue} style={valueStyle}>{productType || '-'}</div>
            </div>
          </Col>
        </Row>
        
        {/* Product info second row */}
        <Row style={rowStyle}>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.productInfo.facilityId')}</div>
              <div className={styles.detailValue} style={valueStyle}>{facilityId || '-'}</div>
            </div>
          </Col>
          <Col span={12}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.productInfo.effectiveDate')}</div>
              <div className={styles.detailValue} style={valueStyle}>{effectiveDate || '-'}</div>
            </div>
          </Col>
        </Row>
        
        {/* Product info third row */}
        <Row>
          <Col span={24}>
            <div className={styles.detailItem} style={detailItemStyle}>
              <div className={styles.detailLabel} style={labelStyle}>{t('facility.productInfo.nextReviewDate')}</div>
              <div className={styles.detailValue} style={valueStyle}>{nextReviewDate || '-'}</div>
            </div>
          </Col>
        </Row>
      </div>
      
    </Card>
  );
};

export default RevenueFacilityInfo; 