.business-info-section {
  width: 100%;
  
  .section {
    margin-bottom: 20px;
    
    .section-title-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      gap: 16px;
      
      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
      }
      @media (max-width: 768px) { // smaller than md
        display: block;
        height: auto;
      }
    }
    
    .form-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 24px;
      
      .ant-form-item {
        margin-bottom: 0;
        position: relative;
        
        /* Fix for error message positioning */
        .ant-form-item-explain, .ant-form-item-extra {
          position: absolute;
          bottom: -22px;
          left: 0;
          min-height: 20px;
          z-index: 5;
        }
        
        /* Ensure specific spacing for form labels */
        .ant-form-item-label {
          padding-bottom: 8px;
        }
      }
      
      .half-width-item {
        width: calc(50% - 8px);
        @media (max-width: 576px) { // smaller than sm
          width: 100%;
        }
      }
      
      .full-width-item {
        width: 100%;
        
        .ant-checkbox-group {
          display: flex;
          flex-wrap: wrap;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          grid-gap: 12px;
          @media (max-width: 576px) { // smaller than sm
            display: grid;
          }
        }
      }
    }
  }
} 