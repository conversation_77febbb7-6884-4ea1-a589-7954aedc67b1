import React, { useRef, useEffect } from 'react';
import { Button } from '@/components/shared/Button';
import Stepper from '@/components/shared/Stepper';
import BusinessInfoSection from './BusinessInfoSection';
import ArrowRightIcon from '@/assets-new/icons/arrow-right-sm.svg?react';
import './BusinessInfoForm.scss';
import '../styles.scss';
import { useNavigate } from 'react-router-dom';
import { Checkbox } from 'antd';
import { usePositionTracking } from '@/hooks/usePositionTracking';
import { useBusinessInfoStore } from '@/store/businessInfo';
import { POSITION_STATUS } from '@/constants/position-status';
import { useMatomoContext } from '@/contexts/MatomoContext';
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from '@/components/shared/tracking/constants';
import { MATOMO_CONFIG } from '@/utils/matomoConfig';

const BusinessInfoForm: React.FC = () => {
  const { trackPosition } = usePositionTracking();
  const { trackEvent, trackEventWithDimensions } = useMatomoContext();
  const navigate = useNavigate();
  const formRef = useRef<any>(null);

  // Get state and actions from the store
  const {
    formData,
    isLoading,
    isConfirmed,
    error,
    setFormData,
    setIsConfirmed,
    fetchBusinessInfo,
    submitBusinessInfo
  } = useBusinessInfoStore();

  // Fetch business information on component mount
  useEffect(() => {
    fetchBusinessInfo();
  }, []); // Empty dependency array - only run on mount

  // Set form values when formData changes
  useEffect(() => {
    if (formRef.current && Object.keys(formData).length > 0) {
      formRef.current.setFieldsValue(formData);
    }
  }, [formData]);

  const handleChange = (values: any) => {
    setFormData(values);
  };

  const handleCheckboxChange = (e: any) => {
    const isChecked = e.target.checked;
    setIsConfirmed(isChecked);
    
    // Track checkbox interaction
    trackEventWithDimensions({
      category: TRACKING_CATEGORIES.FORM,
      action: TRACKING_ACTIONS.SELECT,
      name: TRACKING_EVENTS.CHECK_BUSINESS_INFO_CONFIRMATION,
      customDimensions: {
        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: isChecked ? 'confirmed' : 'unconfirmed'
      }
    });
  };

  const handleNext = async () => {
    if (formRef.current) {
      try {
        // Validate form fields
        await formRef.current.validateFields();
        
        // Track submit button click
        trackEvent(
          TRACKING_CATEGORIES.BUTTON,
          TRACKING_ACTIONS.CLICK,
          TRACKING_EVENTS.CLICK_BUSINESS_INFO_SUBMIT
        );
        
        const result = await submitBusinessInfo();
        
        if (result.success) {
          await trackPosition({ path: "/credit/hook/register/signing/loading", status: POSITION_STATUS.XDJ_BUSINESS_INFO_SUBMITTED });
          navigate('/credit/hook/register/signing/loading');
        } else {
          console.error('Failed to submit business information:', result.error);
        }
      } catch (error) {
        console.error('Validation failed:', error);
      }
    }
  };

  // Define steps for the stepper
  const steps = [
    {
      title: '用款账户',
      content: (<></>),
      action: null,
    },
    {
      title: '信息确认',
      content: (
        <BusinessInfoSection
          formRef={formRef}
          initialValues={formData}
          onChange={handleChange}
        />
      ),
      action: (
        <div className="flex flex-col items-center">
          <div className="mb-6 flex justify-center">
            <Checkbox
              checked={isConfirmed}
              onChange={handleCheckboxChange}
              className="confirm-checkbox"
            >
              本公司确认以上资料信息正确
            </Checkbox>
          </div>
          <Button
            type="primary"
            onClick={handleNext}
            style={{ width: 320 }}
            loading={isLoading}
            disabled={!isConfirmed}
            label={
              <span className="flex items-center justify-center">
                确认以上信息
                <ArrowRightIcon style={{ marginLeft: '4px' }} />
              </span>
            }
          >
            确认以上信息
          </Button>
        </div>
      )
    },
    {
      title: '签约放款',
      content: (<></>),
      action: null,
    },
  ];

  return (
    <div className="business-info-form">
      <Stepper
        steps={steps}
        currentStep={1}
        borderTopRadiusRounded={false}
      />
    </div>
  );
};

export default BusinessInfoForm; 