import React, { useEffect, MutableRefObject } from 'react';
import { Form, Select, Checkbox, Input } from 'antd';
import './BusinessInfoSection.scss';
import InfoBanner from '../../shared/InfoBanner';
import { useConfig } from '@/contexts/ConfigContext';
import { ConfigType } from '@/constants/configTypes';
import { BusinessInfoFormData } from './types';
import { useMatomoContext } from '@/contexts/MatomoContext';
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from '@/components/shared/tracking/constants';
import { MATOMO_CONFIG } from '@/utils/matomoConfig';

const { Option } = Select;
const { Group: CheckboxGroup } = Checkbox;

interface BusinessInfoSectionProps {
  formRef?: MutableRefObject<any>;
  initialValues?: Partial<BusinessInfoFormData>;
  onChange?: (values: Partial<BusinessInfoFormData>) => void;
}

const BusinessInfoSection: React.FC<BusinessInfoSectionProps> = ({ 
  formRef,
  initialValues = {},
  onChange 
}) => {
  const [form] = Form.useForm<BusinessInfoFormData>();
  const { companyConfig, loading: configLoading } = useConfig();
  const { trackEventWithDimensions } = useMatomoContext();

  // Set form reference if provided
  useEffect(() => {
    if (formRef) {
      formRef.current = form;
    }
  }, [form, formRef]);

  // Update form values when initialValues changes
  useEffect(() => {
    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [initialValues, form]);

  const getCountryOptions = () => {
    if (!companyConfig || !companyConfig[ConfigType.COUNTRY_REGION]) {
      return [];
    }
    
    return companyConfig[ConfigType.COUNTRY_REGION].map((country: any) => ({
      value: country.key_id.toString(),
      label: country.name_chi
    }));
  };

  const getIndustryOptions = () => {
    if (!companyConfig || !companyConfig[ConfigType.INDUSTRY_SERVICE]) {
      return [];
    }
    
    return companyConfig[ConfigType.INDUSTRY_SERVICE].map((industry: any) => ({
      value: industry.key_id.toString(),
      label: industry.name_chi
    }));
  };

  const getInitialWealthSourceOptions = () => {
    if (!companyConfig || !companyConfig[ConfigType.INITIAL_SOURCE_OF_WEALTH]) {
      return [];
    }
    
    return companyConfig[ConfigType.INITIAL_SOURCE_OF_WEALTH].map((source: any) => ({
      value: source.key_id.toString(),
      label: source.name_chi
    }));
  };

  const getContinuousWealthSourceOptions = () => {
    if (!companyConfig || !companyConfig[ConfigType.CONTINUOUS_SOURCE_OF_WEALTH]) {
      return [];
    }
    
    return companyConfig[ConfigType.CONTINUOUS_SOURCE_OF_WEALTH].map((source: any) => ({
      value: source.key_id.toString(),
      label: source.name_chi
    }));
  };

  const getFundingSourceOptions = () => {
    if (!companyConfig || !companyConfig[ConfigType.FUNDING_SOURCE]) {
      return [];
    }
    
    return companyConfig[ConfigType.FUNDING_SOURCE].map((source: any) => ({
      value: source.key_id.toString(),
      label: source.name_chi
    }));
  };

  const handleFormChange = (_: any, allValues: BusinessInfoFormData) => {
    if (onChange) {
      onChange(allValues);
    }
  };

  return (
    <div className="business-info-section">
      <Form<BusinessInfoFormData>
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={handleFormChange}
      >
        <div className="section">
          <div className="section-title-container">
            <h4 className="section-title">业务信息</h4>
            <InfoBanner 
              type="info" 
              message="FundPark已为您预先填写以下信息，请确认无误或按需要自行修改"
              width="501px"
            />
          </div>
          
          <div className="form-row">
            <Form.Item
              name="topBuyerCountries"
              label="首三大买家/销售平台的国家"
              rules={[{ required: true, message: '请选择首三大买家/销售平台的国家' }]}
              className="half-width-item"
            >
              <Select 
                mode="multiple"
                placeholder="请选择"
                maxTagCount={3}
                showArrow
                loading={configLoading}
                optionFilterProp="label"
                maxCount={3}
                onBlur={(e) => {
                  // Get the current form values to access the selected countries
                  const currentValues = form.getFieldsValue();
                  const selectedValues = currentValues.topBuyerCountries || [];
                  
                  // Get country names from the selected values
                  const selectedCountries = selectedValues.map((selectedValue: string) => {
                    const country = getCountryOptions().find(option => option.value === selectedValue);
                    return country ? country.label : selectedValue;
                  });
                  
                  if (selectedCountries.length > 0) {
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_TOP_BUYER_COUNTRIES,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: selectedCountries.join(', ')
                      }
                    });
                  }
                }}
              >
                {getCountryOptions().map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="topSupplierCountries"
              label="首三大供应商的国家"
              rules={[{ required: true, message: '请选择首三大供应商的国家' }]}
              className="half-width-item"
            >
              <Select 
                mode="multiple"
                placeholder="请选择"
                maxTagCount={3}
                showArrow
                loading={configLoading}
                optionFilterProp="label"
                maxCount={3}
                onBlur={(e) => {
                  // Get the current form values to access the selected countries
                  const currentValues = form.getFieldsValue();
                  const selectedValues = currentValues.topSupplierCountries || [];
                  
                  // Get country names from the selected values
                  const selectedCountries = selectedValues.map((selectedValue: string) => {
                    const country = getCountryOptions().find(option => option.value === selectedValue);
                    return country ? country.label : selectedValue;
                  });
                  
                  if (selectedCountries.length > 0) {
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_TOP_SUPPLIER_COUNTRIES,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: selectedCountries.join(', ')
                      }
                    });
                  }
                }}
              >
                {getCountryOptions().map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div className="form-row">
            <Form.Item
              name="fundSourceCountries"
              label="资金来源的国家 / 地区"
              rules={[{ required: true, message: '请选择资金来源的国家/地区' }]}
              className="half-width-item"
            >
              <Select 
                placeholder="请选择"
                showArrow
                loading={configLoading}
                optionFilterProp="label"
                showSearch
                onBlur={(e) => {
                  // Get the current form values to access the selected country
                  const currentValues = form.getFieldsValue();
                  const selectedValue = currentValues.fundSourceCountries;
                  
                  if (selectedValue) {
                    // Get country name from the selected value
                    const country = getCountryOptions().find(option => option.value === selectedValue);
                    const countryName = country ? country.label : selectedValue;
                    
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_FUND_SOURCE_COUNTRIES,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: countryName
                      }
                    });
                  }
                }}
              >
                {getCountryOptions().map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="mainProducts"
              label="主要销售商品"
              rules={[{ required: true, message: '请输入主要销售商品' }]}
              className="half-width-item"
            >
              <Input 
                placeholder="请输入"
                onBlur={(e) => {
                  const value = e.target.value;
                  if (value && value.trim()) {
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.INPUT,
                      name: TRACKING_EVENTS.INPUT_MAIN_PRODUCTS,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: value.trim()
                      }
                    });
                  }
                }}
              />
            </Form.Item>
          </div>

          <div className="form-row">
            <Form.Item
              name="industry"
              label="行业"
              rules={[{ required: true, message: '请选择行业' }]}
              className="half-width-item"
            >
              <Select 
                placeholder="请选择"
                showArrow
                loading={configLoading}
                optionFilterProp="label"
                showSearch
                onBlur={(e) => {
                  // Get the current form values to access the selected industry
                  const currentValues = form.getFieldsValue();
                  const selectedValue = currentValues.industry;
                  
                  if (selectedValue) {
                    // Get industry name from the selected value
                    const industry = getIndustryOptions().find(option => option.value === selectedValue);
                    const industryName = industry ? industry.label : selectedValue;
                    
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_INDUSTRY,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: industryName
                      }
                    });
                  }
                }}
              >
                {getIndustryOptions().map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>{option.label}</Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          <div className="form-row">
            <Form.Item
              name="initialWealthSources"
              label="初始财富来源 (可多选)"
              rules={[{ required: true, message: '请选择初始财富来源' }]}
              className="full-width-item"
            >
              <CheckboxGroup 
                options={getInitialWealthSourceOptions()}
                onChange={(checkedValues) => {
                  if (checkedValues && checkedValues.length > 0) {
                    // Get source names from the selected values
                    const selectedSources = checkedValues.map((selectedValue: string) => {
                      const source = getInitialWealthSourceOptions().find(option => option.value === selectedValue);
                      return source ? source.label : selectedValue;
                    });
                    
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_INITIAL_WEALTH_SOURCES,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: selectedSources.join(', ')
                      }
                    });
                  }
                }}
              />
            </Form.Item>
          </div>

          <div className="form-row">
            <Form.Item
              name="ongoingIncomeSources"
              label="持续的财富及收入来源 (可多选)"
              rules={[{ required: true, message: '请选择持续的财富及收入来源' }]}
              className="full-width-item"
            >
              <CheckboxGroup 
                options={getContinuousWealthSourceOptions()}
                onChange={(checkedValues) => {
                  if (checkedValues && checkedValues.length > 0) {
                    // Get source names from the selected values
                    const selectedSources = checkedValues.map((selectedValue: string) => {
                      const source = getContinuousWealthSourceOptions().find(option => option.value === selectedValue);
                      return source ? source.label : selectedValue;
                    });
                    
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_ONGOING_INCOME_SOURCES,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: selectedSources.join(', ')
                      }
                    });
                  }
                }}
              />
            </Form.Item>
          </div>

          <div className="form-row">
            <Form.Item
              name="fundingSources"
              label="资金来源 (可多选)"
              rules={[{ required: true, message: '请选择资金来源' }]}
              className="full-width-item"
            >
              <CheckboxGroup 
                options={getFundingSourceOptions()}
                onChange={(checkedValues) => {
                  if (checkedValues && checkedValues.length > 0) {
                    // Get source names from the selected values
                    const selectedSources = checkedValues.map((selectedValue: string) => {
                      const source = getFundingSourceOptions().find(option => option.value === selectedValue);
                      return source ? source.label : selectedValue;
                    });
                    
                    trackEventWithDimensions({
                      category: TRACKING_CATEGORIES.FORM,
                      action: TRACKING_ACTIONS.SELECT,
                      name: TRACKING_EVENTS.SELECT_FUNDING_SOURCES,
                      customDimensions: {
                        [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: selectedSources.join(', ')
                      }
                    });
                  }
                }}
              />
            </Form.Item>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default BusinessInfoSection; 