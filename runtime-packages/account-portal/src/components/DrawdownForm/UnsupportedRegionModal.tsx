import React, { useState } from 'react';
import { Modal } from '@/components/shared/Modal';
import { SecondaryButton } from '@/components/shared/SecondaryButton';
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import { sendEmailNotificationsByType } from '@fundpark/fp-api';
import { EmailNotifyType } from '@fundpark/fp-api';

export interface UnsupportedRegionModalProps {
  open: boolean;
  onClose: () => void;
}

export const UnsupportedRegionModal: React.FC<UnsupportedRegionModalProps> = ({ open, onClose }) => {
  const [showWechatModal, setShowWechatModal] = useState(false);
  const [wechatMessage, setWechatMessage] = useState("");
  const [wechatMessageAlign, setWechatMessageAlign] = useState("left");

  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        title="温馨提示"
        width={617}
      >
        <div>
          <p className='mb-0'>亲爱的用户，当前仅支持中国香港公司申请用款。请点击【使用中国香港公司信息】重新提交。</p>
          <p className="mb-4">如您未注册中国香港主体，请点击【3天开立中国香港公司】咨询协助</p>
          <div className="flex justify-center mt-8" style={{ gap: '16px' }}>
            <SecondaryButton
              label="使用中国香港公司信息"
              onClick={() => {
                onClose();
              }}
            />
            <SecondaryButton
              label="3天开立中国香港公司"
              onClick={() => {
                onClose();
                setWechatMessage("亲爱的用户，若您想要开立中国香港公司，欢迎联系我们！");
                setWechatMessageAlign("left");
                setShowWechatModal(true);
                sendEmailNotificationsByType(EmailNotifyType.NO_HONG_KONG_COMPANY)();
              }}
            />
          </div>
        </div>
      </Modal>

      <OurWechatModal
        open={showWechatModal}
        onClose={() => setShowWechatModal(false)}
        message={wechatMessage}
        textAlign={wechatMessageAlign as 'left' | 'center' | 'right'}
      />
    </>
  );
}; 