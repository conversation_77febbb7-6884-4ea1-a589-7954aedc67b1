import { useState, useCallback } from 'react';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { getSignerInfo, initiateSign, sendRpaEmail, urgeSign, retrySign } from '@fundpark/fp-api';
import { mapSignerInfoApiToUi } from '../../../mappers/drawdownForm';
import { SigningPerson, SigningStage } from './types';
import { usePositionTracking } from '@/hooks/usePositionTracking';
import { POSITION_STATUS } from '@/constants/position-status';
import {useLimitStore} from "@/store/limits.ts";
import { VerificationStatusCodes as ApiResponseCodes, ErrorMessages, Routes, SuccessMessages } from '@/constants/signing';
import { TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS } from '@/components/shared/tracking/constants';
import { trackEvent } from '@/utils/matomoTracking';

/**
 * Determines the current signing stage based on the status of signing persons
 * 
 * @param signingPersons Array of signing persons with their status
 * @returns The appropriate SigningStage
 */
export const determineSigningStage = (signingPersons: SigningPerson[]): SigningStage => {
  // Filter to only include persons who need to sign
  const personsWhoNeedToSign = signingPersons.filter(person => person.needSign === true);
  
  // No signing persons who need to sign yet
  if (personsWhoNeedToSign.length === 0) {
    return SigningStage.PENDING;
  }
  
  // Check if all persons who need to sign have signed
  const allSigned = personsWhoNeedToSign.every(person => person.status === 'signed');
  if (allSigned) {
    return SigningStage.SIGNED;
  }
  
  // Check if any person who needs to sign is in the signing process
  const anySigning = personsWhoNeedToSign.some(person => person.status === 'signing');
  const anyUnhappy = personsWhoNeedToSign.some(person => person.status === 'rejected' || person.status === 'expired' || person.status === 'withdrawn' || person.status === 'failed');
  if (anySigning || anyUnhappy) {
    return SigningStage.SIGNING;
  }
  
  // Default case: pending
  return SigningStage.PENDING;
};

/**
 * Custom hook to manage signing data and operations
 */
export const useSigningForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [signingPersons, setSigningPersons] = useState<SigningPerson[]>([]);
  const [currentStage, setCurrentStage] = useState<SigningStage>(SigningStage.PENDING);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | React.ReactNode>("");
  const [showSubmitModal, setShowSubmitModal] = useState<boolean>(false);
  const [showInitiateSuccessModal, setShowInitiateSuccessModal] = useState<boolean>(false);
  const [showOurWechatModal, setShowOurWechatModal] = useState<boolean>(false);
  const [showAgreementFailedModal, setShowAgreementFailedModal] = useState<boolean>(false);
  const [agreementFailedReason, setAgreementFailedReason] = useState<string>("");
  const navigate = useNavigate();
  const { trackPosition } = usePositionTracking();
  const {
      fetchUserLimits,
      fetchUserCreditApplication
  } = useLimitStore();
  /**
   * Fetch signer information from API and update component state
   * @returns Promise that resolves when the operation is complete
   */
  const fetchSignerInfo = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await getSignerInfo();
      if (response.code === 404003) {
        navigate(Routes.HOME_PAGE);
        return;
      }
      
      // Use mapper to transform API data to UI format
      const transformedData = mapSignerInfoApiToUi(response);
      setSigningPersons(transformedData);
      
      // Check if any signing person has a failed agreement status
      const failedSigner = transformedData.find(person => person.agreementDetailStatus === 'failed');
      if (failedSigner) {
        setAgreementFailedReason(failedSigner.agreementFailedReason || '签约失败');
        setShowAgreementFailedModal(true);
      }
      
      // Determine and set current stage based on signing status
      setCurrentStage(determineSigningStage(transformedData));
    } catch (error) {
      console.error('Failed to fetch signer information:', error);
      message.error(ErrorMessages.FETCH_ERROR);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Handle showing and closing error modal
   */
  const handleCloseErrorModal = useCallback(() => {
    setShowErrorModal(false);
  }, []);

  /**
   * Handle closing initiate success modal
   */
  const handleCloseInitiateSuccessModal = useCallback(async () => {
    setShowInitiateSuccessModal(false);
    await fetchSignerInfo();
  }, [fetchSignerInfo]);

  const handleCloseOurWechatModal = useCallback(() => {
    setShowOurWechatModal(false);
  }, []);

  /**
   * Handle closing agreement failed modal
   */
  const handleCloseAgreementFailedModal = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await retrySign();
      
      if (response.code === ApiResponseCodes.SUCCESS) {
        message.success('重新签约已启动');
        // Refresh signer info to update status
        await fetchSignerInfo();
      } else {
        message.error(response.message || '重新签约失败，请稍后重试');
      }
    } catch (error) {
      console.error('Failed to retry signing:', error);
      message.error('重新签约失败，请稍后重试');
    } finally {
      setIsLoading(false);
      setShowAgreementFailedModal(false);
    }
  }, [fetchSignerInfo]);

  /**
   * Initiate signing process
   * @param formRef Reference to the form for validation
   * @returns Promise that resolves when the sign initiation is complete
   */
  const handleInitiateSign = useCallback(async (formRef: React.MutableRefObject<any>): Promise<void> => {
    try {
      // Validate form first
      if (formRef.current) {
        try {
          await formRef.current.validateFields();

          // Trigger saveSignerInfo for all signing cards before initiating sign
          if (formRef.current.saveAllSignerInfo) {
            await formRef.current.saveAllSignerInfo();
          }
        } catch (validationError) {
          message.error(ErrorMessages.VALIDATION_ERROR);
          return;
        }
      }

      setIsSubmitting(true);

      const response = await initiateSign();

      if (response.code === ApiResponseCodes.SUCCESS) {
        // Show success modal instead of message
        setShowInitiateSuccessModal(true);
        await trackPosition({ path: "/credit/hook/register/signing/loading", status: POSITION_STATUS.XDJ_SIGNING_INITIATED });
      } else {

        if (response.code === ApiResponseCodes.DIRECTOR_QUANTITY_NOT_MATCH_WITH_RPA_RESULT || response.code === ApiResponseCodes.SHAREHOLDER_QUANTITY_NOT_MATCH_WITH_RPA_RESULT) {
          setShowOurWechatModal(true);
          return;
        }

        // Display specific error message based on response code
        let errorMsg = ErrorMessages.INITIATE_ERROR;

        if (response.code === ApiResponseCodes.SIGNER_INFO_MISMATCH) {
          errorMsg = ErrorMessages.INFO_MISMATCH;
        } else if (response.code === ApiResponseCodes.RPA_INFO_MISMATCH) {
          errorMsg = ErrorMessages.RPA_MISMATCH;
        } else if (response.code === ApiResponseCodes.ANTI_FRAUD_FAILED) {
          errorMsg = ErrorMessages.ANTI_FRAUD_FAILED;
        } else if (response.code === ApiResponseCodes.RPA_INFO_NOT_FOUND) {
          errorMsg = ErrorMessages.RPA_INFO_NOT_FOUND;
        } else if (response.code === ApiResponseCodes.COMPANY_NOT_FOUND) {
          errorMsg = ErrorMessages.COMPANY_NOT_FOUND;
        } else if (response.code === ApiResponseCodes.MEMBER_NOT_FOUND) {
          errorMsg = ErrorMessages.MEMBER_NOT_FOUND;
        } else if (response.code === ApiResponseCodes.MEMBER_NOT_FOUND_IN_CURRENT_COMPANY) {
          errorMsg = ErrorMessages.MEMBER_NOT_FOUND_IN_CURRENT_COMPANY;
        } else if (response.code === ApiResponseCodes.MEMBER_HAS_BEEN_SIGNED) {
          errorMsg = ErrorMessages.MEMBER_HAS_BEEN_SIGNED;
        } else if (response.code === ApiResponseCodes.FIND_MORE_THAN_ONE_CONNECTED_COMPANY) {
          errorMsg = ErrorMessages.FIND_MORE_THAN_ONE_CONNECTED_COMPANY;
        } else if(response.message === 'Attempt to read property \"platformPsp\" on null'){
          errorMsg = ErrorMessages.PSP_IS_NULL;
        }

        // Set error message and show modal instead of using message.error
        setErrorMessage(errorMsg);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Failed to initiate signing process:', error);
      setErrorMessage(ErrorMessages.INITIATE_ERROR);
      setShowErrorModal(true);
    } finally {
      setIsSubmitting(false);
    }
  }, [fetchSignerInfo, trackPosition]);

  /**
   * Send signing reminders
   * @returns Promise that resolves when the reminder is sent
   */
  const handleUrgeSign = useCallback(async (): Promise<void> => {
    try {
      setIsSubmitting(true);

      const response = await urgeSign();

      if (response.code === ApiResponseCodes.SUCCESS) {
        message.success(SuccessMessages.URGE_SUCCESS);
      } else if (response.code === ApiResponseCodes.AGREEMENT_HAS_BEEN_SENT) {
        message.error(ErrorMessages.AGREEMENT_HAS_BEEN_SENT);
      } else if (response.code === ApiResponseCodes.AGREEMENT_GENERATING) {
        message.error(ErrorMessages.AGREEMENT_GENERATING);
      } else if (response.code === ApiResponseCodes.COMPANY_CREDIT_REQUEST_NOT_FOUND) {
        message.error(ErrorMessages.COMPANY_CREDIT_REQUEST_NOT_FOUND);
      } else {
        message.error(ErrorMessages.URGE_ERROR);
      }
    } catch (error) {
      console.error('Failed to send signing reminder:', error);
      message.error(ErrorMessages.URGE_ERROR);
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  /**
   * Handle submit application after all signings are complete
   */
  const handleSubmitApplication = useCallback(async () => {
    try {
      setIsSubmitting(true);
      const response = await sendRpaEmail();
      
      if (response.code === ApiResponseCodes.SUCCESS) {
    setShowSubmitModal(true);
      } else {
        message.error(ErrorMessages.SUBMIT_ERROR);
      }
    } catch (error) {
      console.error('Failed to submit application:', error);
      message.error(ErrorMessages.SUBMIT_ERROR);
    } finally {
      setIsSubmitting(false);
    }
  }, []);

  /**
   * Confirm submission and navigate away
   */
  const handleConfirmSubmit = useCallback(async () => {
    trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.SUBMITTED_MODAL_CONFIRM_BUTTON);
    const fetchData = async () => {
        await fetchUserLimits(true);
        await fetchUserCreditApplication(true);
    }
    await fetchData().catch((err) => {
        console.error("Error fetching data:", err);
    });
    setShowSubmitModal(false);
    await trackPosition({ path: "/credit/hook/home", status: POSITION_STATUS.XDJ_SIGNING_COMPLETED });
    navigate(Routes.HOME_PAGE);
  }, [navigate, trackPosition]);

  return {
    state: {
      isSubmitting,
      isLoading,
      signingPersons,
      currentStage,
      showErrorModal,
      errorMessage,
      showSubmitModal,
      showInitiateSuccessModal,
      showOurWechatModal,
      showAgreementFailedModal,
      agreementFailedReason
    },
    actions: {
      setIsLoading,
      fetchSignerInfo,
      handleCloseErrorModal,
      handleInitiateSign,
      handleUrgeSign,
      handleSubmitApplication,
      handleConfirmSubmit,
      handleCloseInitiateSuccessModal,
      handleCloseOurWechatModal,
      handleCloseAgreementFailedModal
    }
  };
}; 