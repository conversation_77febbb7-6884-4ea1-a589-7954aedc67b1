import React, { useEffect, useState, useCallback } from "react";
import { Modal } from "@/components/shared/Modal/index.tsx";
import { SecondaryButton } from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import alertCircle from "@/assets-new/icons/common/alert-circle.svg"
import PlatformMainModal from "@/components/Platforms";
import { ModalStep } from "@/types/platforms/modalSteps";
import { getOauthShopList, GetOauthShopListRes } from "@fundpark/fp-api";

interface ShopAuthErrorModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: () => void;
}



const ShopAuthErrorModal: React.FC<ShopAuthErrorModalProps> = ({
    open,
    onClose,
    onComplete,
}) => {
    const [showWechatModal, setShowWechatModal] = useState(false);
    const [modalStep, setModalStep] = useState<ModalStep>("none");
    const [authedPlatforms, setAuthedPlatforms] = useState<GetOauthShopListRes>({ shop: [], psp: [] });

    const handleContactService = () => {
        setShowWechatModal(true);
    };

    const fetchAuthedPlatforms = useCallback(async (): Promise<void> => {
        try {
            const response = await getOauthShopList({});
            if (response.code === 0) {
                setAuthedPlatforms(response.data);
            } else {
                console.error("Failed to fetch authed platforms:", response.message);
            }
        } catch (error) {
            console.error('Failed to fetch authed platforms:', error);
        }
    }, []);

    useEffect(() => {
        fetchAuthedPlatforms();
    }, [fetchAuthedPlatforms]);

    return (
        <>
            <PlatformMainModal
                modalStep={modalStep}
                setModalStep={setModalStep}
                authedPlatforms={authedPlatforms}
                redirectPath={window.location.href}
                shouldCompleteRefresh={false}
                onAuthSuccess={onComplete}
            />

            <OurWechatModal
                open={showWechatModal}
                hasAlertIcon
                width={560}
                message='您授权的店铺尚未完全达到要求，请联系我们换个（3个月正常经营）店铺重新申请吧～'
                onClose={onComplete}
                textAlign='center'
            />

            <Modal
                open={open}
                onClose={onClose}
                title={`温馨提示`}
                backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
                width={444}
                height={264}
                closable={false}
                style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    width: "450",
                    height: "312px",
                    margin: "0 auto",
                }}
            >
                <div>
                    <div
                        className="flex items-start gap-2"
                        style={{
                            fontFamily: "Source Sans Pro",
                            fontSize: "14px",
                            fontWeight: "400",
                            lineHeight: "20px",
                            color: "#282830"
                        }}
                    >
                        <img
                            src={alertCircle}
                            alt="Alert Icon"
                            className="w-6 h-6"
                        />
                        <div>
                            您授权的店铺尚未完全达到要求，请换个（3个月正常经营）店铺重新申请吧～
                        </div>
                    </div>
                </div>

                <div style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}>
                    <SecondaryButton
                        label="线上咨询"
                        onClick={handleContactService}
                        style={{
                            height: "40px",
                            borderRadius: "60px",
                            width: "92px",
                            padding: "10px 16px",
                        }}
                    />
                    <PrimaryButton
                        label="重新授权"
                        onClick={() => setModalStep("platformSelection")}
                        style={{
                            height: "40px",
                            borderRadius: "60px",
                            width: "92px",
                            padding: "10px 16px",
                        }}
                    />
                </div>
            </Modal>
        </>
    );
};

export default ShopAuthErrorModal; 