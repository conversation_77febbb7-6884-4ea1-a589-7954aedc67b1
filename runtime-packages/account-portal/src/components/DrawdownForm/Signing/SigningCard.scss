.signing-card {
  margin-bottom: 16px;
  border-radius: 24px;
  height: auto;
  width: 100%;
  background-color: #F6F7FA;
  border: none;
  position: relative;
  overflow: hidden;
  padding: 24px;
  
  @media (max-width: 992px) { // smaller than lg
    height: auto;
  }

  .ant-card-body {
    padding: 24px;
    height: 100%;
  }
  
  .signing-status-badge {
    position: absolute;
    top: 0;
    right: 0;
    height: 48px;
    width: 120px;
    padding: 0;
    font-size: 16px;
    line-height: 48px;
    font-weight: 600;
    border-radius: 100px 0 0 100px;
    text-align: center;
  }
  
  .signing-card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .signing-card-row {
      display: flex;
      align-items: flex-start;
      gap: 0;
      flex-wrap: nowrap;
      height: 48px;
      
      .avatar-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        color: #2463EB;
        background-color: #2017470A;
        margin-right: 12px;
        flex-shrink: 0;
        font-weight: 400;
      }
      
      .user-avatar {
        flex-shrink: 0;
        margin-right: 12px;
        align-self: center;
      }
      
      .name-container {
        display: flex;
        flex-direction: column;
        flex-grow: 0;
        margin-right: 4px;
        align-self: center;
        
        .english-name {
          font-size: 18px;
          font-weight: 700;
          color: #282830;
        }
        
        .chinese-name {
          font-size: 18px;
          font-weight: 700;
          color: #282830;
        }
      }
      
      .signing-role-badge-flex-end {
        align-self: flex-end;
      }

      .signing-role-badge-center {
        align-self: center;
      }
    }

    .signing-card-sections {
      display: flex;
      margin-top: 24px;
      height: calc(100% - 60px);
      @media (max-width: 992px) { // smaller than lg
        display: block;
        height: auto;
      }

      .signing-card-section-left {
        flex: 1;
        padding-right: 20px;
        @media (max-width: 992px) { // smaller than lg
          display: block;
          height: auto;
          padding-right: 0px;
        }
      }

      .signing-card-section-right {
        flex: 1;
        padding-left: 20px;
        @media (max-width: 992px) { // smaller than lg
          display: block;
          height: auto;
          padding-left: 0px;
        }
      }
    }
  }
  
  .signing-card-header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .chinese-name {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #282830;
    }
    
    .english-name {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.65);
    }
    
    .person-id {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      font-family: monospace;
      margin-top: 4px;
    }
  }
  
  .signing-card-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: auto;
  }
  
  .signing-card-role {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .role-label {
      font-size: 15px;
      color: rgba(0, 0, 0, 0.45);
    }
    
    .role-value {
      font-size: 15px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  
  .signing-card-status {
    .ant-tag {
      margin-right: 0;
      font-size: 14px;
      padding: 2px 12px;
      border-radius: 4px;
    }
  }

  /* Form styling */
  .form-row {
    display: flex;
    gap: 30px;
    margin-bottom: 16px;
    @media (max-width: 992px) { // smaller than lg
      display: block;
    }
  }

  .form-item-container {
    flex: 1;
    width: calc(50% - 15px);
    position: relative;
    @media (max-width: 992px) { // smaller than lg
      width: 100%;
    }
  }

  .fixed-height-item {
    margin-bottom: 0 !important;

    // /* Fix for error message positioning */
    // .ant-form-item-explain, .ant-form-item-extra {
    //   position: absolute;
    //   bottom: -22px;
    //   left: 0;
    //   min-height: 20px;
    //   z-index: 5;
    // }
  }

  /* Upload section styling */
  .upload-container {
    height: 100%;
  }

  .upload-row {
    display: flex;
    gap: 16px;
    @media (max-width: 992px) { // smaller than lg
      display: block;
    }
  }

  .upload-item-container {
    flex: 1;
    margin-bottom: 0;
    position: relative;
  }

  .upload-label {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .upload-label-text {
    margin-right: 4px;
  }

  .required-mark {
    color: #ff4d4f;
    margin-right: 4px;
  }

  .upload-placeholder {
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .upload-placeholder-text {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }

  .upload-title {
    font-size: 14px;
    font-weight: 500;
    margin: 8px 0;
    color: #1f1f1f;
  }

  /* Override the upload component styling to maintain fixed height */
  .cp-company-upload {
    height: 120px;
    
    .ant-upload-list {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
    }
  }
} 