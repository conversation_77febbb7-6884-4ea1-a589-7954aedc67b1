import React, { useState, useEffect } from "react";
import { Form, message } from "antd";
import { useTranslation } from "react-i18next";
import { Modal } from "@/components/shared/Modal/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import { Input } from "@/components/common";
import CountryCodePhoneInput from "@/components/shared/CountryCodePhoneInput";
import SendButton from "@/components/common/SendButton";
import { DEFAULT_AREA_CODES } from "@/components/common/constants.ts";
import { getMobileRules } from "@/constants/data_validation_rules.ts";
import { validateMobilePhone } from "@fundpark/ui-utils";
import { SignupValidateToken } from "@fundpark/fp-api";
import { UserInfo } from '@fundpark/fp-api/types/login.ts';
import type { ChangePhoneNumberRequest } from "@fundpark/fp-api";
import { desensitizeCNMobilePhone, desensitizeHkMobilePhone, desensitizeUSMobilePhone } from "@/utils/desensitize";

interface PhoneVerificationFormModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: (phoneData: ChangePhoneNumberRequest) => void;
}

const PhoneVerificationFormModal: React.FC<PhoneVerificationFormModalProps> = ({
    open,
    onClose,
    onComplete,
}) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [currentPhoneNumber, setCurrentPhoneNumber] = useState<string>("");

    const MOBILE_RULES = getMobileRules(t, form, "phoneCountryCode");

    // Get current phone number from localStorage and apply desensitization
    useEffect(() => {
        try {
            const userInfoString = localStorage.getItem('userInfo');
            if (userInfoString) {
                const userInfo: UserInfo = JSON.parse(userInfoString);
                if (userInfo.phone_country_code && userInfo.phone_number) {
                    const countryCode = userInfo.phone_country_code;
                    const phoneNumber = userInfo.phone_number;
                    
                    // Apply appropriate desensitization based on country code
                    let desensitizedPhone: string;
                    if (countryCode === '+86') {
                        // China mainland
                        desensitizedPhone = desensitizeCNMobilePhone(phoneNumber);
                    } else if (countryCode === '+852') {
                        // Hong Kong, overseas, or other regions
                        desensitizedPhone = desensitizeHkMobilePhone(phoneNumber);
                    } else if (countryCode === '+1') {
                        // USA
                        desensitizedPhone = desensitizeUSMobilePhone(phoneNumber);
                    } else {
                        desensitizedPhone = phoneNumber;
                    }
                    
                    setCurrentPhoneNumber(`${countryCode} ${desensitizedPhone}`);
                }
            }
        } catch (error) {
            console.error('Error getting current phone number:', error);
        }
    }, []);

    // Reset form when modal is closed
    useEffect(() => {
        if (!open) {
            form.resetFields();
            // Clear all form field errors
            form.setFields([
                { name: "phoneCountryCode", errors: [] },
                { name: "phoneNumber", errors: [] },
                { name: "mobileVerification", errors: [] }
            ]);
            setLoading(false);
        }
    }, [open, form]);

    const handleSendMobileVerification = async () => {
        try {
            const phone = form.getFieldValue("phoneNumber");
            const areaCode = form.getFieldValue("phoneCountryCode");

            if (!phone) {
                message.error("请输入手机号码");
                return false;
            }

            const res = await SignupValidateToken({
                phone_country_code: areaCode,
                phone_number: phone
            } as any);

            if (res.message === "success") {
                message.success("验证码已发送");
                return true;
            } else {
                form.setFields([
                    {
                        name: "mobileVerification",
                        errors: [res.message]
                    }
                ]);
                return false;
            }
        } catch (error) {
            console.error("Mobile verification error:", error);
            form.setFields([
                {
                    name: "mobileVerification",
                    errors: ["验证码发送失败"]
                }
            ]);
            return false;
        }
    };

    const handleSubmit = async () => {
        try {
            setLoading(true);
            await form.validateFields();

            // Get the new phone number from form
            const newCountryCode = form.getFieldValue("phoneCountryCode");
            const newPhoneNumber = form.getFieldValue("phoneNumber");
            const newFullPhoneNumber = `${newCountryCode}${newPhoneNumber}`;

            // Get the current phone number from localStorage for comparison
            try {
                const userInfoString = localStorage.getItem('userInfo');
                if (userInfoString) {
                    const userInfo: UserInfo = JSON.parse(userInfoString);
                    if (userInfo.phone_country_code && userInfo.phone_number) {
                        const currentFullPhoneNumber = `${userInfo.phone_country_code}${userInfo.phone_number}`;
                        
                        // Check if new phone number is the same as current one
                        if (newFullPhoneNumber === currentFullPhoneNumber) {
                            message.error("新手机号码不能与现绑定手机号相同，请输入不同的手机号码");
                            return;
                        }
                    }
                }
            } catch (error) {
                console.error('Error comparing phone numbers:', error);
            }

            // Get verification code from form
            const verificationCode = form.getFieldValue("mobileVerification");
            
            // Prepare phone data for API call
            const phoneData: ChangePhoneNumberRequest = {
                phone_number: newPhoneNumber,
                phone_country_code: newCountryCode,
                phone_code: verificationCode
            };
            
            // Pass phone data to parent component
            onComplete(phoneData);
        } catch (error) {
            console.error("Phone verification failed:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Modal
            open={open}
            onClose={onClose}
            title="更换手机号"
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={500}
            height={400}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                margin: "0 auto",
            }}
        >
            <Form
                form={form}
                layout="vertical"
                initialValues={{ phoneCountryCode: '+86' }}
                style={{ width: '100%' }}
            >
                {currentPhoneNumber && (
                    <div style={{
                        fontSize: "14px",
                        color: "#000000",
                        marginBottom: "12px",
                        fontFamily: "Source Sans Pro",
                    }}>
                        现绑定手机号：{currentPhoneNumber}
                    </div>
                )}
                
                <Form.Item className="mb-3">
                    <CountryCodePhoneInput
                        className="mb-0"
                        countryCodeName="phoneCountryCode"
                        phoneNumberName="phoneNumber"
                        countryCodeOptions={DEFAULT_AREA_CODES}
                        countryCodeRules={[
                            { required: true, message: "请选择国家/地区代码" },
                            {
                                validator: (_, value) => {
                                    if (value && value.replace("+", "") === "") {
                                        return Promise.reject(new Error("请选择有效的国家/地区代码"));
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                        phoneNumberRules={MOBILE_RULES}
                        countryCodePlaceholder="选择"
                        phoneNumberPlaceholder="请输入手机号码"
                        disabled={loading}
                    />
                </Form.Item>

                <Form.Item
                    name="mobileVerification"
                    rules={[{ required: true, message: "请输入验证码" }]}
                    className="mb-3"
                >
                    <Input
                        placeholder="请输入验证码"
                        size="large"
                        className="!pr-1"
                        disabled={loading}
                        suffix={
                            <Form.Item
                                noStyle
                                shouldUpdate={(prev, curr) =>
                                    prev.phoneNumber !== curr.phoneNumber ||
                                    prev.phoneCountryCode !== curr.phoneCountryCode
                                }
                            >
                                {({ getFieldValue }) => {
                                    const isValidInput = validateMobilePhone(
                                        getFieldValue("phoneNumber"),
                                        getFieldValue("phoneCountryCode")
                                    );

                                    return (
                                        <SendButton
                                            onClick={async () => {
                                                return handleSendMobileVerification();
                                            }}
                                            countdown={60}
                                            disabled={!isValidInput || loading}
                                            title="发送验证码"
                                        />
                                    );
                                }}
                            </Form.Item>
                        }
                    />
                </Form.Item>
            </Form>

            <div style={{
                display: "flex",
                justifyContent: "center",
                height: "40px",
                alignItems: "center",
                gap: "16px",
                marginTop: "40px",
            }}>
                <PrimaryButton
                    label={loading ? "提交中..." : "提交"}
                    onClick={handleSubmit}
                    disabled={loading}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "92px",
                        padding: "10px 16px",
                    }}
                />
            </div>
        </Modal>
    );
};

export default PhoneVerificationFormModal; 