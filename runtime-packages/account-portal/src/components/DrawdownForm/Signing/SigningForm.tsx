import React, { Suspense, useEffect, useRef } from 'react';
import { Spin } from 'antd';
import Stepper from '@/components/shared/Stepper';

// Import shared styles
import './styles.scss';
import '../styles.scss';

// Import components and hooks
import SigningSection from './SigningSection';
import ActionButtons from './ActionButtons';
import { useSigningForm } from './hooks';
import { ErrorModal, SubmitModal, InitiateSuccessModal, AgreementFailedModal } from './modals';
import { OurWechatModal } from '@/components/shared/OurWechatModal';

const SigningForm: React.FC = () => {
  const signingFormRef = useRef<any>(null);
  const { state, actions } = useSigningForm();
  
  // Fetch signer information when component mounts
  useEffect(() => {
    actions.fetchSignerInfo();
  }, [actions.fetchSignerInfo]);

  // Define steps for the stepper component
  const steps = [
    {
      title: '用款账户',
      content: (<></>),
      action: null,
    },
    {
      title: '信息确认',
      content: (<></>),
      action: null,
    },
    {
      title: '签约放款',
      content: (
        <Suspense fallback={<Spin size="large" />}>
          <SigningSection
            formRef={signingFormRef}
            signingPersons={state.signingPersons}
            onInitiateSign={() => actions.handleInitiateSign(signingFormRef)}
            onUrgeSign={actions.handleUrgeSign}
            onSubmitApplication={actions.handleSubmitApplication}
            onRefresh={actions.fetchSignerInfo}
            loading={state.isLoading}
            setIsLoading={actions.setIsLoading}
            currentStage={state.currentStage}
          />
        </Suspense>
      ),
      action: (
        <ActionButtons
          currentStage={state.currentStage}
          isSubmitting={state.isSubmitting}
          isLoading={state.isLoading}
          onInitiateSign={() => actions.handleInitiateSign(signingFormRef)}
          onUrgeSign={actions.handleUrgeSign}
          onRefresh={actions.fetchSignerInfo}
          onSubmitApplication={actions.handleSubmitApplication}
          formRef={signingFormRef}
        />
      ),
    },
  ];

  return (
    <div className="signing-form-wrapper">
      <Stepper
        steps={steps}
        currentStep={2}
        borderTopRadiusRounded={false}
      />
      
      <ErrorModal
        open={state.showErrorModal}
        onClose={actions.handleCloseErrorModal}
        errorMessage={state.errorMessage}
      />
      
      <SubmitModal
        open={state.showSubmitModal}
        onClose={actions.handleConfirmSubmit}
        onConfirm={actions.handleConfirmSubmit}
      />
      
      <InitiateSuccessModal
        open={state.showInitiateSuccessModal}
        onClose={actions.handleCloseInitiateSuccessModal}
      />

      <AgreementFailedModal
        open={state.showAgreementFailedModal}
        onClose={actions.handleCloseAgreementFailedModal}
        failedReason={state.agreementFailedReason}
        isLoading={state.isLoading}
      />

      <OurWechatModal
        open={state.showOurWechatModal}
        hasAlertIcon
        width={560}
        message='很抱歉哦～ 我们没有找到您的董事及实控人信息，请联系我们继续补充! '
        onClose={actions.handleCloseOurWechatModal}
        textAlign='center'
      />
    </div>
  );
};

export default SigningForm; 