import React, { useState } from "react";
import { Modal } from "@/components/shared/Modal/index.tsx";
import { SecondaryButton } from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import alertCircle from "@/assets-new/icons/common/alert-circle.svg"
import PhoneVerificationFormModal from './PhoneVerificationFormModal';
import { changePhoneNumber } from "@fundpark/fp-api";
import type { ChangePhoneNumberRequest } from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper";

interface MobileVerificationErrorModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: () => void;
}

const MobileVerificationErrorModal: React.FC<MobileVerificationErrorModalProps> = ({
    open,
    onClose,
    onComplete,
}) => {
    const [showWechatModal, setShowWechatModal] = useState(false);
    const [showPhoneVerificationModal, setShowPhoneVerificationModal] = useState(false);

    const handleContactService = () => {
        setShowWechatModal(true);
    };

    const handleRetryVerification = () => {
        setShowPhoneVerificationModal(true);
    };

    const userInfoRevalidate = async (phoneData: ChangePhoneNumberRequest) => {
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
            const userInfoObj = JSON.parse(userInfo);
            userInfoObj.phone_number = phoneData.phone_number;
            userInfoObj.phone_country_code = phoneData.phone_country_code;
            appHelper.setUserInfo(userInfoObj);
        }
    };

    const handlePhoneVerificationComplete = async (phoneData: ChangePhoneNumberRequest) => {
        try {
            const response = await changePhoneNumber(phoneData);
            
            if (response.code === 0) {
                appHelper.msgApi.success("手机号更换成功");
                setShowPhoneVerificationModal(false);
                userInfoRevalidate(phoneData);
                onComplete();
            } else {
                appHelper.msgApi.error(response.message || "手机号更换失败");
            }
        } catch (error) {
            console.error("Error changing phone number:", error);
            appHelper.msgApi.error("网络错误，请重试");
        }
    };

    return (
        <>
            <OurWechatModal
                open={showWechatModal}
                hasAlertIcon
                width={560}
                onClose={onComplete}
                textAlign='center'
            />

            <PhoneVerificationFormModal
                open={showPhoneVerificationModal}
                onClose={() => setShowPhoneVerificationModal(false)}
                onComplete={handlePhoneVerificationComplete}
            />

            <Modal
                open={open}
                onClose={onClose}
                title={`手机号验证失败`}
                backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
                width={444}
                height={264}
                closable={false}
                style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    width: "450",
                    height: "312px",
                    margin: "0 auto",
                }}
            >
                <div>
                    <div
                        className="flex items-start gap-2"
                        style={{
                            fontFamily: "Source Sans Pro",
                            fontSize: "14px",
                            fontWeight: "400",
                            lineHeight: "20px",
                            color: "#282830"
                        }}
                    >
                        <img
                            src={alertCircle}
                            alt="Alert Icon"
                            className="w-6 h-6"
                        />
                        <div>
                            检测到您注册的手机号码存在风险，为保障您的信息安全，请更换手机号码后继续申请哦。
                        </div>
                    </div>
                </div>

                <div style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}>
                    <SecondaryButton
                        label="线上咨询"
                        onClick={handleContactService}
                        style={{
                            height: "40px",
                            borderRadius: "60px",
                            width: "auto",
                            padding: "10px 16px",
                        }}
                    />
                    <PrimaryButton
                        label="更换手机号"
                        onClick={handleRetryVerification}
                        style={{
                            height: "40px",
                            borderRadius: "60px",
                            width: "auto",
                            padding: "10px 16px",
                        }}
                    />
                </div>
            </Modal>
        </>
    );
};

export default MobileVerificationErrorModal; 