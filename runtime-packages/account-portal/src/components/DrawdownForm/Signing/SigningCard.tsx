import { useState, useImperativeHandle, forwardRef, useEffect, useCallback } from 'react';
import { Card, Typography, Select, Input, Form } from 'antd';
import AlertIcon from '@/assets-new/icons/alert-icon.svg?react';
import CommonUpload from '@/components/company/Upload';
import './SigningCard.scss';
import { useConfig } from '@/contexts/ConfigContext';
import {
  SigningCardRef,
  SigningCardProps,
  CountryOption,
  UploadedFileInfo
} from './SigningCardTypes';
import {
  idTypeDocuments,
  getNationalityIdType,
  getStatusConfig,
  getCountryOptions,
  getPhoneCountryOptions,
  isManualIdTypeSelection,
  getIdTypeOptions
} from './SigningCardUtils';
import { saveSignerInfo, deleteSignerFile as deleteSignerFileApi } from '@fundpark/fp-api';
import Modal from '@/components/common/Modal';
import SecondaryButton from '@/components/shared/SecondaryButton';
import { getMobileRules } from '@/constants/data_validation_rules';
import { useTranslation } from 'react-i18next';
import FormCountryCodePhoneInput from '@/components/shared/FormCountryCodePhoneInput';
import { useMatomoContext } from '@/contexts/MatomoContext';
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from '@/components/shared/tracking/constants';
import { MATOMO_CONFIG } from '@/utils/matomoConfig';
import { trackEvent } from '@/utils/matomoTracking';

const { Text } = Typography;
const { Option } = Select;


interface UploadValue {
  id: number;
  url: string;
  filename: string;
}


const ErrorModal = ({ open, onClose, errorMessage, buttonLabel = "重新上传" }: { 
  open: boolean; 
  onClose: () => void; 
  errorMessage: string;
  buttonLabel?: string;
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      title="温馨提示"
      width={500}
    >
      <div className="flex flex-col items-center">
        <p className="text-center flex items-center justify-center">
          <AlertIcon className="mr-2" />
          {errorMessage}
        </p>
      </div>
      <div className="flex justify-center mt-8">
        <SecondaryButton
          label={buttonLabel}
          onClick={onClose}
        />
      </div>
    </Modal>
  );
};

// Helper function to map field key to API fileType
const mapFieldKeyToFileType = (fieldKey: string): 'front_id_file_id' | 'back_id_file_id' | 'front_file_id' | 'back_file_id' | undefined => {
  const mapping: Record<string, 'front_id_file_id' | 'back_id_file_id' | 'front_file_id' | 'back_file_id'> = {
    'idDocumentFront': 'front_id_file_id',
    'idDocumentBack': 'back_id_file_id',
    'extraDocumentFront': 'front_file_id',
    'extraDocumentBack': 'back_file_id'
  };
  
  return mapping[fieldKey];
};

const SigningCard = forwardRef<SigningCardRef, SigningCardProps>((props, ref) => {
  const {
    status,
    englishName,
    chineseName,
    role,
    personId,
    onFormChange,
    onUploadChange,
    
    nationality,
    email,
    mobilePhoneAreaCode,
    mobilePhoneNumber,
    papersType,
    frontPreviewLink,
    backPreviewLink,
    frontFileId,
    backFileId,
    frontFileName,
    backFileName,
    setIsLoading,
    disabled = false,
    needSign,
    address,
    extraFrontFileLink,
    extraBackFileLink,
    extraFrontFileId,
    extraBackFileId
  } = props;

  const { companyConfig, loading: configLoading, getCountryLabel } = useConfig();
  const { t } = useTranslation();
  const { trackEventWithDimensions } = useMatomoContext();

  const statusConfig = getStatusConfig(status);
  const hasBothNames = englishName && chineseName;
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("很抱歉，系统未识别到您的身份信息，请清晰拍摄再上传一次");
  const [errorButtonLabel, setErrorButtonLabel] = useState<string>("重新上传");

  const [selectedNationality, setSelectedNationality] = useState<string>(nationality || '');
  const [idType, setIdType] = useState<string>('');
  const [previousIdType, setPreviousIdType] = useState<string>('');

  const deleteSignerFile = useCallback(async () => {
    await deleteSignerFileApi(personId)({
      front_id_file_id: "",
      back_id_file_id: "",
      front_file_id: "",
      back_file_id: ""
    });
  }, [personId]);

  // 添加useEffect，在错误模态框显示时清除上传文档
  useEffect(() => {
    if (showErrorModal) {
      // 清除上传的文档
      setUploadValues({});
      setUploadedFiles({});
      // Properly await the async deleteSignerFile call
      const handleDeleteSignerFile = async () => {
        try {
          await deleteSignerFile();
        } catch (error) {
          console.error('Error deleting signer file:', error);
          // Handle error if needed - could show error message or retry logic
        }
      };
      
      handleDeleteSignerFile();

      // 通知父组件上传文件已清除
      if (onUploadChange) {
        onUploadChange('idDocumentFront', undefined);
        onUploadChange('idDocumentBack', undefined);
        onUploadChange('extraDocumentFront', undefined);
        onUploadChange('extraDocumentBack', undefined);
      }
    }
  }, [showErrorModal, onUploadChange, deleteSignerFile]);
  
  const initialUploads: Record<string, UploadValue> = {};
  if (frontPreviewLink) {
    initialUploads.idDocumentFront = {
      id: frontFileId || 0, 
      url: frontPreviewLink,
      filename: frontFileName || '' 
    };
  }

  if (backPreviewLink) {
    initialUploads.idDocumentBack = {
      id: backFileId || 0, 
      url: backPreviewLink,
      filename: backFileName || '' 
    };
  }

  if (extraFrontFileLink) {
    initialUploads.extraDocumentFront = {
      id: extraFrontFileId || 0, 
      url: extraFrontFileLink,
      filename: '' 
    };
  }

  if (extraBackFileLink) {
    initialUploads.extraDocumentBack = {
      id: extraBackFileId || 0, 
      url: extraBackFileLink,
      filename: '' 
    };
  }

  const [uploadValues, setUploadValues] = useState<Record<string, UploadValue>>(initialUploads);
  
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, UploadedFileInfo>>({});
  
  const [form] = Form.useForm();

  
  useEffect(() => {
    if (selectedNationality) {
      if (isManualIdTypeSelection(selectedNationality)) {
        // For Hong Kong, set default but allow manual selection
        const defaultIdType = getNationalityIdType(selectedNationality);
        setIdTypeWithTracking(defaultIdType, selectedNationality);
        form.setFieldValue('idType', defaultIdType);
      } else {
        // For other nationalities, auto-select and keep disabled
        const newIdType = getNationalityIdType(selectedNationality);
        setIdTypeWithTracking(newIdType, selectedNationality);
        form.setFieldValue('idType', newIdType);
      }
    } else {
      setIdType('');
      form.setFieldValue('idType', '');
    }
  }, [selectedNationality, form]);

  
  useEffect(() => {
    if (previousIdType && idType !== previousIdType) {
      
      setUploadValues({});
      
      setUploadedFiles({});
      
      
      if (onUploadChange) {
        onUploadChange('idDocumentFront', undefined);
        onUploadChange('idDocumentBack', undefined);
      }
    }
    
    
    setPreviousIdType(idType);
  }, [idType, previousIdType, onUploadChange]);

  
  useEffect(() => {
    const initialNationality = nationality || '';

    const initialIdType = initialNationality ? getNationalityIdType(initialNationality, papersType) : '';

    form.setFieldsValue({
      nationality: initialNationality,
      idType: initialIdType,
      phoneNumber: mobilePhoneNumber,
      phoneCountryCode: mobilePhoneAreaCode,
      email: email,
      address: address
    });

    
    setSelectedNationality(initialNationality);
    if (initialIdType && initialNationality) {
      setIdTypeWithTracking(initialIdType, initialNationality);
    } else {
      setIdType(initialIdType);
    }
    setPreviousIdType(initialIdType);
  }, [nationality, mobilePhoneNumber, mobilePhoneAreaCode, email, form, address]);

  
  useImperativeHandle(ref, () => ({
    form,
    uploadedFiles: uploadedFiles,
    validate: async () => {
      try {
        const values = await form.validateFields();

        // Check if required upload files exist
        if (idType && idTypeDocuments[idType]) {
          const requiredFields = idTypeDocuments[idType].fields
            .filter(field => field.required)
            .map(field => field.key);

          const missingUploads = requiredFields.filter(field => !uploadValues[field]);

          if (missingUploads.length > 0) {
            // Create validation error
            const error: any = new Error('Upload validation failed');
            error.errorFields = missingUploads.map(field => ({
              name: [field],
              errors: [`请上传${idTypeDocuments[idType].fields.find(f => f.key === field)?.label || field}`]
            }));
            throw error;
          }
        }

        // Collect file info
        const fileInfo: Record<string, any> = {};
        Object.entries(uploadValues).forEach(([key, value]) => {
          fileInfo[`${key}Id`] = value.id;
          fileInfo[`${key}Url`] = value.url;
          fileInfo[`${key}Filename`] = value.filename;
        });

        // Return all values
        const allValues = {
          ...values,
          ...fileInfo,
          uploadedFiles
        };

        return allValues;
      } catch (error) {
        throw error;
      }
    }
  }));

  
  const handleCloseErrorModal = () => {
    setShowErrorModal(false);
  };

  
  const checkAndSaveSignerInfo = (updatedUploadValues?: Record<string, UploadValue>) => {
    if (!idType || !currentDocConfig) return;
    
    const valuesToCheck = updatedUploadValues || uploadValues;
    
    const requiredFields = currentDocConfig.fields
      .filter(field => field.required)
      .map(field => field.key);
    
    const hasAllRequiredDocuments = requiredFields.every(field => valuesToCheck[field]?.id);
    
    if (hasAllRequiredDocuments) {
      
      const formValues = form.getFieldsValue();
      
      
      // Check required form fields (address is required for non-China)
      const isRequiredFieldsValid = formValues.nationality && 
        formValues.idType && 
        formValues.phoneCountryCode && 
        formValues.phoneNumber && 
        formValues.email &&
        (formValues.nationality === '2' || formValues.address); // Address required for non-China
      
      if (isRequiredFieldsValid) {
        
        const requestData: any = {
          region_code: formValues.nationality,
          phone_region_code: formValues.phoneCountryCode,
          phone_number: formValues.phoneNumber,
          email: formValues.email
        };

        // Include address for non-China nationalities
        if (formValues.nationality !== '2' && formValues.address) {
          requestData.address = formValues.address;
        }

        const idDocumentFront = valuesToCheck['idDocumentFront']?.id;
        const idDocumentBack = valuesToCheck['idDocumentBack']?.id;
        const extraDocumentFront = valuesToCheck['extraDocumentFront']?.id;
        const extraDocumentBack = valuesToCheck['extraDocumentBack']?.id;

        if (idDocumentFront) {
          requestData.front_id_file_id = idDocumentFront;
        }
        if (idDocumentBack) {
          requestData.back_id_file_id = idDocumentBack;
        }
        if (extraDocumentFront) {
          requestData.front_file_id = extraDocumentFront;
        }
        if (extraDocumentBack) {
          requestData.back_file_id = extraDocumentBack;
        }
        
        // Include papers_type specifically for Hong Kong Travel Permit (港澳居民来往内地通行证)
        if (idType === 'hkIdWithTravel' && (extraDocumentFront || extraDocumentBack)) {
          requestData.papers_type = "exitentrypermittomainland";
        }

        setIsLoading(true);
        saveSignerInfo(personId)(requestData)
          .then((response) => {
            
            if (response && response.code === 1) {
              // 未识别的情况
              setErrorMessage("很抱歉，系统未识别到您的身份信息，请清晰拍摄再上传一次");
              setErrorButtonLabel("重新上传");
              setShowErrorModal(true);
              return;
            }

            if (response && response.code === 200400) {
              // 身份信息不匹配的情况
              setErrorMessage("您上传的身份信息似乎不太匹配呢，请按照显示的董事信息再上传一次");
              setErrorButtonLabel("返回检查");
              setShowErrorModal(true);
              return;
            }

            trackEvent(TRACKING_CATEGORIES.FORM, TRACKING_ACTIONS.UPLOAD, TRACKING_EVENTS.ALL_ID_DOCUMENTS_UPLOADED);

          })
          .catch((error) => {
            
            console.error('Error saving signer info', error);
            setErrorMessage(error?.message || "保存签署人信息时出错，请稍后再试。");
            setShowErrorModal(true);
          })
          .finally(() => {
            setIsLoading(false);
          });
      }
    }
  };

  
  const handleUploadChange = (field: string) => (value?: string) => {
    if (value) {
      onUploadChange?.(field, value);
    } else {
      setUploadValues(prev => {
        const newValues = { ...prev };
        delete newValues[field];
        return newValues;
      });
      
      setUploadedFiles(prev => {
        const newValues = { ...prev };
        delete newValues[field];
        return newValues;
      });
      
      onUploadChange?.(field, undefined);
      
      console.log(`Deleted ${field} document`, { personId, field });
    }
  };

  
  const handleFileChange = (field: string) => (fileData?: any) => {
    if (fileData) {
      
      const fileInfo: UploadedFileInfo = {
        file_link: fileData.url,
        file_id: fileData.file_id, 
        file_name: fileData.fileName
      };
      
      
      setUploadedFiles(prev => ({
        ...prev,
        [field]: fileInfo
      }));
      
      
      setUploadValues(prev => ({
        ...prev,
        [field]: {
          id: fileInfo.file_id,
          url: fileInfo.file_link,
          filename: fileInfo.file_name
        }
      }));
      
      
      const updatedUploadValues = {
        ...uploadValues,
        [field]: {
          id: fileInfo.file_id,
          url: fileInfo.file_link,
          filename: fileInfo.file_name
        }
      };
      
      
      setTimeout(() => {
        checkAndSaveSignerInfo(updatedUploadValues);
      }, 0);
      
      
      onUploadChange?.(field, fileInfo.file_link, fileInfo);
    }
  };

  
  const handleNationalityChange = (value: string) => {
    setSelectedNationality(value);
  };

  
  const handleIdTypeChange = (value: string) => {
    setIdType(value);
  };

  // Helper function to set ID type with tracking
  const setIdTypeWithTracking = (idTypeValue: string, nationalityValue?: string) => {
    setIdType(idTypeValue);
    
    // Track the ID type change if we have a valid value and nationality
    if (idTypeValue && (nationalityValue || selectedNationality)) {
      const nationality = nationalityValue || selectedNationality;
      const idTypeOption = getIdTypeOptions(nationality).find((option) => option.value === idTypeValue);
      const idTypeName = idTypeOption ? idTypeOption.label : idTypeValue;
      
      trackEventWithDimensions({
        category: TRACKING_CATEGORIES.FORM,
        action: TRACKING_ACTIONS.SELECT,
        name: TRACKING_EVENTS.SELECT_ID_TYPE,
        customDimensions: {
          [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: idTypeName
        }
      });
    }
  };

  
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    
    if (changedValues.nationality) {
      handleNationalityChange(changedValues.nationality);
    }

    
    if (changedValues.idType) {
      handleIdTypeChange(changedValues.idType);
    }

    
    onFormChange?.(allValues);
  };

  
  const currentDocConfig = idType ? idTypeDocuments[idType] : null;

  return (
    <>
      <Card className="signing-card">
        {needSign && (
          <div className="signing-status-badge" style={{
            backgroundColor: statusConfig.backgroundColor,
            color: statusConfig.color
          }}>
            {statusConfig.label}
          </div>
        )}
        <div className="signing-card-content">
          <div className="signing-card-row">
            <div className="avatar-wrapper">
              {role}
            </div>
            <div className="name-container">
              {englishName && <Text className="english-name">{englishName}</Text>}
              {chineseName && <Text className="chinese-name">{chineseName}</Text>}
            </div>
          </div>

          <div className="signing-card-sections">
            <div className="signing-card-section-left">
              <Form
                form={form}
                layout="vertical"
                onValuesChange={handleFormValuesChange}
                name={`signing-form-${personId}`}
                validateTrigger="onBlur"
                disabled={disabled}
              >
                <div className="form-row">
                  <div className="form-item-container">
                    <Form.Item
                      label="国籍"
                      name="nationality"
                      className="form-item fixed-height-item"
                      required
                      rules={[{ required: true, message: '请选择国籍' }]}
                    >
                      <Select
                        placeholder="请选择国籍"
                        allowClear
                        onChange={handleNationalityChange}
                        onBlur={(e) => {
                          // Get the current form values to access the selected nationality
                          const currentValues = form.getFieldsValue();
                          const selectedValue = currentValues.nationality;
                          
                          if (selectedValue) {
                            // Get country name from the selected value
                            const country = getCountryOptions(companyConfig).find((option: CountryOption) => option.value === selectedValue);
                            const countryName = country ? country.label : selectedValue;
                            
                            trackEventWithDimensions({
                              category: TRACKING_CATEGORIES.FORM,
                              action: TRACKING_ACTIONS.SELECT,
                              name: TRACKING_EVENTS.SELECT_NATIONALITY,
                              customDimensions: {
                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: countryName
                              }
                            });
                          }
                        }}
                        loading={configLoading}
                        showSearch
                        optionFilterProp="label"
                      >
                        {getCountryOptions(companyConfig).map((option: CountryOption) => (
                          <Option key={option.value} value={option.value} label={option.label}>{option.label}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  <div className="form-item-container">
                    <Form.Item
                      label="身份证件类型"
                      name="idType"
                      className="form-item fixed-height-item"
                      required={isManualIdTypeSelection(selectedNationality)}
                      rules={isManualIdTypeSelection(selectedNationality) ? [
                        { required: true, message: '请选择身份证件类型' }
                      ] : []}
                    >
                      <Select
                        placeholder={selectedNationality ? "请选择身份证件类型" : "请先选择国籍"}
                        disabled={disabled || !isManualIdTypeSelection(selectedNationality)}
                        onChange={handleIdTypeChange}
                        onBlur={(e) => {
                          // Get the current form values to access the selected ID type
                          const currentValues = form.getFieldsValue();
                          const selectedValue = currentValues.idType;
                          
                          if (selectedValue && selectedNationality) {
                            // Get ID type name from the selected value
                            const idTypeOption = getIdTypeOptions(selectedNationality).find((option) => option.value === selectedValue);
                            const idTypeName = idTypeOption ? idTypeOption.label : selectedValue;
                            
                            trackEventWithDimensions({
                              category: TRACKING_CATEGORIES.FORM,
                              action: TRACKING_ACTIONS.SELECT,
                              name: TRACKING_EVENTS.SELECT_ID_TYPE,
                              customDimensions: {
                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: idTypeName
                              }
                            });
                          }
                        }}
                      >
                        {selectedNationality && getIdTypeOptions(selectedNationality).map((option) => (
                          <Option key={option.value} value={option.value} label={option.label}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>
                </div>

                <div className="form-row">
                  <div className="form-item-container">
                    <FormCountryCodePhoneInput
                      countryCodeName="phoneCountryCode"
                      phoneNumberName="phoneNumber"
                      countryCodeOptions={getPhoneCountryOptions(companyConfig)}
                      phoneNumberRules={getMobileRules(t, form, "phoneCountryCode")}
                      label="手机号"
                      countryCodePlaceholder="选择"
                      className="form-item fixed-height-item"
                      countryCodeWidth={88}
                      disabled={disabled}
                      onPhoneNumberBlur={(phoneNumber) => {
                        // Get the current form values to access the country code
                        const currentValues = form.getFieldsValue();
                        const countryCode = currentValues.phoneCountryCode;
                        
                        if (phoneNumber && countryCode) {
                          // Combine country code and phone number
                          const combinedPhoneNumber = `${countryCode} ${phoneNumber}`;
                          
                          trackEventWithDimensions({
                            category: TRACKING_CATEGORIES.FORM,
                            action: TRACKING_ACTIONS.INPUT,
                            name: TRACKING_EVENTS.INPUT_SIGNING_PHONE_NUMBER,
                            customDimensions: {
                              [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: combinedPhoneNumber
                            }
                          });
                        }
                      }}
                    />
                  </div>

                  <div className="form-item-container">
                    <Form.Item
                      label="邮箱"
                      name="email"
                      className="form-item fixed-height-item"
                      required
                      rules={[
                        { required: true, message: '请输入邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' }
                      ]}
                    >
                      <Input 
                        placeholder="请输入邮箱"
                        onBlur={(e) => {
                          const value = e.target.value;
                          if (value && value.trim()) {
                            trackEventWithDimensions({
                              category: TRACKING_CATEGORIES.FORM,
                              action: TRACKING_ACTIONS.INPUT,
                              name: TRACKING_EVENTS.INPUT_SIGNING_EMAIL,
                              customDimensions: {
                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: value.trim()
                              }
                            });
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                </div>

                {/* Address field for non-China nationalities */}
                {selectedNationality && selectedNationality !== '2' && (
                  <div className="form-row">
                    <div className="form-item-container" style={{ flex: '1 1 100%' }}>
                      <Form.Item
                        label="居住地址"
                        name="address"
                        className="form-item fixed-height-item"
                        required
                        rules={[
                          { required: true, message: '请输入居住地址' }
                        ]}
                      >
                        <Input placeholder="请输入居住地址" />
                      </Form.Item>
                    </div>
                  </div>
                )}
              </Form>
            </div>

            <div className="signing-card-section-right">
              {idType && currentDocConfig ? (
                <div className="upload-container">
                  {idType === 'hkIdWithTravel' ? (
                    // Special layout for Hong Kong ID + Travel Permit
                    <>
                      {/* First row: Hong Kong ID documents */}
                      <div className="upload-row">
                        {currentDocConfig.fields
                          .filter(field => field.key.includes('idDocument'))
                          .map((field) => (
                            <div key={field.key} className="upload-item-container">
                              <div className="upload-label">
                                {field.required && <span className="required-mark">*</span>}
                                <span className="upload-label-text">{field.label}</span>
                              </div>
                              <CommonUpload
                                maxFileSize={10}
                                allowedExtensions="jpg,jpeg,png,pdf"
                                onChange={handleUploadChange(field.key)}
                                onFileChange={handleFileChange(field.key)}
                                value={uploadValues[field.key]?.url}
                                fileName={uploadValues[field.key]?.filename}
                                disabled={disabled}
                                showDeleteBtn={!disabled}
                                signerId={personId}
                                fileType={mapFieldKeyToFileType(field.key)}
                              />
                            </div>
                          ))}
                      </div>
                      
                      {/* Second row: Travel Permit documents */}
                      <div className="upload-row">
                        {currentDocConfig.fields
                          .filter(field => field.key.includes('extraDocument'))
                          .map((field) => (
                            <div key={field.key} className="upload-item-container">
                              <div className="upload-label">
                                {field.required && <span className="required-mark">*</span>}
                                <span className="upload-label-text">{field.label}</span>
                              </div>
                              <CommonUpload
                                maxFileSize={10}
                                allowedExtensions="jpg,jpeg,png,pdf"
                                onChange={handleUploadChange(field.key)}
                                onFileChange={handleFileChange(field.key)}
                                value={uploadValues[field.key]?.url}
                                fileName={uploadValues[field.key]?.filename}
                                disabled={disabled}
                                showDeleteBtn={!disabled}
                                signerId={personId}
                                fileType={mapFieldKeyToFileType(field.key)}
                              />
                            </div>
                          ))}
                      </div>
                    </>
                  ) : (
                    // Default layout for other ID types
                    <div className="upload-row">
                      {currentDocConfig.fields.map((field) => (
                        <div key={field.key} className="upload-item-container">
                          <div className="upload-label">
                            {field.required && <span className="required-mark">*</span>}
                            <span className="upload-label-text">{field.label}</span>
                          </div>
                          <CommonUpload
                            maxFileSize={10}
                            allowedExtensions="jpg,jpeg,png,pdf"
                            onChange={handleUploadChange(field.key)}
                            onFileChange={handleFileChange(field.key)}
                            value={uploadValues[field.key]?.url}
                            fileName={uploadValues[field.key]?.filename}
                            disabled={disabled}
                            showDeleteBtn={!disabled}
                            signerId={personId}
                            fileType={mapFieldKeyToFileType(field.key)}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      </Card>
      
      <ErrorModal 
        open={showErrorModal}
        onClose={handleCloseErrorModal}
        errorMessage={errorMessage}
        buttonLabel={errorButtonLabel}
      />
    </>
  );
});

export default SigningCard; 