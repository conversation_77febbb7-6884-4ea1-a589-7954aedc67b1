import React, {Mu<PERSON>RefObject, useEffect, useRef} from "react";
import {Form, Input, InputNumber, Tooltip} from "antd";
import {useConfig} from "@/contexts/ConfigContext";
import "./DrawdownAccountSection.scss";
import CurrencyAmountInput from "../../shared/CurrencyAmountInput";
import NoHKBank from "../../redirect-links/NoHKBank";
import InfoBanner from "../../shared/InfoBanner";
import {ConfigType, COUNTRY_ID_TO_LEGACY_CODE, LegacyCountryCode} from "@/constants/configTypes";
import ConfigDataSelect from "../../shared/ConfigDataSelect";
import {DrawdownAccountFormData} from "./types";
import {InfoCircleOutlined} from "@ant-design/icons";
import brSample from "@/assets-new/images/br-sample.png";
import {ENGLISH_CHARS_AND_SYMBOLS_PATTERN, SWIFT_CODE_PATTERN} from "@/utils/formValidation";
import {useMatomoContext} from "@/contexts/MatomoContext";
import {MATOMO_CONFIG} from "@/utils/matomoConfig";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants";

interface DrawdownAccountSectionProps {
    formRef?: MutableRefObject<any>;
    initialValues?: Partial<DrawdownAccountFormData>;
    onChange?: (values: Partial<DrawdownAccountFormData>) => void;
}

const DrawdownAccountSection: React.FC<DrawdownAccountSectionProps> = ({formRef, initialValues = {}, onChange}) => {
    const {trackEventWithDimensions} = useMatomoContext();
    const {loading, getCountryLabel, companyConfig} = useConfig();
    const [form] = Form.useForm<DrawdownAccountFormData>();
    const [selectedCountry, setSelectedCountry] = React.useState<string | undefined>(
        initialValues.countryRegion ? COUNTRY_ID_TO_LEGACY_CODE[initialValues.countryRegion] : undefined
    );
    const selectedBankKey = Form.useWatch('bank', form);

    const hasPrefilled = useRef(false);
    const hasCapturedOriginalDrawdown = useRef(false);
    const hasCapturedInitials = useRef(false);
    const initialRef = useRef<Partial<DrawdownAccountFormData>>({});
    const originalDrawdownRef = useRef<{ swiftCode?: string; bankAddress?: string; bank?:string }>({});

    useEffect(() => {
        if (!hasCapturedInitials.current && Object.values(initialValues)
            .some(val => val !== "" && val !== undefined && val !== null)) {
            initialRef.current = initialValues;
            hasCapturedInitials.current = true;
        }
    }, [initialValues]);

    // Set form reference if provided
    useEffect(() => {
        if (formRef) {
            formRef.current = form;
        }
    }, [form, formRef]);

    // Update form values when initialValues changes
    useEffect(() => {
        if (Object.keys(initialValues).length > 0) {
            form.setFieldsValue(initialValues);

            // Update selectedCountry if countryRegion exists
            if (initialValues.countryRegion) {
                const legacyCode = COUNTRY_ID_TO_LEGACY_CODE[initialValues.countryRegion];
                setSelectedCountry(legacyCode);
            }
        }
    }, [initialValues, form]);

    useEffect(() => {
        if (hasPrefilled.current) return;
        if (!initialValues.bank || !companyConfig?.bank_account || !initialValues) return;

        const byId = companyConfig.bank_account.find(
            b => b.key_id.toString() === initialValues.bank?.toString()
        );
        if (byId) {
            form.setFieldsValue({bank: byId.key_id.toString()});
        } else {
            const byName = companyConfig.bank_account.find(
                b => b.name === initialValues.bank
            );
            if (byName) {
                form.setFieldsValue({bank: byName.key_id.toString()});
            } else {
                form.setFieldsValue({
                    bank: "other",
                    other_bank_name: initialValues.bank,
                });
            }
        }

        hasPrefilled.current = true;
    }, [initialValues.bank, companyConfig, form]);

    useEffect(() => {
        if (hasPrefilled.current && !hasCapturedOriginalDrawdown.current) {
            originalDrawdownRef.current = {
                swiftCode: form.getFieldValue("swiftCode"),
                bankAddress: form.getFieldValue("bankAddress"),
            };
            hasCapturedOriginalDrawdown.current = true;
        }
    }, [hasPrefilled.current, form]);

    const currencyOptions = [{value: "USD", label: "USD"}];

    const handleCountryChange = (value: string) => {
        setSelectedCountry(value);
    };

    // Function to convert API key_id back to legacy codes for internal logic
    const getLegacyCountryCode = (keyId: string): string => {
        return COUNTRY_ID_TO_LEGACY_CODE[keyId] || keyId;
    };

    const handleFormChange = (_: any, allValues: DrawdownAccountFormData) => {
        if (_.countryRegion) {
            const legacyCode = getLegacyCountryCode(_.countryRegion);
            handleCountryChange(legacyCode);
        }

        if (onChange) {
            onChange(allValues);
        }
    };

    useEffect(() => {
        if (!hasPrefilled.current) return;
        if (!selectedBankKey) return;
        if (selectedBankKey === "other") {
            // restore exactly once, then blank thereafter
            form.setFieldsValue({
                swiftCode: originalDrawdownRef.current.swiftCode ?? "",
                bankAddress: originalDrawdownRef.current.bankAddress ?? "",
                other_bank_name: originalDrawdownRef.current.bank ?? "",
            });
            // clear the “original” so future “Other” clicks are pure blank
            originalDrawdownRef.current.swiftCode = undefined;
            originalDrawdownRef.current.bankAddress = undefined;
        } else {
            const bank = companyConfig?.bank_account?.find(
                b => b.key_id.toString() === selectedBankKey
            );
            if (bank) {
                form.setFieldsValue({
                    swiftCode: bank.swift ?? undefined,
                    bankAddress: bank.address ?? undefined,
                    other_bank_name: undefined,
                });
            }
        }
    }, [selectedBankKey, companyConfig, form]);

    return (
        <div className="drawdown-account-section">
            <Form<DrawdownAccountFormData>
                form={form}
                layout="vertical"
                initialValues={initialValues}
                onValuesChange={handleFormChange}
            >
                <div className="section">
                    <div className="section-title-container">
                        <h4 className="section-title">公司信息</h4>
                        {selectedCountry === LegacyCountryCode.HONG_KONG && (
                            <InfoBanner type="note" message="特快申请：推荐使用香港公司" width="501px"/>
                        )}
                        {selectedCountry === LegacyCountryCode.CHINA && (
                            <InfoBanner type="default" message="特快申请：推荐使用香港公司" width="501px"/>
                        )}
                    </div>
                    <div className="form-row">
                        <Form.Item
                            name="countryRegion"
                            label="国家及地区"
                            rules={[{required: true, message: "请选择国家及地区"}]}
                            className="half-width-item"
                        >
                            <ConfigDataSelect
                                type={ConfigType.COUNTRY_REGION}
                                placeholder="请选择"
                                disabled={loading}
                                onChange={(value) => {
                                    trackEventWithDimensions({
                                        category: TRACKING_CATEGORIES.FORM,
                                        action: TRACKING_ACTIONS.SELECT,
                                        name: TRACKING_EVENTS.SELECT_COUNTRY_REGION,
                                        customDimensions: {
                                            [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: getCountryLabel(value)
                                        }
                                    });
                                }}
                            />
                        </Form.Item>
                    </div>

                    {selectedCountry && (
                        <div className="form-row">
                            {selectedCountry === LegacyCountryCode.CHINA && (
                                <Form.Item
                                    name="companyNameCn"
                                    label="公司注册名称 (中文)"
                                    rules={[{required: true, message: "请输入公司注册名称"}]}
                                    className="half-width-item"
                                >
                                    <Input
                                        placeholder="请输入"
                                        onBlur={e => {
                                            trackEventWithDimensions({
                                                category: TRACKING_CATEGORIES.FORM,
                                                action: TRACKING_ACTIONS.INPUT,
                                                name: TRACKING_EVENTS.INPUT_COMPANY_NAME_CN,
                                                customDimensions: {
                                                    [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                                }
                                            });
                                        }}
                                    />
                                </Form.Item>
                            )}

                            {selectedCountry === LegacyCountryCode.HONG_KONG && (
                                <Form.Item
                                    name="companyNameEn"
                                    label="公司注册名称 (英文)"
                                    rules={[
                                        {required: true, message: "请输入公司注册名称"},
                                        {
                                            pattern: ENGLISH_CHARS_AND_SYMBOLS_PATTERN,
                                            message: "请输入有效的公司注册名称"
                                        }
                                    ]}
                                    className="half-width-item"
                                >
                                    <Input
                                        placeholder="请输入"
                                        onBlur={e => {
                                            trackEventWithDimensions({
                                                category: TRACKING_CATEGORIES.FORM,
                                                action: TRACKING_ACTIONS.INPUT,
                                                name: TRACKING_EVENTS.INPUT_COMPANY_NAME_EN,
                                                customDimensions: {
                                                    [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                                }
                                            });
                                        }}
                                    />
                                </Form.Item>
                            )}

                            {selectedCountry === LegacyCountryCode.HONG_KONG && (
                                <Form.Item
                                    name="businessRegistrationNumber"
                                    label={
                                        <span>
                                            商业登记号码 (BRN)
                                            <Tooltip
                                                title={
                                                    <div className="brn-tooltip-image">
                                                        <img
                                                            src={brSample}
                                                            alt="BRN Example"
                                                            className="brn-sample-image"
                                                            width="320"
                                                            style={{maxWidth: "100%"}}
                                                        />
                                                    </div>
                                                }
                                                overlayStyle={{maxWidth: "350px"}}
                                                color="#fff"
                                                placement="top"
                                            >
                                                <InfoCircleOutlined className="info-icon"/>
                                            </Tooltip>
                                        </span>
                                    }
                                    rules={[
                                        {required: true, message: "请输入商业登记号码"},
                                        {
                                            pattern: /^\d{8}$/,
                                            message: "请输入8位数字"
                                        }
                                    ]}
                                    className="half-width-item"
                                >
                                    <InputNumber
                                        placeholder="请输入"
                                        className="full-width-input"
                                        min={0}
                                        controls={false}
                                        onBlur={e => {
                                            trackEventWithDimensions({
                                                category: TRACKING_CATEGORIES.FORM,
                                                action: TRACKING_ACTIONS.INPUT,
                                                name: TRACKING_EVENTS.INPUT_BRN,
                                                customDimensions: {
                                                    [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                                }
                                            });
                                        }}
                                    />
                                </Form.Item>
                            )}
                        </div>
                    )}
                </div>

                {selectedCountry === LegacyCountryCode.HONG_KONG && (
                    <div className="section section-spacing">
                        <div className="section-title-container">
                            <h4 className="section-title">首次支用信息</h4>
                            <InfoBanner
                                type="notice"
                                message="请确保以下同名银行账户 (或空中云汇、连连支付、寻汇的同名虚拟香港银行账户）能接收美金转账"
                                width="auto"
                            />
                        </div>
                        <div className="form-row">
                            <div className="drawdown-amount-container">
                                <CurrencyAmountInput
                                    currencyName="drawdownCurrency"
                                    amountName="drawdownAmount"
                                    currencyOptions={currencyOptions}
                                    currencyRules={[{required: true, message: "请选择币种"}]}
                                    amountRules={[
                                        {required: true, message: "请输入支用金额"},
                                        {type: "number", min: 50, message: "最低支用金額：USD 50.00"},
                                        {type: "number", max: 2000, message: "最高支用金額：USD 2,000.00"},
                                        {
                                            pattern: /^\d+(\.\d{0,2})?$/,
                                            message: "只能输入两位小数"
                                        }
                                    ]}
                                    amountPlaceholder="最小支用金额：USD50.00"
                                    label="支用金额"
                                    className="half-width-item"
                                    onAmountBlur={(value) => {
                                        const currency = form.getFieldValue("drawdownCurrency");
                                        const combinedValue = currency && value ? `${currency} ${value}` : "";
                                        if (combinedValue) {
                                            trackEventWithDimensions({
                                                category: TRACKING_CATEGORIES.FORM,
                                                action: TRACKING_ACTIONS.INPUT,
                                                name: TRACKING_EVENTS.INPUT_DRAWDOWN_AMOUNT,
                                                customDimensions: {
                                                    [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: combinedValue
                                                }
                                            });
                                        }
                                    }}
                                />
                                <div className="interest-rate-info">
                                    <div className="interest-rate-text">
                                        日利率约0.00% (借1万用1天约0美元) 年利息：0% 贷款限期：7天
                                    </div>
                                    <div className="interest-rate-text">
                                        日利率约0.039% (借1万用1天约3.9美元) 年利息： SOFR+10% 贷款限期：90天
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="form-row">
                            <Form.Item
                                name="bank"
                                label="银行名称"
                                rules={[{required: true, message: "请选择银行名称"}]}
                                className="half-width-item"
                            >
                                <ConfigDataSelect
                                    showSearch
                                    type={ConfigType.BANK_ACCOUNT}
                                    placeholder="请选择"
                                    disabled={loading}
                                    onChange={(value) => {
                                        const bankName = companyConfig?.bank_account?.find(
                                            b => b.key_id.toString() === value
                                        )?.name || value;
                                        trackEventWithDimensions({
                                            category: TRACKING_CATEGORIES.FORM,
                                            action: TRACKING_ACTIONS.SELECT,
                                            name: TRACKING_EVENTS.SELECT_BANK,
                                            customDimensions: {
                                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: bankName
                                            }
                                        });
                                    }}
                                />
                            </Form.Item>

                            {selectedBankKey === "other" && (
                                <Form.Item
                                    name="other_bank_name"
                                    label="如其他, 请填写(英文)"
                                    rules={[
                                        {required: true, message: "请输入银行账户名称 (英文)"},
                                        {
                                            pattern: ENGLISH_CHARS_AND_SYMBOLS_PATTERN,
                                            message: "请输入正确的英文银行名称",
                                        },
                                    ]}
                                    className="half-width-item"
                                >
                                    <Input 
                                        placeholder="请输入"
                                        onBlur={e => {
                                            trackEventWithDimensions({
                                                category: TRACKING_CATEGORIES.FORM,
                                                action: TRACKING_ACTIONS.INPUT,
                                                name: TRACKING_EVENTS.INPUT_OTHER_BANK_NAME,
                                                customDimensions: {
                                                    [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                                }
                                            });
                                        }}
                                    />
                                </Form.Item>
                            )}

                            <Form.Item
                                name="bankAccountName"
                                label="银行账户名称 (英文)"
                                rules={[
                                    {required: true, message: "请输入银行账户名称 (英文)"},
                                    {
                                        pattern: ENGLISH_CHARS_AND_SYMBOLS_PATTERN,
                                        message: "请输入有效的银行账户名称 (英文)"
                                    },
                                    {
                                        validator: (_, value) => {
                                            const companyNameEn = form.getFieldValue("companyNameEn");
                                            if (value && companyNameEn && value.trim() !== companyNameEn.trim()) {
                                                return Promise.reject(new Error("银行账户名称需与公司注册名称一致"));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                                dependencies={["companyNameEn"]}
                                className="half-width-item"
                            >
                                <Input 
                                    placeholder="请输入"
                                    onBlur={e => {
                                        trackEventWithDimensions({
                                            category: TRACKING_CATEGORIES.FORM,
                                            action: TRACKING_ACTIONS.INPUT,
                                            name: TRACKING_EVENTS.INPUT_BANK_ACCOUNT_NAME,
                                            customDimensions: {
                                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                            }
                                        });
                                    }}
                                />
                            </Form.Item>

                            <Form.Item
                                name="bankAccountNumber"
                                label="银行账户号码"
                                rules={[
                                    {required: true, message: "请输入银行账户号码"},
                                    {
                                        pattern: /^\d{1,19}$/,
                                        message: "请输入正确的银行号码，最多19位"
                                    }
                                ]}
                                className="half-width-item"
                            >
                                <Input
                                    type="text"
                                    placeholder="请输入"
                                    maxLength={19}
                                    className="full-width-input"
                                    onChange={e => {
                                        const value = e.target.value.replace(/\D/g, "");
                                        form.setFieldsValue({bankAccountNumber: value});
                                    }}
                                    onBlur={e => {
                                        trackEventWithDimensions({
                                            category: TRACKING_CATEGORIES.FORM,
                                            action: TRACKING_ACTIONS.INPUT,
                                            name: TRACKING_EVENTS.INPUT_BANK_ACCOUNT_NUMBER,
                                            customDimensions: {
                                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                            }
                                        });
                                    }}
                                    onKeyDown={e => {
                                        // Allow only numbers, backspace, delete, tab, arrows
                                        const isNumberKey = /^\d$/.test(e.key);
                                        const isAllowedKey = [
                                            "Backspace",
                                            "Delete",
                                            "Tab",
                                            "ArrowLeft",
                                            "ArrowRight",
                                            "ArrowUp",
                                            "ArrowDown"
                                        ].includes(e.key);

                                        if (!isNumberKey && !isAllowedKey) {
                                            e.preventDefault();
                                        }
                                    }}
                                />
                            </Form.Item>

                            <Form.Item
                                name="bankAddress"
                                label="银行地址 (英文)"
                                rules={[
                                    {required: true, message: "请输入银行地址 (英文)"},
                                    {
                                        pattern: ENGLISH_CHARS_AND_SYMBOLS_PATTERN,
                                        message: "请输入有效的银行地址 (英文)"
                                    }
                                ]}
                                className="half-width-item"
                            >
                                <Input 
                                    placeholder="请输入"
                                    onBlur={e => {
                                        trackEventWithDimensions({
                                            category: TRACKING_CATEGORIES.FORM,
                                            action: TRACKING_ACTIONS.INPUT,
                                            name: TRACKING_EVENTS.INPUT_BANK_ADDRESS,
                                            customDimensions: {
                                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                            }
                                        });
                                    }}
                                />
                            </Form.Item>

                            <Form.Item
                                name="swiftCode"
                                label="SWIFT代码"
                                rules={[
                                    {required: true, message: "请输入SWIFT代码"},
                                    {
                                        pattern: SWIFT_CODE_PATTERN,
                                        message: "请输入有效的SWIFT代码"
                                    }
                                ]}
                                className="half-width-item"
                            >
                                <Input
                                    className="swift-code-input"
                                    placeholder="请输入"
                                    onChange={e => {
                                        const value = e.target.value.toUpperCase();
                                        form.setFieldsValue({swiftCode: value});
                                    }}
                                    onBlur={e => {
                                        trackEventWithDimensions({
                                            category: TRACKING_CATEGORIES.FORM,
                                            action: TRACKING_ACTIONS.INPUT,
                                            name: TRACKING_EVENTS.INPUT_SWIFT_CODE,
                                            customDimensions: {
                                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: e.target.value
                                            }
                                        });
                                    }}
                                />
                            </Form.Item>
                        </div>
                        <NoHKBank/>
                    </div>
                )}
            </Form>
        </div>
    );
};

export default DrawdownAccountSection;
