.drawdown-account-section {
  width: 100%;
  
  .section {
    margin-bottom: 20px;
    
    &.section-spacing {
      margin-top: 40px;
    }
    
    .section-title-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      gap: 16px;
      height: 32px;
      
      .section-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
        height: auto;
      }
      @media (max-width: 768px) { // smaller than md
        display: block;
        height: auto;
      }
    }
    
    .form-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 24px;
      
      .ant-form-item {
        margin-bottom: 0;
        position: relative;
        
        /* Fix for error message positioning */
        .ant-form-item-explain, .ant-form-item-extra {
          position: absolute;
          bottom: -22px;
          left: 0;
          min-height: 20px;
          z-index: 5;
        }
        
        /* Ensure specific spacing for form labels */
        .ant-form-item-label {
          padding-bottom: 8px;
        }
        
        // MDC-compliant styling for SWIFT code input
        .swift-code-input {
          text-transform: uppercase;
          
          input {
            text-transform: uppercase;
          }
        }
      }
      
      .half-width-item {
        width: calc(50% - 8px);
        @media (max-width: 576px) { // smaller than sm
          width: 100%;
        }
      }

      // New classes to replace inline styles
      .full-width-input {
        width: 100%;
      }

      .info-icon {
        margin-left: 5px;
        color: #6e6e75;
        cursor: pointer;
      }

      .brn-tooltip-image {
        text-align: center;
        padding: 0;
        margin: 0;

        .brn-sample-image {
          max-width: 100%;
          display: block;
          height: auto;
          margin: 0 auto;
        }
      }

      .drawdown-amount-container {
        display: flex;
        align-items: flex-start;
        width: 100%;

        .interest-rate-info {
          margin-left: 8px;
          position: relative;
          top: 28px; /* Position exactly at input level */

          .interest-rate-text {
            font-size: 12px;
            color: #9E9EA3;
            font-weight: 400;
            line-height: 18px;
            margin-bottom: 4px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
        @media (max-width: 576px) { // smaller than sm
          display: block;
        }
      }
    }
  }
} 

// MDC-compliant tooltip styling to ensure image is displayed properly
:global {
  .ant-tooltip {
    max-width: 350px !important;
  }
  
  .ant-tooltip-inner {
    padding: 8px;
    max-width: none !important;
    width: auto;
    overflow: visible;
  }
  
  .ant-tooltip-arrow {
    display: block;
  }
} 