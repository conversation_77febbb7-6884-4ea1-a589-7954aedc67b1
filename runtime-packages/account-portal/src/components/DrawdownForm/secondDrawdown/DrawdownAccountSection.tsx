import React, { useEffect, useState, MutableRefObject } from "react";
import { Form, Select, Input } from "antd";
import { useConfig } from "@/contexts/ConfigContext";
import "./DrawdownAccountSection.scss";
import CurrencyAmountInput from "../../shared/CurrencyAmountInput";
import InfoBanner from "../../shared/InfoBanner";
import { DrawdownAccountFormData } from "./types";
import { getInterestRate, getValidBankList } from "@fundpark/fp-api";
import { useRedPocketStore } from "@/store/redpocket";

const { Option } = Select;

interface DrawdownAccountSectionProps {
    formRef?: MutableRefObject<any>;
    initialValues?: Partial<DrawdownAccountFormData>;
    onChange?: (values: Partial<DrawdownAccountFormData>) => void;
    limit: number | null;
}

function formatNumber(number: number | string): string {
    if (typeof number !== "number") {
        return "0.00";
    }

    return number.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

const interestCalculator = (interest_rate, amount, sofrRate, tenorDay) => {
    var interest = amount * (((Number(interest_rate) + sofrRate) / 100.0 / 360) * tenorDay);
    return interest;
};

const DrawdownAccountSection: React.FC<DrawdownAccountSectionProps> = ({
    formRef,
    initialValues = {},
    onChange,
    limit
}) => {
    const { loading } = useConfig();
    const [form] = Form.useForm();
    const [formData, setFormData] = useState(initialValues);
    const [showExpectedResult, setShowExpectedResult] = useState(false);

    const [expectedInterest, setExpectedInterest] = useState(0);
    const [redPocketAmount, setRedPocketAmount] = useState(0);
    const [redpocketWaive, setRedpocketWaive] = useState(0);

    const [validBankAccountList, setValidBankAccountList] = useState<any[]>([]);
    const [interestRate, setInterestRate] = useState(0);
    const [sofrRate, setSofrRate] = useState(0);
    const [tenorDays, setTenorDays] = useState(0);
    const { redPockets, getRedPocketByType, fetchRedPocketList } = useRedPocketStore();
    const [minDrawdown, SetMinDrawdown] = useState(0);
    const [annualInterestRate, SetAnnualInterestRate] = useState("");
    const validLimit = Number(limit);

    useEffect(() => {
        
        const claimedRedPocket = getRedPocketByType("deduction");
        if (claimedRedPocket?.status === "claimed" && claimedRedPocket?.can_use) {
            setRedPocketAmount(Number(claimedRedPocket.value));
        } else {
            setRedPocketAmount(0);
        }
    }, [redPockets,getRedPocketByType]);

    useEffect(()=>{
        fetchRedPocketList();
    },[])

    useEffect(() => {
        console.log(redpocketWaive, "redpocketWaiveredpocketWaive");
    }, [redpocketWaive]);

    useEffect(() => {
        (async () => {
            let response = await getValidBankList();
            // response = {
            //     code: 0,
            //     data: [
            //         {
            //             id: 1380,
            //             bank_name: "STANDARD CHARTERED BANK (HONG KONG) LIMITED",
            //             bank_account_name: "\u4e2d\u56fd\u94f6\u884c",
            //             bank_account_number: "123456543234567654",
            //             bank_account_currency: "USD",
            //             bank_address: "\u4e2d\u56fd\u94f6\u884c\u6df1\u5733\u9f99\u534e\u652f\u884c",
            //             swift: "345643",
            //             bank_country_code: ""
            //         }
            //     ],
            //     message: "success"
            // };

            if (response.code == 0 && response.data) {
                setValidBankAccountList(response.data);
            }
            console.log("response in", validBankAccountList);
            console.log("limit", limit);
        })();
    }, []);

    useEffect(() => {
        (async () => {
            let response = await getInterestRate();
            // // response = {
            // //     status: 0,
            // //     data: {
            // //         interest_rate: 15,
            // //         sofr_rate: 4.28,
            // //         tenor_day: 90,
            // //         date: "2025-05-19",
            // //         currency: "USD",
            // //         product_name: "WRB",
            // //         floating_rate_type: 1,
            // //         minimum_drawdown_amount: 2000
            // //     },
            // //     message: "success"
            // // };

            // console.log(response);

            if (response.code === 0) {
                setInterestRate(response.data.interest_rate);
                setSofrRate(response.data.sofr_rate);
                setTenorDays(response.data.tenor_day);
                SetMinDrawdown(response.data.minimum_drawdown_amount);

                if (response.data.floating_rate_type === 1 || response.data.floating_rate_type === 2) {
                    SetAnnualInterestRate(`SOFR+ ${response.data.interest_rate} %`);
                } else if (response.data.floating_rate_type === 0) {
                    SetAnnualInterestRate(`${response.data.interest_rate} %`);
                }
            }
        })();
    }, []);

    useEffect(() => {
        try {
            let newExpectedInterest = interestCalculator(interestRate, formData.drawdownAmount, sofrRate, tenorDays);
            console.log(redPocketAmount, newExpectedInterest);

            if (redPocketAmount >= newExpectedInterest) {
                setRedpocketWaive(newExpectedInterest);
            } else {
                setRedpocketWaive(redPocketAmount);
            }

            setExpectedInterest(newExpectedInterest);
        } catch (error) {
            console.error("repaymentCalculator error:", error);
        }

        setShowExpectedResult(formData.drawdownAmount >= minDrawdown);
    }, [formData.drawdownAmount, redPocketAmount]);

    // Set form reference if provided
    useEffect(() => {
        if (formRef) {
            formRef.current = form;
        }
    }, [form, formRef]);

    // Update form values when initialValues changes
    useEffect(() => {
        console.log("initialValues changed in DrawdownAccountSection:", initialValues);
        if (Object.keys(initialValues).length > 0) {
            // Reset fields and set values
            form.setFieldsValue(initialValues);

            // Update local form data
            setFormData(initialValues);
        }
    }, [initialValues, form]);

    const currencyOptions = [{ value: "USD", label: "USD" }];

    const handleFormChange = (changedValues: any, allValues: any) => {
        setFormData(allValues);

        if (onChange) {
            onChange(allValues);
        }
    };

    return (
        <div className="drawdown-account-section">
            <Form form={form} layout="vertical" initialValues={initialValues} onValuesChange={handleFormChange}>
                <div className="section section-spacing">
                    <div className="section-title-container">
                        <h4 className="section-title">用款信息</h4>
                        <InfoBanner
                            type="notice"
                            message="请确保以下同名银行账户 (或空中云汇、连连支付、寻汇的同名虚拟香港银行账户）能接收美金转账"
                            width="auto"
                        />
                    </div>
                    <div className="form-row">
                        <div className="drawdown-amount-container ">
                            <CurrencyAmountInput
                                currencyName="drawdownCurrency"
                                amountName="drawdownAmount"
                                currencyOptions={currencyOptions}
                                currencyRules={[{ required: true, message: "请选择币种" }]}
                                amountRules={[
                                    { required: true, message: "请输入支用金额" },
                                    {
                                        type: "number",
                                        min: minDrawdown,
                                        message: `最低支用金額：USD ${formatNumber(minDrawdown)}`
                                    },
                                    {
                                        pattern: /^\d+(\.\d{0,2})?$/,
                                        message: "只能输入两位小数"
                                    },
                                    {
                                        type: "number",
                                        max: validLimit,
                                        message: `最大支用金額：USD ${formatNumber(validLimit)}`
                                    }
                                ]}
                                amountPlaceholder={`最低支用金額：USD ${formatNumber(minDrawdown)}`}
                                label="支用金额"
                                className="half-width-item"
                            />
                            <div className="interest-rate-info">
                                <div className="interest-rate-text" style={{ marginTop: "12px" }}>
                                    日利率约0.039% (借1万用1天约3.9美元) 年利率：{annualInterestRate} 贷款限期：
                                    {tenorDays}天
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="form-row">
                        <Form.Item
                            name="bankId"
                            label="收款银行账户"
                            rules={[{ required: true, message: "请选择银行名称" }]}
                            className="half-width-item"
                        >
                            <Select placeholder={"请选择"} showSearch optionFilterProp="label">
                                {validBankAccountList.map(account => (
                                    <Option key={account.id} value={account.id} label={account.bank_account_name}>
                                        {`(${account.bank_account_currency})(${account.bank_name} ${account.bank_account_number}) ${account.bank_account_name} `}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>
                    </div>

                    <div className="form-row">
                        <Form.Item name="refNumber" label="参考号码（选填）" className="half-width-item">
                            <Input placeholder="请输入" />
                        </Form.Item>
                    </div>
                </div>
            </Form>

            <div>
                <span style={{ fontSize: "16px", fontWeight: "bold" }}>还款计算机</span>
                <div
                    className="repayment-calculator"
                    style={{
                        padding: "20px",
                        alignItems: "center",
                        backgroundColor: "#F6F7FA",
                        borderRadius: "20px",
                        marginTop: "24px",
                        marginBottom: "24px",
                        fontSize: "20px",
                        fontWeight: "bold"
                    }}
                >
                    <span style={{ color: "black", fontSize: "16px" }}>预计截至到期日还款：</span>

                    {showExpectedResult && (
                        <>
                            <div style={{ color: "#2463EB" }}>
                                <span>本金 $</span>
                                <span>{formatNumber(formData.drawdownAmount)}</span>
                            </div>

                            <span>&nbsp;&nbsp;+&nbsp;&nbsp; </span>
                            <div style={{ color: "#BF79DF" }}>
                                <span>利息 $</span>
                                <span>{formatNumber(expectedInterest)}</span>
                            </div>

                            {redpocketWaive>0 && (
                                <>
                                    <span>-</span>
                                    <div style={{ color: "#DD4C4C" }}>
                                        <span>红包抵扣 $</span>
                                        <span>{formatNumber(redpocketWaive)}</span>
                                    </div>
                                </>
                            )}

                            <span>&nbsp;&nbsp;=&nbsp;&nbsp;</span>
                            <span>{formatNumber(formData.drawdownAmount + expectedInterest - redpocketWaive)}</span>
                        </>
                    )}
                </div>
                <div style={{ color: "#6E6E75" }}>
                    上述计算结果只供参考。丰泊国际并不会为该等资料的准确性，可靠性及完整性和/或针对某特定用途的适用性作出任何保证或及陈述。
                    是次支用申请以丰泊国际本公司最终评估和批准审核为准，贷款确认函之条款及细则均适用于本次支用申请。
                </div>
                <div style={{ color: "#6E6E75", marginTop: "24px", marginBottom: "24px" }}>
                    适用于利息按照有抵押隔夜融资利率（SOFR）：
                    <br />
                    利息按照有抵押隔夜融资利率（SOFR）（由纽约联邦储备银行管理和发表的有抵押隔夜融资利率（newyorkfed.org））以单利形式依照每年360天为计算基础根据实际天数每天累计，并基于五
                    (5) 个工作日的回溯期相关利率。
                    <br />
                    具体而言，SOFR并非预先指定或固定的利率，而每天累计的利息是基于每天获取的SOFR利率，而非平均計算的SOFR利率。针对回溯期以外的利息是基于不能知悉SOFR的情况下计算出来，相关计算结果会视为粗略估算和近似计算。
                </div>
            </div>
        </div>
    );
};

export default DrawdownAccountSection;
