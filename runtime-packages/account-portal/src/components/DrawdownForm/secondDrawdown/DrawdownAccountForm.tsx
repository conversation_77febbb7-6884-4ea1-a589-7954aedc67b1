import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/components/shared/Button";
import Stepper from "@/components/shared/Stepper";
import DrawdownAccountSection from "./DrawdownAccountSection";
import { UnsupportedRegionModal } from "../UnsupportedRegionModal";
import { DrawdownAccountFormData } from "./types";
import "./DrawdownAccountForm.scss";
import "../styles.scss";
import { useNavigate } from "react-router-dom";
import { usesecondDrawdownFormStore } from "@/store/secondDrawdown";
import SecondaryButton from "@/components/shared/SecondaryButton";
import { useRedPocketStore } from "@/store/redpocket";
import { Grid } from "antd";
interface DrawdownAccountFormProps {
    limit: number | null;
}

const DrawdownAccountForm: React.FC<DrawdownAccountFormProps> = ({ limit }) => {
    const screens = Grid.useBreakpoint();
    const navigate = useNavigate();
    const [formData, setFormData] = useState<DrawdownAccountFormData>({
        drawdownCurrency: "USD",
        drawdownAmount: "",
        bankId: null,
        referenceNumber: ""
    });
    const [loading, setLoading] = useState(false);
    const formRef = useRef<any>(null);
    const [showUnsupportedRegionModal, setShowUnsupportedRegionModal] = useState(false);
    const { fetchRedPocketList, redPockets, getRedPocketByType } = useRedPocketStore();

    // Debug: Log formData on change
    useEffect(() => {
        console.log("Current form data:", formData);

        // If form ref exists, try to set values directly
        if (formRef.current && Object.keys(formData).length > 0) {
            console.log("Setting form fields on formData change");
            formRef.current.setFieldsValue(formData);
        }
    }, [formData]);

    const handleChange = (values: Partial<DrawdownAccountFormData>) => {
        console.log("Form values changed:", values);
        setFormData({
            ...formData,
            ...values
        });
    };
    const handleNext = async () => {
        if (formRef.current) {
            try {
                const values = await formRef.current.validateFields();
                await fetchRedPocketList();

                try {
                    setLoading(true);
                    usesecondDrawdownFormStore.getState().setFormData(values);

                    navigate("/credit/hook/terms-and-condition");
                } catch (error) {
                    console.error("API error when submitting debit account:", error);
                } finally {
                    setLoading(false);
                }
            } catch (error) {
                console.error("Validation failed:", error);
            }
        }
    };

    // Define steps for the stepper
    const steps = [
        {
            title: "用款信息",
            content: (
                <DrawdownAccountSection
                    formRef={formRef}
                    initialValues={formData}
                    onChange={handleChange}
                    limit={limit}
                />
            ),
            action: (
                <div className="flex flex-col items-center">
                    <div className={screens.md ? "flex items-center space-x-24": "grid grid-cols-1 gap-4"}>
                        <SecondaryButton
                            onClick={() => navigate("/")}
                            style={screens.md ? { width: 160, backgroundColor: "white", height: 48 }: {width: 160, margin: "0"}}
                            label={"返回"}
                        ></SecondaryButton>

                        <Button
                            type="primary"
                            onClick={handleNext}
                            style={screens.md ? { width: 160 }: {width: 160, margin: "0"}}
                            loading={loading}
                            label={<span>提交支用</span>}
                        ></Button>
                    </div>
                </div>
            )
        },
        {
            title: "放款",
            content: <></>,
            action: null
        }
    ];

    return (
        <div className="drawdown-account-form">
            <Stepper steps={steps} currentStep={0} borderTopRadiusRounded={false} />
            <UnsupportedRegionModal
                open={showUnsupportedRegionModal}
                onClose={() => setShowUnsupportedRegionModal(false)}
            />
        </div>
    );
};

export default DrawdownAccountForm;
