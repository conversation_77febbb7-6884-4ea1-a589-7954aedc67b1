import React, { useEffect } from "react";
import { Modal } from "@/components/shared/Modal";
import AlertIcon from "@/assets-new/icons/alert-icon.svg?react";
import "./index.scss";
import PrimaryButton from "../shared/PrimaryButton";
import { deleteNotification, getCompanyNotificationsList } from "@fundpark/fp-api";
import { useNavigate } from "react-router-dom";
import SecondaryButton from "../shared/SecondaryButton";
import { useSelectedProduct } from "@/hooks/useSelectedProduct";
import appHelper from "@/utils/appHelper";

export interface AuthGuideModalProps {
    open: boolean;
    textAlign?: "left" | "center" | "right";
    width?: number;
    canClose: boolean;
    onClose: () => void;
    notificationId?: number;
    thirdPartyCheckResultFailed:boolean;
    controlCheckResultFailed:boolean

}

export const AuthGuideModal: React.FC<AuthGuideModalProps> = ({
    open,
    textAlign = "left",
    width = 535,
    canClose = true,
    onClose,
    notificationId,
    controlCheckResultFailed,
    thirdPartyCheckResultFailed
}) => {
    const navigate = useNavigate();
    const product = useSelectedProduct();

    const refreshNotifications = async () => {
        try {
            // get notifications
            const noti_res = await getCompanyNotificationsList({ per_page: 1 });
            if (noti_res.message === "success") {
                console.log("called notifications", noti_res.data.data);
                appHelper.setNotifications(noti_res.data.data);
            }
        } catch (error) {
            console.error("Failed to retrieve notifications.", error);
        }
    };
    const authGuideModalOnHanlde = () => {
        let direction;

        if (product === "xdj") {
            direction = "hook";
        } else if (product === "credit-approval-automation") {
            direction = "underwriting";
        }

        const targetPath = `/credit/${direction}/activate-shop/`;
        onClose();
        navigate(targetPath, { replace: true });
    };

    const finishedHandle = async () => {
        const res = await deleteNotification({ id: parseInt(notificationId) });
        console.log(res);
        refreshNotifications();
        window.location.reload();
        onClose();
    };

    const renderMessage = () => {
        const messageContent = (
            <div className={`message-content text-${textAlign}`} style={{ fontSize: "18px" }}>
                <p>原因如下：</p>
                <ul style={{ paddingLeft: "2em" }}>
                    {" "}
                    {controlCheckResultFailed && <li>您的店铺未绑定在用收款支付账户</li>}
                    {thirdPartyCheckResultFailed && <li>检测到第三方在贷</li>}
                </ul>
                <br />
                <p>请前往授权店铺页面，编辑更新您的收款公司信息，重新发起校验。</p>
            </div>
        );

        return messageContent;
    };

    const title = (
        <>
            <AlertIcon />
            很抱歉，您的授信审核未通过
        </>
    );

    const justifyClass = {
        left: "justify-start",
        center: "justify-center",
        right: "justify-end"
    }[textAlign];

    return (
        <Modal open={open} onClose={onClose} title={title} width={width} closable={canClose}>
            <div>
                <div className={`mb-4 flex ${justifyClass}`}>{renderMessage()}</div>

                <div className="flex justify-center mt-10 space-x-5">
                    {notificationId && (
                        <SecondaryButton
                            style={{ fontWeight: 450, fontSize: "16px" }}
                            label="已处理,不再显示"
                            onClick={finishedHandle}
                        />
                    )}
                    <PrimaryButton label="去处理" onClick={authGuideModalOnHanlde} />
                </div>
            </div>
        </Modal>
    );
};
