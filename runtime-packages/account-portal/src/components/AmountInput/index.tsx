import { InputNumber } from "antd";
import type { InputNumberProps } from "antd";

const AmountInput: React.FC<InputNumberProps> = props => {
    return (
        <InputNumber
            {...props}
            formatter={value => String(value ?? "").replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
            parser={value => value?.replace(/\$\s?|(,*)/g, "") as unknown as number}
        />
    );
};

export default AmountInput;
