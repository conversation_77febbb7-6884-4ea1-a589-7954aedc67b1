import React, { useState } from "react";
import { Select, message, Card } from "antd";
import PrimaryButton from "../shared/PrimaryButton";
import EditedPrimaryButton from "../shared/EditedPrimaryButton";
import ReportIcon from "@/assets-new/icons/common/coin-hand.svg";
import { Link } from "react-router-dom";
import { url } from "inspector";
import { title } from "process";

interface CategoryReportProps {}
const MAX_COUNT = 3;
interface ReportItem {
    id: number;
    url: string;
    title: string;
  }

const CategoryReport: React.FC<CategoryReportProps> = ({}) => {
    const [value, setValue] = React.useState<string[]>([]);
    const handleSubmit = async () => {
        setShowSubmitButton(false);
        setShowProcessingButton(true);
    };
    const [showSubmitButton, setShowSubmitButton] = useState<boolean>(true);
    const [showProcessingButton, setShowProcessingButton] = useState<boolean>(false);
    const [showDoneButton, setShowDoneButton] = useState<boolean>(false);

    //set record list when
    const [reportList, setReportList] = useState<[ReportItem]>([{
        id:1,
        url:"/hi",
        title:"ok"
    }]);

    return (
        <div>
            <Select
                mode="multiple"
                maxCount={MAX_COUNT}
                value={value}
                style={{ width: "100%" }}
                onChange={setValue}
                placeholder="Please select"
                options={[
                    { value: "Ava Swift", label: "Ava Swift" },
                    { value: "Cole Reed", label: "Cole Reed" },
                    { value: "Mia Blake", label: "Mia Blake" },
                    { value: "Jake Stone", label: "Jake Stone" },
                    { value: "Lily Lane", label: "Lily Lane" },
                    { value: "Ryan Chase", label: "Ryan Chase" },
                    { value: "Zoe Fox", label: "Zoe Fox" },
                    { value: "Alex Grey", label: "Alex Grey" },
                    { value: "Elle Blair", label: "Elle Blair" }
                ]}
            />

            {showSubmitButton && <EditedPrimaryButton onClick={handleSubmit} label={"提交"} width={100} />}
            {showProcessingButton && (
                <EditedPrimaryButton
                    onClick={function (): void {
                        throw new Error("Function not implemented.");
                    }}
                    label={"報告生成中"}
                    width={100}
                    disabled
                />
            )}
            {showDoneButton && (
                <EditedPrimaryButton
                    onClick={function (): void {
                        throw new Error("Function not implemented.");
                    }}
                    label={"已生成"}
                    width={100}
                    disabled
                />
            )}
            {reportList.map(item => (
                <Card key={item.id} style={{ width: "25%" }}>
                    {" "}
                    <img src={ReportIcon} /> {/* 使用 item.title 作為 alt text */}
                    <Link to={item.url}>{item.title}</Link> {/* 使用 item.url 和 item.title */}
                </Card>
            ))}
        </div>
    );
};

export default CategoryReport;
