import React, {useEffect, useRef, useState} from "react";
import {useHref} from "react-router-dom";
import "./styles.scss";
import {WechatImage} from "@/components/WechatImage.t.tsx";
import freeReportIcon from '@/assets/icons/sdg/free-report.svg';

interface LandingPageFloatingWidgetProps {
    type?: "red" | "blue";
}

const QAndAIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg
        width="56"
        height="40"
        viewBox="0 0 56 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...props}
    >
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M28 9C34.0744 9 39 13.9244 39 20C39 26.0744 34.0744 31 28 31C21.9244 31 17 26.0744 17 20C17 13.9244 21.9244 9 28 9Z"
            stroke="black"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
        <path
            d="M25.1641 15.7994C24.9054 16.1229 24.9578 16.5948 25.2812 16.8536C25.6046 17.1124 26.0766 17.06 26.3354 16.7365L25.1641 15.7994ZM28.2234 15.2756L28.2628 14.5266L28.2234 15.2756ZM28.6135 19.1001L28.2064 18.4703V18.4703L28.6135 19.1001ZM26.3354 16.7365C26.7806 16.1801 27.4104 15.9839 28.1841 16.0246L28.2628 14.5266C27.1807 14.4698 26.0059 14.7473 25.1641 15.7994L26.3354 16.7365ZM26.9998 20.6866V22.2498H28.4998V20.6866H26.9998ZM28.2064 18.4703C27.4541 18.9564 26.9998 19.7909 26.9998 20.6866H28.4998C28.4998 20.3 28.6959 19.9399 29.0206 19.73L28.2064 18.4703ZM28.1841 16.0246C28.5267 16.0426 28.7262 16.1581 28.8455 16.2822C28.9711 16.4129 29.0604 16.6088 29.0786 16.8653C29.1158 17.3912 28.8391 18.0613 28.2064 18.4703L29.0206 19.73C30.1034 19.0302 30.6515 17.8416 30.5748 16.7592C30.536 16.2115 30.3337 15.6659 29.9269 15.2427C29.5137 14.8129 28.9401 14.5622 28.2628 14.5266L28.1841 16.0246Z"
            fill="currentColor"
        />
        <path
            d="M27.7654 24.25H27.7758"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);


const WechatIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
    <svg width="56" height="40" viewBox="0 0 56 40" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
        <path
            d="M34.2503 17.1842C33.3038 13.9154 29.9655 11.5 25.9926 11.5C21.2837 11.5 17.4663 14.8932 17.4663 19.0789C17.4663 21.1129 18.3677 22.9597 19.8347 24.321C20.2272 24.6852 20.0985 26.8488 19.9677 28.2618C19.9275 28.6956 20.4511 28.9754 20.7891 28.7005C21.9208 27.7802 23.7273 26.4067 24.3105 26.5104C24.8543 26.6072 25.4168 26.6579 25.9926 26.6579C26.7271 26.6579 27.4399 26.5753 28.1199 26.4201"
            stroke="#201747" stroke-width="1.42105" stroke-linecap="round"/>
        <path
            d="M38.5 22.931C38.5 24.3914 37.7919 25.7036 36.6667 26.6065C36.4441 26.7851 36.7706 28.1378 36.9257 28.7271C36.9497 28.8183 36.8456 28.8931 36.7676 28.8402C36.2189 28.4682 34.864 27.5824 34.5267 27.6696C34.042 27.7949 33.5297 27.8621 33 27.8621C29.9624 27.8621 27.5 25.6544 27.5 22.931C27.5 20.2077 29.9624 18 33 18C36.0376 18 38.5 20.2077 38.5 22.931Z"
            stroke="currentColor" stroke-width="1.5"/>
        <circle cx="23.2498" cy="17.2191" r="0.958333" fill="#201747"/>
        <circle cx="27.9583" cy="17.2191" r="0.958333" fill="#201747"/>
        <circle cx="30.7188" cy="21.7295" r="0.71875" fill="currentColor"/>
        <circle cx="34.4688" cy="21.7295" r="0.71875" fill="currentColor"/>
    </svg>
)


const FloatingWidget: React.FC<LandingPageFloatingWidgetProps> = ({type = "red"}) => {
    const [showBackToTop, setShowBackToTop] = useState(false);
    const [showWechatPopup, setShowWechatPopup] = useState(false);
    const location = useHref("/");
    const wechatBtnRef = useRef<HTMLButtonElement>(null);
    const currentDomain = useHref("");

    const scrollToTop = () => window.scrollTo({top: 0, behavior: "smooth"});
    const handleScroll = () => setShowBackToTop(window.scrollY > 200);

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (wechatBtnRef.current && !wechatBtnRef.current.contains(e.target as Node)) {
                setShowWechatPopup(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    useEffect(() => {
        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    const openByBlank = (e: React.MouseEvent<HTMLElement>, url: string) => {
        e.preventDefault();
        window.open(location + url, "_blank");
    };

    const palette = {
        red: {accent: "#FF3434"},
        blue: {accent: "#1890FF"},
    } as const;

    const variantColor = palette[type].accent;

    const containerClass = `floating-widget-container-${type}`;
    const floatingWidgetClass = `floating-widget-${type}`;

    return (
        <div className={containerClass}>
            <div className={floatingWidgetClass}>
                <button onClick={() => window.location.href = currentDomain + '?signup=1'}>
                    <div className="widget-icon" style={{color: variantColor}}>
                        <img src={freeReportIcon} style={{width: '55px', height: '48px'}} alt="Free Report"/>
                    </div>
                </button>

                <button onClick={(e) => openByBlank(e, "/questions")}>
                    <div className="widget-icon" style={{color: variantColor}}>
                        <QAndAIcon width={55} height={40}/>
                        <div className="icon-texts">常见问题</div>
                    </div>
                </button>

                <button
                    ref={wechatBtnRef}
                    onClick={() => setShowWechatPopup((prev) => !prev)}
                    className="wechat-btn"
                >
                    <div className="widget-icon" style={{color: variantColor}}>
                        <WechatIcon width={55} height={40}/>
                        <div className="icon-texts">线上客服</div>
                    </div>
                </button>

                {showWechatPopup && (
                    <div className={`wechat-popup ${type}`}> {/* keep border color variant */}
                        <WechatImage />
                    </div>
                )}
            </div>

            {showBackToTop && (
                <div className="floating-back-to-top">
                    <button onClick={scrollToTop} style={{padding: 0, border: "none", background: "none"}}>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="50"
                            fill="none"
                            stroke="#161616"
                            strokeWidth="1.7"
                            strokeLinecap="round"
                            viewBox="0 0 24 24"
                        >
                            <line x1="12" y1="22" x2="12" y2="5"/>
                            <line x1="5" y1="12" x2="12" y2="5"/>
                            <line x1="19" y1="12" x2="12" y2="5"/>
                        </svg>
                    </button>
                </div>
            )}
        </div>
    );
};

export default FloatingWidget;