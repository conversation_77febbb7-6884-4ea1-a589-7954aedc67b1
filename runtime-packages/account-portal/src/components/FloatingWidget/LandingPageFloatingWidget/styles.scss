%floating-widget-container-base {
  display: flex;
  flex-direction: column;
  position: fixed;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  gap: 16px;
  align-items: center;
}

.floating-widget-container-red {
  @extend %floating-widget-container-base;
}

.floating-widget-container-blue {
  @extend %floating-widget-container-base;
}

%floating-widget-base {
  display: flex; /* Use Flexbox for contents */
  flex-direction: column; /* Stack contents vertically */
  border-radius: 60px; /* Rounded corners */
  padding: 24px 0; /* Padding for spacing */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); /* Shadow for depth */
  width: filled 56px; /* Fixed width */
  align-items: center; /* Center contents */
  gap: 8px;
  position: relative;

  button {
    background: white; /* Button background */
    border: none; /* No border */
    border-radius: 5px; /* Rounded corners */
    cursor: pointer; /* Pointer cursor */
    width: filled 56px;
    height: fixed 62px;
    padding: 3px 2px;
  }
}

.floating-widget-red {
  @extend %floating-widget-base;
  background: linear-gradient(360deg, #FFF9DF 0%, #FFFDF7 75.57%);

  button {
    background: transparent;

    &:hover,
    &:focus-visible {
      background-color: #FFEFCE;
    }
  }
}

.floating-widget-blue {
  @extend %floating-widget-base;
  background: #FFFFFF4D;

  button {
    background: transparent;

    &:hover,
    &:focus-visible {
      background-color: #FFFFFFB2;
    }
  }
}


.floating-back-to-top {
  display: flex; /* Use Flexbox for contents */
  flex-direction: column; /* Stack contents vertically */
  background: white; /* Set background */
  border-radius: 60px; /* Rounded corners */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); /* Shadow for depth */
  width: 56px; /* Fixed width */
  height: 56px; /* Fixed height */
  align-items: center; /* Center contents */
}

.floating-widget button {
  background: white; /* Button background */
  border: none; /* No border */
  border-radius: 5px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor */
  width: filled 56px;
  height: fixed 62px;
  padding: 3px 2px;
}

.floating-back-to-top button {
  background: white; /* Button background */
  border: none; /* No border */
  border-radius: 50px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor */
  font-size: 20px; /* Font size */
}

.icon-texts {
  width: filled 48px;
  height: fixed 18px;
  font-size: 12px;
  font-family: "Source Sans Pro", sans-serif;
  line-height: 18px;
  font-weight: 400;
  color: #282830;
  padding-bottom: 10px;
}


.wechat-popup {
  position: absolute;
  right: 100%;
  top: 90%;
  transform: translate(-8px, -50%);
  background: #fff;
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 20;

  img {
    width: 140px;
    height: auto;
    display: block;
  }
}

