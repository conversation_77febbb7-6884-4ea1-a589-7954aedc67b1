import React, {useEffect, useState} from 'react';
import {useHref} from 'react-router-dom';
import {useNavigate} from "react-router-dom";
import './styles.scss';
import qAndAIcon from '@/assets/icons/sdg/qAndA.svg';
import wechatIcon from '@/assets/icons/sdg/wechatIcon.svg';
import freeReportIcon from '@/assets/icons/sdg/free-report.svg';
import {OurWechatModal} from '@/components/shared/OurWechatModal';
import {useCommonStore} from "@/store/common.ts";

const FloatingWidget: React.FC = () => {
    const [showBackToTop, setShowBackToTop] = useState(false);
    const [showWechatModal, setShowWechatModal] = useState(false);
    const navigate = useNavigate();
    const location = useHref("/");
    const user = useCommonStore((s) => s.userInfo);
    const currentDomain = useHref("");


    const scrollToTop = () => {
        window.scrollTo({top: 0, behavior: 'smooth'});
    };

    const handleScroll = () => {
        if (window.scrollY > 200) { // Adjust this threshold as needed
            setShowBackToTop(true);
        } else {
            setShowBackToTop(false);
        }
    };

    useEffect(() => {
        window.addEventListener('scroll', handleScroll);

        // Clean up the event listener on component unmount
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    const openByBlank = (e: React.MouseEvent<HTMLElement, MouseEvent>, url: string) => {
        e.preventDefault();
        window.open(location + url, "_blank");
    };

    const handleClickFreeReport = () => {
        if (user) {
            window.open(location + '/free-reports', "_blank");
        } else {
            window.location.href = currentDomain + '?signup=1';
        }
    }

    return (
        <div className="floating-widget-container">
            <div className="floating-widget">
                <button onClick={() => handleClickFreeReport()}>
                <img src={freeReportIcon} style={{width: '55px', height: '48px'}} alt="Free Report"/>
                </button>
                <button onClick={(e) => openByBlank(e, '/questions')}>
                    <div>
                        <img src={qAndAIcon} style={{width: '55px', height: '40px'}} alt="Q&A"/>
                        <div className="icon-texts">常见问题</div>
                    </div>
                </button>
                <button onClick={() => setShowWechatModal(true)}>
                    <img src={wechatIcon} style={{width: '55px', height: '40px'}} alt="WeChat"/>
                    <div className="icon-texts">线上客服</div>
                </button>
                <OurWechatModal
                    open={showWechatModal}
                    onClose={() => setShowWechatModal(false)}
                    message={"有任何问题，欢迎联系我们～"}
                    hasAlertIcon={true}
                    textAlign='center'
                />
            </div>
            {showBackToTop && (
                <div className="floating-back-to-top">
                    <button onClick={scrollToTop} style={{padding: 0, border: 'none', background: 'none'}}>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="50"
                            fill="none"
                            stroke="#161616"
                            strokeWidth="1.7"
                            strokeLinecap="round"
                            viewBox="0 0 24 24">
                            <line x1="12" y1="22" x2="12" y2="5"/>
                            <line x1="5" y1="12" x2="12" y2="5"/>
                            <line x1="19" y1="12" x2="12" y2="5"/>
                        </svg>
                    </button>
                </div>
            )}
        </div>
    );
};

export default FloatingWidget;