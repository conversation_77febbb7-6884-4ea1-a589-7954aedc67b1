.floating-widget-container {
  display: flex; /* Ensure Flexbox is used */
  flex-direction: column; /* Stack children vertically */
  position: fixed; /* Keep the container fixed */
  right: 10px; /* Positioning from the right */
  top: 50%; /* Center vertically */
  transform: translateY(-50%); /* Center the container vertically */
  gap: 16px; /* Space between children */
  align-items: center; /* Center align children horizontally */
}

.floating-widget {
  display: flex; /* Use Flexbox for contents */
  flex-direction: column; /* Stack contents vertically */
  background: white; /* Set background */
  border-radius: 60px; /* Rounded corners */
  padding: 24px 0; /* Padding for spacing */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); /* Shadow for depth */
  width: filled 56px; /* Fixed width */
  align-items: center; /* Center contents */
  gap: 8px;
}

.floating-back-to-top {
  display: flex; /* Use Flexbox for contents */
  flex-direction: column; /* Stack contents vertically */
  background: white; /* Set background */
  border-radius: 60px; /* Rounded corners */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); /* Shadow for depth */
  width: 56px; /* Fixed width */
  height: 56px; /* Fixed height */
  align-items: center; /* Center contents */
}

.floating-widget button {
  background: white; /* Button background */
  border: none; /* No border */
  border-radius: 5px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor */
  width: filled 56px;
  height: fixed 62px;
  padding: 3px 2px;
}

.floating-back-to-top button {
  background: white; /* Button background */
  border: none; /* No border */
  border-radius: 50px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor */
  font-size: 20px; /* Font size */
}

.icon-texts {
  width: filled 48px;
  height: fixed 18px;
  font-size: 12px;
  font-family: "Source Sans Pro", sans-serif;
  line-height: 18px;
  font-weight: 400;
  color: #282830;
  padding-bottom: 10px;
}