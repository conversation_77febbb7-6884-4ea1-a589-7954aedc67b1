.footer-link {
  color: var(--footer-link);
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover, &:active {
    color: #282830;
    text-decoration: underline;
  }
} 

// Keeping this for reference but not using it
.fixed-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: transparent;
  z-index: 1000;
  box-shadow: none;
}



.footer-bottom {
  --footer-emphasized:        #201747;
  --footer-normal:            #282830;
  --footer-divider:           #DDDFE6;
  --footer-link:              #9E9EA3;
  --footer-link-active:       #282830;
  --footer-policy-statement:  #282830;
  --footer-clickable-content:  #6E6E75;
  --footer-base-color:  #6E6E75;

  width: 100%;
  background: transparent;
  text-align: center;
  font-size: 0.75rem; // text-xs
  padding: 1rem; // py-4 px-4
  font-weight: normal;
  border-top-width: 1px;
  color: #282830; // Assuming this is the PRIMARY_TEXT_COLOR
  display: flex;
  flex-direction: column;
  
  @media (min-width: 1024px) {
    font-size: 0.875rem; // lg:text-sm
  }
}

.base-color {
  color: var(--footer-base-color);
}

.footer-line {
  color: var(--footer-link);
  padding: 0.5rem 0;
  width: 100%;
}

.policy-statement{
  color: var(--footer-policy-statement)
}

.content-line {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
  margin-top: 8px;
  margin-bottom: 8px;
  @media (max-width: 768px) { // smaller than md
    display: grid;
  }
  
  .emphasized-text {
    color: var(--footer-emphasized);
    font-weight: 600;
    font-size: 40px;
    line-height: 36px;
    @media (max-width: 768px) { // smaller than md
      font-size: 32px;
    }
    @media (max-width: 576px) { // smaller than sm
      font-size: 20px;
    }
  }
  
  .normal-text {
    color: var(--footer-normal);
    font-weight: 600;
    font-size: 16px;
  }
  
  .divider {
    display: inline-block;
    width: 1px;
    height: 16px;
    background-color: var(--footer-divider);
    margin: 0 16px;
    @media (max-width: 768px) { // smaller than md
      display: none;
    }
  }
}

.first-line {
  color: #6E6E75;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: opacity 0.3s ease;
  position: relative; /* 添加 relative 定位 */

  
  .clickable-content {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: opacity 0.3s ease;
    color: var(--footer-clickable-content);
    
    &:hover {
      color: #282830;
      text-decoration: underline;
      .info-icon {
        margin-right: 4px;
        width: 16px;
        height: 16px;
        
        path.info-icon-circle {
          stroke: #282830;
        }
        path.info-icon-tail {
          fill: #282830;
        }
        path.info-icon-dot {
          fill: #282830;
        }
      }
    }
    .info-icon {
      margin-right: 4px;
      width: 16px;
      height: 16px;
      
      path.info-icon-circle {
        stroke: var(--footer-clickable-content);
      }
      path.info-icon-tail {
        fill: var(--footer-clickable-content);
      }
      path.info-icon-dot {
        fill: var(--footer-clickable-content);
      }
    }
  }
  
  
  
  span {
    font-size: 0.875rem;
  }
}

.footer-divider {
  height: 1px;
  width: 100%;
  background-color: #DDDFE6;
  margin: 0.5rem 0;
} 


.first-line > div:first-child { /* 确保只选择警告信息的 div */
  position: absolute;
  left: 20px;
}

/* 当屏幕宽度小于某个值时，改变布局 */
@media (max-width: 576px) { /* 根据实际情况调整屏幕宽度 */
  .first-line {
    flex-direction: column; /* 垂直排列 */
    align-items: center; /* 左对齐 */
  }

  .first-line > div:first-child { /* 警告信息的样式 */
    position: static; /* 恢复到静态定位 */
    left: auto; /* 移除左边距 */
    margin-bottom: 2rem; /* 添加下边距 */
  }}