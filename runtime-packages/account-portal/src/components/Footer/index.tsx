import { useTranslation } from "react-i18next";
import { useHref } from "react-router-dom";
import InfoIcon from "@/assets-new/icons/info-icon.svg?react";
import "./styles.scss";
import { useLocation } from "react-router-dom";
import { useSelectedProduct } from "@/hooks/useSelectedProduct.ts";

type FooterColorVariant = "default" | "white";

interface FooterProps {
  type?: FooterColorVariant;
}

type ColorSet = {
  emphasizedText: string;
  normalText: string;
  divider: string;
  activeLink: string;
  footerLink: string;
  policyStatement: string;
  clickableContent: string;
  baseColor: string;
};

const colorMap: Record<FooterColorVariant, ColorSet> = {
  default: {
    emphasizedText: "#201747",
    normalText: "#282830",
    divider: "#DDDFE6",
    activeLink: "#282830",
    footerLink: "#9E9EA3",
    policyStatement: "#282830",
    clickableContent: "#6E6E75",
    baseColor: "#6E6E75",
  },
  white: {
    emphasizedText: "#FFFFFF",
    normalText: "#FFFFFF",
    divider: "#DDDFE6",
    activeLink: "#282830",
    footerLink: "#FFFFFF",
    policyStatement: "#FFFFFF",
    clickableContent: "#FFFFFF",
    baseColor: "#FFFFFF",
  },
};

const Footer: React.FC<FooterProps> = ({ type = "default" }) => {
    const { t } = useTranslation();
    const { emphasizedText, normalText, divider, activeLink, footerLink, policyStatement, clickableContent, baseColor } = colorMap[type];
    const href = useHref("/");
    const location = useLocation();

    const openByBlank = (e: React.MouseEvent<HTMLElement, MouseEvent>, url: string) => {
        e.preventDefault();
        window.open(href + url, "_blank");
    };

    const openByBlankAbsolute = (e: React.MouseEvent<HTMLElement, MouseEvent>, url: string) => {
        e.preventDefault();
        window.open(url, "_blank");
    };

    const product = useSelectedProduct();
    const cssVars: React.CSSProperties = {
        "--footer-emphasized": emphasizedText,
        "--footer-normal":     normalText,
        "--footer-divider":    divider,
        "--footer-link":       footerLink,
        "--footer-link-active": activeLink,
        "--footer-policy-statement": policyStatement,
        "--footer-clickable-content": clickableContent,
        "--footer-base-color": baseColor,
      } as any;


    return (
        <footer className="footer-bottom" style={cssVars}>
            <div className="footer-line first-line">
           
                <div className={"base-color"} style={{ fontSize: "14px", position: "absolute", left: "20px" }}>
                    {product === 'xdj' ?"*无最低流水要求，需通过反欺诈审核" : ""}
                </div>

                {location.pathname === "/questions" ? null : (
                    <div className="clickable-content" onClick={e => openByBlank(e, "/questions")}>
                        <InfoIcon className="info-icon" />
                        <span>常见问题</span>
                    </div>
                )}
            </div>
            <div className="footer-divider"></div>
            <div className="footer-line content-line">
                <span className="emphasized-text">忠告：借钱梗要还，咪俾钱中介</span>
                <span className="divider"></span>
                <span className="normal-text">客户服务/投诉热线：(852) 3460 2871</span>
                <span className="divider"></span>
                <span className="normal-text">放债人牌照号码：1662/2024</span>
            </div>
            <div className="footer-line">
                丰泊国际有限公司 &nbsp;
                {t("pb.copyright")} © | {t("pb.allRightsReserved")} |{" "}
                <a
                    href="https://www.fundpark.com/en/terms-and-conditions/"
                    className="footer-link"
                    onClick={e => openByBlankAbsolute(e, "https://www.fundpark.com/en/terms-and-conditions/")}
                >
                    {t("pb.termsAndConditions")}
                </a>{" "}
                |{" "}
                <a
                    href="https://www.fundpark.com/en/privacy-policy/"
                    className="footer-link"
                    onClick={e => openByBlankAbsolute(e, "https://www.fundpark.com/en/privacy-policy/")}
                >
                    {t("pb.privacyPolicy")}
                </a>
            </div>
        </footer>
    );
};

export { Footer };
