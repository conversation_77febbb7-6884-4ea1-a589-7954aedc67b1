@use "@/assets/styles/variables.module.scss" as *;

.cp-pagination {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    gap: 12px;
    &-left {
		min-width: 120px;
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        color: $fp-text-color;
    }
    &-right {
		min-width: 150px;
        display: flex;
        align-items: center;
        font-size: 14px;
        strong {
            font-size: 16px;
            font-weight: 700;
            margin: 0 8px;
            color: $fp-accent-color;
        }
        &-suffix {
            font-size: 24px;
            color: #0a090b;
        }
        :global {
            .ant-select-selector {
                padding-left: 0 !important;
            }
        }
    }
    &-center {
        flex: 1;
        display: flex;
        justify-content: center;
        :global {
            .ant-pagination {
                border-radius: 30px;
                padding: 4px 16px;
                background-color: #f6f7fa;
                .ant-pagination-prev,
                .ant-pagination-next {
                    color: #000;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .ant-pagination-prev {
                    margin-right: 20px;
                }
                .ant-pagination-next {
                    margin-left: 20px;
                }
                .ant-pagination-item {
                    font-size: 14px;
                    font-weight: 400;
                    background-color: transparent;
                    &.ant-pagination-item-active {
                        background-color: $fp-accent-color;
                        a {
                            color: #fff;
                        }
                    }
                }
            }
        }
    }
}
