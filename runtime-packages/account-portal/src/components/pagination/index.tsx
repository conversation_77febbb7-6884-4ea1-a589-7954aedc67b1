import { useTranslation } from "react-i18next";
import { Pagination, Select } from "antd";
import ArrowDownIcon from "@/assets/icons/arrow-down3.svg?react";
import styles from "./index.module.scss";

const ArrowIcon: React.FC<{
    className?: string;
    style?: React.CSSProperties;
}> = ({ className, style }) => (
    <svg
        className={className}
        style={style}
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path
            d="M15.5 17L10.5 12L15.5 7"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </svg>
);

const CustomPagination: React.FC<{
    current: number;
    pageSize?: number;
    total: number;
    onChange?: (page: number, pageSize: number) => void;
}> = ({ current, pageSize = 10, total, onChange }) => {
    const { t } = useTranslation();

    return (
        <div className={styles.cpPagination}>
            <div className={styles.cpPaginationLeft}>
                {t("pagination.customTotal", {
                    total,
                    range1: parseInt((1 + (current - 1) * pageSize).toString()),
                    range2: parseInt(Math.min(current * pageSize, total).toString())
                })}
            </div>
            <div className={styles.cpPaginationCenter}>
                <Pagination
                    current={current}
                    pageSize={pageSize}
                    total={total}
                    onChange={onChange}
                    prevIcon={<ArrowIcon />}
                    nextIcon={<ArrowIcon className="rotate-180" />}
                />
            </div>
            <div className={styles.cpPaginationRight}>
                <span>{t("pagination.show")}</span>
                <Select
                    variant="borderless"
                    suffixIcon={<ArrowDownIcon className={styles.cpPaginationRightSuffix} />}
                    value={pageSize}
                    labelRender={() => <strong>{`${t("pagination.rows")} ${pageSize}`}</strong>}
                    onChange={value => {
                        onChange && onChange(1, value);
                    }}
                    options={[
                        {
                            value: 10,
                            label: "10"
                        },
                        {
                            value: 20,
                            label: "20"
                        },
                        {
                            value: 50,
                            label: "50"
                        },
                        {
                            value: 100,
                            label: "100"
                        }
                    ]}
                />
            </div>
        </div>
    );
};

export default CustomPagination;
