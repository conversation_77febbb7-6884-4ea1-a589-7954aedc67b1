.navButton {
    font-size: 16px;
    &:hover {
        color: rgba(156, 163, 175, 1) !important;
    }
}

.activeNavButton {
    color: #64ccc9 !important;
    position: relative;
    display: flex !important;
    align-items: center !important;
    
    &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 32px); // Subtracting padding
        height: 4px;
        border-radius: 40px 40px 0 0;
        background-color: #64ccc9;
    }
}

.defaultNavButton {
    color: white !important;
}

.languageItem {
    font-size: 14px !important;
    font-weight: 400 !important;
    color: #282830 !important;
    &:hover {
        background-color: rgba(32, 23, 71, 0.04) !important;
    }
}

.selectedLanguageItem {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #282830 !important;
    background-color: rgba(32, 23, 71, 0.08) !important;
}
