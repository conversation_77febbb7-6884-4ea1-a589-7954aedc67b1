import React from 'react';
import { Modal } from '@/components/shared/Modal';
import { SecondaryButton } from '@/components/shared/SecondaryButton';
import AlertIcon from '@/assets-new/icons/alert-icon.svg?react';
import './index.scss';
import {WechatImage} from "@/components/WechatImage.t.tsx";

export interface OurWechatModalProps {
    open: boolean;
    onClose: () => void;
    message?: React.ReactNode;
    textAlign?: 'left' | 'center' | 'right';
    width?: number;
    hasAlertIcon?: boolean;
}

export const OurWechatModal: React.FC<OurWechatModalProps> = ({
    open,
    onClose,
    message = "有任何问题，欢迎联系我们～",
    textAlign = 'left',
    width = 535,
    hasAlertIcon = false
}) => {
    const renderMessage = () => {
        const messageContent = (
            <div className={`message-content text-${textAlign}`}>
                {message}
            </div>
        );

        if (hasAlertIcon) {
            return (
                <div className="flex gap-2">
                    <AlertIcon className="flex-shrink-0" />
                    {messageContent}
                </div>
            );
        }
        return messageContent;
    };

    const justifyClass = {
        left: 'justify-start',
        center: 'justify-center',
        right: 'justify-end'
    }[textAlign];

    return (
        <Modal
            open={open}
            onClose={onClose}
            title="温馨提示"
            width={width}
        >
            <div>
                <div className={`mb-4 flex ${justifyClass}`}>
                    {renderMessage()}
                </div>
                <div className="flex justify-center">
                    <WechatImage
                        style={{ width: '140px', height: '142px'}}
                    />
                </div>
                <div className="flex justify-center mt-10">
                    <SecondaryButton
                        label="关闭"
                        onClick={onClose}
                    />
                </div>
            </div>
        </Modal>
    );
}; 