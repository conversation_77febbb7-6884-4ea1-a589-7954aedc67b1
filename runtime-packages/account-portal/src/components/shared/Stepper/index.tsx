import React, { ReactNode } from 'react';
import { Card, Row } from 'antd';
import FormStepper from '@/components/shared/FormStepper';
import './styles.scss';

interface StepConfig {
  title: string;
  content: ReactNode;
  action?: ReactNode;
}

interface StepperProps {
  steps: StepConfig[];
  currentStep: number;
  borderTopRadiusRounded?: boolean;
}

const Stepper: React.FC<StepperProps> = ({ 
  steps, 
  currentStep,
  borderTopRadiusRounded = true
}) => {
  const stepTitles = steps.map(step => step.title);
  const currentContent = steps[currentStep]?.content;
  const currentAction = steps[currentStep]?.action;

  const borderRadius = borderTopRadiusRounded 
    ? '20px' 
    : '0 0 20px 20px';

  const cardTitle = (
    <div className="drawdown-form-header">
      <FormStepper 
        steps={stepTitles} 
        activeStep={currentStep} 
        borderTopRadiusRounded={borderTopRadiusRounded}
      />
    </div>
  );

  return (
    <div className="stepper-wrapper">
      <Card 
        title={cardTitle} 
        className="drawdown-form-card"
        bordered={true}
        style={{ 
          border: '1px solid #DDDFE6', 
          borderRadius: borderRadius,
        }}
        headStyle={{ 
          padding: 0, 
          borderBottom: 'none', 
          minHeight: '0', 
          width: '100%', 
          overflow: 'hidden',
          borderRadius: 0
        }}
        bodyStyle={{ 
          padding: '24px',
          width: '100%'
        }}
      >
        <div className="drawdown-form-content" style={{ margin: '20px 0' }}>
          {currentContent}
        </div>
      </Card>
      
      {currentAction && (
        <Row justify="center" className="stepper-actions" style={{ marginTop: '40px' }}>
          {currentAction}
        </Row>
      )}
    </div>
  );
};

export default Stepper; 