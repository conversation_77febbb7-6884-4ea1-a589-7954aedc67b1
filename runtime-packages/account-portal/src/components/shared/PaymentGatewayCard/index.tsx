import React from "react";
import { THEME_CONSTANTS } from "../../common";
import RoleBadge from "../RoleBadge";

interface PaymentGatewayCardProps {
    user_id?: string;
    logo?: string;
}

const PaymentGatewayCard: React.FC<PaymentGatewayCardProps> = ({ logo, user_id }) => {
    //   const { t } = useTranslation();

    return (
      <div className="p-4 ">
        <img src={logo} alt="Logo" className="h-24 w-110 mr-2" />
        <div className="ml-4">
            <RoleBadge role={'Authorized'} className={'signing-role-badge'} />
        </div>
        <p style={{color:THEME_CONSTANTS.TERTIARY_TEXT_COLOR,fontSize:14}}> 用户id：{user_id}</p>
      </div>
    );
};


export default PaymentGatewayCard;
