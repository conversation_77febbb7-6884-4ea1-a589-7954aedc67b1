import React from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {SecondaryButton} from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import alertCircle from "@/assets-new/icons/common/alert-circle.svg"

interface TwoButtonModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: () => void;
    title?: string;
    content: React.ReactNode;
    width?: number;
    height?: number;
    completeBtnLabel?: string;
    isShowCircle?: boolean;
    variant?: 'one_btn' | 'two_btn';
    isWidthAuto?: boolean;
    closeBtnLabel?: string;
}

const TwoButtonModal: React.FC<TwoButtonModalProps> = ({
                                                           open,
                                                           onClose,
                                                           onComplete,
                                                           title = "温馨提示",
                                                           content,
                                                           completeBtnLabel = "联系客服",
                                                            isShowCircle = true,
                                                            variant= "two_btn",
                                                            isWidthAuto = false,
                                                            closeBtnLabel = "关闭",
                                                       }) => {


    return (
        <Modal
            open={open}
            onClose={onClose}
            title={title}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={isWidthAuto ? "auto" : undefined}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                margin: "0 auto",
            }}
        >
            <div
                className="flex items-center justify-center gap-2"
                style={{
                    fontFamily: "Source Sans Pro",
                    fontSize: "14px",
                    fontWeight: "400",
                    lineHeight: "20px",
                    color: "#282830"
                }}
            >
                {isShowCircle &&
                    <img
                        src={alertCircle}
                        alt="Alert Icon"
                        className="w-6 h-6"
                    />
                }

                {content}
            </div>


            <div style={{
                display: "flex",
                justifyContent: "center",
                height: "40px",
                alignItems: "center",
                gap: "16px",
                marginTop: "40px",
            }}>
                {variant !== 'one_btn' && <SecondaryButton
                    label={closeBtnLabel}
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: isWidthAuto ? "auto" : "92px",
                        padding: "10px 16px",
                    }}
                />}
                <PrimaryButton
                    label={completeBtnLabel}
                    onClick={onComplete}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: isWidthAuto ? "auto" : "92px",
                        padding: "10px 16px",
                    }}
                />
            </div>
        </Modal>
    );
};

export default TwoButtonModal;
