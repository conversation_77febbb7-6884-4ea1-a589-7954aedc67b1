import React from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {Button} from "@/components/shared/GenricButton/index.tsx";
import alertCircle from "@/assets-new/icons/common/alert-circle.svg"

type ModalDialogAction = {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'danger';
    disabled?: boolean;
    autoFocus?: boolean;
};

export interface ModalDialogProps {
    open: boolean;
    title?: React.ReactNode;
    children: React.ReactNode;
    actions?: ModalDialogAction[];
    isClosable?: boolean;
    isShowCircle?: boolean;
    onClose?: () => void;
}


const ModalDialog: React.FC<ModalDialogProps> = ({
                                                     open,
                                                     title,
                                                     children,
                                                     actions = [],
                                                     isClosable = true,
                                                     isShowCircle = false,
                                                     onClose,
                                                 }) => {
    if (!open) return null;

    const handleOnClose = () => {
        if (!isClosable) return;
        onClose?.();
    };

    return (
        <Modal
            open={open}
            onClose={handleOnClose}
            title={title}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                margin: "0 auto",
            }}
        >
            <div
                className="flex items-center justify-center gap-2"
                style={{
                    fontFamily: "Source Sans Pro",
                    fontSize: "14px",
                    fontWeight: "400",
                    lineHeight: "20px",
                    color: "#282830"
                }}
            >
                {isShowCircle &&
                    <img
                        src={alertCircle}
                        alt="Alert Icon"
                        className="w-6 h-6"
                    />
                }

                {children}
            </div>


            {actions.length > 0 && (
                <footer className="mt-6 flex justify-center gap-4">
                    {actions.map(({label, onClick, variant, disabled}) => (
                        <Button
                            key={label}
                            variant={variant ?? 'primary'}
                            disabled={disabled}
                            onClick={onClick}
                        >
                            {label}
                        </Button>
                    ))}
                </footer>
            )}
        </Modal>
    );
};

export default ModalDialog;
