import React from 'react';
import './InfoBanner.scss';
import { Grid } from 'antd';

export interface InfoBannerProps {
  type?: 'default' | 'note' | 'notice' | 'warning' | 'info';
  message: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  width?: number | string;
  height?: number | string;
  showIcon?: boolean;
  backgroundColor?: string;
  color?: string;
}


const getBannerColor = (type: string) => {
  let bgColor = '';
  let fontColor = '';
  switch (type) {
    case 'info':
      bgColor = '#E9EFFD';
      fontColor = '#2463EB';
      break;
    case 'note':
      bgColor = '#201747';
      fontColor = '#FFFFFF';
      break;
    case 'notice':
      bgColor = '#FEF6EC';
      fontColor = '#F4A83C';
      break;
    case 'warning':
      bgColor = '#FEF6EC';
      fontColor = '#F4A83C';
      break;
    case 'default':
      bgColor = '#E9E8ED';
      fontColor = '#201747';
      break;
    default:
      bgColor = '#E9E8ED';
      fontColor = '#201747';
      break;
  }
  return { bgColor, fontColor };
}

const InfoBanner: React.FC<InfoBannerProps> = ({
  type = 'default',
  message,
  className = '',
  style = {},
  width,
  height = 32,
  showIcon = true,
  backgroundColor,
  color,
}) => {
  const screens = Grid.useBreakpoint();
  const { bgColor, fontColor } = getBannerColor(type);

  const combinedStyle: React.CSSProperties = {
    width: screens.md ? (width || 'auto') : 'auto', // do not override width on mobile
    height: typeof height === 'number' ? `${height}px` : height,
    backgroundColor: bgColor || backgroundColor,
    color: fontColor || color,
    ...style,
  };

  return (
    <div
      className={`info-banner ${type} ${className}`}
      style={combinedStyle}
    >
      {showIcon && <span className="info-icon" style={{ border: `1.67px solid ${fontColor || color}` }}>i</span>}
      {message}
    </div>
  );
};

export default InfoBanner; 