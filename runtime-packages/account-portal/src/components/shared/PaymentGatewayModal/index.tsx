import React, { useEffect, useState } from "react";
import { Modal } from "@/components/shared/Modal";
import PaymentGatewayCard from "../PaymentGatewayCard";
import { usePaymentGatewayStore } from "@/store/platform/paymentGatewayStore";

interface PaymentGatewaySelectionModalProps {
    open: boolean;
    onClose: () => void;
}

const PaymentGatewaySelectionModal: React.FC<PaymentGatewaySelectionModalProps> = ({
    open,
    onClose,
}) => {

    const [selectedPspId, setSelectedPspId] = useState<number | null>(null);
    const {payment_gateway, fetchPaymentGateway} = usePaymentGatewayStore();

    useEffect(() => {
        fetchPaymentGateway().catch((error) => {
            console.error("Error fetching platforms:", error);
        });
    }, [fetchPaymentGateway]);


    const handleSelect = (id: number) => {
        setSelectedPspId(id);
    };
    return (
       
        <Modal
            open={open}
            onClose={onClose}
            title="请选择正在使用的支付公司"
        >
          <div
                style={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "16px",
                    padding: "16px",
                    justifyContent: "flex-start", // 或者 "center", "space-between", "space-around"
                }}
            >
                {payment_gateway.map((psp) => (
                    <PaymentGatewayCard
                        key={psp.id}
                        title={psp.platform}
                        authButtonLink={psp.platform}
                        logo={psp.platform}
                    />
                ))}
            </div>
        </Modal>
    );
};

export default PaymentGatewaySelectionModal;