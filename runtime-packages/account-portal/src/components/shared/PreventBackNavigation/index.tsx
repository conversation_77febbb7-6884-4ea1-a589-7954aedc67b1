import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { PROJECT_BASE_PATH } from '@/constants/routing';
interface PreventBackNavigationProps {
  children: React.ReactNode;
}

const PreventBackNavigation: React.FC<PreventBackNavigationProps> = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    const preventNavigation = (event: PopStateEvent) => {
      // Prevent the default back navigation
      event.preventDefault();
      
      // Push the current state back to maintain the URL
      window.history.pushState(null, '', PROJECT_BASE_PATH + location.pathname);
    };

    // Push the current state to the history stack
    window.history.pushState(null, '', PROJECT_BASE_PATH + location.pathname);

    // Add event listener for popstate (back/forward navigation)
    window.addEventListener('popstate', preventNavigation);

    return () => {
      // Clean up event listener
      window.removeEventListener('popstate', preventNavigation);
    };
  }, [location.pathname]);

  return <>{children}</>;
};

export default PreventBackNavigation; 