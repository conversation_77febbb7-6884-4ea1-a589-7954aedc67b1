import React from 'react';
import { Form, InputNumber, Select } from 'antd';
import { Rule } from 'antd/es/form';
import './styles.scss';

const { Option } = Select;

export interface CurrencyOption {
  value: string;
  label: string;
}

export interface CurrencyAmountInputProps {
  currencyName: string;
  amountName: string;
  currencyOptions: CurrencyOption[];
  currencyRules?: Rule[];
  amountRules?: Rule[];
  label?: string;
  currencyPlaceholder?: string;
  amountPlaceholder?: string;
  className?: string;
  currencyWidth?: number;
  onCurrencyChange?: (value: string) => void;
  onAmountChange?: (value: number | null) => void;
  onCurrencyBlur?: (value: string) => void;
  onAmountBlur?: (value: number | null) => void;
  combinedErrorMessage?: string;
}

const CurrencyAmountInput: React.FC<CurrencyAmountInputProps> = ({
  currencyName,
  amountName,
  currencyOptions,
  currencyRules = [{ required: true, message: '请选择币种' }],
  amountRules = [{ required: true, message: '请输入金额' }],
  label,
  currencyPlaceholder = '请选择',
  amountPlaceholder = '请输入',
  className = '',
  currencyWidth = 120,
  onCurrencyChange,
  onAmountChange,
  onCurrencyBlur,
  onAmountBlur,
  combinedErrorMessage = '请选择币种并输入金额',
}) => {
  const [currentCurrency, setCurrentCurrency] = React.useState<string>('');
  const [currentAmount, setCurrentAmount] = React.useState<number | null>(null);
  // Create a combined validation field
  const combinedFieldName = `${currencyName}_${amountName}_combined`;
  
  // Check if fields are required based on the first rule
  const isCurrencyRequired = currencyRules.some(rule => 
    typeof rule === 'object' && 'required' in rule && rule.required === true
  );
  
  const isAmountRequired = amountRules.some(rule => 
    typeof rule === 'object' && 'required' in rule && rule.required === true
  );

  return (
    <Form.Item 
      label={label}
      className={`half-width-item ${className}`}
      required={isCurrencyRequired || isAmountRequired}
    >
      <Form.Item
        name={combinedFieldName}
        dependencies={[currencyName, amountName]}
        rules={[
          ({ getFieldValue }) => ({
            validator(_, value) {
              const currencyValue = getFieldValue(currencyName);
              const amountValue = getFieldValue(amountName);
              
              // If neither field is required, allow both to be empty
              if (!isCurrencyRequired && !isAmountRequired && !currencyValue && !amountValue) {
                return Promise.resolve();
              }
              
              // If currency is required but empty
              if (isCurrencyRequired && !currencyValue) {
                return Promise.reject(typeof currencyRules[0] === 'object' && 'message' in currencyRules[0] 
                  ? currencyRules[0].message 
                  : '请选择币种');
              }
              
              // If amount is required but empty
              if (isAmountRequired && !amountValue) {
                return Promise.reject(typeof amountRules[0] === 'object' && 'message' in amountRules[0] 
                  ? amountRules[0].message 
                  : '请输入金额');
              }
              
              // If amount is provided (even if not required), validate other rules
              if (amountValue !== undefined && amountValue !== null && amountValue !== '') {
                // Apply additional amount validation rules (like min, max, pattern)
                for (const rule of amountRules) {
                  if (typeof rule === 'object') {
                    // Skip the required rule since we already handled it
                    if ('required' in rule) continue;
                    
                    // Validate min value
                    if ('min' in rule && typeof rule.min === 'number' && amountValue < rule.min) {
                      return Promise.reject(rule.message || `Value must be at least ${rule.min}`);
                    }
                    
                    // Validate max value
                    if ('max' in rule && typeof rule.max === 'number' && amountValue > rule.max) {
                      return Promise.reject(rule.message || `Value must be at most ${rule.max}`);
                    }
                    
                    // Validate pattern
                    if ('pattern' in rule && rule.pattern instanceof RegExp) {
                      const stringValue = String(amountValue);
                      if (!rule.pattern.test(stringValue)) {
                        return Promise.reject(rule.message || 'Invalid format');
                      }
                    }
                  }
                }
              }
              
              return Promise.resolve();
            },
          }),
        ]}
      >
        <div className="fp-compact">
          <Form.Item 
            name={currencyName} 
            noStyle
            rules={[]}
          >
            <Select
              variant="borderless"
              size="middle"
              style={{ width: currencyWidth }}
              placeholder={currencyPlaceholder}
              onChange={(value) => {
                setCurrentCurrency(value);
                if (onCurrencyChange) {
                  onCurrencyChange(value);
                }
              }}
              onBlur={() => {
                if (onCurrencyBlur) {
                  onCurrencyBlur(currentCurrency);
                }
              }}
            >
              {currencyOptions.map(option => (
                <Option key={option.value} value={option.value}>{option.label}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item 
            name={amountName} 
            noStyle
            rules={[]}
          >
            <InputNumber
              variant="borderless"
              className="flex-1"
              size="middle"
              placeholder={amountPlaceholder}
              controls={false}
              onChange={(value) => {
                const numericValue = typeof value === 'string' ? parseFloat(value) || null : value;
                setCurrentAmount(numericValue);
                if (onAmountChange && (numericValue === null || typeof numericValue === 'number')) {
                  onAmountChange(numericValue);
                }
              }}
              onBlur={() => {
                if (onAmountBlur) {
                  onAmountBlur(currentAmount);
                }
              }}
            />
          </Form.Item>
        </div>
      </Form.Item>
    </Form.Item>
  );
};

export default CurrencyAmountInput; 