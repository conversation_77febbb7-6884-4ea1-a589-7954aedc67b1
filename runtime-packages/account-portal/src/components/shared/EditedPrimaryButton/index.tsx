import { Button } from "antd";
import { THEME_CONSTANTS } from "../constants";

interface PrimaryButtonProps {
  onClick: () => void;
  loading?: boolean;
  label: string;
  type?: "primary" | "default";
  width?: number;
  height?: number;
  disabled?: boolean; 
}

const EditedPrimaryButton = ({
  onClick,
  loading,
  label,
  type = "primary",
  width = 88,
  height = 40,
  disabled, // 接收 disabled 屬性
}: PrimaryButtonProps) => {
  let className = `w-[${width}px] h-[${height}px] rounded-[40px] border-none transition-all flex items-center justify-center`; // 增加 flex 佈局，使文字居中
  
  if (type === "primary") {
    className += `bg-[#FF4D4F] text-white hover:opacity-80 hover:!bg-[#FF7875] hover:!text-white`;
  } else {
    className += ` ${disabled ? 'border border-[#B2B5BF] text-gray-500 bg-gray-100 cursor-not-allowed' : 'border border-[#B2B5BF] hover:border-black hover:text-black hover:bg-[#F5F5F5]'}`;
  }

  let style = {};

  if (type === "primary") {
    if (!disabled) {
      style = {
        background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR,
        // Add other styles specific to the primary button when not disabled
      };
    } else {
      style = {
        background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR,
        opacity: 0.6, 
        color: "white", 
      };
    }
  }




  return (
    <Button
      loading={loading}
      className={className}
      style={style}
      onClick={onClick}
      disabled={disabled} 
      
    >
      {label}
    </Button>
  );
};

// Set display name for debugging
EditedPrimaryButton.displayName = 'EditedPrimaryButton';

export default EditedPrimaryButton;