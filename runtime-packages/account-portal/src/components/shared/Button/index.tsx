import { Button as AntButton } from "antd";
import styles from "./styles.module.scss";
import { CustomButtonProps } from "@/types/common/Button/customButton.types";
import { FORM_CONSTANTS } from "../constants";

// Button component
export const Button: React.FC<CustomButtonProps> = ({ className, fullWidth, style, label, disabled, ...props }) => {
    return (
        <AntButton
            className={`${styles.customButton} ${fullWidth ? "w-full" : ""} ${className || ""}`}
            style={{
                height: FORM_CONSTANTS.BUTTON.HEIGHT,
                borderRadius: FORM_CONSTANTS.BUTTON.BORDER_RADIUS,
                width: FORM_CONSTANTS.BUTTON.WIDTH,
                fontSize: FORM_CONSTANTS.BUTTON.FONT_SIZE,
                ...style
            }}
            disabled={disabled}
            {...props}
        >
            {label}
        </AntButton>
    );
};

// Export with proper display name for debugging
Button.displayName = 'Button';

export default Button;
