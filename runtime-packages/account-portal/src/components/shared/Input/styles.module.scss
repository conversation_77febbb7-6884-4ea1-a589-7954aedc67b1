@use "@/assets/styles/variables.module.scss" as *;

.customInput {
    :global {
      .ant-input {
        font-size: 14px;
        
        &:focus {
          box-shadow: none;
          border-color: #1677ff;
        }
      }
  
      .ant-input-password-icon {
        color: #9E9EA3;
        font-size: 24px;
		transition: all 0.3s;
		cursor: pointer;
        &:hover {
          color: $fp-text-color;
        }
      }
    }
  }

.passwordContainer {
  position: relative;
  width: 100%;
}

.verificationTips {
  position: absolute;
  margin-top: 4px;
  padding: 16px;
  background-color: #f5f6f7;
  border-radius: 10px;
  z-index: 100;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .title {
    font-weight: 700;
    margin-bottom: 12px;
  }

  .requirement {
    display: flex;
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;
    font-weight:400;

    .checkmark {
      margin-right: 8.25px;
      color: #6E6E75;
      
      &.valid {
        margin-right: 8.25px;
        color: #40B16E;
      }
    }

    .checkText {
      color: #6E6E75;
      font-weight: 400;
      
      &.valid {
        color: #40B16E;
      }
    }
  }

  .reminder {
    display: flex;
    align-items: center;
    margin-top: 16px;
    color: #666;
    font-size: 14px;

    .reminderIcon {
      margin-right: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .divider {
    height: 1px;
    background-color: #DDDFE6;
    margin: 12px 0;
  }
}