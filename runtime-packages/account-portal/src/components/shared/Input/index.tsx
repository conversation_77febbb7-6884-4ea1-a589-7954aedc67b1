import { Input as AntInput } from "antd";
import type { InputRef } from "antd";
import { FORM_CONSTANTS, THEME_CONSTANTS } from "../constants";
import styles from "./styles.module.scss";
import { CustomInputProps, CustomPasswordProps } from "@/types/common/Input/customeInput.types";
import { useTranslation } from "react-i18next";
import { useMemo, useState, useCallback, forwardRef } from "react";
import NotCheckedIcon from "@/assets/icons/not-checked.svg?react";
import CheckedIcon from "@/assets/icons/checked.svg?react";
import OpenEyeIcon from "@/assets/icons/open-eye.svg?react";
import CloseEyeIcon from "@/assets/icons/close-eye.svg?react";
import ReminderFaceIcon from "@/assets/icons/reminder-face.svg?react";

export const Input = forwardRef<InputRef, CustomInputProps>((props, ref) => {
    const { onChange, onBlur, onFocus, trackingId, ...rest } = props;
    
    const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
        onFocus?.(e);
    }, [onFocus]);

    const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
        onBlur?.(e);
    }, [onBlur]);

    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        onChange?.(e);
    }, [onChange]);

    return (
        <AntInput
            className={`${styles.customInput} ${props.className || ""}`}
            style={{
                height: FORM_CONSTANTS.INPUT.HEIGHT,
                borderRadius: FORM_CONSTANTS.INPUT.BORDER_RADIUS,
                color: props.disabled ? "rgba(0, 0, 0, 0.25)" : THEME_CONSTANTS.PRIMARY_TEXT_COLOR,
                fontSize: "14px",
                backgroundColor: props.disabled ? "#F6F6F8" : undefined,
                border: props.disabled ? "1px solid #DDDFE6" : undefined,
                ...props.style
            }}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...rest}
            ref={ref}
        />
    );
});

export const Password = forwardRef<InputRef, CustomPasswordProps>((props, ref) => {
    const { onChange, onBlur, onFocus, trackingId, showVerificationTips, disableAutoComplete, ...rest } = props;
    
    const [value, setValue] = useState<string>("");
    const [isFocused, setIsFocused] = useState(false);
    const [hasInteracted, setHasInteracted] = useState(false);
    
    const currentValue = value || String(props.value || "");
    const lengthValid = currentValue.length >= 8 && currentValue.length <= 16;
    const complexityValid = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/.test(currentValue);
    const hasValidationErrors = !lengthValid || !complexityValid;
    
    const showErrorTips = useMemo<boolean>(
        () => !!showVerificationTips && (isFocused || (hasInteracted && hasValidationErrors && !!currentValue)),
        [showVerificationTips, currentValue, isFocused, hasInteracted, hasValidationErrors]
    );

    const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(true);
        onFocus?.(e);
    }, [onFocus]);

    const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        onBlur?.(e);
    }, [onBlur]);

    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(e.target.value);
        setHasInteracted(true);
        onChange?.(e);
    }, [onChange]);

    return (
        <div className={styles.passwordContainer}>
            <AntInput.Password
                className={`${styles.customInput} ${props.className || ""}`}
                style={{
                    height: FORM_CONSTANTS.INPUT.HEIGHT,
                    borderRadius: FORM_CONSTANTS.INPUT.BORDER_RADIUS,
                    color: props.disabled ? "rgba(0, 0, 0, 0.25)" : THEME_CONSTANTS.PRIMARY_TEXT_COLOR,
                    fontSize: "14px",
                    ...props.style
                }}
                iconRender={visible => {
                    return visible ? (
                        <OpenEyeIcon className="ant-input-password-icon" />
                    ) : (
                        <CloseEyeIcon className="ant-input-password-icon" />
                    );
                }}
                {...rest}
                autoComplete={disableAutoComplete ? "new-password" : props.autoComplete}
                onChange={handleChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
                ref={ref}
            />
            {showErrorTips && <VerificationTips value={currentValue} />}
        </div>
    );
});

const VerificationTips: React.FC<{ value: string }> = ({ value }) => {
    const { t } = useTranslation();

    const lengthValid = value.length >= 8 && value.length <= 16;
    const complexityValid = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/.test(value);

    return (
        <div className={styles.verificationTips}>
            <div className={styles.title}>{t("password.requirements")}</div>
            <div className={styles.requirement}>
                <span className={`${styles.checkmark} ${lengthValid ? styles.valid : ""}`}>
                    {lengthValid ? <CheckedIcon /> : <NotCheckedIcon />}
                </span>
                <span className={`${styles.checkText} ${lengthValid ? styles.valid : ""}`}>
                    {t("password.lengthRequirement")}
                </span>
            </div>
            <div className={styles.requirement}>
                <span className={`${styles.checkmark} ${complexityValid ? styles.valid : ""}`}>
                    {complexityValid ? <CheckedIcon /> : <NotCheckedIcon />}
                </span>
                <span className={`${styles.checkText} ${complexityValid ? styles.valid : ""}`}>
                    {t("password.complexityRequirement")}
                </span>
            </div>
            <div className={styles.divider}/>
            <div className={styles.reminder}>
                <span className={styles.reminderIcon}>
                    <ReminderFaceIcon className="w-[16px] h-[16px]" />
                </span>
                {t("password.reminder")}
            </div>
        </div>
    );
};

export default {
    Input,
    Password
};
