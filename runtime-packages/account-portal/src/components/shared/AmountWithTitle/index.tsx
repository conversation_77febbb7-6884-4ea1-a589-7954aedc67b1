import React from "react";
import styles from "./styles.module.scss";
import {formatNumberToFraction} from "@/utils/math";

interface AmountWithTitleProps {
    title?: string;
    amount?: number | string;
    loading?: boolean;
    helperText?: string;
}

const AmountWithTitle: React.FC<AmountWithTitleProps> = ({title, amount, loading = false, helperText}) => {
    if (loading) {
        return (
            <div className="flex flex-col items-center justify-center" style={{gap: "10px"}}>
                {title && <div className={styles.title}>{title}</div>}
                <div className={styles.amountWrapper}>
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"/>
                </div>
            </div>
        );
    }

    let formattedAmount: string | undefined;
    let shouldShowDollarSign = true;
    if (typeof amount === "number") {
        // formattedAmount = amount.toLocaleString('en-US', {
        //     minimumFractionDigits: 2,
        //     maximumFractionDigits: 2,
        // });
        formattedAmount = `${formatNumberToFraction(amount)}`;
    } else if (typeof amount === "string") {
        const n = Number(amount);
        if (!isNaN(n)) {
            formattedAmount = `${formatNumberToFraction(n)}`;
        } else {
            shouldShowDollarSign = false;
            formattedAmount = amount;
        }
    }

    return (
        <div className="flex flex-col items-center justify-center" style={{gap: "10px"}}>
            {title && <div className={styles.title}>{title}</div>}
            {formattedAmount != null && (
                <div className={styles.amountWrapper}>
                    {shouldShowDollarSign && <span className={styles.dollarSign}>$</span>}
                    <div className={styles.amount}>{formattedAmount}</div>
                </div>
            )}
            {helperText && <div className={styles.helperText}>{helperText}</div>}
        </div>
    );
};

export default AmountWithTitle;
