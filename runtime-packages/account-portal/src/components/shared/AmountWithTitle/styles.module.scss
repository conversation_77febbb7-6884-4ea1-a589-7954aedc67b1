@font-face {
  font-family: "Poppins";
  src: url("/assets/fonts/Poppins-Bold.ttf") format("truetype");
  font-weight: 700; // Adjust weight if necessary
  font-style: normal;
}

.title {
  font-family: Source Sans Pro, sans-serif;
  font-weight: 600;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  color: #635D7E;
  cursor: default;
}

.amountWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 135px;
  cursor: default;
}

.helperText {
  font-family: Poppins, sans-serif;
  font-weight: 700;
  font-size:  25px;
  line-height: 100%;
  letter-spacing: 0;
  background: linear-gradient(360deg, #201747 0%, #2B1D68 82.44%, #FFFFFF 94.21%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  @media (max-width: 768px) { // smaller than md
    font-size: clamp(14px, 10vw, 25px);
  }
}

.dollarSign {
  font-family: Poppins, sans-serif;
  font-weight: 600;
  font-size: 110px;
  line-height: 100%;
  letter-spacing: 0;
  background: linear-gradient(360deg, #201747 0%, #2B1D68 82.44%, #FFFFFF 94.21%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  @media (max-width: 768px) { // smaller than md
    font-size: 64px;
  }
  @media (max-width: 576px) { // smaller than sm
    font-size: 40px;
  }
}

.amount {
  font-family: Poppins, sans-serif;
  font-weight: 700;
  font-size:  110px;
  line-height: 100%;
  letter-spacing: 0;
  background: linear-gradient(360deg, #201747 0%, #2B1D68 82.44%, #FFFFFF 94.21%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  @media (max-width: 768px) { // smaller than md
    font-size: 64px;
  }
  @media (max-width: 576px) { // smaller than sm
    font-size: 40px;
  }
}
