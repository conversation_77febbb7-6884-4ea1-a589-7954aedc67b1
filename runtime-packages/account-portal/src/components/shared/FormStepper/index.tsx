import React, { useState } from 'react';
import './styles.scss';
interface StepperProps {
    steps: string[];
    activeStep?: number;
    borderTopRadiusRounded?: boolean;
}

const FormStepper: React.FC<StepperProps> = ({ 
    steps = [], 
    activeStep: externalActiveStep,
    borderTopRadiusRounded = false
}) => {
    const [internalActiveStep, setInternalActiveStep] = useState<number>(0);
    
    // Use external or internal state depending on which is provided
    const activeStep = externalActiveStep !== undefined ? externalActiveStep : internalActiveStep;

    const borderRadius = borderTopRadiusRounded ? '20px' : '0';

    return (
        <div className="stepper-container" style={{ borderTopLeftRadius: borderRadius, borderTopRightRadius: borderRadius }}>
            <div className="stepper">
                {steps.map((step, index) => (
                    <React.Fragment key={index}>
                        <div className={`step`}>
                            <div 
                                className={`step-content 
                                    ${index < activeStep ? 'completed' : ''} 
                                    ${index === activeStep ? 'active' : ''} 
                                    ${index > activeStep ? 'upcoming' : ''}`}
                                style={index === 0 ? { borderTopLeftRadius: borderRadius } : undefined}
                            >
                                <div className={`icon 
                                    ${index < activeStep ? 'completed' : ''} 
                                    ${index === activeStep ? 'active' : ''} 
                                    ${index > activeStep ? 'upcoming' : ''}`}>
                                    {index+1}
                                </div>
                                <div className={`step-name 
                                    ${index < activeStep ? 'completed' : ''} 
                                    ${index === activeStep ? 'active' : ''} 
                                    ${index > activeStep ? 'upcoming' : ''}`}>
                                    {step}
                                </div>
                            </div>
                        </div>
                    </React.Fragment>
                ))}
            </div>
        </div>
    );
};

export default FormStepper;