.stepper-container {
    width: 100%; /* Full width */
    display: flex; /* Use flexbox for layout */
    justify-content: center; /* Center the stepper */
}

.stepper {
    display: flex; /* Horizontal layout for steps */
    flex-grow: 1; /* Allow the stepper to grow */
    align-items: center; /* Center items vertically */
    position: relative; /* For positioning borders */
}

.step {
    flex: 1; /* Each step takes equal space */
    position: relative; /* Position relative for the border effect */
}

.step-content {
    padding: 10px;
    // cursor: pointer;
    display: flex; /* Use flexbox for the content */
    align-items: center; /* Center items vertically */
    justify-content: center; /* Center items horizontally */
    position: relative; /* Position relative for pseudo-element */
    flex: 100%;
    padding: 20px 20px 20px 40px;
    font-size: 18px;
    margin: 0 0 0 -19px;
    -webkit-clip-path: polygon(23px 50%, 0% 0%, calc(100% - 23px) 0%, 100% 50%, calc(100% - 23px) 100%, 0% 100%);
    height: 48px;
    @media (max-width: 576px) { // smaller than sm
        margin: 0;
    }
}

/* First child styling */
.step:first-child .step-content {
    padding: 20px;
    -webkit-clip-path: polygon(0% 0%, calc(100% - 23px) 0%, 100% 50%, calc(100% - 23px) 100%, 0% 100%);
    clip-path: polygon(0% 0%, calc(100% - 23px) 0%, 100% 50%, calc(100% - 23px) 100%, 0% 100%);
    /* border-radius is now handled dynamically via style prop */
}

/* Last child styling */
.step:last-child .step-content {
    -webkit-clip-path: polygon(23px 50%, 0% 0%, 100% 0%, 100% 100%, 0% 100%);
}


.step-content .icon {
    width: 24px; /* Width of the icon */
    height: 24px; /* Height of the icon */
    border-radius: 50%; /* Make it circular */
    border: 3px solid white; /* Circular border */
    background-color: transparent; /* No fill */
    color: white; /* Text color */
    display: flex; /* Use flexbox to center the text */
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    margin-right: 5px; /* Space between icon and text */
    font-size: 14px;
    font-family: 'Poppins', sans-serif; /* Apply Poppins font */
}

.step-content.completed {
    background-color: #201747; /* Color for completed steps */
}

.step-content.active {
    background-color: #201747; /* Color for active step */
}

.step-content.upcoming {
    background-color: #64CCC9; /* Color for upcoming steps */
}

.icon.completed {
    opacity: 0.4; 
}

.icon.upcoming {
    opacity: 0.4; 
}

.step-name {
    color: white; 
    transition: opacity 0.3s;
}

.step-name.completed {
    opacity: 0.4; 
}

.step-name.upcoming {
    opacity: 0.4;
}