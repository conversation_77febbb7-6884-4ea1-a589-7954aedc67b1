import { Modal as AntModal } from "antd";
import { ReactNode, useEffect } from "react";

interface ModalProps {
    open: boolean;
    onClose: () => void;
    title?: string | ReactNode;
    children: ReactNode;
    style?: React.CSSProperties;
    width?: number | "auto";
    loading?: boolean;
    height?: number | string;
    backgroundColor?: string;
    closeText?: string;
    closable?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
    open,
    onClose,
    title = "",
    children,
    style,
    width = 472,
    loading,
    height = "auto",
    closable = true,
    backgroundColor = "bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]",
    closeText,
}) => {

    const handleClose = () => {
        onClose();
    };
    
    return (
        <AntModal
            open={open}
            onCancel={handleClose}
            footer={null}
            className={`rounded-[24px] [&_.ant-modal-content]:!p-0 [&_.ant-modal-content]:!rounded-[24px] ${
                !closeText
                    ? "[&_.ant-modal-close]:!w-8 [&_.ant-modal-close]:!h-8 [&_.ant-modal-close]:!bg-white/40 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:!rounded-full"
                    : "[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!shadow-none [&_.ant-modal-close]:!border-none hover:[&_.ant-modal-close]:!bg-[transparent] active:[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!px-3 [&_.ant-modal-close]:!py-1 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:before:!content-[''] [&_.ant-modal-close]:after:!content-[''] [&_.ant-modal-close-x]:!bg-[transparent] [&_.ant-modal-close]:mr-2 [&_.ant-modal-close]:w-auto"
            }`}
            maskClosable={false}
            closable={closable}
            centered
            width={width}
            style={style}
            loading={loading}
            height={height}
            closeIcon={closeText}
        >
            <div className="h-full min-h-[124px]">
                <div
                    className={`flex justify-center items-center w-full rounded-t-[24px] py-10 ${backgroundColor}`}
                >
                    <span className="text-[22px] font-[900] text-[#282830]">{title}</span>
                </div>
                <div className="px-10 pb-10">
                    {children}
                </div>
            </div>
        </AntModal>
    );
};
