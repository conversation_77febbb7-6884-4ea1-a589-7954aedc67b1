import React, { useEffect } from 'react';

interface StatusBadgeProps {
    status: string;
    label: string;
  }

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, label }) => {
    const {backgroundColor, color } = getStatusConfig(status);

    return (
        <div style={{
            backgroundColor, 
            color,
            paddingTop: '2px',
            paddingLeft: '12px',
            paddingBottom: '2px',
            paddingRight: '12px',
            fontSize: '14px',
            borderRadius: '24px',
            border: 'none',
            gap: '4px',
            height: '30px',
            width: '66px',
            alignContent: 'center'
        }}>
        <span style={{
            width: '42px',
            height:'20px',
            fontSize: '14px',
        }}>
            {label}
        </span>
        </div>
    );
};

const getStatusConfig = (status: string) => {
        switch (status.toLowerCase()) {
        case 'warning':
            return {
            backgroundColor: 'rgba(221, 76, 76, 0.1)',
            color: '#DD4C4C'
            };
        case 'pass':
            return {
            backgroundColor: 'rgba(236, 247, 240, 1)',
            color: '#40B16E'
            };
        case 'pending':
            return {
                backgroundColor: '#E9E8ED',
                color: '#282830'
            };
        default:
            return {
            label: '',
            backgroundColor: '#E6FFF8',
            color: '#00A37A'
            };
        }
    };

export default StatusBadge;