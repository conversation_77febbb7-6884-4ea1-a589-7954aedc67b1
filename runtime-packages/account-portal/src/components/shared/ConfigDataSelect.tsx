import React from 'react';
import { Select, Spin } from 'antd';
import { useConfig } from '@/contexts/ConfigContext';
import { ConfigType } from '@/constants/configTypes';
import { CountryRegion, BankAccount } from '@fundpark/fp-api/types/drawdownForm.js';

interface ConfigDataSelectProps {
  type: ConfigType;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  style?: React.CSSProperties;
  className?: string;
  showSearch?: boolean;
}

const { Option } = Select;

const ConfigDataSelect: React.FC<ConfigDataSelectProps> = ({
  type,
  placeholder = '请选择',
  value,
  onChange,
  disabled = false,
  style,
  className,
  showSearch = false,
}) => {
  const { companyConfig, loading } = useConfig();
  
  if (loading) {
    return <Spin />;
  }
  
  if (!companyConfig || !companyConfig[type]) {
    return <Select placeholder={placeholder} disabled={true} />;
  }
  
  let options = companyConfig[type];

  // filter country_region to be china and hongkong
  if (type === ConfigType.COUNTRY_REGION) {
    options = options.filter((item: CountryRegion) => item.key_id === 1 || item.key_id === 2);
  }

  // Function to get the display name based on option type
  const getDisplayName = (option: any) => {
    // For bank accounts, display show_name if available
    if (type === ConfigType.BANK_ACCOUNT && 'show_name' in option) {
      return option.show_name;
    }
    return option.name_chi;
  };

  return (
    <Select
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      disabled={disabled}
      style={style}
      className={className}
      showSearch={showSearch}
      optionFilterProp="label"
    >
      {options.map(option => (
        <Option key={option.key_id.toString()} value={option.key_id.toString()} label={getDisplayName(option)}>
          {getDisplayName(option)}
        </Option>
      ))}
      {type === ConfigType.BANK_ACCOUNT && (
        <Option key="other" value="other" label="其他">
          其他
        </Option>
      )}
    </Select>
  );
};

export default ConfigDataSelect; 