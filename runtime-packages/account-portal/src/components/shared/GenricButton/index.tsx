import React from 'react';
import {But<PERSON> as AntButton, ButtonProps as Ant<PERSON><PERSON>} from 'antd';
import {FORM_CONSTANTS} from '../constants';
import styles from "./styles.module.scss";

function mergeClasses(...vals: Array<string | undefined | false>): string {
    return vals.filter(Boolean).join(' ');
}

export type ButtonVariant = 'primary' | 'secondary' | 'danger';

type OwnProps = {
    variant?: ButtonVariant;
    fullWidth?: boolean;
} & Pick<AntProps, 'loading' | 'disabled'>;

type Polymorphic<T extends React.ElementType> = {
    as?: T;
} & React.ComponentPropsWithRef<T>;

export const Button = React.forwardRef(
    <T extends React.ElementType = 'button'>(
        {
            as,
            variant = 'primary',
            fullWidth,
            className,
            children,
            style,
            ...rest
        }: OwnProps & Polymorphic<T>,
        ref: React.Ref<Element>,
    ) => {

        const Component = variant === 'primary' ? AntButton : (as ?? 'button');

        const variantClasses: Record<ButtonVariant, string> = {
            primary: styles.primaryButton,
            secondary: styles.secondaryButton,
            danger: styles.dangerButton,
        };

        const variantKey: ButtonVariant = variant;

        return (
            <Component
                ref={ref}
                type="button"
                className={mergeClasses(
                    variantClasses[variantKey],
                    fullWidth && 'w-full',
                    className,
                )}
                style={{
                    height: '40px',
                    borderRadius: FORM_CONSTANTS.BUTTON.BORDER_RADIUS,
                    fontSize: FORM_CONSTANTS.BUTTON.FONT_SIZE,
                    ...style,
                }}
                {...rest}
            >
                {children}
            </Component>
        );
    },
);

Button.displayName = 'Button';
export default Button;
