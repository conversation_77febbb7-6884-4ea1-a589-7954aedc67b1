
.primaryButton {
  color: white;
  background: linear-gradient(to bottom, #1E285F, #0c1026);

  &:hover {
    color: white !important;
    background: linear-gradient(to bottom, #2a367c, #161c3d) !important;
  }

  &:active {
    color: white !important;
    background: linear-gradient(to bottom, #141937, #060813) !important;
  }
}


.secondaryButton {
  display: flex;
  align-items: center;
  justify-content: center;

  min-width: 88px;
  width: auto;
  padding: 6px 12px;

  border: 1px solid #DDDFE6;
  border-radius: 52px;

  background: transparent;
  color: #201747;
  cursor: pointer;

  &:hover   { border-color: #201747; }
  &:active  { background: rgba(32, 23, 71, 0.04); }
  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}


.dangerButton {

  background: #D32F2F;
  color: #fff;

  &:hover  { background: #C62828; }
  &:active { background: #B71C1C; }

  &[disabled] {
    background: #EF9A9A;
    cursor: not-allowed;
  }
}
