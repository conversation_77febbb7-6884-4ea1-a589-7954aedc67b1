import React from "react";
import SupportedPlatform from '@/assets-new/temp/supported_platforms.svg'

interface AuthPlatformBannerProps {

}

const AuthPlatformBanner: React.FC<AuthPlatformBannerProps> = () => {
    return (
        <div
            style={{
                width: "100%",
                position: "relative",
                overflow: "hidden",
                borderRadius: "15px",
                marginTop: '32px',
                marginBottom: '128px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                paddingTop: '32px',
            }}
        >
            <h1 style={{ marginBottom: '16px', color: "#635D7E" }}>支持店铺授权平台</h1>
            <img
                src={SupportedPlatform}
                alt="Supported Platforms"
                style={{
                    height: "auto",
                    width: "100%",
                    objectFit: "cover",
                    marginTop: '32px',
                }}
            />
        </div>
    );
};

export default AuthPlatformBanner;