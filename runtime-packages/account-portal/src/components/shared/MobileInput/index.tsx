import React from 'react';
import { Form, Input } from 'antd';
import { FormItemProps } from 'antd/lib/form';
import type { FormInstance } from 'antd/lib/form';
import styles from "./styles.module.scss";

export interface AreaCodeOption {
  label: string;
  value: string;
}

interface MobileInputProps extends Omit<FormItemProps, 'children'> {
  // Visual props
  disabled?: boolean;
  placeholder?: string;

  // Form field names (for Form.Item context)
  mobileFieldName?: string;
  areaCodeFieldName?: string;

  // Standalone mode props (when not used within Form.Item)
  mobileValue?: string;
  areaCodeValue?: string;
  onChange?: (areaCode: string, mobile: string) => void;
  form: FormInstance;
  // Customization props
  areaCodes?: AreaCodeOption[];
  defaultAreaCode?: string;

  // Additional customization
  className?: string;
  inputClassName?: string;
  selectClassName?: string;
}

const DEFAULT_AREA_CODES: AreaCodeOption[] = [
  { value: "+86", label: "+86" },
  { value: "+852", label: "+852" },
  { value: "+1", label: "+1" }
];

const MobileInput: React.FC<MobileInputProps> = ({
  // Default props
  disabled = false,
  mobileFieldName = 'mobile',
  areaCodeFieldName = 'mobilePhoneAreaCode',
  placeholder = 'Phone number',

  // Standalone mode props
  mobileValue,
  areaCodeValue,
  onChange,
  form,

  // Customization props
  areaCodes = DEFAULT_AREA_CODES,
  defaultAreaCode = DEFAULT_AREA_CODES[0].value,
  className,
  inputClassName,
  selectClassName,
  
  // Rest of form props
  ...formItemProps
}) => {
  // Handle direct value changes (for standalone mode)
  const handleAreaCodeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onChange && mobileValue !== undefined) {
      onChange(e.target.value, mobileValue);
    }
  };

  const handleMobileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange && areaCodeValue !== undefined) {
      onChange(areaCodeValue, e.target.value);
    }
  };

  // Determine if we're in standalone mode (outside Form.Item context)
  const isStandalone = mobileValue !== undefined && areaCodeValue !== undefined && onChange !== undefined;

  // Create options for select element
  const renderOptions = () => {
    return areaCodes.map((option) => (
      <option key={option.value} value={option.value}>
        {option.label}
      </option>
    ));
  };

  // When component is disabled and inside a form
  const renderDisabledInForm = () => (
    <Form.Item noStyle shouldUpdate>
      {(form: FormInstance) => {
        const areaCode = form.getFieldValue(areaCodeFieldName) || defaultAreaCode;
        const number = form.getFieldValue(mobileFieldName) || '';
        return (
          <Input
            disabled={true}
            size="large"
            value={`${areaCode} ${number}`}
            className={`[&.ant-input-disabled]:!text-[#282830] [&.ant-input-disabled]:!rounded-[10px] [&.ant-input-disabled]:!bg-[rgba(32,23,71,0.04)] ${inputClassName || ''}`}
          />
        );
      }}
    </Form.Item>
  );

  // When component is enabled and inside a form
  const renderEnabledInForm = () => (
    <Form.Item
      name={mobileFieldName}
      validateStatus={form.getFieldError(mobileFieldName).length ? 'error' : ''}
      {...formItemProps}
    >
      <Input
        disabled={disabled}
        placeholder={placeholder}
        className={`${styles.mobileInput} ${inputClassName || ''}`}
        addonBefore={
          <Form.Item name={areaCodeFieldName} noStyle initialValue={defaultAreaCode}>
            <div className={`${styles.selectWrapper} ${selectClassName || ''}`}>
              <select className={styles.areaCodeSelect}
                onChange={(e) => {
                  const newAreaCode = e.target.value;
                  form.validateFields([mobileFieldName]); // Re-validate mobile field
                  onChange && onChange(newAreaCode, form.getFieldValue(mobileFieldName)); // Call onChange if defined
                }}>
                {renderOptions()}
              </select>
          
            <div className={styles.dropdownIcon}>
                <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 1L5 5L9 1" stroke="#666" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </Form.Item>
        }
        onChange={(e) => {
          onChange && onChange(form.getFieldValue(areaCodeFieldName), e.target.value);
        }}
      />
    </Form.Item>
  );

  // For standalone use outside of Form context
  const renderStandalone = () => (
    <Input
      disabled={disabled}
      value={mobileValue}
      onChange={handleMobileChange}
      addonBefore={
        <div className={`${styles.selectWrapper} ${selectClassName || ''}`}>
          <select 
            className={styles.areaCodeSelect}
            value={areaCodeValue}
            onChange={handleAreaCodeChange}
            disabled={disabled}
          >
            {renderOptions()}
          </select>
          <div className={styles.dropdownIcon}>
            <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 1L5 5L9 1" stroke="#666" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
      }
      placeholder={placeholder}
      size="large"
      className={`${styles.mobileInput} ${inputClassName || ''}`}
    />
  );

  // Main render logic
  if (isStandalone) {
    return <div className={className}>{renderStandalone()}</div>;
  }

  // Simply pass className directly 
  return (
    <Form.Item {...formItemProps} className={`${className} mb-0`}>
      {disabled ? renderDisabledInForm() : renderEnabledInForm()}
    </Form.Item>
  );
};

export default MobileInput; 