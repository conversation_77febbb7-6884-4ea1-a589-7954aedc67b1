.info-banner {
  padding: 8px 16px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  
  @media (max-width: 768px) { // smaller than md
    display: inline-table;
    padding: 16px 16px;
  }
  &.hongkong {
    background-color: #201747;
    color: #FFFFFF;
    
    .info-icon {
      border: 1px solid white;
    }
  }
  
  &.china {
    background-color: #E9E8ED;
    color: #201747;
    
    .info-icon {
      border: 1px solid #201747;
    }
  }
  
  &.warning {
    background-color: #FEF6EC;
    color: #F4A83C;
    
    .info-icon {
      border: 1px solid #F4A83C;
    }
  }
  
  &.info {
    background-color: #E8F4FD;
    color: #1890FF;
    
    .info-icon {
      border: 1px solid #1890FF;
    }
  }
  
  .info-icon {
    margin-right: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }
} 