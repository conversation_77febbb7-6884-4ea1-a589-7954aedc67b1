import React from 'react';
import styles from './styles.module.scss';

export interface TextProps extends React.HTMLAttributes<HTMLElement> {
  size?: 's';
  className?: string;
  style?: React.CSSProperties;
}

export const Text: React.FC<TextProps> = ({ className = '', size = 's', style, ...props }) => {
  const classes = [styles.text, styles[size], className]
    .filter(Boolean)
    .join(' ');

  return <span className={classes} style={style} {...props} />;
};

export default Text;
