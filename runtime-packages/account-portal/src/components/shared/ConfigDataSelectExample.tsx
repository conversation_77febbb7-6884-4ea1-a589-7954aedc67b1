import React from 'react';
import { Form, Card, Row, Col, Typography } from 'antd';
import ConfigDataSelect from './ConfigDataSelect';
import { ConfigType } from '@/constants/configTypes';

const { Title } = Typography;

const ConfigDataSelectExample: React.FC = () => {
  const [form] = Form.useForm();
  
  return (
    <Card title="Config Data Examples">
      <Form
        form={form}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col span={8}>
            <Title level={5}>国家及地区</Title>
            <Form.Item name="country_region" label="选择国家及地区">
              <ConfigDataSelect type={ConfigType.COUNTRY_REGION} />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Title level={5}>行业服务</Title>
            <Form.Item name="industry_service" label="选择行业服务">
              <ConfigDataSelect type={ConfigType.INDUSTRY_SERVICE} />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Title level={5}>手机区号</Title>
            <Form.Item name="mobile_area_code" label="选择手机区号">
              <ConfigDataSelect type={ConfigType.MOBILE_AREA_CODE} />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={8}>
            <Title level={5}>实体类型</Title>
            <Form.Item name="entity_type" label="选择实体类型">
              <ConfigDataSelect type={ConfigType.ENTITY_TYPE} />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Title level={5}>业务性质</Title>
            <Form.Item name="business_nature" label="选择业务性质">
              <ConfigDataSelect type={ConfigType.BUSINESS_NATURE} />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Title level={5}>称谓</Title>
            <Form.Item name="character_title" label="选择称谓">
              <ConfigDataSelect type={ConfigType.CHARACTER_TITLE} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default ConfigDataSelectExample; 