import { Button } from "antd";
import { THEME_CONSTANTS } from "../constants";

interface PrimaryButtonProps {
    onClick: () => void;
    loading?: boolean;
    label: string;
    type?: "primary" | "default";
    width?: number;
    height?: number;
}

// Primary Button component
const PrimaryButton = ({
    onClick,
    loading,
    label,
    type = "primary",
    width = 88,
    height = 40,
}: PrimaryButtonProps) => {
    const className = type === "primary" 
        ? `w-[${width}px] h-[${height}px] rounded-[40px] bg-[#FF4D4F] text-white border-none hover:opacity-80 hover:!bg-[#FF7875] hover:!text-white transition-all` 
        : `w-[${width}px] h-[${height}px] rounded-[40px] border border-[#B2B5BF] hover:border-black hover:text-black hover:bg-[#F5F5F5] transition-all`;

    const style = type === "primary" ? {
        background: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR
    } : {};

    return (
        <Button
            loading={loading}
            className={className}
            style={style}
            onClick={onClick}
        >
            {label}
        </Button>
    );
};

// Set display name for debugging
PrimaryButton.displayName = 'PrimaryButton';

export default PrimaryButton;
