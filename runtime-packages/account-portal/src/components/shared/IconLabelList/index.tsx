import React from 'react';
import styles from "./styles.module.scss";
import { IconLabelList as IconLabelData } from "@/constants/iconLabelList.ts";


export interface IconItem {
  id: string;
  icon: string;
  title: string;
}

interface IconLabelListProps {
  items: string[];
  containerClassName?: string;
  itemClassName?: string;
  iconStyle?: React.CSSProperties;
  labelStyle?: React.CSSProperties;
  renderItem?: (iconItem: IconItem) => React.ReactNode;
  containerStyle?: React.CSSProperties;
}


/**
 * Basic usage:
 * <IconLabelList items={LandingPageIconLabelListSDG} />
 *
 * Custom styling:
 * <IconLabelList
 *   items={LandingPageIconLabelListSDG}
 *   iconStyle={{ width: 32, height: 32 }}
 *   labelStyle={{ color: '#ff00aa' }}
 * />
 *
 * Fully custom rendering:
 * <IconLabelList
 *   items={LandingPageIconLabelListSDG}
 *   renderItem={(item) => (
 *     <div key={item.id} style={{ display: 'flex', alignItems: 'center' }}>
 *       <img src={item.icon} alt={item.title} width={24} height={24} />
 *       <span style={{ marginLeft: 8 }}>{item.title}</span>
 *     </div>
 *   )}
 * />
 */

const IconLabelList: React.FC<IconLabelListProps> = ({
  items,
  containerClassName,
  itemClassName,
  iconStyle,
  labelStyle,
  renderItem,
  containerStyle,
}) => {
  const filteredItems = IconLabelData.filter(item => items.includes(item.id));

  const defaultRenderItem = (iconItem: IconItem) => (
    <div key={iconItem.id} className={itemClassName || styles.item}>
      <img
        src={iconItem.icon}
        alt={iconItem.title}
        style={{ width: 18, height: 18, ...iconStyle }}
      />
      <p style={{ margin: 0, textAlign: "center", ...labelStyle }}>
        {iconItem.title}
      </p>
    </div>
  );

  return (
    <div className={containerClassName || styles.list} style={{ marginBottom: 14, ...containerStyle }}>
      {filteredItems.map(iconItem =>
        renderItem ? renderItem(iconItem) : defaultRenderItem(iconItem)
      )}
    </div>
  );
};

export default IconLabelList;
