// Component specific styles can be added here if needed
// Most styling is currently handled by the global fp-compact class 

// Add custom font styling for CountryCodePhoneInput
.fp-compact {
  display: flex;
  align-items: center;
  border: 1px solid #dddfe6;
  border-radius: 10px;
  transition: all 0.3s;
  overflow: hidden;

  &:hover {
    border-color: #000000;
  }

  &:focus-within {
    border-color: #000000;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  }

  &.fp-compact-disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    
    &:hover {
      border-color: #dddfe6;
    }
  }

  &.fp-compact-error {
    border-color: #ff4d4f !important;

    &:focus-within {
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }

  .ant-select-selector {
    border: none !important;
    box-shadow: none !important;
  }

  .ant-input {
    border: none !important;
    box-shadow: none !important;
  }
}

.full-width-item {
  width: 100%;
}

// Add styles for disabled state
.fp-compact-disabled {
  background-color: rgba(32, 23, 71, 0.04);
  border-radius: 8px;
  
  .ant-select-disabled {
    background-color: #F6F7FA !important;
  }
  
  .ant-input-disabled {
    background-color: transparent !important;
  }
  
  .ant-select-disabled,
  .ant-input-disabled {
    &::placeholder {
      color: rgba(0, 0, 0, 0.25);
    }
  }
} 