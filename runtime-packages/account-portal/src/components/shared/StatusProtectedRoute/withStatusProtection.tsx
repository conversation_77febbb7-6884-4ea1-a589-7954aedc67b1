import React from 'react';
import type { ComponentType } from 'react';
import type { PositionStatus } from '@/constants/position-status';
import StatusProtectedRoute from './index';

interface StatusProtectionConfig {
  requiredStatus: PositionStatus | PositionStatus[];
  fallbackPath?: string;
}

/**
 * Higher-order component that wraps a route component with status protection
 * 
 * @param WrappedComponent - The component to wrap with status protection
 * @param config - Configuration for status protection
 * @returns A new component with status protection
 */
export const withStatusProtection = (
  WrappedComponent: ComponentType<any>,
  config?: StatusProtectionConfig
) => {
  // If no protection config provided, return original component
  if (!config) {
    return WrappedComponent;
  }
  
  // Return protected component
  return function ProtectedComponent(props: any) {
    return (
      <StatusProtectedRoute
        requiredStatus={config.requiredStatus}
        fallbackPath={config.fallbackPath}
      >
        <WrappedComponent {...props} />
      </StatusProtectedRoute>
    );
  };
}; 