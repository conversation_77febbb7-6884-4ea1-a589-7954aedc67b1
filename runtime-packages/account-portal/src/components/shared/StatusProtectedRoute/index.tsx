import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { usePositionTracking } from '@/hooks/usePositionTracking';

interface StatusProtectedRouteProps {
  children: React.ReactNode;
  requiredStatus: string | string[];
  fallbackPath?: string;
}

/**
 * A wrapper component that protects routes based on the user's current position status
 * which is fetched from the API
 * 
 * @param children - The route content to render if status check passes
 * @param requiredStatus - Single status string or array of allowed status values
 * @param fallbackPath - (Deprecated) Path to redirect to if status check fails (defaults to '/')
 */
const StatusProtectedRoute: React.FC<StatusProtectedRouteProps> = ({
  children,
  requiredStatus,
  fallbackPath // deprecated
}) => {
  const location = useLocation();
  const { getCurrentPosition, loading } = usePositionTracking();
  const [currentStatus, setCurrentStatus] = useState<string | null>(null);
  const [savedPath, setSavedPath] = useState<string | null>(null);
  const [isChecking, setIsChecking] = useState(true);
  
  // Convert required status to array for easier checking
  const allowedStatuses = Array.isArray(requiredStatus) ? requiredStatus : [requiredStatus];
  
  useEffect(() => {
    const fetchPosition = async () => {
      try {
        setIsChecking(true);
        const position = await getCurrentPosition();
        setCurrentStatus(position?.status || null);
        setSavedPath(position?.path || null);
      } catch (error) {
        console.error('Error fetching position status:', error);
        setCurrentStatus(null);
        setSavedPath(null);
      } finally {
        setIsChecking(false);
      }
    };
    
    fetchPosition();
  }, [getCurrentPosition]);
  
  // Show a loading state while checking status
  if (isChecking || loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }
  
  // Check if current status matches any of the allowed statuses
  const hasValidStatus = currentStatus && allowedStatuses.includes(currentStatus);
  
  if (!hasValidStatus) {
    let path;
    if (savedPath && savedPath !== "undefined") {
      path = savedPath;
    } else {
      path = '/';
    }

    // Redirect to fallback path if status is invalid
    return <Navigate to={path} state={{ from: location }} replace />;
  }
  
  // Render children if status is valid
  return <>{children}</>;
};

export default StatusProtectedRoute; 