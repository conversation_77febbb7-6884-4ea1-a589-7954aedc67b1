import React from "react";

interface PageProps {
    className?: string;
    style?: React.CSSProperties;
    children: React.ReactNode;
}

const Page: React.FC<PageProps> = ({ className, style, children }) => {
    return (
        <div
            className={`max-w-[1440px] mx-auto px-4 lg:px-12 relative pt-[39px]${className ? " " + className : ""}`}
            style={style}
        >
            {children}
        </div>
    );
};

export default Page;
