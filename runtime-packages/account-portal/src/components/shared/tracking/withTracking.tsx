import React, { useContext } from 'react';
import { useMatomoContext } from '@/contexts/MatomoContext';

// Allow tracking configuration at a global level
export interface TrackingContextType {
  eventNamePrefix?: string;
  trackUserId?: boolean;
  trackUrl?: boolean;
  trackTime?: boolean;
  disableTracking?: boolean;
}

// Create a context for tracking configuration
export const TrackingContext = React.createContext<TrackingContextType>({
  eventNamePrefix: '',
  trackUserId: true,
  trackUrl: true,
  trackTime: true,
  disableTracking: false
});

// Provider component to configure tracking
export const TrackingProvider: React.FC<TrackingContextType & { children: React.ReactNode }> = ({ 
  children,
  ...config 
}) => {
  return (
    <TrackingContext.Provider value={config}>
      {children}
    </TrackingContext.Provider>
  );
};

// Hook to use tracking configuration
export const useTrackingConfig = () => useContext(TrackingContext);

export interface TrackingProps {
  /**
   * Simple tracking ID to identify this component in analytics
   * This is the preferred way to identify components - just add a trackingId
   */
  trackingId?: string;
  
  /**
   * Category for the tracking event (e.g., 'UI', 'Form', 'Navigation')
   */
  trackingCategory?: string;
  
  /**
   * Action for the tracking event (e.g., 'Click', 'Submit', 'Change')
   */
  trackingAction?: string;
  
  /**
   * Name for the tracking event (e.g., 'Submit Button', 'Login Form')
   * Note: If trackingId is provided, it will be used instead of trackingName
   */
  trackingName?: string;
  
  /**
   * Optional numeric value for the tracking event
   */
  trackingValue?: number;
  
  /**
   * Whether to disable tracking for this component
   */
  disableTracking?: boolean;
  
  /**
   * Prefix for the event name
   */
  eventNamePrefix?: string;
  
  /**
   * Whether to include user ID in tracking
   */
  trackUserId?: boolean;
  
  /**
   * Whether to include URL in tracking
   */
  trackUrl?: boolean;
  
  /**
   * Whether to include timestamp in tracking
   */
  trackTime?: boolean;
  
  /**
   * Custom dimensions to track
   */
  customDimensions?: Record<string, string | number>;
}

/**
 * Higher-order component that adds Matomo tracking to any component
 * 
 * @param Component - The component to wrap with tracking
 * @param defaultCategory - Default category for tracking events
 * @param defaultAction - Default action for tracking events
 * @returns A wrapped component with tracking capabilities
 */
export function withTracking<P extends object>(
  Component: React.ComponentType<P>,
  defaultCategory: string = 'UI',
  defaultAction: string = 'Click'
): React.FC<P & TrackingProps> {
  return (props: P & TrackingProps) => {
    const { 
      trackEvent, 
      setCustomDimension, 
      setUserId, 
      isEnabled 
    } = useMatomoContext();
    
    // Get global tracking configuration
    const globalConfig = useTrackingConfig();
    
    const {
      trackingId,
      trackingCategory = defaultCategory,
      trackingAction = defaultAction,
      trackingName,
      trackingValue,
      disableTracking = globalConfig.disableTracking,
      eventNamePrefix = globalConfig.eventNamePrefix,
      trackUserId = globalConfig.trackUserId,
      trackUrl = globalConfig.trackUrl,
      trackTime = globalConfig.trackTime,
      customDimensions,
      ...componentProps
    } = props;
    
    // Get a display name from the component for tracking if no name is provided
    const componentName = 
      Component.displayName || 
      Component.name || 
      'Component';
    
    // The name to use for tracking - prioritize trackingId if provided
    let eventName = trackingId || trackingName || componentName;
    
    // Add prefix if provided
    if (eventNamePrefix) {
      eventName = `${eventNamePrefix}${eventName}`;
    }
    
    /**
     * Helper function to get user ID from the actual user storage pattern
     * Gets user ID from userInfo object in localStorage (matches app's storage pattern)
     * Only returns user ID if user is actually logged in (has valid access token)
     */
    const getUserId = (): string | null => {
      try {
        // Check if user is actually logged in by verifying access token exists
        const accessToken = localStorage.getItem('accessToken');
        if (!accessToken) {
          return null; // User is not logged in
        }
        
        // Get userInfo from localStorage (this is how the app actually stores user data)
        const userInfoString = localStorage.getItem('userInfo');
        if (userInfoString) {
          const userInfo = JSON.parse(userInfoString);
          return userInfo?.id ? String(userInfo.id) : null;
        }
        
        return null;
      } catch (error) {
        console.error('Error retrieving user ID for tracking:', error);
        return null;
      }
    };
    
    /**
     * Set custom dimensions for additional tracking information
     */
    const setTrackingDimensions = () => {
      // Set custom dimensions if provided
      if (customDimensions && isEnabled) {
        Object.entries(customDimensions).forEach(([dimensionId, value]) => {
          setCustomDimension(parseInt(dimensionId, 10), String(value));
        });
      }
      
      // Track user ID if enabled
      if (trackUserId && isEnabled) {
        const userId = getUserId();
        if (userId) {
          // Set the user ID in Matomo
          setUserId(userId);
          
          // Also set as custom dimension for this specific event
          setCustomDimension(1, userId);
        }
      }
      
      // Track current time if enabled
      if (trackTime && isEnabled) {
        setCustomDimension(2, new Date().toISOString());
      }
      
      // Track URL if enabled
      if (trackUrl && isEnabled) {
        setCustomDimension(3, window.location.href);
      }
    };
    
    /**
     * Handle click events with tracking
     */
    const handleTrackedEvent = (
      event: React.MouseEvent<HTMLElement>,
      originalHandler?: (event: React.MouseEvent<HTMLElement>) => void
    ) => {
      // Track the event if tracking is enabled and not explicitly disabled
      if (isEnabled && !disableTracking) {
        // Set any custom dimensions before tracking
        setTrackingDimensions();
        
        // Track the event
        trackEvent(trackingCategory, trackingAction, eventName, trackingValue);
      }
      
      // Call the original handler if provided
      if (originalHandler) {
        originalHandler(event);
      }
    };
    
    // Find onClick handler if it exists in props
    const onClick = (componentProps as any).onClick;
    
    // Prepare the new props with the tracked click handler
    const newProps = {
      ...componentProps,
      onClick: onClick 
        ? (e: React.MouseEvent<HTMLElement>) => handleTrackedEvent(e, onClick)
        : undefined
    } as any;
    
    return <Component {...newProps} />;
  };
}

export default withTracking; 