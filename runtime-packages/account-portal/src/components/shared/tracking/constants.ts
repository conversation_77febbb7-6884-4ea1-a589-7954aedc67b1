// Tracking Event Constants
// Centralized location for all trackEvent names used throughout the application

// Categories
export const TRACKING_CATEGORIES = {
    UI: "UI",
    BUTTON: "Button",
    FORM: "Form",
    USER: "User",
    PURCHASE: "Purchase",
    USER_INTERFACE: "User Interface"
} as const;

// Actions
export const TRACKING_ACTIONS = {
    CLICK: "Click",
    INPUT: "Input",
    SIGNUP: "Signup",
    LOGIN: "Login",
    SUBMIT: "Submit",
    CHANGE: "Input Change",
    BLUR: "Input Blur",
    BUTTON_CLICK: "Button Click",
    COMPLETE: "Complete",
    SELECT: "Select",
    VIEW: "View",
    UPLOAD: "Upload"
} as const;

// Event Names
export const TRACKING_EVENTS = {
    // Authentication Events
    SIGNUP_SUCCESS: "signup_success",
    LOGIN_SUCCESS: "login_success",
    ENTER_LOGIN_PAGE: "enter_login_page",

    // Red Pocket Events
    USE_FREE_XDJ: "use_free_xdj",
    GET_7_DAY_RED_PACKET: "get_7_day_red_packet",
    GOT_500_RED_PACKET: "got_500_red_packet",
    GET_500_RED_PACKET: "get_500_red_packet",

    // Limit Actions Events
    INCREASE_LIMIT_HOOK_TO_LITE: "increase_limit_hook_to_lite",

    // KYC Events
    FINISH_FILLING_KYC: "finish_filling_kyc",

    // Platform Events
    CLICK_AUTHORIZE_STORE: "click_authorize_store",
    PSP_CONFIRM_AUTHORIZE: "psp_confirm_authorize",

    // Form Events
    SUBMIT_APPLICATION: "submit_application",
    USERNAME_FIELD: "username_field",
    CONTACT_FORM: "contact_form",
    SUBMIT_BUTTON: "submit_button",
    INITIATE_SIGNING_BUTTON: "initiate_signing_button",
    RESEND_SIGNING_BUTTON: "resend_signing_button",
    REFRESH_SIGNING_PAGE_BUTTON: "refresh_signing_page_button",

    // Form Input
    INPUT_COMPANY_NAME_CN: "company_name_cn",
    INPUT_COMPANY_NAME_EN: "company_name_en",
    INPUT_PHONE_NUMBER: "phone_number",
    INPUT_EMAIL: "email",
    SELECT_INDUSTRY: "industry",
    SELECT_COUNTRY_REGION: "country_region",
    INPUT_BRN: "brn",
    INPUT_DRAWDOWN_AMOUNT: "drawdown_amount",
    SELECT_BANK: "bank",
    INPUT_BANK_ACCOUNT_NAME: "bank_account_name",
    INPUT_OTHER_BANK_NAME: "other_bank_name",
    INPUT_BANK_ACCOUNT_NUMBER: "bank_account_number",
    INPUT_SWIFT_CODE: "swift_code",
    INPUT_BANK_ADDRESS: "bank_address",
    SELECT_TOP_BUYER_COUNTRIES: "top_buyer_countries",
    SELECT_TOP_SUPPLIER_COUNTRIES: "top_supplier_countries",
    SELECT_FUND_SOURCE_COUNTRIES: "fund_source_countries",
    INPUT_MAIN_PRODUCTS: "main_products",
    SELECT_INITIAL_WEALTH_SOURCES: "initial_wealth_sources",
    SELECT_ONGOING_INCOME_SOURCES: "ongoing_income_sources",
    SELECT_FUNDING_SOURCES: "funding_sources",
    CHECK_BUSINESS_INFO_CONFIRMATION: "business_info_confirmation",
    CLICK_BUSINESS_INFO_SUBMIT: "business_info_submit",
    SELECT_NATIONALITY: "nationality",
    SELECT_ID_TYPE: "id_type",
    INPUT_SIGNING_PHONE_NUMBER: "signing_phone_number",
    INPUT_SIGNING_EMAIL: "signing_email",
    ALL_ID_DOCUMENTS_UPLOADED: "all_id_documents_uploaded",
    SUBMITTED_MODAL_CONFIRM_BUTTON: "submitted_modal_confirm_button",

    VIEW_LANDING_PAGE: "view_landing_page",
    CLICK_BANNER_SWITCH_PAGE: "click_banner_switch_page",
    PAGE_EXIT_BROWSER_CLOSE: "page_exit_browser_close",

    // Landing page modal
    OPEN_2507_RED_MODAL: "open_2507_red_modal",
    CLOSE_2507_RED_MODAL: "close_2507_red_modal",
} as const;

// Type definitions for better type safety
export type TrackingCategory = (typeof TRACKING_CATEGORIES)[keyof typeof TRACKING_CATEGORIES];
export type TrackingAction = (typeof TRACKING_ACTIONS)[keyof typeof TRACKING_ACTIONS];
export type TrackingEvent = (typeof TRACKING_EVENTS)[keyof typeof TRACKING_EVENTS];
