import React from 'react';
import { LimitIncreaseMessageProps } from './types';

export const LimitIncreaseMessage: React.FC<LimitIncreaseMessageProps> = () => {
    const baseStyles: React.CSSProperties = {
        fontSize: "20px",
        fontWeight: 600,
        color: "#BCB9C8",
        textAlign: "center",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        marginTop: "10px",
        padding: "0 16px",
        lineHeight: "1.4",
        flexWrap: "wrap"
    };

    // Use window width to determine responsive styles
    const [windowWidth, setWindowWidth] = React.useState(typeof window !== 'undefined' ? window.innerWidth : 1024);

    React.useEffect(() => {
        const handleResize = () => setWindowWidth(window.innerWidth);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const getResponsiveStyles = (): React.CSSProperties => {
        if (windowWidth <= 480) {
            return {
                ...baseStyles,
                fontSize: "14px",
                marginTop: "6px",
                padding: "0 8px",
                lineHeight: "1.5"
            };
        } else if (windowWidth <= 768) {
            return {
                ...baseStyles,
                fontSize: "16px",
                marginTop: "8px",
                padding: "0 12px"
            };
        } else if (windowWidth >= 1200) {
            return {
                ...baseStyles,
                fontSize: "22px",
                marginTop: "12px"
            };
        }
        return baseStyles;
    };

    return (
        <div style={getResponsiveStyles()}>
            预计<span style={{ color: "#4D456C", margin: "0 4px" }}>30分钟</span>完成，请点击右侧【线上客服】联系我们，查看额度
        </div>
    );
};

export default LimitIncreaseMessage; 