import React from "react";
import { XD<PERSON><PERSON><PERSON><PERSON><PERSON>ist,  CAABannerList} from "@/constants/carouselBannerList";
import { Carousel } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import { useCommonStore } from "@/store/common.ts";
import { useMatomoContext } from "@/contexts/MatomoContext";
import { TRACKING_CATEGORIES, TRACKING_EVENTS, TRACKING_ACTIONS } from "@/components/shared/tracking/constants";
import { MATOMO_CONFIG } from "@/utils/matomoConfig";
interface BannerCarouselProps {
}

const BannerCarousel: React.FC<BannerCarouselProps> = () => {
    const location = useLocation();
    const currentPath = location.pathname;
    const BannerList = currentPath.includes("underwriting")? CAABannerList : XDJBannerList;
    const navigate = useNavigate();
    const user = useCommonStore((s) => s.userInfo);
    const { trackEventWithDimensions } = useMatomoContext();

    return (
        <Carousel
            autoplay
            dots={true}
            style={{
                width: "100%",
                position: "relative",
                overflow: "hidden",
                borderRadius: "24px",
                marginTop:'30px',
                marginBottom:'16px',
                cursor:!user? 'pointer': 'default'
            }}
        >
            {BannerList.map(bannerItem => (
                <div 
                key={bannerItem.id} 
                onClick={() => {
                    if (!user) {
                        trackEventWithDimensions({
                            category: TRACKING_CATEGORIES.UI,
                            action: TRACKING_ACTIONS.CLICK,
                            name: TRACKING_EVENTS.CLICK_BANNER_SWITCH_PAGE,
                            customDimensions: {
                                [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]:bannerItem.banner_id
                            }
                        });
                        bannerItem.bannerEvent(navigate);
                    }}} 
                style={{ width: '100%', height: '100%' }}>
                    <img
                        src={bannerItem.banner}
                        alt={"hi"}
                        style={{ height: "100%", width: "100%", objectFit: "cover",display: "block" }}
                    />
                </div>
            ))}
        </Carousel>
    );
};

export default BannerCarousel;
