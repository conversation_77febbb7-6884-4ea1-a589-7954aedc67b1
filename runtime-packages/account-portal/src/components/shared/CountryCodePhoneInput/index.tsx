import React, {useState} from 'react';
import {Form, Input, Select} from 'antd';
import {Rule} from 'antd/es/form';
import './styles.scss';

const {Option} = Select;

export interface CountryCodeOption {
    value: string;
    label: string;
}

export interface CountryCodePhoneInputProps {
    countryCodeName: string;
    phoneNumberName: string;
    countryCodeOptions: CountryCodeOption[];
    countryCodeRules?: Rule[];
    phoneNumberRules?: Rule[];
    label?: string;
    countryCodePlaceholder?: string;
    phoneNumberPlaceholder?: string;
    className?: string;
    countryCodeWidth?: number;
    onCountryCodeChange?: (value: string) => void;
    onPhoneNumberBlur?: (value: string) => void;
    combinedErrorMessage?: string;
    disabled?: boolean;
    defaultCountryCode?: string;
    defaultPhoneNumber?: string;
    combinedInputStyle?:  React.CSSProperties;
    placeholderColor?: string;
    countryCodeColor?: string;
    errorColor?: string;
}

const CountryCodePhoneInput: React.FC<CountryCodePhoneInputProps> = ({
                                                                         countryCodeName,
                                                                         phoneNumberName,
                                                                         countryCodeOptions,
                                                                         countryCodeRules = [{
                                                                             required: true,
                                                                             message: '请选择国家/地区代码'
                                                                         }],
                                                                         phoneNumberRules = [{
                                                                             required: true,
                                                                             message: '请输入电话号码'
                                                                         }],
                                                                         label,
                                                                         countryCodePlaceholder = '请选择',
                                                                         phoneNumberPlaceholder = '请输入',
                                                                         className = '',
                                                                         countryCodeWidth = 88,
                                                                         onCountryCodeChange,
                                                                         onPhoneNumberBlur,
                                                                         combinedErrorMessage = '请选择国家/地区代码并输入电话号码',
                                                                         disabled = false,
                                                                         defaultCountryCode,
                                                                         defaultPhoneNumber,
                                                                         combinedInputStyle,
                                                                         placeholderColor,
                                                                         countryCodeColor,
                                                                         errorColor
                                                                     }) => {
    // Create a combined validation field
    const combinedFieldName = `${countryCodeName}_${phoneNumberName}_combined`;
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [validationStatus, setValidationStatus] = useState<'' | 'error'>('');

    // Get the form instance from context
    const form = Form.useFormInstance();

    // Handle validation
    const validateFields = async () => {
        try {
            await form.validateFields([combinedFieldName]);
            setErrorMessage(null);
            setValidationStatus('');
        } catch (error: any) {
            if (error.errorFields && error.errorFields.length > 0) {
                const fieldErrors = error.errorFields[0];
                if (fieldErrors.name.includes(combinedFieldName)) {
                    setErrorMessage(fieldErrors.errors[0]);
                    setValidationStatus('error');
                }
            }
        }
    };

    // // Add effect to validate when form values change
    // useEffect(() => {
    //   const values = form.getFieldsValue([countryCodeName, phoneNumberName]);
    //   if (values[countryCodeName] || values[phoneNumberName]) {
    //     validateFields();
    //   }
    // }, [form.getFieldValue(countryCodeName), form.getFieldValue(phoneNumberName)]);

    return (
        <Form.Item
            label={label}
            className={`full-width-item ${className}`}
            required
            validateStatus={validationStatus}
            help={errorMessage}
            validateTrigger={['onBlur', 'onSubmit']}
            style={{
                marginBottom:  'auto',
            }}
            rules={[
                () => ({
                    async validator() {
                        const ccTouched = form.isFieldTouched(countryCodeName);
                        const phoneTouched = form.isFieldTouched(phoneNumberName);

                        if (!ccTouched && !phoneTouched) return;

                        const cc = form.getFieldValue(countryCodeName);
                        const phone = form.getFieldValue(phoneNumberName);

                        if (!cc) throw new Error('请选择国家/地区代码');
                        if (!phone) throw new Error('请输入电话号码');
                    },
                }),
            ]}
        >
            <Form.Item
                name={combinedFieldName}
                dependencies={[countryCodeName, phoneNumberName]}
                noStyle
                rules={[
                    ({getFieldValue}) => ({
                        validator(_, value) {
                            const countryCodeValue = getFieldValue(countryCodeName);
                            const phoneNumberValue = getFieldValue(phoneNumberName);

                            if (!countryCodeValue && !phoneNumberValue) {
                                return Promise.reject(combinedErrorMessage);
                            }
                            if (!countryCodeValue) {
                                return Promise.reject(typeof countryCodeRules[0] === 'object' && 'message' in countryCodeRules[0]
                                    ? countryCodeRules[0].message
                                    : '请选择国家/地区代码');
                            }
                            if (!phoneNumberValue) {
                                return Promise.reject(typeof phoneNumberRules[0] === 'object' && 'message' in phoneNumberRules[0]
                                    ? phoneNumberRules[0].message
                                    : '请输入电话号码');
                            }

                            // Apply additional phone number validation rules (like pattern)
                            for (const rule of phoneNumberRules) {
                                if (typeof rule === 'object') {
                                    // Validate pattern
                                    if ('pattern' in rule && rule.pattern instanceof RegExp) {
                                        const stringValue = String(phoneNumberValue);
                                        if (!rule.pattern.test(stringValue)) {
                                            return Promise.reject(rule.message || '电话号码格式不正确');
                                        }
                                    }

                                    // Validate min/max length
                                    if ('min' in rule && typeof rule.min === 'number' && phoneNumberValue.length < rule.min) {
                                        return Promise.reject(rule.message || `电话号码至少需要 ${rule.min} 位`);
                                    }

                                    if ('max' in rule && typeof rule.max === 'number' && phoneNumberValue.length > rule.max) {
                                        return Promise.reject(rule.message || `电话号码不能超过 ${rule.max} 位`);
                                    }

                                    // Execute custom validator functions
                                    if ('validator' in rule && typeof rule.validator === 'function') {
                                        try {
                                            // Use Promise.resolve to handle both synchronous and asynchronous validators
                                            const validationResult = rule.validator(_, phoneNumberValue, getFieldValue);
                                            if (validationResult && typeof validationResult.then === 'function') {
                                                return validationResult;
                                            }
                                        } catch (error: any) {
                                            return Promise.reject(error.message || '电话号码验证失败');
                                        }
                                    }
                                }
                            }

                            return Promise.resolve();
                        },
                    }),
                ]}
            >
                <div
                    className={`fp-compact ${disabled ? 'fp-compact-disabled' : ''} ${validationStatus === 'error' ? 'fp-compact-error' : ''}`}
                    style={{'--errorText-color': errorColor,...combinedInputStyle}}
                    >
                    <Form.Item
                        name={countryCodeName}
                        noStyle
                        rules={countryCodeRules}
                        initialValue={defaultCountryCode}
                    >
                        <Select
                            variant="borderless"
                            size="middle"
                            style={{width: countryCodeWidth, textAlign: 'left', backgroundColor: countryCodeColor}}
                            placeholder={countryCodePlaceholder}
                            disabled={disabled}
                            status={validationStatus}
                            virtual={false}
                            dropdownMatchSelectWidth={false}
                            listHeight={256}
                            dropdownStyle={{minWidth: countryCodeWidth}}
                            onChange={(value) => {
                                if (onCountryCodeChange) {
                                    onCountryCodeChange(value);
                                }
                                // Trigger validation on change
                                setTimeout(() => {
                                    validateFields();
                                }, 0);
                            }}
                        >
                            {countryCodeOptions.map(option => (
                                <Option key={option.value} value={option.value}>{option.label}</Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item
                        name={phoneNumberName}
                        noStyle
                        rules={[]}
                    >
                        <Input
                            variant="borderless"
                            className="flex-1"
                            size="middle"
                            placeholder={phoneNumberPlaceholder}
                            disabled={disabled}
                            status={validationStatus}
                            initialValue={defaultPhoneNumber}
                            style={{                                 
                                '--placeholder-color': placeholderColor || '#9E9EA3',
                            }}
                            onChange={e => {
                                form.setFields([
                                    {name: phoneNumberName, errors: []},
                                    {name: combinedFieldName, errors: []}
                                ]);

                                setTimeout(validateFields, 0);
                            }}
                            onBlur={(e) => {
                                onPhoneNumberBlur?.(e.target.value);
                                // Trigger validation on blur
                                validateFields();
                            }}
                        />
                    </Form.Item>
                </div>
            </Form.Item>
        </Form.Item>
    );
};

export default CountryCodePhoneInput; 