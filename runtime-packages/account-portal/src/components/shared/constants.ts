export const THEME_CONSTANTS = {
  PRIMARY_GRADIENT_COLOR_TAILWIND: 'bg-gradient-to-b from-[#1E285F] to-[#0c1026]',
  PRIMARY_GRADIENT_COLOR: 'linear-gradient(to bottom, #1E285F, #0c1026)',
  PRIMARY_COLOR: '#201747',
  PRIMARY_TEXT_COLOR: '#282830',
  SECONDARY_TEXT_COLOR: '#2463EB',
  TERTIARY_TEXT_COLOR: '#9E9EA3',

  PRIMARY_GRADIENT_COLOR_BUTTON_DEFAULT: 'linear-gradient(to bottom, #1E285F, #000000)',
  PRIMARY_GRADIENT_COLOR_BUTTON_HOVER: 'linear-gradient(to bottom, rgba(30, 40, 95, 0.9), rgba(0, 0, 0, 0.9))',
  PRIMARY_GRADIENT_COLOR_BUTTON_ACTIVE: '#161032',
} as const;

export const FORM_CONSTANTS = {
    INPUT: {
      HEIGHT: '40px',
      BORDER_RADIUS: '8px',
      FONT_SIZE: '14px'
    },
    BUTTON: {
      HEIGHT: '48px',
      BORDER_RADIUS: '30px',
      WIDTH: '100%',
      FONT_SIZE: '14px',
      BG_COLOR: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR_BUTTON_DEFAULT,
      BG_COLOR_HOVER: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR_BUTTON_HOVER,
      BG_COLOR_ACTIVE: THEME_CONSTANTS.PRIMARY_GRADIENT_COLOR_BUTTON_ACTIVE
    }
  } as const;
