@use "@/assets/styles/variables.module.scss" as *;

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.send-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: white;
    width: 100px;
    height: 32px;
    border-radius: 0.375rem;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    border: none;
    background-color: $fp-accent-color;
}

.send-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.send-button:not(:disabled) {
    cursor: pointer;
}

.send-button:not(:disabled):hover {
    background-color: rgba(32, 23, 71, 0.9); /* 90% of #201747 */
}

.send-button:not(:disabled):active {
    background-color: #161032; /* Click color */
}

.send-button-text {
    font-size: 14px;
    line-height: 20px;
    font-weight: normal;
}

.send-button-icon {
    width: 1rem;
    height: 1rem;
}

.send-button-icon-spinning {
    animation: spin 3s linear infinite;
}
