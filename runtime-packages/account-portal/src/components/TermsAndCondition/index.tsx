import { useEffect, useState } from "react";
import fundparkLongLogo from "@/assets-new/images/fp_long_logo.png";
import { Button } from "../shared";
import EditedPrimaryButton from "../shared/EditedPrimaryButton";
import { useNavigate } from "react-router-dom";
import VerifyModal from "../VerifyModal";
import { useCommonStore } from "@/store/common";
import { usesecondDrawdownFormStore } from "@/store/secondDrawdown";
import { getDirectorInfo, postDrawdown } from "@fundpark/fp-api";
import { useLimitStore } from "@/store/limits";

const TermsAndCondition = () => {
    const navigate = useNavigate();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentDate, setCurrentDate] = useState("");
    const userInfo = useCommonStore(state => state.userInfo);
    // const firstName = userInfo?.first_name;
    // const lastName = userInfo?.last_name;
    const [directirName, setDirectorName] = useState("");
    const companyName = userInfo?.company_name;
    const formData = usesecondDrawdownFormStore(state => state.formData);
    const {fetchUserLimits,fetchUserCreditApplication} = useLimitStore();

    const handleVerifySuccess = async () => {
        const apiData = {
            currency: formData.drawdownCurrency,
            amount: formData.drawdownAmount,
            bank_id: formData.bankId,
            reference_number: formData.refNumber
        };
        console.log("Submitting data to API:", apiData);

        // Submit data to the API
        const response = await postDrawdown(apiData);
        console.log("API response:", response);

        if (response.code === 0) {
            fetchUserLimits(true)
            fetchUserCreditApplication(true)
            navigate("/credit/hook/");
            
        } else {
            alert("Drawndown failed, please contact us");
        }
    };

    const returnOnclick = () => {
        navigate("/credit/hook/second-drawdown");
    };

    const confirmOnclick = () => {
        setIsModalOpen(true);
    };

    const handleModalClose = () => {
        setIsModalOpen(false); // 关闭 Modal
    };

    useEffect(() => {
        const date = new Date();
        const day = String(date.getDate()).padStart(2, "0");
        const month = date.toLocaleString("default", {
            month: "short"
        });
        const year = date.getFullYear();

        setCurrentDate(`${day} ${month} ${year}`);
    }, []);

    useEffect(() => {
        const fetchDirectorInfo = async () => {
            const response = await getDirectorInfo();
            setDirectorName(response.data.directors_name);
        };

        fetchDirectorInfo();
    }, []);

    return (
        <div>
            <div>
                <div
                    style={{
                        backgroundColor: "#f2f2f2",
                        padding: "10px",
                        boxShadow: "0 0 10px rgba(0, 0, 0, 0.1)"
                    }}
                >
                    <div
                        style={{
                            backgroundColor: "white",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                            padding: "20px"
                        }}
                    >
                        <img
                            src={fundparkLongLogo}
                            alt="FundPark Logo"
                            style={{
                                maxWidth: "200px",
                                marginBottom: "20px"
                            }}
                        />

                        <p style={{ lineHeight: "1.6", fontSize: "14px", color: "#333" }}>
                            {currentDate}
                            <br />
                            {companyName} (「本公司」)
                            <br />
                            <br />
                            <br />
                            <b>提取申请 – 条款及细则</b>
                            <br />
                            本公司确认提交提取申请。本公司按下「确认」键,即表示同意理解并遵守以下条款及细则:
                            <br />
                            (1) 提取申请已获本公司授权及不可撤回。提取申请提交后不可取消,退出或中止。
                            <br />
                            (2) 提取申请及用户账号所载的资料皆是真实,准确及完整。
                            <br />
                            (3) 载於贷款确认函,担保文件,融资文件(包括但不限于押记文书,担保文书(如适用))
                            的全部条款均适用於本次提取。
                            <br />
                            (4)
                            本公司明白丰泊国际出于审批提取申请的原因,会不时向本公司要求提供额外资料。本公司同意配合及提供有关资料。
                            <br />
                            (5)
                            丰泊国际对同意或拒绝任何提取申请或施加任何条件保留所有权利。在丰泊国际同意有关提取申请后,丰泊国际将会安排有关款项至载于贷款确认函的借款公司或现时融资公司账户。
                            <br />
                            (6) 本公司同意通过本平台递交提取申请即表示同意载於丰泊国际公司网页 www.fundpark.com
                            使用条款及隐私政策,并同意承担网上交易及电子通讯所带来的风险。本公司同意,若本公司用户账号在未经授权下被使用
                            或有保安漏洞的情形,会立刻通知丰泊国际。
                            <br />
                            (7)
                            本公司确认没有与任何第三方同意或签订任何有关促致,洽谈,取得或申请融资的协议(纯粹为取得法律服务而与律师签订的协议除外)。
                            <br />
                            (8) 公司确认丰泊可能藉收取服务提供者支付的报酬,作为其所提供金融服务的酬金。
                            <br />
                            若公司同意进行是次支用,将构成公司同意丰泊收取有关报酬。公司同意丰泊须应公司要求,尽其所知地依据适用法律和合同约定向公司披露就安排相关金融服务的外汇汇率资讯及相关费用,作为支付或将支付的报酬。
                            <br />
                            本人按下「确认」键即表示本人已获公司授权确认接受及同意以上所载之所有条款及细则。本人代表本公司确认已全面知悉本公司因应是次提取申请受有关条款及细则约束。
                            <br />
                            <br />
                            <br />
                            {directirName}
                            <br />
                            职位：董事
                            <br />
                            获授权为本公司及代表本公司
                        </p>
                    </div>

                    <div
                        style={{
                            padding: "40px",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            gap: "40px"
                        }}
                    >
                        <EditedPrimaryButton
                            onClick={returnOnclick}
                            label={"返回"}
                            type="default"
                        ></EditedPrimaryButton>
                        <Button
                            type="primary"
                            onClick={confirmOnclick}
                            label={<span>确认</span>}
                            style={{ width: "88px", height: "40px" }}
                        ></Button>
                    </div>
                </div>
            </div>
            <VerifyModal isModalOpen={isModalOpen} onClose={handleModalClose} handleSuccess={handleVerifySuccess}>
                {" "}
            </VerifyModal>
        </div>
    );
};

export default TermsAndCondition;
