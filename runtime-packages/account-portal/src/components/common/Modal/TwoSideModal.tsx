import { Modal as AntModal, Grid } from "antd";
import { ReactNode } from "react";
import styles from "./TwoSideModal.module.scss";

interface TwoSideModalProps {
  open: boolean;
  onClose: () => void;
  leftContent?: ReactNode; // For the image that will be imported later
  rightContent?: ReactNode; // Right side content
  style?: React.CSSProperties;
}

export const TwoSideModal: React.FC<TwoSideModalProps> = ({
  open,
  onClose,
  leftContent,
  rightContent,
  style,
}) => {
const screens = Grid.useBreakpoint();

  // Handler for modal closing with tracking
  const handleClose = () => {
    onClose();
  };

  // Default gradient background style for left side
  const leftSideStyle = {
    background: 'linear-gradient(to bottom, #FFA4A1, #FF635E)',
  };

  return (
    <AntModal
      open={open}
      onCancel={handleClose}
      footer={null}
      className={styles.twoSideModalContainer}
      maskClosable={false}
      centered
      width={1000}
      style={{ ...style, height: 648 }}
    >
      <div className={styles.modalContent}>
        {/* Left side - Image content */}
        {screens.lg && ( // Only render if screen size is large or larger
          <div className={styles.leftSide} style={leftSideStyle}>
            {leftContent || (
              <div className={styles.placeholder}>
                Image will be placed here
              </div>
            )}
          </div>
        )}

        {/* Right side - Content */}
        <div className={screens.lg ? styles.rightSide : styles.rightSideSm}>
          {rightContent || (
            <div className={styles.placeholder}>
              Content will be placed here
            </div>
          )}
        </div>
      </div>
    </AntModal>
  );
};

export default TwoSideModal; 