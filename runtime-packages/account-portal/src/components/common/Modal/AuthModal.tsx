import {ReactNode} from "react";
import {TwoSideModal} from "./TwoSideModal";
import styles from "./AuthModal.module.scss";
import xdjImage from "@/assets-new/images/sdg_login_adv.jpg";
import caaImage from "@/assets-new/images/caa_login_adv.jpg";
import {useSelectedProduct} from "@/hooks/useSelectedProduct";

interface AuthModalProps {
    open: boolean;
    onClose: () => void;
    children: ReactNode;
    height?: string;
    width?: string;
}

export const AuthModal: React.FC<AuthModalProps> = ({
                                                        open,
                                                        onClose,
                                                        children,
                                                    }) => {
    const product = useSelectedProduct();

    return (
        <TwoSideModal
            open={open}
            onClose={onClose}
            leftContent={
                <div className={styles.imageContainer}>
                  {product === "credit-approval-automation"
                    ? <img src={caaImage} alt="Authentication" />
                    : <img src={xdjImage} alt="Authentication" />
                  }
                </div>
            }
            rightContent={
                <div className={styles.authContent}>
                    {children}
                </div>
            }
        />
    );
};

export default AuthModal; 