.twoSideModalContainer {
  :global {
    .ant-modal-content {
      height: 648px;
      border-radius: 24px;
      overflow: hidden;
      padding: 0;
    }

    .ant-modal-close {
      width: 32px !important;
      height: 32px !important;
      background-color: rgba(255, 255, 255, 0.4) !important;
      top: 16px !important;
      right: 16px !important;
      border-radius: 50% !important;
    }
  }
}

.modalContent {
  display: flex;
  height: 648px;
  border-radius: 24px;
  overflow: hidden;
}

.leftSide {
  position: relative;
  width: 50%;
  height: 100%;
  background-color: #f3f4f6;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
  }

  .placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
  }
}

.rightSide {
  width: 50%;
  height: 100%;
  padding: 32px;
  background-color: #fff;

  .placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
  }

  &-sm {
    @extend .rightSide;
    width: 100%;
  }
}