import { useEffect, useRef, useState } from "react";
import styles from "./styles.module.scss";
import { ScrollContainerProps } from "@/types/common/ScrollContainer/scrollContainer.types";

export const ScrollContainer: React.FC<ScrollContainerProps> = ({ 
    children, 
    className = '',
    containerClassName = '',
    dependencies = [],
}) => {
    const scrollRef = useRef<HTMLDivElement>(null);
    const [showTopFade, setShowTopFade] = useState(false);
    const [showBottomFade, setShowBottomFade] = useState(false);

    const handleScroll = () => {
        if (scrollRef.current) {
            const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
            setShowTopFade(scrollTop > 0);
            setShowBottomFade(Math.ceil(scrollTop + clientHeight) < scrollHeight - 1);
        }
    };

    useEffect(() => {
        handleScroll();
        
        // Add both scroll and resize event listeners
        if (scrollRef.current) {
            scrollRef.current.addEventListener('scroll', handleScroll);
            window.addEventListener('resize', handleScroll);
        }

        return () => {
            if (scrollRef.current) {
                scrollRef.current.removeEventListener('scroll', handleScroll);
                window.removeEventListener('resize', handleScroll);
            }
        };
    }, [...dependencies]);

    // Additional effect to check scroll state after content changes
    useEffect(() => {
        // Use setTimeout to ensure content has been rendered
        const timer = setTimeout(handleScroll, 100);
        return () => clearTimeout(timer);
    }, [children]);

    return (
        <div className={`relative ${styles.scrollContainer} ${containerClassName}`}>
            <div 
                ref={scrollRef}
                className={`overflow-y-auto ${className} ${styles.customScroll}`}
            >
                {children}
            </div>
            {/* {showTopFade && <div className={styles.fadeTop}></div>} */}
            {showBottomFade && <div className={styles.fadeBottom}></div>}
        </div>
    );
};