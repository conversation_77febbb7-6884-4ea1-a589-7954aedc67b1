.scrollContainer {
  position: relative;
}

.fadeTop,
.fadeBottom {
  position: absolute;
  left: 0;
  right: 0;
  height: 40px;
  pointer-events: none;
  z-index: 10;
}

.fadeTop {
  top: 0;
  background: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 100%);
}

.fadeBottom {
  bottom: 0;
  background: linear-gradient(to top, white 0%, rgba(255, 255, 255, 0) 100%);
}

.customScroll {
  &::-webkit-scrollbar {
      width: 0;
      background: transparent;
  }
}