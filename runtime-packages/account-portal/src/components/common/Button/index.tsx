import { Button as AntButton } from "antd";
import { FORM_CONSTANTS } from "../constants";
import styles from "./styles.module.scss";
import { CustomButtonProps } from "@/types/common/Button/customButton.types";

export const Button: React.FC<CustomButtonProps> = ({ className, fullWidth, style, label, ...props }) => {
    return (
        <AntButton
            className={`${styles.customButton} ${fullWidth ? "w-full" : ""} ${className || ""}`}
            style={{
                height: FORM_CONSTANTS.BUTTON.HEIGHT,
                borderRadius: FORM_CONSTANTS.BUTTON.BORDER_RADIUS,
                width: FORM_CONSTANTS.BUTTON.WIDTH,
                fontSize: FORM_CONSTANTS.BUTTON.FONT_SIZE,
                ...style
            }}
            {...props}
        >
            {label}
        </AntButton>
    );
};

export default Button;
