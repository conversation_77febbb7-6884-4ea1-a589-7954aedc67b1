import { Input as AntInput } from "antd";
import { FORM_CONSTANTS, THEME_CONSTANTS } from "../constants";
import styles from "./styles.module.scss";
import { CustomInputProps, CustomPasswordProps } from "@/types/common/Input/customeInput.types";
import { useTranslation } from "react-i18next";
import { useMemo, useState } from "react";
import NotCheckedIcon from "@/assets/icons/not-checked.svg?react";
import CheckedIcon from "@/assets/icons/checked.svg?react";
import OpenEyeIcon from "@/assets/icons/open-eye.svg?react";
import CloseEyeIcon from "@/assets/icons/close-eye.svg?react";
import ReminderFaceIcon from "@/assets/icons/reminder-face.svg?react";

export const Input: React.FC<CustomInputProps> = ({ className, ...props }) => {
    return (
        <AntInput
            className={`${styles.customInput} ${className || ""}`}
            style={{
                height: FORM_CONSTANTS.INPUT.HEIGHT,
                borderRadius: FORM_CONSTANTS.INPUT.BORDER_RADIUS,
                color: props.disabled ? "rgba(0, 0, 0, 0.25)" : THEME_CONSTANTS.PRIMARY_TEXT_COLOR,
                fontSize: "14px",
                backgroundColor: props.disabled ? "#F6F6F8" : undefined,
                border: props.disabled ? "1px solid #DDDFE6" : undefined,
                ...props.style
            }}
            {...props}
        />
    );
};

export const Password: React.FC<CustomPasswordProps> = ({
    className,
    showVerificationTips = false,
    disableAutoComplete,
    ...props
}) => {
    const [value, setValue] = useState<string>("");
    const [isFocused, setIsFocused] = useState(false);
    const [hasInteracted, setHasInteracted] = useState(false);
    
    const currentValue = value || String(props.value || "");
    const lengthValid = currentValue.length >= 8 && currentValue.length <= 16;
    const complexityValid = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/.test(currentValue);
    const hasValidationErrors = !lengthValid || !complexityValid;
    
    const showErrorTips = useMemo<boolean>(
        () => !!showVerificationTips && (isFocused || (hasInteracted && hasValidationErrors && !!currentValue)),
        [showVerificationTips, currentValue, isFocused, hasInteracted, hasValidationErrors]
    );

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(e.target.value);
        setHasInteracted(true);
        props.onChange?.(e);
    };

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(true);
        props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        props.onBlur?.(e);
    };

    return (
        <div className={styles.passwordContainer}>
            <AntInput.Password
                className={`${styles.customInput} ${className || ""}`}
                style={{
                    height: FORM_CONSTANTS.INPUT.HEIGHT,
                    borderRadius: FORM_CONSTANTS.INPUT.BORDER_RADIUS,
                    color: props.disabled ? "rgba(0, 0, 0, 0.25)" : THEME_CONSTANTS.PRIMARY_TEXT_COLOR,
                    fontSize: "14px",
                    ...props.style
                }}
                iconRender={visible =>
                    visible ? (
                        <OpenEyeIcon className="ant-input-password-icon" />
                    ) : (
                        <CloseEyeIcon className="ant-input-password-icon" />
                    )
                }
                {...props}
                autoComplete={disableAutoComplete ? "new-password" : props.autoComplete}
                onChange={handleChange}
                onFocus={handleFocus}
                onBlur={handleBlur}
            />
            {showErrorTips && <VerificationTips value={currentValue} />}
        </div>
    );
};

const VerificationTips: React.FC<{ value: string }> = ({ value }) => {
    const { t } = useTranslation();

    const lengthValid = value.length >= 8 && value.length <= 16;
    const complexityValid = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/.test(value);

    return (
        <div className={styles.verificationTips}>
            <div className={styles.title}>{t("password.requirements")}</div>
            <div className={styles.requirement}>
                <span className={`${styles.checkmark} ${lengthValid ? styles.valid : ""}`}>
                    {lengthValid ? <CheckedIcon /> : <NotCheckedIcon />}
                </span>
                <span className={`${styles.checkText} ${lengthValid ? styles.valid : ""}`}>
                    {t("password.lengthRequirement")}
                </span>
            </div>
            <div className={styles.requirement}>
                <span className={`${styles.checkmark} ${complexityValid ? styles.valid : ""}`}>
                    {complexityValid ? <CheckedIcon /> : <NotCheckedIcon />}
                </span>
                <span className={`${styles.checkText} ${complexityValid ? styles.valid : ""}`}>
                    {t("password.complexityRequirement")}
                </span>
            </div>
            <div className={styles.divider}/>
            <div className={styles.reminder}>
                <span className={styles.reminderIcon}>
                    <ReminderFaceIcon className="w-[16px] h-[16px]" />
                </span>
                {t("password.reminder")}
            </div>
        </div>
    );
};

export default {
    Input,
    Password
};
