import { FormContainerProps } from "@/types/common/Form/customForm.types";

const FormContainer = ({ children, title, mt }: FormContainerProps) => {
    return (
        <div className={`flex flex-col items-center flex-1 w-full my-1 ${mt ? `mt-[${mt}px]` : ''}`}>
            <div className="flex flex-col w-full justify-center items-center">
                {title && (<h1 className="text-[30px] font-[600] mb-6 lg:mb-[50px] text-center text-[#10154D] font-poppins">
                    {title}
                </h1>)}
                <div className="w-full max-w-[400px]">
                    {children}
                </div>
            </div>
        </div>
    );
};

export default FormContainer;
