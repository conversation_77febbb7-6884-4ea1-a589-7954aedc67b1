import Breadcrumb, { BreadcrumbItem } from "@/components/Breadcrumb";

const Page: React.FC<{
    className?: string;
    style?: React.CSSProperties;
    children: React.ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}> = ({ className, style, children, breadcrumbs }) => {
    return (
        <div
            className={`max-w-[1440px] mx-auto px-4 lg:px-12 relative${className ? " " + className : ""}`}
            style={style}
        >
            <Breadcrumb list={breadcrumbs} />
            {children}
        </div>
    );
};

export default Page;
