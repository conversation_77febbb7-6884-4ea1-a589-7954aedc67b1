import "./styles.scss";

const TextButton = ({ text, hasUnderline = false, onClick }: { text: string, hasUnderline?: boolean, onClick: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void }) => {
    return (
        <span
            className={`text-button ${hasUnderline ? 'has-underline' : ''}`}
            onClick={(e) => {
                e.preventDefault();
                onClick(e);
            }}
        >
            {text}
        </span>
    );
};

export default TextButton; 