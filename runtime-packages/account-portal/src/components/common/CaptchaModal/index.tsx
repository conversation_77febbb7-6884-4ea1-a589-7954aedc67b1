import { useEffect } from "react";
import { Form } from "antd";
import { useTranslation } from "react-i18next";
import { Modal } from "@/components/common/Modal";
import { Input } from "@/components/common/Input";
import { Button } from "@/components/common";

interface CaptchaModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (validateCode: string) => void;
    loading?: boolean;
    captchaImage?: string;
    onRefreshCaptcha: () => void;
    title?: string;
}

const CaptchaModal: React.FC<CaptchaModalProps> = ({
    open,
    onClose,
    onSubmit,
    loading,
    captchaImage,
    onRefreshCaptcha,
    title
}) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();

    useEffect(() => {
        if (open) {
            form.resetFields();
        }
    }, [open]);

    const handleSubmit = async () => {
        try {
            const { validateCode } = await form.validateFields();
            onSubmit(validateCode);
        } catch (error) {
            console.error("Captcha validation error:", error);
        }
    };

    const handleClose = () => {
        form.resetFields();
        onClose();
    };

    return (
        <Modal
            open={open}
            onClose={handleClose}
            title={t("login.captcha.title")}
        >
            <Form form={form} layout="vertical">
                <Form.Item
                    name="validateCode"
                    rules={[{ required: true, message: t("login.captcha.required") }]}
                    className="mb-2"
                >
                    <div className="flex flex-col gap-8">
                        <div className="text-sm text-[#6E6E75] text-center max-w-[269px] mx-auto">
                            {title}
                        </div>
                        <div className="flex items-center gap-4">
                            <Input
                                disabled={loading}
                                placeholder={t("login.captcha.placeholder")}
                            />
                            {captchaImage && (
                                <img
                                    src={`data:image/png;base64,${captchaImage}`}
                                    alt="Captcha"
                                    onClick={onRefreshCaptcha}
                                    className="h-[40px] w-[100px] cursor-pointer flex-shrink-0"
                                />
                            )}
                        </div>
                    </div>
                </Form.Item>
                <Button
                    onClick={handleSubmit}
                    loading={loading}
                    label={t("login.captcha.submit")}
                    className="w-full mt-10"
                />
            </Form>
        </Modal>
    );
};

export default CaptchaModal; 