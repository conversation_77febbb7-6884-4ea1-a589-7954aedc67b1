import { useState, useRef, useImperativeHandle, forwardRef } from "react";
import { Image, Watermark } from "antd";
import { GetFileOnlineUrl } from "@fundpark/fp-api";
import { downloadFile } from "@fundpark/ui-utils";

export interface FilePreviewRef {
    open: (resourceId: string, fileName?: string) => Promise<void>;
}

const FilePreview = forwardRef<FilePreviewRef, { width?: number }>((props, ref) => {
    const { width } = props;
    const [previewImage, setPreviewImage] = useState(false);
    const [src, setSrc] = useState("");
    const resourceMapRef = useRef<Record<string, string>>({});

    useImperativeHandle(
        ref,
        () => ({
            async open(resourceId: string, fileName?: string) {
                if (resourceId) {
                    let tmpUrl = resourceMapRef.current[resourceId];
                    if (!tmpUrl) {
                        const res = await GetFileOnlineUrl({ resourceId });
                        if (res.success) {
                            tmpUrl = res.data;
                            resourceMapRef.current[resourceId] = tmpUrl;
                        }
                    }
                    if (tmpUrl) {
                        setSrc(resourceId);
                        if (fileName && isImageType(fileName)) {
                            setPreviewImage(true);
                        } else {
                            const resp = await fetch(tmpUrl);
                            const blob = await resp.blob();
                            downloadFile(blob, fileName!);
                        }
                    }
                }
            }
        }),
        []
    );

    const isImageType = (name: string) => {
        return /\.(gif|jpe?g|tiff?|png|webp|bmp)$/i.test(name);
    };

    return (
        <Image
            className="hidden"
            width={width}
            src=""
            preview={{
                visible: previewImage,
                src: src ? resourceMapRef.current[src] : "",
                onVisibleChange: setPreviewImage,
                imageRender: originNode => {
                    return (
                        <Watermark
                            content="FundPark"
                            rotate={-30}
                            gap={[100, 15]}
                            font={{
                                color: "rgba(0, 0, 0, 0.10)",
                                fontSize: 20,
                                fontWeight: 600,
                                fontFamily: "Source Sans Pro"
                            }}
                        >
                            {originNode}
                        </Watermark>
                    );
                }
            }}
        ></Image>
    );
});

export default FilePreview;
