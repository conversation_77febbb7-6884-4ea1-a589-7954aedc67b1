import { Link } from "react-router-dom";
import "./styles.scss";
import { useHref } from "react-router-dom";

const RedirectButton = ({ to, text, hasUnderline = false, blank }: { to: string, text: string, hasUnderline?: boolean, blank?: boolean }) => {
	const location = useHref("/");
    const openByBlank = (
        e: React.MouseEvent<HTMLElement, MouseEvent>,
        url: string,
    ) => {
        e.preventDefault();
        window.open(location + url, "_blank");
    };

	if (blank) {
		return (
			<a
				href={to}
				className={`redirect-button ${hasUnderline ? 'has-underline' : ''}`}
				onClick={(e) => openByBlank(e, to)}
			>
				{text}
			</a>
		);
	}

    return (
        <Link
            to={to}
            className={`redirect-button ${hasUnderline ? 'has-underline' : ''}`}
        >
            {text}
        </Link>
    );
};

export default RedirectButton;
