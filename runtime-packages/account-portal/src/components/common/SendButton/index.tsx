import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { SendButtonProps } from "@/types/common/SendButton/sendButton.types";
import eventBus from "@/event";
import "./styles.scss";
import SpinnerIcon from "@/assets/icons/spinner.svg?react";
import SendIcon from "@/assets/icons/sendIcon.svg?react";

const SendButton = ({
    onClick,
    isLoading = false,
    countdown = 0,
    disabled = false,
	eventName,
    title = "发送",
    bgc,
    style
}: SendButtonProps) => {
    const { t } = useTranslation();

    const [timeLeft, setTimeLeft] = useState(0);
    const [isDisabled, setIsDisabled] = useState(false);
    const [hasBeenClicked, setHasBeenClicked] = useState(false);
    const [isLoading2, setIsLoading2] = useState(false);

	useEffect(() => {
		if (eventName) {
			const off = eventBus.on(eventName as any, () => {
				setTimeLeft(countdown);
                setIsDisabled(true);
			});
			return () => {
				off();
			}
		}
	}, [eventName]);

    useEffect(() => {
        if (timeLeft <= 0) {
            setIsDisabled(false);
            return;
        }

        const timer = setInterval(() => {
            setTimeLeft(prev => prev - 1);
        }, 1000);

        return () => clearInterval(timer);
    }, [timeLeft]);

    const handleClick = async () => {
        setIsLoading2(true);
        try {
            const shouldStartCountdown = await onClick();
            setHasBeenClicked(true);
            if (shouldStartCountdown) {
                setTimeLeft(countdown);
                setIsDisabled(true);
            }
        } finally {
            setIsLoading2(false);
        }
    };

    const isInLoadingState = isLoading || isLoading2 || timeLeft > 0;

    const bgcolor = bgc ? { backgroundColor: bgc } : {undefined};
    const combinedInputStyle = { ...bgcolor, ...style};

    return (
        <button
            onClick={handleClick}
            disabled={isInLoadingState || isDisabled || disabled}
            className="send-button"
            style={combinedInputStyle}
        >
            <span className="send-button-text">
                {isLoading2 
                    ? ""
                    : isInLoadingState
                        ? `${timeLeft}${t("sendButton.seconds")}`
                        : hasBeenClicked
                            ? t("sendButton.resend")
                            : title}
            </span>
            {isInLoadingState ? (
                <SpinnerIcon className="send-button-icon send-button-icon-spinning" />
            ) : (
                <SendIcon className="send-button-icon" />
            )}
        </button>
    );
};

export default SendButton;
