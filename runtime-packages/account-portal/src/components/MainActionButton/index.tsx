import { Button } from "antd";

const MainActionButton = ({
    onClick,
    loading,
    label,
    style
}: {
    onClick: () => void;
    loading?: boolean;
    label: React.ReactNode | string;
    style?: React.CSSProperties;
}) => {

    return (
        <Button
            loading={loading}
            className="hover:!bg-[#4BA3A0] transition-all duration-200 flex items-center justify-center"
            style={{
                height: "40px",
                borderRadius: "60px",
                backgroundColor: "#64CCC9",
                color: "white",
                fontSize: "14px",
                border: "none",
                ...style
            }}
            onClick={() => onClick()}
        >
            {label}
        </Button>
    );
};

export default MainActionButton;
