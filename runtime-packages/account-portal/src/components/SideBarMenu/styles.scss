@use "@/assets/styles/variables.module.scss" as *;

.sidebar-menu {
  width: 240px;
  margin-right: 40px;
  position: sticky;
  top: 148px;
  left: 0;
  
  &.with-border {
    border-right: 1px solid #EAECF0;
  }

  &-item {
    font-size: 16px;
    height: 48px;
    padding: 16px 24px;
    margin-right: 16px;
    margin-top: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    border-radius: 30px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    transition: all 0.3s ease-in-out;

    &:hover:not(.active) {
      background-color: #f3f4f6;
    }

    &.active {
      background: linear-gradient(to bottom, #1E285F, #0c1026);
      color: white;
    }

    &-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .sidebar-menu {
    width: 100%;
    margin-right: 0;
    border-right: none;
    border-bottom: 1px solid #EAECF0;
    padding-bottom: 16px;
    
    &-item {
      margin-right: 0;
    }
  }
}
