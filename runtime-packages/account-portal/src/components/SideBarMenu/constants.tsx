import i18n from "@/i18n";
import MyProfile from "@/assets/icons/myProfile/default.svg?react";
import MyProfileActive from "@/assets/icons/myProfile/click.svg?react";
import MyCompany from "@/assets/icons/myCompany/default.svg?react";
import MyCompanyActive from "@/assets/icons/myCompany/click.svg?react";
import CompanyUser from "@/assets/icons/companyUser/default.svg?react";
import CompanyUserActive from "@/assets/icons/companyUser/click.svg?react";
import BankPSP from "@/assets/icons/bankPSP/default.svg?react";
import BankPSPActive from "@/assets/icons/bankPSP/click.svg?react";
import CompanyProfile from "@/assets/icons/companyProfile/default.svg?react";
import CompanyProfileActive from "@/assets/icons/companyProfile/click.svg?react";
import { SideBarMenuItem } from "./index";

import Announcement from "@/assets/icons/announcement/default.svg?react";
import AnnouncementActive from "@/assets/icons/announcement/active.svg?react";
import Message from "@/assets/icons/message/default.svg?react";
import MessageActive from "@/assets/icons/message/active.svg?react";

import RevenueFinancing from "@/assets/icons/sdg/bank-note-05 (default).svg?react";
import RevenueFinancingActive from "@/assets/icons/sdg/bank-note-05 (active).svg?react";


export const getMainSideBarItems = (): SideBarMenuItem[] => {
	return [
		{ key: "myProfile", label: i18n.t("layout.userSidebar.myProfile"), path: "/myProfile", defaultIcon: <MyProfile />, activeIcon: <MyProfileActive /> },
		{ key: "companyProfile", label: i18n.t("layout.userSidebar.companyProfile"), path: "/companyProfile", defaultIcon: <CompanyProfile />, activeIcon: <CompanyProfileActive /> },
		{ key: "companyUser", label: i18n.t("layout.userSidebar.companyUser"), path: "/companyUser", defaultIcon: <CompanyUser />, activeIcon: <CompanyUserActive />, requiredRole: "administrator" },
		{ key: "bankPSPAccount", label: i18n.t("layout.userSidebar.bankPSP"), path: "/bankPSPAccount", defaultIcon: <BankPSP />, activeIcon: <BankPSPActive /> },
		{ key: "myCompany", label: i18n.t("layout.userSidebar.myCompany"), path: "/company", defaultIcon: <MyCompany />, activeIcon: <MyCompanyActive /> },
		{ key: "chargeOrder", label: i18n.t("layout.userSidebar.chargeOrder"), path: "/chargeOrder", defaultIcon: <CompanyProfile />, activeIcon: <CompanyProfileActive /> }
	]
};

export const getNotificationsSideBarItems = (): SideBarMenuItem[] => {
	return [
		{ key: "messages", label: i18n.t("notifications.menu.messages"), path: "/messages", defaultIcon: <Message />, activeIcon: <MessageActive /> },
		{ key: "announcements", label: i18n.t("notifications.menu.announcements"), path: "/announcements", defaultIcon: <Announcement />, activeIcon: <AnnouncementActive /> }
	]
};

export const getFAQSideBarProducts = (): SideBarMenuItem[] => {
	return [
		{ key: "revenueFinancing", label: "丰收融", path: "/questions", defaultIcon: <RevenueFinancing />, activeIcon: <RevenueFinancingActive /> },
	]
};