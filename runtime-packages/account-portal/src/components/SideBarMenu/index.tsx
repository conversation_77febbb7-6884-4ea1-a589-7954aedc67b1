import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { getMainSideBarItems } from "./constants";
import { useCommonStore } from "@/store/common";
import "./styles.scss";

export interface SideBarMenuItem {
	key: string;
	label: string;
	path: string;
	defaultIcon: React.ReactNode;
	activeIcon: React.ReactNode;
	requiredRole?: string;
	onClick?: (path: string) => void;
}

export interface SideBarMenuProps {
	/** Custom menu items. If not provided, default items from constants will be used */
	items?: SideBarMenuItem[];
	/** Width of the sidebar (default: 240px) */
	width?: number | string;
	/** Custom class name for the sidebar container */
	className?: string;
	/** Whether to show the right border (default: true) */
	showBorder?: boolean;
	/** Custom handler for item click. If not provided, will use react-router navigation */
	onItemClick?: (path: string) => void;
	/** Whether to filter items by user role (default: true) */
	filterByRole?: boolean;
	onSetActive?: Function;
}

const SideBarMenu: React.FC<SideBarMenuProps> = ({
	items,
	width = 240,
	className = "",
	showBorder = false,
	onItemClick,
	filterByRole = true,
}) => {
	const { i18n } = useTranslation();
	const navigate = useNavigate();
	const location = useLocation();
	const { userRole } = useCommonStore();

	const menuItems = useMemo(() => {
		const baseItems = items || getMainSideBarItems();
		return filterByRole 
			? baseItems.filter(item => !item.requiredRole || item.requiredRole === userRole)
			: baseItems;
	}, [items, userRole, i18n.language, filterByRole]);

	const handleItemClick = (item: SideBarMenuItem) => {
		if (item.onClick) {
			item.onClick(item.path);
		} else if (onItemClick) {
			onItemClick(item.path);
		} else {
			navigate(item.path);
		}
	};

	const style = {
		width: typeof width === 'number' ? `${width}px` : width,
	};

	return (
		<div 
			className={`sidebar-menu ${showBorder ? 'with-border' : ''} ${className}`}
			style={style}
		>
			{menuItems.map(item => (
				<div
					key={item.key}
					className={`sidebar-menu-item ${location.pathname === item.path ? 'active' : ''}`}
					onClick={() => handleItemClick(item)}
				>
					<div className="sidebar-menu-item-icon">
						{location.pathname === item.path ? item.activeIcon : item.defaultIcon}
					</div>
					{item.label}
				</div>
			))}
		</div>
	);
};

export default SideBarMenu;
