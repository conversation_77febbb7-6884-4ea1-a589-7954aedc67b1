interface IconButtonProps {
    onClick: () => void;
    icon?: React.ReactNode;
    label: string;
    style?: React.CSSProperties;
    disabled?: boolean;
}

export const IconButton: React.FC<IconButtonProps> = ({ onClick, icon, label = "Modify", style, disabled = false }) => {
    return (
        <button
            className="flex items-center justify-center min-w-[88px] group w-auto h-[40px] bg-transparent border-[1px] border-solid border-[#DDDFE6] rounded-[52px] py-[6px] px-3 cursor-pointer hover:border-[#201747] active:bg-[#2017470A] disabled:opacity-50 disabled:cursor-not-allowed"
            style={style}
            onClick={onClick}
            role="button"
            tabIndex={0}
            disabled={disabled}
        >
            <div className="flex items-center justify-center">
                {icon && (
                    <div className="flex items-center justify-center h-[20px] w-[20px] mr-1">
                        {icon}
                    </div>
                )}
                <span>{label}</span>
            </div>
        </button>
    );
};