import React, { Mu<PERSON>RefObject, useState, useRef, useEffect } from "react";
import "./CAAAuthLimit.scss";
import shopIcon from "@/assets/icons/sdg/shop_black.svg";
import { Form } from "antd";
import { getAuthURL, deleteAuthedShop, getOauthShopList, bindPSPtoShop, getAuthCallbackResult, getDebitAccount } from "@fundpark/fp-api";
import { useTranslation } from "react-i18next";
import { ModalStep } from "@/types/platforms/modalSteps";
import PlatformMainModal from "../Platforms";
import PspSelectionModal from "../Platforms/pspSelectionModal";
import SelectOrAddPaymentGateway from "../Platforms/selectOrAddPaymentGatewayModal";
import { IPingPongData, IPsp } from "@/types/platforms/psp";
import { usePaymentGatewayStore } from "@/store/platform/paymentGatewayStore";
import { IPlatform } from "@/types/platforms/platforms";
import DrawdownAccountSection from './DrawdownAccountSection';
import { DrawdownAccountFormData } from './types';
import { Modal } from '@/components/shared/Modal';
import SecondaryButton from '@/components/shared/SecondaryButton';
import { Button } from '@/components/shared/Button';
import {usePlatformStore} from "@/store/platform/platformStore";
import {OauthPspItem} from "@fundpark/fp-api/types/platform/platform.ts";
import type { GetOauthShopListRes } from "@fundpark/fp-api";
import {useLocation, useNavigate} from "react-router-dom";
import {useCommonStore} from "@/store/common.ts";
import AppHelper from "@/utils/appHelper.ts";
import AuthErrorModal from "@/components/Platforms/authErrorModal";
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import PingPongModal from "@/components/Platforms/pingpongModal";
import AuthConfirmationModal from "@/components/Platforms/authConfirmationModal";


interface CAAAuthLimitProps {
  formRef?: MutableRefObject<any>;
  initialValues?: any;
  onChange?: (values: any) => void;
  addShopButton?: boolean;
  canDelete?: boolean;
  rows: any[];
  authedPlatforms: GetOauthShopListRes;
  updateRows: (updatedValue: any[]) => void;
}

export interface AuthErrorParams {
    errorMsg: string;
    width?: number;
    height?: number;
    contactMsg?:string;
}

const CAAAuthLimit: React.FC<CAAAuthLimitProps> = ({
    formRef,
    initialValues,
    onChange,
    addShopButton = true,
    canDelete = true,
    rows,
    authedPlatforms,
    updateRows
}) => {
    const { t } = useTranslation();

    // todo: need communicate with backend, need psp verified status in response
    const [allRows, setRows] = useState<any[]>([rows]);
    const [modalStep, setModalStep] = useState<ModalStep>("none");
    const [selectedPlatform, setSelectedPlatform] = useState<IPlatform | null>(null);
    const { payment_gateway, fetchPaymentGateway } = usePaymentGatewayStore();
    const [selectedPaymentGateway, setSelectedPaymentGateway] = useState<IPsp | null>(null);
    const [openPspModal, setOpenPspModal] = useState<boolean>(false);
    const [form] = Form.useForm();
    const [formData, setFormData] = useState<DrawdownAccountFormData>({countryRegion: '',});
    const [showConfirmDelete, setShowConfirmDelete] = useState(false);
    const { platforms, fetchPlatforms } = usePlatformStore();
    const [authErrorParams, setAuthErrorParams] = useState<AuthErrorParams | null>(null);
    const [showAuthError, setShowAuthError] = useState(false);
    const [showWechatModal, setShowWechatModal] = useState(false);
    const [activeRowId, setActiveRowId] = useState(null);
    const [showPSPSelection, setShowPSPSelection] = useState(false);
    const [authUrl, setAuthUrl] = useState<string>("");
    const [authToken, setAuthToken] = useState<string | null>(null);
    const [allAuthedPlatforms, setAuthedPlatforms] = useState(authedPlatforms);
    const [showPPModal, setShowPPModal] = useState(false);
    const [showAuthConfirm, setShowAuthConfirm] = useState(false);
    const location = useLocation();
    const [disabledForm,setdisabledForm] = useState(false)

    const fetchData = async () => {
        try {
            const res = await getOauthShopList({});
                console.log(res);
                if (res.code === 0) {
                    // @ts-ignore
                    setAuthedPlatforms(res.data);
                    const updatedData = res.data.shop.map((shop, index) => ({
                        id: index + 1,
                        platform_id: shop.platform_id,
                        auth_status: shop.oauth_status,
                        credit_status: shop.credit_status,
                        platform: shop.platform,
                        shop_id: shop.seller_id,
                        platform_shop_id: shop.id,
                        psp: shop.choose_psp? `${shop.choose_psp.platform}: ID - ${shop.choose_psp.account_id}` : null,
                        psp_id: shop.choose_psp? shop.choose_psp.oauth_psp_id : null,
                        psp_verify: shop.choose_psp? shop.choose_psp.oauth_status : null,
                        can_delete: shop.can_delete,
                    }))
                    setRows(updatedData);
                    updateRows(updatedData);
                    console.log(authedPlatforms);
                } else {
                    console.error("Oauth list error:", res.message);
                }
            } catch (error) {
                console.error("Error fetching authorized platforms", error);
            }
            };
    
    const handleShowConfirmDelete = (state: boolean) => {
        if (state) {
            setShowConfirmDelete(true);
        } else {
            setShowConfirmDelete(false);
        }
    };

    const handleDeletePsp = async (id: string) => {
        try{        
            setShowConfirmDelete(false);
            const res = await deleteAuthedShop(id);
            if (res.code === 0) {
                fetchData(); // need to fetch again for updated data
            }
        } catch (err) {
            console.error(err);
            alert("Error fetching auth URL");
            return false;
        }
        console.log("handleDeletePsp");
    };
    

    const addNewShop = () => {
        setModalStep("platformSelection");
    };

    const fetchUrlAndToken = async (
        platform: IPlatform | null,
        psp: IPsp | null,
        pingpongData: IPingPongData | null = null,
        shop_id: number
    ): Promise<boolean> => {
        try {
            const metaData: Record<string, any> = {
                scene: "psp",
                bind_ouath_shop_id: shop_id,
                ...(pingpongData || {})
            };

            const currentUrl = window.location.href;
            const res = await getAuthURL({
                platform_shop_id: platform?.id || null,
                platform_psp_id: psp?.id || null,
                redirect_url: currentUrl,
                meta_data: metaData
            });
            if (res.code !== 0) throw new Error("Auth URL fetch failed");
            setAuthUrl(res.data.oauth_url);
            setAuthToken(res.data.id_token);
            fetchPaymentGateway()
            if (psp?.is_callback != 1 ) {
                setShowPPModal(false);
                setShowAuthConfirm(true);
                window.open(res.data.oauth_url, "_blank");
            } else {
                window.location.href = res.data.oauth_url;
            }
            return true;
        } catch (err) {
            console.error(err);
            alert("Error fetching auth URL");
            return false;
        }
    };

    React.useEffect(() => {
        setRows(rows);
        setAuthedPlatforms(authedPlatforms);
    }, [rows, authedPlatforms]);

    useEffect(()=>{
        fetchPaymentGateway()
    },[])
    
    useEffect(()=>{
        fetchPaymentGateway()
    },[rows])


    const fetchDrawdownAccountFormData =async ()=>{
        let response = await getDebitAccount()

        if (response.status == 0 && response.data.country){
            setFormData({
                countryRegion: response.data.country,
                companyNameEn: response.data.company_name_english,
                companyNameCn: response.data.company_name_chinese,
                businessRegistrationNumber:response.data.brn
            })
            setdisabledForm(true)

        }else{
            setdisabledForm(false)
        }
    }

    useEffect(()=>{
        fetchDrawdownAccountFormData()

    },[])



    const handlePspComplete = async (psp: IPsp, shop_id: number) => {
        if (psp && psp.platform.toLowerCase() === "PingPong".toLowerCase()) {
            setOpenPspModal(false);
            setShowPPModal(true);
        } else {
            console.log('ready to bind new psp acc', psp);
            const res = await fetchUrlAndToken(null, psp, null, shop_id);
            if (res) {
                setModalStep('none');
                setShowPSPSelection(false);
            } else {
                console.error("Bind psp to shop error");
            }
        }
    }
    
    const handleSelectOrAddPaymentGatewayComplete = async (psp: OauthPspItem | null, shop_id: number) => {
        if (psp === null){
            await handlePspComplete(selectedPaymentGateway, shop_id);
        }else if (psp) { 
            await handleBindExistingPsp(psp, shop_id);
        }
    }
    
    const handleBindExistingPsp = async (selectedPsp: OauthPspItem, shop_id: number) => {
        console.log('selectedPlatform:' , selectedPlatform);
        // existing psp acc
        try{
            console.log('have selected shop: ', shop_id);
            const res = await bindPSPtoShop({shop_id: shop_id, bind_psp_id: selectedPsp.id});
            console.log(res);
            if (res.code === 0) {
                setShowPSPSelection(false);
                AppHelper.msgApi.success("授权成功");
                setTimeout(() => {
                    window.location.reload();
                }, 2000);

            } else {
                console.error("Bind psp to shop error:", res.message);
            }
        } catch (error) {
            console.error("Error fetching authorized shop data", error);
        }
    };
    
    const checkPspAuthed = async (psp: IPsp, shop_id: number) => {
        setSelectedPaymentGateway(psp);
        const alreadyAuthed = authedPlatforms.psp.some(auth => auth.platform_id === psp.id);
        if (alreadyAuthed){
            setOpenPspModal(false);
            setShowPSPSelection(true);
        }else{
            await handlePspComplete(psp, shop_id);
        }
        fetchData();
    }

    const handlePingPongComplete = async (pingpongData: IPingPongData | null | undefined) => {
        if (!allRows) return;
        const targetPlatform = allRows.find((row) => row.platform_id === selectedPlatform?.id);
        if (selectedPlatform && selectedPaymentGateway && pingpongData) {
            await fetchUrlAndToken(null , selectedPaymentGateway, pingpongData, activeRowId || targetPlatform.platform_shop_id);
        }
    };
    

    return (
        <div className="auth-shop-section">
            <PlatformMainModal modalStep={modalStep} setModalStep={setModalStep} authedPlatforms={authedPlatforms} redirectPath={window.location.href}/>
            <AuthErrorModal
                open={showAuthError}
                onClose={() => {
                    setModalStep('platformSelection')
                }}
                onComplete={() => {
                    setShowAuthError(false);
                    setShowWechatModal(true);
                }}
                authError={authErrorParams?.errorMsg || ""}
                contactMsg={authErrorParams?.contactMsg || ""}
            />
            <OurWechatModal
                open={showWechatModal}
                onClose={() => setShowWechatModal(false)}
                message={"有任何问题，欢迎联系我们～"}
                hasAlertIcon={true}
                textAlign='center'
                />
            <Form form={form} layout="vertical">
                <div className="section">
                    <div className="section-title-container">
                        <h4 className="section-title">授权店铺</h4>
                        {addShopButton === true && (
                            <button className="add-shop-btn" onClick={addNewShop}>
                                <img
                                    src={shopIcon}
                                    style={{ width: "20px", height: "20px", marginTop: "-4px", marginRight: "4px" }}
                                    alt="shop"
                                />
                                添加店铺继续提额
                            </button>
                        )}
                    </div>

                    <div className="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>平台</th>
                                    <th>店铺ID</th>
                                    <th>授权状态</th>
                                    <th>授信状态</th>
                                    <th>收款公司</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {allRows.map((row, index) => (
                                    <tr key={index}>
                                        <td>{row.id}</td>
                                        <td>{row.platform}</td>
                                        <td>{row.shop_id}</td>
                                        <td style={{color: row.auth_status === 1 ? '#282830' : '#DD4C4C'}}>{row.auth_status === 0? "校验中" : row.auth_status === 1 ? "有效" : "无效"}</td>
                                        <td style={{color: row.credit_status === 1 ? '#282830' : '#DD4C4C'}}>{row.credit_status === 1 ? "可授信" : "不可授信"}</td>
                                        <td>{row.psp}</td>
                                        <td>
                                            <div className="button-container">
                                                {(!row.psp || row.psp_verify===2) && (
                                                        <>
                                                            <button
                                                                className="edit-button"
                                                                onClick={() => {
                                                                    const selectedPlatformObj = platforms.find((p: IPsp) => p.id === row.platform_id);
                                                                    setActiveRowId(row.platform_shop_id);
                                                                    setSelectedPlatform(selectedPlatformObj? selectedPlatformObj : null);
                                                                    setOpenPspModal(true);
                                                                }}
                                                            >
                                                                编辑收款公司
                                                            </button>
                                                            {openPspModal&&<PspSelectionModal
                                                                open={openPspModal}
                                                                onClose={() => setOpenPspModal(false)}
                                                                onComplete={checkPspAuthed}
                                                                payment_gateway={payment_gateway}
                                                                platform_id={activeRowId || row.platform_shop_id}
                                                            />}
                                                            {showPSPSelection && <SelectOrAddPaymentGateway
                                                                open={showPSPSelection}
                                                                onClose={() => {
                                                                    setShowPSPSelection(false);
                                                                }}
                                                                onComplete={handleSelectOrAddPaymentGatewayComplete}
                                                                paymentGateways={allAuthedPlatforms.psp}
                                                                selectedPSP={selectedPaymentGateway}
                                                                platform_id={activeRowId || row.platform_shop_id}
                                                            />}
                                                            {showPPModal && <PingPongModal
                                                                open={showPPModal}
                                                                onClose={() => setShowPPModal(false)}
                                                                onComplete={handlePingPongComplete}
                                                                payment_gateway={selectedPaymentGateway}
                                                                platform={selectedPlatform}
                                                            />}
                                                            {showAuthConfirm && <AuthConfirmationModal
                                                                open={showAuthConfirm}
                                                                onClose={() => setShowAuthConfirm(false)}
                                                                onComplete={async () => {
                                                                    setShowAuthConfirm(false);
                                                                    const currentPath = window.location.href;
                                                                    const newParam = `id_token=${authToken}`; 
                                                                    const newPath = `${currentPath}?${newParam}`;
                                                                    window.location.href = newPath;
                                                                }}
                                                                authUrl={authUrl}
                                                                isHandleAuth={true}
                                                                isOpenNewTab={(selectedPaymentGateway?.is_callback ?? selectedPlatform?.is_callback) !== 1}
                                                            />}
                                                        </>
                                                    )}
                                                    {(!row.psp || row.psp_verify===2) && (rows.length > 1 && canDelete === true && row.can_delete == 1) 
                                                    && <div className="separator"></div>}
                                                    {(rows.length > 1 && canDelete === true && row.can_delete == 1) && (
                                                        <>
                                                            <button
                                                                className="delete-button"
                                                                onClick={() => {
                                                                    setActiveRowId(row.platform_shop_id); // Set active row ID
                                                                    handleShowConfirmDelete(true);
                                                                }}
                                                            >
                                                                删除
                                                            </button>
                                                            <Modal
                                                            open={showConfirmDelete}
                                                            onClose={() => handleShowConfirmDelete(false)}
                                                            title="温馨提示"
                                                            >
                                                            <div>
                                                                <p className="mb-4" style={{ textAlign: 'center' }}>
                                                                    您是否确认删除该店铺信息？
                                                                </p>
                                                                
                                                                <div className="flex justify-center mt-10" style={{ gap: '16px' }}>
                                                                <SecondaryButton
                                                                    label="取消"
                                                                    onClick={() => handleShowConfirmDelete(false)}
                                                                />
                                                                <Button
                                                                    type="primary"
                                                                    label="确定"
                                                                    onClick={() => {
                                                                        handleDeletePsp(activeRowId || row.platform_shop_id); // Use the active row ID
                                                                        handleShowConfirmDelete(false); // Close the modal
                                                                    }}
                                                                    style={{
                                                                    height: '40px',
                                                                    width: '88px'
                                                                    }}
                                                                />
                                                                </div>
                                                                
                                                            </div>
                                                        </Modal>
                                                        </>
                                                    )}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </Form>
            <br/>
            <DrawdownAccountSection
                formRef={formRef}
                initialValues={formData}
                onChange={onChange}
                disabledForm={disabledForm}
                />
        </div>
    );
};

export default CAAAuthLimit;
