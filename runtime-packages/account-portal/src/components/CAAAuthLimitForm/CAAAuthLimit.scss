.table-container {
    overflow-x: auto;  
    white-space: nowrap;   
}

table {
    width: 100%;
    border-collapse: separate; /* Separate cell borders */
    border-spacing: 0; /* Remove spacing between cells */
    border-radius: 16px; /* Rounded corners */
    overflow: hidden; /* Clip content to border radius */
    background-color: white; /* Background color for the table */
    border: 1px solid #2017471A; /* Border around the entire table */
    margin: 4px;
    gap: 32px;
    max-width: 99%;
}

th {
    padding-top: 12px;
    padding-right: 32px;
    padding-bottom: 12px;
    padding-left: 32px;
    text-align: left; /* Center content in header */
    background-color: white; /* Ensure header has the same background */
    border-bottom: 1px solid #8F8BA3; /* Thicker line under headers */
} 

td {
    padding-top: 12px;
    padding-right: 32px;
    padding-bottom: 12px;
    padding-left: 32px;
    text-align: left; /* Center content in cell */
    background-color: white; /* Ensure cells have the same background */
    border-bottom: 1px solid #20174714; /* Thinner line under each row */
    @media (max-width: 576px) { // smaller than sm
        height: 180px;
    }
}
.button-container {
    display: flex;
    align-items: center; /* Center buttons vertically */
}

button {
    background: transparent; /* Remove background */
    cursor: pointer; /* Show pointer cursor on hover */
    padding: 8px 12px; /* Button padding */
}

.add-shop-btn {
    border: 1px solid #DDDFE6; /* Remove border */
    border-radius: 60px;
    cursor: pointer; /* Show pointer cursor on hover */
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 16px;
    padding-right: 16px; 
    color: #282830;
    gap: 4px;
}

.section-title-container {
    display: flex;                 /* Use Flexbox layout */
    align-items: center;          /* Center items vertically */
    justify-content: space-between; /* Space items evenly */
    padding: 10px;                /* Optional padding */
    border: none;
}

.section-title {
    font-size: 16px;
}


.select-container {
    position: relative; /* Position container for absolute positioning of the arrow */
    width: 100%; /* Full width */
}

select {
    border: 1px solid lightgrey; /* Light grey border */
    border-radius: 8px; /* Rounded corners */
    padding: 8px 30px 8px 10px; /* Adjust padding to make room for the arrow */
    background-color: white; /* Background color */
    color: #9E9EA3; /* Text color for options */
    appearance: none; /* Remove default dropdown arrow */
    width: 100%; /* Full width */
}

select:focus {
    outline: none; /* Remove focus outline */
    border-color: #DDDFE6; /* Darker border on focus */
}

/* Style for the default option */
select option {
    color: #9E9EA3; /* Grey color for the default text */
}

/* Hide the default option when selected */
select option:disabled {
    display: none; /* Hide the default option */
}

/* Arrow styles */
.select-container::after {
    content: '';
    position: absolute;
    top: 30%; /* Center vertically */
    right: 10px; /* Position from the right */
    transform: translateY(-50%); /* Adjust for centering */
    border: solid #DDDFE6; /* Arrow color */
    border-width: 0 2px 2px 0; /* Arrow shape */
    display: inline-block;
    padding: 4px; /* Arrow size */
    transform: rotate(45deg); /* Rotate to create downward arrow */
    pointer-events: none; /* Prevent pointer events on the arrow */
}

.edit-button {
    color: #2463EB; /* Edit button text color */
    border: none; 
}

.delete-button {
    color: #DD4C4C; /* Delete button text color */
    border: none; 
}

.separator {
    width: 1px; /* Width of the separator */
    height: 18px; /* Height of the separator */
    background-color: #CACAD1; /* Color of the separator */
    margin: 0 8px; /* Spacing around the separator */
}

