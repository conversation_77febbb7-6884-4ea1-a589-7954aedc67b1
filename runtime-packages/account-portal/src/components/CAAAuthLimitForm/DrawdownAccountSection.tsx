import React, { useEffect, useState, MutableRefObject } from "react";
import { Form, Select, Input, InputNumber, Tooltip } from "antd";
import { useConfig } from "@/contexts/ConfigContext";
import "./DrawdownAccountSection.scss";
import InfoBanner from "@/components/shared/InfoBanner";
import {
    LegacyCountryCode,
    COUNTRY_ID_TO_LEGACY_CODE
} from "@/constants/configTypes";
import ConfigDataSelect from "@/components/shared/ConfigDataSelect";
import { ConfigType } from "@/constants/configTypes";
import { DrawdownAccountFormData } from "./types";
import { InfoCircleOutlined } from "@ant-design/icons";
import brSample from "@/assets-new/images/br-sample.png";
import { ENGLISH_CHARS_AND_SYMBOLS_PATTERN } from "@/utils/formValidation";
import { TRACKING_CATEGORIES, TRACKING_EVENTS, TRACKING_ACTIONS } from "@/components/shared/tracking/constants";
import { MATOMO_CONFIG } from "@/utils/matomoConfig";
import { useMatomoContext } from "@/contexts/MatomoContext";

interface DrawdownAccountSectionProps {
    formRef?: MutableRefObject<any>;
    initialValues?: Partial<DrawdownAccountFormData>;
    onChange?: (values: Partial<DrawdownAccountFormData>) => void;
    disabledForm?:boolean
}

const DrawdownAccountSection: React.FC<DrawdownAccountSectionProps> = ({
    formRef,
    initialValues = {},
    onChange,
    disabledForm
}) => {
    const { trackEventWithDimensions } = useMatomoContext();
    const { loading, getCountryLabel } = useConfig();
    const [form] = Form.useForm();
    const [formData, setFormData] = useState(initialValues);
    const [selectedCountry, setSelectedCountry] = useState<string | undefined>(
        initialValues.countryRegion
            ? COUNTRY_ID_TO_LEGACY_CODE[initialValues.countryRegion]
            : undefined
    );

    // Set form reference if provided
    useEffect(() => {
        if (formRef) {
            formRef.current = form;
        }
    }, [form, formRef]);

    // Update form values when initialValues changes
    useEffect(() => {
        console.log('initialValues changed in DrawdownAccountSection:', initialValues);
        if (Object.keys(initialValues).length > 0) {
            // Reset fields and set values
            form.setFieldsValue(initialValues);

            // Update selectedCountry if countryRegion exists
            if (initialValues.countryRegion) {
                const legacyCode = COUNTRY_ID_TO_LEGACY_CODE[initialValues.countryRegion];
                setSelectedCountry(legacyCode);
            }

            // Update local form data
            setFormData(initialValues);
        }
    }, [initialValues, form]);

    const handleCountryChange = (value: string) => {
        setSelectedCountry(value);
    };

    // Function to convert API key_id back to legacy codes for internal logic
    const getLegacyCountryCode = (keyId: string): string => {
        return COUNTRY_ID_TO_LEGACY_CODE[keyId] || keyId;
    };

    const handleFormChange = (changedValues: any, allValues: any) => {
        if (changedValues.countryRegion) {
            const legacyCode = getLegacyCountryCode(changedValues.countryRegion);
            handleCountryChange(legacyCode);
        }

        setFormData(allValues);

        if (onChange) {
            onChange(allValues);
        }
    };

    return (
        <div className="company-section">
            <Form
                form={form}
                layout="vertical"
                initialValues={initialValues}
                onValuesChange={handleFormChange}
            >
                <div className="section">
                    <div className="company-section-title-container">
                        <h4 className="section-title">公司信息</h4>
                        {selectedCountry === LegacyCountryCode.HONG_KONG && (
                            <InfoBanner type="note" message="特快申请：推荐使用香港公司" width="501px" className="info-banner"/>
                        )}
                        {selectedCountry === LegacyCountryCode.CHINA && (
                            <InfoBanner type="default" message="特快申请：推荐使用香港公司" width="501px" className="info-banner"/>
                        )}
                    </div>
                    <div className="form-row">
                        <Form.Item
                            name="countryRegion"
                            label="国家及地区"
                            rules={[{ required: true, message: "请选择国家及地区" }]}
                            className="half-width-item"
                        >
                            <ConfigDataSelect
                                type={ConfigType.COUNTRY_REGION}
                                placeholder="请选择"
                                disabled={loading ||disabledForm}
                                onChange={(value) => {
                                    trackEventWithDimensions({
                                        category: TRACKING_CATEGORIES.FORM,
                                        action: TRACKING_ACTIONS.SELECT,
                                        name: TRACKING_EVENTS.SELECT_COUNTRY_REGION,
                                        customDimensions: {
                                            [MATOMO_CONFIG.CUSTOM_DATA_DIMENSION_ID]: getCountryLabel(value)
                                        }
                                    });
                                }}
                            />
                        </Form.Item>
                    </div>

                    {selectedCountry && (
                        <div className="form-row">
                            {selectedCountry === LegacyCountryCode.CHINA && (
                                <Form.Item
                                    name="companyNameCn"
                                    label="公司注册名称 (中文)"
                                    rules={[{ required: true, message: "请输入公司注册名称" }]}
                                    className="half-width-item"
                                >
                                    <Input placeholder="请输入"  disabled={disabledForm}/>
                                </Form.Item>
                            )}

                            {selectedCountry === LegacyCountryCode.HONG_KONG && (
                                <Form.Item
                                    name="companyNameEn"
                                    label="公司注册名称 (英文)"
                                    rules={[
                                        { required: true, message: "请输入公司注册名称" },
                                        {
                                            pattern: ENGLISH_CHARS_AND_SYMBOLS_PATTERN,
                                            message: "请输入有效的公司注册名称"
                                        }
                                    ]}
                                    className="half-width-item"
                                >
                                    <Input placeholder="请输入" disabled={disabledForm}/>
                                </Form.Item>
                            )}

                            {selectedCountry === LegacyCountryCode.HONG_KONG && (
                                <Form.Item
                                    name="businessRegistrationNumber"
                                    label={
                                        <span>
                                            商业登记号码 (BRN)
                                            <Tooltip 
                                                title={
                                                    <div className="brn-tooltip-image">
                                                        <img 
                                                            src={brSample} 
                                                            alt="BRN Example" 
                                                            className="brn-sample-image"
                                                            width="320"
                                                            style={{ maxWidth: '100%' }}
                                                        />
                                                    </div>
                                                }
                                                overlayStyle={{ maxWidth: '350px' }}
                                                color="#fff"
                                                placement="top"
                                            >
                                                <InfoCircleOutlined className="info-icon" />
                                            </Tooltip>
                                        </span>
                                    }
                                    rules={[
                                        { required: true, message: "请输入商业登记号码" },
                                        {
                                            pattern: /^\d{8}$/,
                                            message: "请输入8位数字"
                                        }]
                                    }
                                    className="half-width-item"
                                >
                                    <InputNumber
                                        placeholder="请输入"
                                        className="full-width-input"
                                        min={0}
                                        controls={false}
                                        disabled={disabledForm}
                                    />
                                </Form.Item>
                            )}
                        </div>
                    )}
                </div>
            </Form>
        </div>
    );
};

export default DrawdownAccountSection; 