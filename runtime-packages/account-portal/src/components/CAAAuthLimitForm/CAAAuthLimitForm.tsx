import React, {lazy, useEffect, useRef, useState} from 'react';
import { DrawdownFormProvider } from '@/contexts/DrawdownFormContext';
import { Button } from '@/components/shared/Button';
import Stepper from '@/components/shared/Stepper';
import './styles.scss';
import { useNavigate } from 'react-router-dom';
import { getUserCreditApplication, submitCompanyInfo, getOauthShopList, sendEmailNotificationsByType, EmailNotifyType} from "@fundpark/fp-api";
import { useLimitStore } from '@/store/limits';
import { mapCompanyInfoFormToApi } from '@/mappers/businessInfo';
import { Modal } from '@/components/shared/Modal';
import SecondaryButton from '@/components/shared/SecondaryButton';
import { OurWechatModal } from '@/components/shared/OurWechatModal';
import AlertIcon from '@/assets-new/icons/alert-icon.svg?react';
import {ModalStep} from "@/types/platforms/modalSteps";
import appHelper from "@/utils/appHelper";
import { usePositionTracking } from '@/hooks/usePositionTracking';

// Dynamically import the sections to avoid circular dependencies
const CAAAuthLimit = lazy(() => import('./CAAAuthLimit'));

const CAAAuthLimitFormContent: React.FC = () => {
  // add state to validate the whether next step button should be enabled
  const [formData, setFormData] = useState({});
  const formRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [rows, setRows] = useState<any[]>([]);
  const [authedPlatforms, setAuthedPlatforms] = useState([]);
  const [showWarning, setShowWarning] = useState(false);
  const [warningMessage, setWarningMessage] = useState('');
  const [showWechatModal, setShowWechatModal] = useState(false);
  const [modalStep, setModalStep] = useState<ModalStep>("none");
  const [showRegionWarning, setShowRegionWarning] = useState(false);
  const { fetchUserLimits, fetchUserCreditApplication,creditApplication } = useLimitStore();
  const { trackPosition } = usePositionTracking();
  
  const { isCalculatingLimit } = useLimitStore();

  const handleChange = (values: any) => {
    console.log('Form values changed:', values);
    setFormData({
      ...formData,
      ...values
    });
  };

  const fetchData = async () => {
    try {
      // Map form data to API format using the mapper
          const res = await getUserCreditApplication(null);
          if (res.code === 0) {
              // @ts-ignore
              // set data
              console.log('Got credit application data:', res);
          } else {
              console.error("credit application error:", res.message);
          }
        } catch (error) {
          console.error("Error fetching credit application", error);
    }
    try {
        const res = await getOauthShopList({});
        console.log(res);
        if (res.code === 0) {
            // @ts-ignore
            setAuthedPlatforms(res.data);
            setRows(
                res.data.shop.map((shop, index) => ({
                  id: index + 1,
                  platform_id: shop.platform_id,
                  auth_status: shop.oauth_status,
                  credit_status: shop.credit_status,
                  platform: shop.platform,
                  shop_id: shop.seller_id,
                  platform_shop_id: shop.id,
                  psp: shop.choose_psp? `${shop.choose_psp.platform}: ID - ${shop.choose_psp.account_id}` : null,
                  psp_id: shop.choose_psp? shop.choose_psp.oauth_psp_id : null,
                  psp_verify: shop.choose_psp? shop.choose_psp.oauth_status : null,
                  can_delete: shop.can_delete,
                }))
            );
            console.log(authedPlatforms);
        } else {
            console.error("Oauth list error:", res.message);
        }
    } catch (error) {
        console.error("Error fetching authorized shop data", error);
    }
  };

  const handleShowWechat = () => {
    setShowWarning(false);
    setShowWechatModal(true);
  };

  React.useEffect(() => {
      fetchData();
  }, []);

  let isFirstTime =  (!creditApplication?.pre_limit)? true:false //determine if is firsttime by checking if it has prelimit

  const handleClickNext = async () => {
    if (formData.countryRegion == 2 && !isFirstTime) {
      setShowRegionWarning(true);
    } else {
      handleNext();
    }
  }

  const handleNext = async () => {

      console.log('data: ', rows);
      const hasOccupied = rows.some(row => row.credit_status === 2);
      const hasFailed = rows.some(row => row.auth_status === 2); 
      if (hasOccupied) {
        console.log('Shop is occupied.');
        setShowWarning(true);
        setWarningMessage('检测到您的店铺/支付平台已被其他用户绑定授权了，建议您换一个店铺/支付平台继续申请');
      } else if (hasFailed) {
        setShowWarning(true);
        setWarningMessage('检测到您的店铺/支付平台授权失效，建议您重新授权或换一个店铺/支付平台继续申请');
      } else {
        try {
          // Validate form fields
          await formRef.current.validateFields();
          try {
            setLoading(true);
              // trigger api, wait for response
              // Submit data to the APIs - post company details, 
              const apiData = mapCompanyInfoFormToApi(formRef.current.getFieldsValue());
              const res = await submitCompanyInfo(apiData);
              // If submission successful, navigate to next page
              if (res.code === 0) {
                  await fetchUserCreditApplication(true);
                  navigate('/credit/underwriting/home', {replace: true});
                // storeCurrentPath({ path: '/credit-approval-automation/home', pathName: 'Credit Approval Automation Dashboard'}); // store next path to fix
                // navigate('/credit/underwriting/home', {replace: true}); // switch back to this after path changes
                  await fetchUserLimits(true);
                  await trackPosition({ path: "/credit/underwriting/home" });

              } else if(res.code === 4021){
                appHelper.msgApi.error('店铺状态异常');

              }else if (res.code == 4022){
                appHelper.msgApi.error('未选择收款公司');
              }else if (res.code === 4023){
                appHelper.msgApi.error('收款公司状态异常');
              }else{
                console.error('Failed to submit company information:', res.message);
                appHelper.msgApi.error(res.message);
              }
            } catch (error) {
              console.error('API error when submitting company information:', error);
            } finally {
              setLoading(false);
              console.log('Done')
            }
        } catch (error) {
          console.error('Validation failed:', error);
        }
      }
    };

  const steps = [
    {
      title: '平台授权及公司信息',
      content: (
        <div>
        <CAAAuthLimit 
          formRef={formRef}
          initialValues={formData}
          onChange={handleChange}
          addShopButton={!isCalculatingLimit}
          authedPlatforms={authedPlatforms}
          rows={rows}
          updateRows={setRows}
        />
        <Modal
            open={showWarning}
            onClose={() => setShowWarning(false)}
            title="温馨提示"
            >
            <div>
              <div style={{ display: 'flex', alignItems: 'flex-start', textAlign: 'left'}}>
              <AlertIcon className="alert-icon" style={{ width: '40px', height: '30px' }}/>
                <p className="mb-4" >
                {warningMessage}
                </p>
                </div>
                
                <div className="flex justify-center mt-10" style={{ gap: '16px' }}>
                <SecondaryButton
                    label="线上查询"
                    onClick={() => handleShowWechat()}
                />
                <Button
                    type="primary"
                    label="重新授权"
                    onClick={() =>  {
                      setShowWarning(false)
                      setModalStep('platformSelection')
                    }}
                    style={{
                    height: '40px',
                    width: '88px'
                    }}
                />
                </div>
                
            </div>
        </Modal>
        <Modal
            open={showRegionWarning}
            onClose={() => setShowRegionWarning(false)}
            title="温馨提示"
            width={617}
          >
            <div>
              <p className='mb-0'>亲爱的用户，当前仅支持中国香港公司申请用款。请点击【使用中国香港公司信息】重新提交。</p>
              <p className="mb-4">如您未注册中国香港主体，请点击【3天开立中国香港公司】咨询协助</p>
              <div className="flex justify-center mt-8" style={{ gap: '16px' }}>
                <SecondaryButton
                  label="使用中国香港公司信息"
                  onClick={() => {
                    setShowRegionWarning(false);
                  }}
                />
                <SecondaryButton
                  label="3天开立中国香港公司"
                  onClick={() => {
                    setShowRegionWarning(false);
                    setShowWechatModal(true);
                    sendEmailNotificationsByType(EmailNotifyType.NO_HONG_KONG_COMPANY)();
                  }}
                />
              </div>
            </div>
          </Modal>
        <OurWechatModal
          open={showWechatModal}
          onClose={() => setShowWechatModal(false)}
          message={"有任何问题，欢迎联系我们～"}
          hasAlertIcon={true}
          textAlign='center'
          />
        </div>
      ),
      action: (
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '24px'}}>
            <Button 
            style={{ width: 320 }}
            label={
              <span className="flex items-center justify-center">
                查看额度
              </span>
            }
            onClick={handleClickNext} 
            type="primary"
            disabled={loading}
          />
        </div>
      ),
    },
    {
      title: '查看额度',
      content: (
        <></>
      ),
      action: null,
    },
  ];

  return (
    <div className="activate-shop-form-wrapper">
      <Stepper 
        steps={steps} 
        currentStep={0}
        borderTopRadiusRounded={false}
      />
    </div>
  );
};

// Wrapper component that provides the context
const CAAAuthLimitForm: React.FC = () => {
  return (
    <DrawdownFormProvider>
      <CAAAuthLimitFormContent />
    </DrawdownFormProvider>
  );
};

export default CAAAuthLimitForm; 