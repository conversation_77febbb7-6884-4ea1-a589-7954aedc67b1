import { Button } from "antd";
import HomePageIcon from "@/assets/icons/home-page-icon.svg?react";
import { useTranslation } from "react-i18next";
import { useState } from "react";


export const HomeButton: React.FC<{
    className?: string;
}> = ({ className }) => {
    const { i18n } = useTranslation();
    const locale = i18n.language;
    const [isClicked, setIsClicked] = useState(false);

    const languageMapping = {
        en_US: 'en',
        zh_CN: 'sc',
        zh_HK: 'tc'
    };
    
    const handleClick = () => {
        setIsClicked(true);
        const mappedLanguage = languageMapping[locale as keyof typeof languageMapping];
        window.location.href = `https://www.fundpark.com/${mappedLanguage}/`;
    };

    return (
        <Button
            className={`w-[40px] h-[40px] ${className}`}
            style={{
                borderRadius: "34px",
                backgroundColor: isClicked ? "#ECEFF6" : "#F5F5F5",
                border: "1px solid #F5F5F5"
            }}
            onClick={handleClick}
        >
            <div className="flex items-center justify-center">
                <HomePageIcon />
            </div>
        </Button>
    );
};
