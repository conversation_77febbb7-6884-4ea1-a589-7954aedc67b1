import { useState, useEffect, useRef } from "react";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import eventBus from "@/event";

const FullSpin: React.FC = () => {
    const [open, setOpen] = useState(false);
    const count = useRef(0);

    useEffect(() => {
        const off = eventBus.on("toggleFullLoading", flag => {
            count.current = flag ? count.current + 1 : Math.max(count.current - 1, 0);
            setOpen(count.current > 0);
        });

        return () => {
            off();
        };
    }, []);

    if (open) {
        return (
            <Spin
                spinning={open}
                rootClassName="z-[1999]"
                size="large"
                fullscreen
                indicator={<LoadingOutlined style={{ color: "#fff" }} />}
            />
        );
    }

    return null;
};

export default FullSpin;
