import React, { ReactNode, useEffect } from "react";
import RedPocket from "@/components/RedPocket";
import BackdropContainer from "../backdropContainer";
import AmountWithTitle from "@/components/shared/AmountWithTitle";
import IconLabelList from "@/components/shared/IconLabelList";
import { LandingPageIconLabelListSDG } from "@/constants/iconLabelList";
import { useSelectedProduct } from "@/hooks/useSelectedProduct";
import { useRedPocketStore } from "@/store/redpocket";
import { useState } from "react";

interface DrawdownFormContainerProps {
    landingPageIconLabelList?: any;
    children: ReactNode;
    title?: string;
    limit?: number;
}

const DrawdownFormContainer: React.FC<DrawdownFormContainerProps> = ({
    children,
    landingPageIconLabelList = LandingPageIconLabelListSDG,
    title,
    limit
}) => {
    const product = useSelectedProduct();

    const fetchRedPocketList = useRedPocketStore(state => state.fetchRedPocketList);

    const { getRedPocketByType } = useRedPocketStore();
    const [redPocketAmount, setRedPocketAmount] = useState(0);

    useEffect(() => {
        fetchRedPocketList();
        const claimedRedPocket = getRedPocketByType("deduction");

        if (claimedRedPocket?.status === "claimed") {
            setRedPocketAmount(Number(claimedRedPocket.value));
        } else {
            setRedPocketAmount(0);
        }
    }, []);

    useEffect(() => {
        fetchRedPocketList();
    }, [fetchRedPocketList]);

    return (
        <>
            <BackdropContainer style={{ paddingBottom: "0px" }}>
                <AmountWithTitle title={title} amount={limit || "-"} />
                <IconLabelList items={landingPageIconLabelList} />

                {product === "xdj" &&
                    (window.location.href.includes("register/drawdown-account") ||
                        window.location.href.includes("register/business-info") ||
                        window.location.href.includes("register/signing")) && (
                        <RedPocket redPocketTitle="7天免息金，有店就能用" type="default" />
                    )}

                {redPocketAmount === 500 && (
                    <RedPocket type="doubleLine" titleLineOne="申请支用使用红包" titleLineTwo="500 美元" />
                )}
            </BackdropContainer>
            <div style={{ marginBottom: "80px" }}>{children}</div>
        </>
    );
};

export default DrawdownFormContainer;
