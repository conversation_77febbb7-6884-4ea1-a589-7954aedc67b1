import React, { ReactNode, useEffect, useState } from 'react';
import { Spin } from 'antd';
import RedPocket from '@/components/RedPocket';
import BackdropContainer from '../backdropContainer';
import IconLabelList from '@/components/shared/IconLabelList';
import { LandingPageIconLabelListCAA } from '@/constants/iconLabelList';
import { useRedPocketStore } from "@/store/redpocket.ts";
import LimitTitleContainerCAA from '@/containers/LimitTitleContainerCAA';

interface DrawdownFormContainerProps {
  children: ReactNode;
}

const DrawdownFormContainer: React.FC<DrawdownFormContainerProps> = ({
  children,
}) => {
  const { getRedPocketByType } = useRedPocketStore();
  const [isRedPocketUsed, setIsRedPocketUsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchRedPocket = async () => {
      setIsLoading(true);
      try {
        const data = await getRedPocketByType('used');
        if (data) {
          setIsRedPocketUsed(true);
        }
      } catch (error) {
        console.error('Failed to fetch red pocket:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRedPocket();
  }, [getRedPocketByType]);

  return (
    <>
      {isLoading ? (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin />
        </div>
      ) : (
        <>
          <BackdropContainer style={{ paddingBottom: !isRedPocketUsed ? '0px' : '40px' }}>
            <LimitTitleContainerCAA />
            <IconLabelList items={LandingPageIconLabelListCAA} />
            {!isRedPocketUsed && (
              <RedPocket
                type="doubleLine"
                titleLineOne="激活额度领红包"
                titleLineTwo="500 美元"
              />
            )}
          </BackdropContainer>
          <div style={{ marginBottom: '80px' }}>
            {children}
          </div>
        </>
      )}
    </>
  );
};

export default DrawdownFormContainer; 