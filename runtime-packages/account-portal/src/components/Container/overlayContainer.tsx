import {CSSProperties, ReactNode, FC} from 'react';

interface OverlayContainerProps {
    height?: string | number;
    width?: string | number;
    children?: ReactNode;
    style?: CSSProperties;
    className?: string;
    isShowWhiteBottom?: boolean;
    borderRadius?: string;
    [key: string]: any;
}

const OverlayContainer: FC<OverlayContainerProps> = ({
                                                         height = '84px',
                                                         width = '100%',
                                                         children,
                                                         style = {},
                                                         className = '',
                                                         isShowWhiteBottom = false,
                                                         borderRadius = '0 0 24px 24px',
                                                         ...restProps
                                                     }) => {
    const containerStyle: CSSProperties = {
        height,
        width: '100%',
        maxWidth: width,
        paddingTop: '0px',
        background: '#FFFFFF80',
        border: '1px solid',
        borderImageSource: 'linear-gradient(180deg, #FFFFFF 0%, #B3E6E5 100%)',
        backdropFilter: 'blur(20px)',
        borderRadius: borderRadius,
        display: 'flex',
        flexDirection: 'column',
        gap: '10px',
        ...style,
    };


    return (
        <div className={className} style={containerStyle} {...restProps}>
            <div
                style={{
                    width: '100%',
                    height: '84px',
                    borderRadius: borderRadius,
                    marginTop: 'auto',
                    border: '1px solid transparent',
                    borderImageSource: 'linear-gradient(180deg, #FFFFFF 0%, #B3E6E5 100%)',
                    backdropFilter: 'blur(44px)',
                    display: 'flex',
                    alignItems: 'flex-end',
                    justifyContent: 'center',
                    position: 'relative',
                }}
            >
                {children}
            </div>

        </div>
    );
};

export default OverlayContainer;
