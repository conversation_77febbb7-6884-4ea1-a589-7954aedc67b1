import {CSSProperties, ReactNode, FC} from 'react';

interface BackdropContainerProps {
    height?: string | number;
    width?: string | number;
    children?: ReactNode;
    style?: CSSProperties;
    className?: string;
    isShowWhiteBottom?: boolean;

    [key: string]: any;
}

const BackdropContainer: FC<BackdropContainerProps> = ({
                                                           height,
                                                           width = '100%',
                                                           children,
                                                           style = {},
                                                           className = '',
                                                           isShowOverlay = true,
                                                           ...restProps
                                                       }) => {
    const containerStyle: CSSProperties = {
        height: height ?? 'auto',
        width: '100%',
        maxWidth: width,
        paddingTop: '67px',
        paddingBottom: isShowOverlay ? '152px' : '0px',
        background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, rgba(255, 255, 255, 0.4) 71.75%)',
        backdropFilter: 'blur(20px)',
        borderRadius: '24px 24px 0 0',
        display: 'flex',
        flexDirection: 'column',
        gap: '10px',
        ...style,
    };


    return (
        <div className={className} style={containerStyle} {...restProps}>
            {children}
        </div>
    );
};

export default BackdropContainer;
