import React, {useEffect} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import alertCircle from "@/assets-new/icons/common/alert-circle.svg"
import Text from "@/components/shared/Text/index.tsx";
import appHelper from "@/utils/appHelper.ts";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";

interface AuthConfirmationModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: () => void;
    authUrl: string;
    isHandleAuth: boolean;
    isOpenNewTab: boolean;
}

const AuthConfirmationModal: React.FC<AuthConfirmationModalProps> = ({
                                                                         open,
                                                                         onClose,
                                                                         onComplete,
                                                                         authUrl,
                                                                         isHandleAuth,
                                                                         isOpenNewTab
                                                                     }) => {

    const {trackEvent, isEnabled} = useMatomoContext();

    useEffect(() => {
        if (open && isHandleAuth) {
            if (isEnabled) {
                trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.CLICK_AUTHORIZE_STORE); 
            }
            if (isOpenNewTab) {
                window.open(authUrl, "_blank");
            } else {
                window.open(authUrl, "_self");
            }
        }
    }, [open, authUrl, isHandleAuth, isOpenNewTab,trackEvent, isEnabled]);


    const copyToClipboard = async () => {
        try {
            await navigator.clipboard.writeText(authUrl);
            await appHelper.msgApi.success("已复制到剪贴板！");
        } catch (err) {
            console.error("Failed to copy: ", err);
            await appHelper.msgApi.error("Copy failed");
        }
    };


    if (!isOpenNewTab) return null;

    return (
        <Modal
            open={open}
            onClose={onClose}
            title={`授权中`}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={448}
            height={344}
            closable={false}
            style={{
                padding: 0,
                maxWidth: "100%",
                maxHeight: "100%",
                width: "349px",
                height: "291px",
                margin: "0 auto",
            }}
        >
            <div
                className="flex items-center justify-center gap-2 w-full text-center"
                style={{
                    fontFamily: "Source Sans Pro",
                    fontSize: "14px",
                    fontWeight: "400",
                    lineHeight: "20px",
                    color: "#282830",
                }}
            >
                <img
                    src={alertCircle}
                    alt="Alert Icon"
                    className="w-6 h-6"
                />
                您正在处理授权， <br/>
                如您已完成授权流程，请进行下一步
            </div>

            <div style={{
                display: "flex",
                justifyContent: "center",
                height: "40px",
                alignItems: "center",
                gap: "16px",
                marginTop: "40px",
            }}>
                <PrimaryButton
                    label="完成授权，领取红包"
                    onClick={onComplete}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "158px",
                        padding: "10px 16px",
                    }}
                />
            </div>
            <div style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginTop: "40px",
            }}>
                <Text
                    style={{
                        fontSize: "14px",
                        textDecoration: "underline",
                        cursor: "pointer",
                        color: "#2463EB"
                    }}
                    onClick={copyToClipboard}
                >
                    无法跳转？点此复制链接前往授权
                </Text>
            </div>
            <div style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                marginTop: "16px",
            }}>
                <Text
                    style={{
                        fontSize: "14px",
                        textDecoration: "underline",
                        cursor: "pointer",
                    }}
                    onClick={() => {
                        window.open(`/financing/questions?q=${encodeURIComponent('平台API授权')}`, "_blank")
                    }}
                >
                    不知道如何授权？
                </Text>
            </div>
        </Modal>
    );
};

export default AuthConfirmationModal;
