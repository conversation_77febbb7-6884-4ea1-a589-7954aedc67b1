import React from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import QRcode from "@/assets-new/images/qrcode.png"

interface AuthConfirmationModalProps {
    open: boolean;
    onClose: () => void;
}

const AskForHelpModal: React.FC<AuthConfirmationModalProps> = ({
                                                                         open,
                                                                         onClose,
                                                                     }) => {


    return (
        <Modal
            open={open}
            onClose={onClose}
            title={`温馨提示`}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={349}
            height={248}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "450",
                height: "312px",
                margin: "0 auto",
            }}
        >
            <img
                    src={QRcode}
                    alt="qr code"
                    className="w-6 h-6"
                />



        </Modal>
    );
};

export default AskForHelpModal;
