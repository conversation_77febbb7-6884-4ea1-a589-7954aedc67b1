import React, {useState} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import Button from "@/components/shared/Button/index.tsx";
import {IPlatform} from "@/types/platforms/platforms.ts";
import appHelper from "@/utils/appHelper.ts";
import { Row, Col } from "antd";

interface PlatformSelectionModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: (platform: any) => void;
    platforms: IPlatform[];
}

const PlatformSelectionModal: React.FC<PlatformSelectionModalProps> = ({
                                                                           open,
                                                                           onClose,
                                                                           onComplete,
                                                                            platforms
                                                                       }) => {
    const [selectedPlatformId, setSelectedPlatformId] = useState<number | null>(null);

    const handleSelect = (id: number) => {
        setSelectedPlatformId(id);
    };

    const handleContinue = () => {
        if (selectedPlatformId === null) {
            appHelper.msgApi.success("请选择一个平台").then();
            return;
        }
        const selectedPlatform = platforms.find((p: IPlatform) => p.id === selectedPlatformId);
        if (selectedPlatform) {
            onComplete(selectedPlatform);
        }
    };

    const isContinueDisabled = selectedPlatformId === null;

    return (
        <Modal
            open={open}
            onClose={onClose}
            title="请选择您店铺所在的平台"
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={952}
            height={768}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "952px",
                height: "768px",
                margin: "0 auto",
            }}
        >
            <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
                {platforms.map((platform: IPlatform) => {
                    const isSelected = platform.id === selectedPlatformId;
                    return (
                        <Col
                            key={platform.id}
                            xs={24}
                            sm={12}
                            md={8}
                            lg={6}
                        >
                            <div
                                onClick={() => handleSelect(platform.id)}
                                style={{
                                    display: "flex",
                                    height: "56px",
                                    border: isSelected ? "1px solid #201747" : "1px solid #DDDFE6",
                                    borderRadius: "10px",
                                    padding: "0 12px",
                                    gap: "12px",
                                    alignItems: "center",
                                    backgroundColor: isSelected ? "#2017470A" : "",
                                    cursor: "pointer",
                                }}
                                className={
                                    `transform-gpu transition-transform transition-shadow transition-colors duration-200 ease-in-out
                                    hover:scale-105 hover:-translate-y-1 hover:shadow-lg hover:bg-[#2017470A]`
                                }
                            >
                                <img src={platform.icon} alt={platform.platform} style={{ width: 32, height: 32 }} />
                                <p style={{ margin: 0, textAlign: "center" }}>{platform.platform}</p>
                            </div>
                        </Col>
                    );
                })}
            </Row>

            <div style={{display: "flex", justifyContent: "center"}}>
                <Button
                    label="一键授权，立即用款"
                    onClick={handleContinue}
                    disabled={isContinueDisabled}
                    style={{
                        height: "48px",
                        borderRadius: "30px",
                        width: "400px",
                        padding: "10px 20px",
                        opacity: isContinueDisabled ? 0.5 : 1,
                        cursor: isContinueDisabled ? 'not-allowed' : 'pointer'
                    }}
                />
            </div>
        </Modal>
    );
};

export default PlatformSelectionModal;
