import React, {useEffect, useState} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {SecondaryButton} from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import {IPlatform, ISite} from "@/types/platforms/platforms.ts";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants";
import {Col} from "antd";

interface SelectOrAddPaymentGatewayProps {
    open: boolean;
    onClose: () => void;
    onComplete: (selectedSite: ISite) => void;
    platform: IPlatform;
}

const SelectAmazonSite: React.FC<SelectOrAddPaymentGatewayProps> = ({
                                                                        open,
                                                                        onClose,
                                                                        onComplete,
                                                                        platform
                                                                    }) => {
    const [selectedSite, setSelectedSite] = useState<ISite | null>(null);
    const {trackEvent, isEnabled} = useMatomoContext();

    useEffect(() => {
        console.log(platform)
    }, []);

    const handleContinue = () => {
        if (isEnabled) {
            trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.PSP_CONFIRM_AUTHORIZE);
        }
        if( selectedSite === null)  return
        onComplete(selectedSite);
    };

    const isContinueDisabled = selectedSite === null;

    if (!platform?.sites?.length) {
        return (
            <Modal
                open={open}
                onClose={onClose}
                title="请选择Amazon站点"
                backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF]"
                width={728}
                height={368}
                style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    width: "500px",
                    height: "312px",
                    margin: "0 auto",
                }}
            >
                <div style={{textAlign: "center", marginTop: 20}}>
                    <p>当前平台没有可用的站点，请稍后再试。</p>
                </div>
            </Modal>
        );
    }

    return (
        <Modal
            open={open}
            onClose={onClose}
            title={`请选择Amazon站点`}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF]"
            width={728}
            height={368}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "500px",
                height: "312px",
                margin: "0 auto",
            }}
        >
            <div
                style={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: 16,
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 24,
                }}
            >
                {platform.sites?.map((site: ISite) => {
                    const isSelected = selectedSite?.addr === site.addr;
                    return (
                        <Col key={site.addr} xs={24} sm={12} md={8} lg={6}>
                            <div
                                role="button"
                                tabIndex={0}
                                onClick={() => setSelectedSite(site)}
                                onKeyDown={(e) => {
                                    if (e.key === "Enter") setSelectedSite(site);
                                }}
                                style={{
                                    display: "flex",
                                    height: 56,
                                    border: isSelected ? "1px solid #201747" : "1px solid #DDDFE6",
                                    borderRadius: 10,
                                    padding: "0 12px",
                                    gap: 12,
                                    alignItems: "center",
                                    backgroundColor: isSelected ? "#2017470A" : undefined,
                                    cursor: "pointer",
                                }}
                                className="transform-gpu transition-transform transition-shadow transition-colors duration-200 ease-in-out hover:scale-105 hover:-translate-y-1 hover:shadow-lg hover:bg-[#2017470A]"
                            >
                                <p style={{margin: 0, textAlign: "center"}}>{site.name}</p>
                            </div>
                        </Col>
                    );
                })}

            </div>
            <div
                style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}
            >
                <SecondaryButton
                    label="返回"
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "88px",
                        padding: "10px 16px",
                    }}
                />
                <PrimaryButton
                    label="一键授权，立即用款"
                    onClick={handleContinue}
                    disabled={isContinueDisabled}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "160px",
                        padding: "10px 16px",
                        opacity: isContinueDisabled ? 0.5 : 1,
                        cursor: isContinueDisabled ? "not-allowed" : "pointer",
                    }}
                />
            </div>
        </Modal>
    );
};

export default SelectAmazonSite;