import React, {useState} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {SecondaryButton} from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import { Row, Col } from "antd";

interface SelectPlatformOrPspProps {
    open: boolean;
    onClose: () => void;
    onComplete: (option: "platform" | "psp") => void;
}

const SelectPlatformOrPsp: React.FC<SelectPlatformOrPspProps> = ({
                                                                     open,
                                                                     onClose,
                                                                     onComplete,
                                                                 }) => {
    const [option, setOption] = useState<"platform" | "psp" | null>(null);

    const isSelectedPlatform = option === "platform";
    const isSelectedPsp = option === "psp";

    const handleConfirm = (selectedOption: "platform" | "psp") => {
        onComplete(selectedOption);
    }

    const isContinueDisabled = option === null;

    return (
        <Modal
            open={open}
            onClose={onClose}
            title="温馨提示"
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF]"
            width={500}
            height={248}
            closable={false}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "500px",
                height: "312px",
                margin: "0 auto",
            }}
        >
            <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
                <Col
                    key="platform"
                    xs={24}
                    sm={24}
                    md={12}
                    lg={12}
                >
                    <div
                        key="platform"
                        onClick={() => setOption("platform")}
                        style={{
                            display: "flex",
                            height: "56px",
                            border: isSelectedPlatform
                                ? "1px solid #201747"
                                : "1px solid #DDDFE6",
                            borderRadius: "10px",
                            padding: "0 12px",
                            gap: "12px",
                            alignItems: "center",
                            backgroundColor: isSelectedPlatform ? "#2017470A" : undefined,
                            cursor: "pointer",
                        }}
                        className={
                        `transform-gpu transition-transform transition-shadow transition-colors duration-200 ease-in-out
                                hover:bg-[#2017470A]`
                        }
                    >
                        <p style={{margin: 0, textAlign: "center"}}>授权店铺信息</p>
                    </div>
                </Col>

                <Col
                    key="psp"
                    xs={24}
                    sm={24}
                    md={12}
                    lg={12}
                >
                    <div
                        key="psp"
                        onClick={() => setOption("psp")}
                        style={{
                            display: "flex",
                            height: "56px",
                            border: isSelectedPsp
                                ? "1px solid #201747"
                                : "1px solid #DDDFE6",
                            borderRadius: "10px",
                            padding: "0 12px",
                            gap: "12px",
                            alignItems: "center",
                            backgroundColor: isSelectedPsp ? "#2017470A" : undefined,
                            cursor: "pointer",
                        }}
                        className={
                        `transform-gpu transition-transform transition-shadow transition-colors duration-200 ease-in-out
                                hover:bg-[#2017470A]`
                        }
                    >
                        <p style={{margin: 0, textAlign: "center"}}>
                            授权支付公司信息
                        </p>
                    </div>
                </Col>
            </Row>

            <div
                style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}
            >
                <SecondaryButton
                    label="返回"
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "88px",
                        padding: "10px 16px",
                    }}
                />
                <PrimaryButton
                    label="一键授权，立即用款"
                    onClick={() => handleConfirm(option)}
                    disabled={isContinueDisabled}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "160px",
                        padding: "10px 16px",
                        opacity: isContinueDisabled ? 0.5 : 1,
                        cursor: isContinueDisabled ? 'not-allowed' : 'pointer'
                    }}
                />
            </div>
        </Modal>
    );
};

export default SelectPlatformOrPsp;
