import React from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {SecondaryButton} from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import alertCircle from "@/assets-new/icons/common/alert-circle.svg"

interface AuthConfirmationModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: () => void;
    authError: string;
    contactMsg?: string;
}

const AuthErrorModal: React.FC<AuthConfirmationModalProps> = ({
                                                                open,
                                                                onClose,
                                                                onComplete,
                                                                authError,
                                                                contactMsg = '遇到其他问题，请点击【联系客服】'
                                                            }) => {


    return (
        <Modal
            open={open}
            onClose={onClose}
            title={`温馨提示`}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={444}
            height={264}
            closable={false}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "450",
                height: "312px",
                margin: "0 auto",
            }}
        >
            <div>
                <div
                    className="flex items-start gap-2"
                    style={{
                        fontFamily: "Source Sans Pro",
                        fontSize: "14px",
                        fontWeight: "400",
                        lineHeight: "20px",
                        color: "#282830"
                    }}
                >
                    <img
                        src={alertCircle}
                        alt="Alert Icon"
                        className="w-6 h-6"
                    />
                    <div>
                        {authError}
                        <br></br><br/>
                        <div>
                            {contactMsg}
                        </div>
                    </div>

                </div>




                
            </div>

            <div style={{
                display: "flex",
                justifyContent: "center",
                height: "40px",
                alignItems: "center",
                gap: "16px",
                marginTop: "40px",
            }}>
                <SecondaryButton
                    label="联系客服"
                    onClick={onComplete}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "92px",
                        padding: "10px 16px",
                    }}
                />
                <PrimaryButton
                    label="重新授权"
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "92px",
                        padding: "10px 16px",
                    }}
                />
            </div>
        </Modal>
    );
};

export default AuthErrorModal;
