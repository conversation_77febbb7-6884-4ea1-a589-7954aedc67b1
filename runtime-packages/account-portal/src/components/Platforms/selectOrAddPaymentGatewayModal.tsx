import React, {useState} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {SecondaryButton} from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import {OauthPspItem} from "@fundpark/fp-api/types/platform/platform.ts";
import ShopIcon from "@/assets-new/icons/platforms/shopIcon.svg";
import {IPsp} from "@/types/platforms/psp";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import { SHORT_ICON_MAPPING as ICON_MAPPING } from "@/constants/psp.ts";

interface SelectOrAddPaymentGatewayProps {
    open: boolean;
    onClose: () => void;
    onComplete: (selectedPsp: OauthPspItem | null, shop_id?: number | null) => void;
    paymentGateways: OauthPspItem[];
    selectedPSP: IPsp | null;
    platform_id?: number | null;
}

const SelectOrAddPaymentGateway: React.FC<SelectOrAddPaymentGatewayProps> = ({
                                                                                 open,
                                                                                 onClose,
                                                                                 onComplete,
                                                                                 paymentGateways,
                                                                                 selectedPSP,
                                                                                 platform_id = null
                                                                             }) => {
    const [selectedPsp, setSelectedPsp] = useState<OauthPspItem | null>(null);
    const [isAddingNew, setIsAddingNew] = useState(false);
    const {trackEvent, isEnabled} = useMatomoContext();

    const handleSelectPsp = (psp: OauthPspItem) => {
        setSelectedPsp(psp);
        setIsAddingNew(false);
    };

    const handleSelectNew = () => {
        setSelectedPsp(null);
        setIsAddingNew(true);
    };

    const handleContinue = () => {
        if (isEnabled) {
            trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.PSP_CONFIRM_AUTHORIZE); 
        }
        if (isAddingNew) {
            onComplete(null, platform_id);
        } else if (selectedPsp !== null) {
            onComplete(selectedPsp,platform_id);
        }
    };

    const isContinueDisabled = !isAddingNew && selectedPsp === null;

    return (
        <Modal
            open={open}
            onClose={onClose}
            title={`请选择正在使用的${selectedPSP?.platform}账户`}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF]"
            width={728}
            height={368}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "500px",
                height: "312px",
                margin: "0 auto",
            }}
        >
            <div
                style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(2, 1fr)",
                    gap: "16px",
                    justifyItems: "center",
                    alignItems: "center",
                    marginBottom: "24px",
                }}
            >
                {paymentGateways && (paymentGateways.filter(psp => psp.platform === selectedPSP?.platform)).map((psp) => {
                    const isSelected = !isAddingNew && psp === selectedPsp;
                    return (
                        <div
                            key={psp.id}
                            onClick={() => handleSelectPsp(psp)}
                            style={{
                                display: "flex",
                                width: "280px",
                                height: "94px",
                                border: isSelected ? "1px solid #201747" : "1px solid #DDDFE6",
                                borderRadius: "10px",
                                backgroundColor: isSelected ? "#2017470A" : "#FFFFFF",
                                cursor: "pointer",
                                overflow: "hidden",
                                paddingLeft: "16px",
                            }}
                        >
                            <div
                                style={{
                                    flex: 7,
                                    display: "flex",
                                    flexDirection: "column",
                                    gap: "0px",
                                }}
                            >
                                <div
                                    style={{
                                        flex: 6,
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "12px",
                                    }}
                                >
                                    <img
                                        src={ICON_MAPPING[psp.platform] || ""}
                                        alt={`${psp.platform} icon`}
                                        style={{width: 32, height: 32, borderRadius: 4}}
                                    />
                                    <p
                                        style={{
                                            margin: 0,
                                            fontSize: "16px",
                                            fontWeight: 600,
                                            color: "#201747",
                                        }}
                                    >
                                        {psp.platform}
                                    </p>
                                </div>
                                <div
                                    style={{
                                        flex: 4,
                                        display: "flex",
                                        alignItems: "baseline",
                                        gap: "4px",
                                    }}
                                >
                                <span style={{fontSize: "14px", color: "#7A7A7A", display: "inline-block", width: '91px'}}>
                                    用户 id：
                                </span>
                                <span style={{fontSize: "14px",
                                            color: "#000000",
                                            wordBreak: "break-all",
                                            overflowWrap: "break-word", 
                                            maxWidth: "100%"}}>
                                    {psp.account_id}
                                </span>
                                </div>
                            </div>
                                    <div
                                        style={{
                                            flex: 3,
                                            display: "flex",
                                            alignItems: "flex-start",
                                            justifyContent: "flex-end",
                                        }}
                                    >
                                    <span
                                        style={{
                                            padding: "2px 12px",
                                            fontSize: "14px",
                                            fontWeight: 500,
                                            color: "#1A73E8",
                                            backgroundColor: "#E8F0FE",
                                            borderRadius: "0 10px 0 15px",
                                            width: "88px",
                                            height: "32px",
                                        }}
                                    >
                                    Authorized
                                    </span>
                                </div>
                        </div>
                    );
                })}

                {/* 新增授权 卡片 */}
                <div
                    onClick={handleSelectNew}
                    style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "280px",
                        height: "94px",
                        border: isAddingNew ? "1px solid #201747" : "1px solid #DDDFE6",
                        borderRadius: "10px",
                        backgroundColor: isAddingNew ? "#2017470A" : "#FFFFFF",
                        cursor: "pointer",
                        padding: "0 16px",
                    }}
                >
                    <img src={ShopIcon} alt="新增授权" style={{marginRight: 8}}/>
                    <span style={{fontSize: "16px", fontWeight: 600, color: "#2463EB"}}>
            新增授权
          </span>
                </div>
            </div>

            <div
                style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}
            >
                <SecondaryButton
                    label="返回"
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "88px",
                        padding: "10px 16px",
                    }}
                />
                <PrimaryButton
                    label="一键授权，立即用款"
                    onClick={handleContinue}
                    disabled={isContinueDisabled}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "160px",
                        padding: "10px 16px",
                        opacity: isContinueDisabled ? 0.5 : 1,
                        cursor: isContinueDisabled ? "not-allowed" : "pointer",
                    }}
                />
            </div>
        </Modal>
    );
};

export default SelectOrAddPaymentGateway;