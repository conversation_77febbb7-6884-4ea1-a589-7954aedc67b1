import React from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {IPingPongData} from "@/types/platforms/psp.ts";
import PrimaryButton from "@/components/shared/Button";
import PingPong from "@/assets-new/icons/psps/long/pingpong.svg";
import PingPongHelp from "@/assets-new/images/platforms/pingpong_help.png";
import {Form, Select} from "antd";
import {Input} from "@/components/common";
import FormContainer from "@/components/common/form/FormContainer";
import {useTranslation} from "react-i18next";
import {getCiRules, getEmailRules, getMobileRules} from "@/constants/data_validation_rules.ts";
import {useCommonStore} from "@/store/common.ts";
import CountryCodePhoneInput from "../shared/CountryCodePhoneInput";
import { DEFAULT_AREA_CODES } from "../common/constants";
import {validateEmail} from "@fundpark/ui-utils";

interface PspSelectionModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: (pingpongData: IPingPongData, shop_id?: number | null) => void;
    // platform: IPlatform;
    // payment_gateway: IPsp;
}

const PingPongModal: React.FC<PspSelectionModalProps> = ({
                                                             open,
                                                             onClose,
                                                             onComplete,
                                                             // platform,
                                                             // payment_gateway
                                                         }) => {

    const handleContinue = async () => {
        await form.validateFields();
        onComplete({
            "id_number": form.getFieldValue("ci_number"),
            "phone_area_code": form.getFieldValue("mobilePhoneAreaCode"),
            "phone": form.getFieldValue("mobile"),
            "company_name": form.getFieldValue("company_name"),
            "email": form.getFieldValue("email"),
        });
    };

    const [form] = Form.useForm();
    const {t} = useTranslation();
    const store = useCommonStore.getState();

    const EMAIL_RULES = getEmailRules(t);
    const MOBILE_RULES = getMobileRules(t, form, 'mobilePhoneAreaCode');
    const CI_RULES = getCiRules(t);


    const handleMobileChange = (areaCode: string, mobile: string) => {
        form.setFieldsValue({mobile, mobilePhoneAreaCode: areaCode});
        form.validateFields(['mobile']);
    };

    console.log(store.userInfo)

    return (
        <Modal
            open={open}
            onClose={onClose}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={1067}
            height={785}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "1067px",
                height: "785px",
                margin: "0 auto",
            }}
        >
            <div className="flex flex-col items-center">
                <div>
                    <img src={PingPong} style={{width: "88px", height: "28px", marginBottom: "28px"}} alt={"pp_img"}/>
                </div>
                <div
                    style={{
                        fontFamily: "Inter",
                        fontSize: "16px",
                        fontWeight: "700",
                        lineHeight: "100%",
                        textAlign: "center",
                        marginBottom: "30px",
                        color: "#7D7D7D"
                    }}
                >请填写您在pingpong注册账户的信息
                </div>
                <div>
                    <img src={PingPongHelp} style={{width: "492px", height: "194px", marginBottom: "28px"}}
                         alt={"pp_help"}/>
                </div>
                <div
                    style={{
                        fontFamily: "Inter",
                        fontSize: "10px",
                        fontWeight: "400",
                        lineHeight: "100%",
                        textAlign: "center",
                        marginBottom: "30px",
                        color: "#7D7D7D"
                    }}
                >登录PingPong (https://us.pingpongx.com/userCenter)，点击右上角"账号中心"查看"我的账号"
                </div>
                <FormContainer>
                    <Form form={form} layout="vertical" className="w-full" requiredMark={false}
                          initialValues={{
                              email: store.userInfo?.email,
                              company_name: store.userInfo?.company_name,
                    }}>
                        <Form.Item label={"类别"} name="type" className="mb-4">
                            <Select
                                defaultValue="hong_kong"
                                disabled
                                options={[{value: 'hong_kong', label: '香港企业'}]}
                            />
                        </Form.Item>
                        <Form.Item label={"公司名称"} name="company_name" rules={[
                            { required: true, message: "请输入公司名称" },
                        ]} className="mb-4">
                            <Input placeholder={"请输入公司名称"} size="large"
                                   defaultValue={store.userInfo?.company_name}/>
                        </Form.Item>

                        <Form.Item label={"公司注册证CI号码"} name="ci_number" rules={CI_RULES} className="mb-4">
                            <Input placeholder={"请输入公司注册证CI号码"} size="large"/>
                        </Form.Item>

                        <Form.Item label={t("login.mobile")} className="mb-4" >
                            <CountryCodePhoneInput
                                className="mb-0"
                                countryCodeName="mobilePhoneAreaCode"
                                phoneNumberName="mobile"
                                countryCodeOptions={DEFAULT_AREA_CODES}
                                countryCodeRules={[
                                    { required: true, message: '请选择国家/地区代码' },
                                    { 
                                      validator: (_, value) => {
                                        if (value && value.replace('+', '') === '') {
                                          console.log('value', value);
                                          console.log(value.replace('+', ''),'value.replace')
                                          return Promise.reject(new Error('请选择有效的国家/地区代码'));
                                        }
                                        return Promise.resolve();
                                      }
                                    }
                                  ]}
                                phoneNumberRules={MOBILE_RULES}
                                countryCodePlaceholder="选择"
                                phoneNumberPlaceholder={t("login.mobileRequired")}
                            />
                        </Form.Item>
                    </Form>
                </FormContainer>
            </div>
            <div
                style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}
            >
                {/*<SecondaryButton*/}
                {/*    label="取消"*/}
                {/*    onClick={onClose}*/}
                {/*    style={{*/}
                {/*        height: "40px",*/}
                {/*        borderRadius: "60px",*/}
                {/*        width: "102px",*/}
                {/*        padding: "10px 16px",*/}
                {/*    }}*/}
                {/*/>*/}
                <PrimaryButton
                    label="绑定用款"
                    onClick={handleContinue}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "158px",
                        padding: "10px 16px",
                    }}
                />
            </div>
        </Modal>
    );
};

export default PingPongModal;
