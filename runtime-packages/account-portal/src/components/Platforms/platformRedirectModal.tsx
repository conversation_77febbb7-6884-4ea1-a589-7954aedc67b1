import React, {useEffect} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {SecondaryButton} from "@/components/shared/SecondaryButton/index.tsx";
import PrimaryButton from "@/components/shared/Button/index.tsx";
import appHelper from "@/utils/appHelper";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";

interface PlatformRedirectModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: () => void;
    selectedPlatform: any;
    authUrl: string
}

const PlatformRedirectModal: React.FC<PlatformRedirectModalProps> = ({
                                                                         open,
                                                                         onClose,
                                                                         onComplete,
                                                                         selectedPlatform,
                                                                         authUrl
                                                                     }) => {

    const copyToClipboard = async () => {
      try {
        await navigator.clipboard.writeText(authUrl);
        await appHelper.msgApi.success("已复制到剪贴板！");
      } catch (err) {
        console.error("Failed to copy: ", err);
        await appHelper.msgApi.error("Copy failed");
      }
    };

    const {trackEvent, isEnabled} = useMatomoContext();
    useEffect(() => {
        if (open && isEnabled) {
            trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.CLICK_AUTHORIZE_STORE); 
        }
    }, [open, authUrl, trackEvent, isEnabled]);

    return (
        <Modal
            open={open}
            onClose={onClose}
            title={`请点击以下链接进行${selectedPlatform?.platform}授权`}
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={450}
            height={312}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "450",
                height: "312px",
                margin: "0 auto",
            }}
        >
            <div style={{}}>
                <a
                    href={authUrl}
                    style={{
                        color: "#282830",
                        fontFamily: "Source Sans Pro",
                        fontWeight: "400",
                        fontSize: "14px",
                        lineHeight: "20px",
                        letterSpacing: "0%",
                        verticalAlign: "center",
                    }}
                >
                    {authUrl}
                </a>
                <div
                    style={{
                        height: "20px",
                        width: "56px",
                        color: "#2463EB",
                        fontFamily: "Source Sans Pro",
                        fontWeight: "400",
                        fontSize: "14px",
                        lineHeight: "20px",
                        letterSpacing: "0%",
                        verticalAlign: "center",
                        marginTop: "8px",
                        cursor: "pointer",
                    }}
                    onClick={copyToClipboard}
                >
                    一键复制
                </div>
            </div>

            <div style={{
                display: "flex",
                justifyContent: "center",
                height: "40px",
                alignItems: "center",
                gap: "16px",
                marginTop: "40px",
            }}>
                <SecondaryButton
                    label="取消"
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "102px",
                        padding: "10px 16px",
                    }}
                />
                <PrimaryButton
                    label="已完成授权"
                    onClick={onComplete}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "102px",
                        padding: "10px 16px",
                    }}
                />
            </div>
        </Modal>
    );
};

export default PlatformRedirectModal;
