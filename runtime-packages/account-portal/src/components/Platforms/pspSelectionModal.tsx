import React, {useState} from "react";
import {Modal} from "@/components/shared/Modal/index.tsx";
import {IPsp} from "@/types/platforms/psp.ts";
import {SecondaryButton} from "@/components/shared/SecondaryButton";
import PrimaryButton from "@/components/shared/Button";
import { Row, Col } from "antd";

interface PspSelectionModalProps {
    open: boolean;
    onClose: () => void;
    onComplete: (psp: IPsp, shop_id?: number) => void;
    payment_gateway: IPsp[];
    platform_id?: number;
}

const PspSelectionModal: React.FC<PspSelectionModalProps> = ({
                                                                 open,
                                                                 onClose,
                                                                 onComplete,
                                                                 payment_gateway,
                                                                 platform_id,
                                                             }) => {
    const [selectedPspId, setSelectedPspId] = useState<number | null>(null);


    const handleSelect = (id: number) => {
        setSelectedPspId(id);
    };

    const handleContinue = () => {
        console.log(selectedPspId);
        if (selectedPspId) {
            const selectedPsp = payment_gateway.find((p) => p.id === selectedPspId);
            if (selectedPsp) {
                onComplete(selectedPsp, platform_id);
            }
        } else {
            return;
        };
    };

    const isContinueDisabled = selectedPspId === null;

    return (
        <Modal
            open={open}
            onClose={onClose}
            title="请选择正在使用的支付公司"
            backgroundColor="bg-gradient-to-b from-[#65C6C395] via-[#D0F0EF30] to-[#FFFFFF] bg-[length:100%_130%]"
            width={728}
            height={368}
            closable={false}
            style={{
                maxWidth: "100%",
                maxHeight: "100%",
                width: "728px",
                height: "368px",
                margin: "0 auto",
            }}
        >
            <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
                {payment_gateway.map((psp) => {
                    const isSelected = psp.id === selectedPspId;
                    return (
                        <Col
                            key={psp.id}
                            xs={24}
                            sm={12}
                            md={8}
                            lg={8}
                        >
                            <div
                                key={psp.id}
                                onClick={() => handleSelect(psp.id)}
                                style={{
                                    display: "flex",
                                    height: "56px",
                                    border: isSelected ? "1px solid #201747" : "1px solid #DDDFE6",
                                    borderRadius: "10px",
                                    padding: "0 12px",
                                    gap: "12px",
                                    alignItems: "center",
                                    backgroundColor: isSelected ? "#2017470A" : "",
                                    cursor: "pointer",
                                }}
                                className={
                                    `transform-gpu transition-transform transition-shadow transition-colors duration-200 ease-in-out
                                    hover:scale-105 hover:-translate-y-1 hover:shadow-lg hover:bg-[#2017470A]`
                                }
                            >
                                <img
                                    src={psp.icon}
                                    alt={psp.platform}
                                    style={{width: 32, height: 32}}
                                />
                                <p style={{margin: 0, textAlign: "center"}}>{psp.platform}</p>
                            </div>
                        </Col>
                    );
                })}
            </Row>

            <div
                style={{
                    display: "flex",
                    justifyContent: "center",
                    height: "40px",
                    alignItems: "center",
                    gap: "16px",
                    marginTop: "40px",
                }}
            >
                <SecondaryButton
                    label="返回"
                    onClick={onClose}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "102px",
                        padding: "10px 16px",
                    }}
                />
                <PrimaryButton
                    label="一键授权，立即用款"
                    onClick={handleContinue}
                    disabled={isContinueDisabled}
                    style={{
                        height: "40px",
                        borderRadius: "60px",
                        width: "158px",
                        padding: "10px 16px",
                        opacity: isContinueDisabled ? 0.5 : 1,
                        cursor: isContinueDisabled ? 'not-allowed' : 'pointer'
                    }}
                />
            </div>
        </Modal>
    );
};

export default PspSelectionModal;
