import React, {useEffect, useMemo, useRef, useState} from "react";
import {IPlatform, ISite} from "@/types/platforms/platforms";
import {IPingPongData, IPsp} from "@/types/platforms/psp";
import PlatformSelectionModal from "./platformSelectionModal";
import PlatformRedirectModal from "./platformRedirectModal";
import AuthConfirmationModal from "./authConfirmationModal";
import AuthErrorModal from "./authErrorModal.tsx";
import PspSelectionModal from "./pspSelectionModal";
import SelectPlatformOrPspModal from "./selectPlatformOrPspModal.tsx";
import SelectOrAddPaymentGateway from "./selectOrAddPaymentGatewayModal.tsx";
import SelectAmazonSite from "./selectAmazonSiteModal.tsx";
import PingPongModal from "./pingpongModal.tsx";
import {ModalStep} from "@/types/platforms/modalSteps";
import {PlatformActionEnum} from "@/constants/platforms";
import {usePlatformStore} from "@/store/platform/platformStore";
import {usePaymentGatewayStore} from "@/store/platform/paymentGatewayStore";
import type {GetOauthShopListRes} from "@fundpark/fp-api";
import {bindPSPtoShop, getAuthCallbackResult, getAuthURL} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper.ts";
import AppHelper from "@/utils/appHelper.ts";
import {useCommonStore} from "@/store/common.ts";
import {OurWechatModal} from "@/components/shared/OurWechatModal";
import {OauthPspItem} from "@fundpark/fp-api/types/platform/platform.ts";
import {useLocation, useNavigate} from "react-router-dom";
import {useMatomoContext} from "@/contexts/MatomoContext.tsx";
import {TRACKING_ACTIONS, TRACKING_CATEGORIES, TRACKING_EVENTS} from "@/components/shared/tracking/constants";


export interface PlatformMainModalProps {
    modalStep: ModalStep;
    setModalStep: (step: ModalStep) => void;
    authedPlatforms: GetOauthShopListRes | null;
    redirectPath?: string;
    shouldCompleteRefresh?: boolean;
    onAuthSuccess?: () => void;
}

export interface AuthErrorParams {
    errorMsg: string;
    width?: number;
    height?: number;
}

const PlatformMainModal: React.FC<PlatformMainModalProps> = ({
                                                                 modalStep,
                                                                 setModalStep,
                                                                 authedPlatforms,
                                                                 redirectPath = '',
                                                                 shouldCompleteRefresh = true,
                                                                 onAuthSuccess = () => {}
                                                             }) => {
    const [selectedPlatform, setSelectedPlatform] = useState<IPlatform | null>(null);
    const [selectedPaymentGateway, setSelectedPaymentGateway] = useState<IPsp | null>(null);
    const [selectedAmazonSite, setSelectedAmazonSite] = useState<ISite | null>(null);

    const [authUrl, setAuthUrl] = useState<string>("");
    const [authToken, setAuthToken] = useState<string | null>(null);
    const user = useCommonStore((s) => s.userInfo);
    const redirect_url = redirectPath;

    const {platforms, fetchPlatforms} = usePlatformStore();
    const {payment_gateway, fetchPaymentGateway} = usePaymentGatewayStore();
    const [authErrorParams, setAuthErrorParams] = useState<AuthErrorParams | null>(null);
    // const isAnyShopAuthed = useCommonStore((s) => s.isAnyShopAuthed);


    const navigate = useNavigate();
    const {search, pathname} = useLocation();
    const fetchAuthedList = useCommonStore((s) => s.fetchAuthedList);
    const {trackEvent, isEnabled} = useMatomoContext();


    useEffect(() => {
        if (!user) {
            return
        }
        fetchPaymentGateway().catch(console.error);
    }, [fetchPaymentGateway, user]);

    useEffect(() => {
        if (!user) {
            return
        }
        fetchPlatforms().catch(console.error);
    }, [fetchPlatforms, user]);

    useEffect(() => {
        if (modalStep === 'platformSelection') {
            setSelectedPaymentGateway(null);
        }
    }, [modalStep]);

    useEffect(() => {
        if (!selectedPlatform || !authUrl || !authToken) return;
        switch (selectedPlatform.action) {
            case PlatformActionEnum.auth_without_psp:
                if (selectedPlatform.platform.toLowerCase() === 'Amazon'.toLowerCase()) {
                    if (selectedPaymentGateway === null) {
                        setModalStep('platformRedirect');
                    } else {
                        setModalStep('authConfirmation');
                    }
                } else {
                    setModalStep('authConfirmation');
                }
                break;

            case PlatformActionEnum.auth_with_psp:
                setModalStep('authConfirmation');
                break;

            default:
                console.error(`Unknown action: ${selectedPlatform.action}`);
                setModalStep('none');
        }
    }, [selectedPlatform, authUrl, authToken, setModalStep]);

    const fetchUrlAndToken = async (
        platform: IPlatform,
        pspId: number | null,
        pingpongData: IPingPongData | null = null,
        site: ISite | null = null
    ): Promise<boolean> => {
        try {
            if (platform.platform === 'Amazon' && !pspId && !site) {
                return false;
            }

            const metaData: Record<string, any> = {
                scene: "psp",
                ...(pingpongData || {}),
                ...(site?.addr ? { sites: site.addr } : {}),
            };

            const app_host = appHelper.getAppHost(true);

            const res = await getAuthURL({
                platform_shop_id: platform.id,
                platform_psp_id: pspId,
                redirect_url: redirect_url === '' ? `${app_host}/financing?platform_id=${platform.id}&psp_id=${pspId ?? ''}` : redirect_url,
                meta_data: metaData,
            });

            if (res.code !== 0) throw new Error('Auth URL fetch failed');
            setAuthUrl(res.data.oauth_url);
            setAuthToken(res.data.id_token);
            return true;
        } catch (err) {
            console.error(err);
            alert('Error fetching auth URL');
            return false;
        }
    };

    const handlePlatformSelected = async (platform: IPlatform) => {
        setSelectedPlatform(platform);
        switch (platform.action) {
            case PlatformActionEnum.auth_without_psp:
                setAuthUrl("");
                setAuthToken(null);
                setModalStep('selectPlatformOrPsp');
                break;
            case PlatformActionEnum.auth_with_psp:
                setModalStep('pspSelection');
                break;
            default:
                console.error(`Unsupported action: ${platform.action}`);
                alert('Unknown platform action');
                setModalStep('none');
        }
    };

    const handlePlatformOrPspSelect = async (option) => {
        setAuthUrl("");
        setAuthToken(null);
        setSelectedPaymentGateway(null);

        if (selectedPlatform?.platform === 'Amazon') {
            setModalStep('selectAmazonSite');
        }

        if (!option) {
            if (isEnabled) {
                trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.PSP_CONFIRM_AUTHORIZE);
            }
            return
        }
        switch (option) {
            case "platform":
                await fetchUrlAndToken(selectedPlatform, null);
                break;
            case "psp":
                setModalStep('pspSelection');
                break;
            default:
                console.error(`Unsupported action: ${option}`);
                alert('Unknown platform action');
                setModalStep('none');
        }
    };

    const handleSelectOrAddPaymentGatewayComplete = async (psp: OauthPspItem | null) => {
        if (psp === null) {
            await handlePspComplete(selectedPaymentGateway);
        } else if (psp) {
            await handleSelectedAuthedPspComplete(psp);
        }
    }

    const checkPspAuthed = async (psp: IPsp) => {
        setSelectedPaymentGateway(psp);
        const alreadyAuthed = authedPlatforms.psp.some(auth => auth.platform_id === psp.id);
        if (alreadyAuthed) {
            setModalStep('selectOrAddPaymentGateway');
        } else {
            await handlePspComplete(psp);
        }
    }

    const handlePspComplete = async (psp: IPsp) => {
        setSelectedPaymentGateway(psp);
        setAuthUrl("");
        setAuthToken(null);
        if (psp.platform.toLowerCase() === 'PingPong'.toLowerCase()) {
            setModalStep('pingpongVerification');
        } else if (selectedPlatform) {
            await fetchUrlAndToken(selectedPlatform, psp.id);
        }
    };

    const handleSelectedAuthedPspComplete = async (psp: OauthPspItem) => {
        const selectedPsp = payment_gateway.find((p) => p.id === psp.platform_id) || null;
        setSelectedPaymentGateway(selectedPsp);
        try {
            const res = await bindPSPtoShop({
                platform_shop_id: selectedPlatform.id,
                bind_psp_id: psp.id
            })
            authResp(res.code, res.message);
            if (res.code === 0) {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        } catch (error) {
            console.error("Error fetching authorized shop data", error);
        }
    };

    const handlePingPongComplete = async (pingpongData: IPingPongData | null | undefined) => {
        if (selectedPlatform && selectedPaymentGateway && pingpongData) {
            await fetchUrlAndToken(selectedPlatform, selectedPaymentGateway.id, pingpongData);
        }
    };

    const handleRedirectComplete = () => {
        setModalStep('none');
        alert(`Authorized with ${selectedPlatform?.platform}`);
    };

    const handleAuthComplete = async (token: any) => {
        if (!token) {
            return;
        }
        try {
            const res = await getAuthCallbackResult({id_token: token});
            await authResp(res.code, res.message);
        } catch (rawErr: any) {
            console.log(rawErr);
        }
    };

    const authResp = async (code: number, msg?: string) => {
        let params: AuthErrorParams;
        switch (code) {
            case 0:
                await fetchAuthedList(true);
                setModalStep('none');
                AppHelper.msgApi.success("授权成功");
                onAuthSuccess();
                return;
            case 4004:
                params = {
                    errorMsg: "未收到您的授权申请，授权店铺不会改变IP地址，请放心授权哦",
                    width: 450,
                    height: 220,
                };
                break;
            case 4003:
                params = {
                    errorMsg: "检测到您的店铺/支付账户已被其他用户绑定授权了，换一个店铺/支付账户继续申请～",
                    width: 450,
                    height: 220,
                };
                break;
            case 4015:
                params = {
                    errorMsg: "关联关系已存在，请勿重复提交。",
                    width: 450,
                    height: 220,
                };
                break;
            default:
                params = {
                    errorMsg: msg || 'An unexpected error occurred.',
                    width: 400,
                    height: 200,
                };
        }
        setAuthErrorParams(params);
        setModalStep('authError');
    }

    const resetAuthState = () => {
        setSelectedPlatform(null);
        setAuthUrl("");
        setAuthToken(null);
        setSelectedPaymentGateway(null);
        setSelectedAmazonSite(null);
    }

    const didPlatformAuth = useRef(false);

    useEffect(() => {
        if (didPlatformAuth.current) return;
        didPlatformAuth.current = true;

        const params = new URLSearchParams(search);
        const token = params.get("id_token");

        if (token) {
            (async () => {
                await handleAuthComplete(token);

            })();
            setAuthToken(null);
            if (shouldCompleteRefresh) {
                navigate(pathname, {replace: true});
            }
        }
    }, []);

    const ActiveModal = useMemo(() => {
        switch (modalStep) {
            case 'platformSelection':
                return (
                    <PlatformSelectionModal
                        open
                        onClose={() => setModalStep('none')}
                        onComplete={handlePlatformSelected}
                        platforms={platforms}
                    />
                );
            case 'pspSelection':
                return (
                    <PspSelectionModal
                        open
                        onClose={() => setModalStep('platformSelection')}
                        onComplete={checkPspAuthed}
                        payment_gateway={payment_gateway}
                    />
                );
            case 'pingpongVerification':
                return (
                    <PingPongModal
                        open
                        onClose={() => setModalStep('pspSelection')}
                        onComplete={handlePingPongComplete}
                        payment_gateway={selectedPaymentGateway}
                        platform={selectedPlatform}
                    />
                );
            case 'platformRedirect': // for Amazon
                return selectedPlatform && (
                    <PlatformRedirectModal
                        open
                        onClose={() => {
                            setModalStep('platformSelection');
                            resetAuthState();
                        }}
                        onComplete={async () => {
                            await handleAuthComplete(authToken)
                        }}
                        selectedPlatform={selectedPlatform}
                        authUrl={authUrl}
                    />
                );
            case 'authConfirmation':
                return (
                    <AuthConfirmationModal
                        open
                        onClose={() => setModalStep('platformSelection')}
                        onComplete={async () => {
                            await handleAuthComplete(authToken)
                        }}
                        authUrl={authUrl}
                        isHandleAuth={true}
                        isOpenNewTab={(selectedPaymentGateway?.is_callback ?? selectedPlatform?.is_callback) !== 1}
                    />
                );
            case 'authError':
                return (
                    <AuthErrorModal
                        open
                        onClose={() => {
                            setModalStep('platformSelection')
                            resetAuthState()
                        }}
                        onComplete={() => {
                            setModalStep('askForHelp')
                            resetAuthState()
                        }}
                        authError={authErrorParams?.errorMsg || ''}
                    />
                );
            case 'askForHelp':
                return (
                    <OurWechatModal
                        open
                        onClose={() => {
                            resetAuthState()
                            setModalStep('platformSelection')
                        }}
                        hasAlertIcon
                        textAlign="center"
                    />
                );
            case 'selectPlatformOrPsp':
                return (
                    <SelectPlatformOrPspModal
                        open
                        onClose={() => {
                            setModalStep('platformSelection');
                            setSelectedPlatform(null);
                        }}
                        onComplete={handlePlatformOrPspSelect}
                    />
                );
            case 'selectOrAddPaymentGateway':
                return (
                    <SelectOrAddPaymentGateway
                        open
                        onClose={() => {
                            setModalStep('pspSelection');
                        }}
                        onComplete={handleSelectOrAddPaymentGatewayComplete}
                        paymentGateways={authedPlatforms.psp}
                        selectedPSP={selectedPaymentGateway}
                    />
                );
            case 'selectAmazonSite':
                return (
                    <SelectAmazonSite
                        open
                        onClose={() => {
                            setModalStep('selectPlatformOrPsp');
                            setSelectedAmazonSite(null);
                        }}
                        onComplete={async (site) => {
                            setSelectedAmazonSite(site);
                            setAuthUrl("");
                            setAuthToken(null);
                            setSelectedPaymentGateway(null);
                            await fetchUrlAndToken(selectedPlatform, null, null, site);
                        }}
                        platform={selectedPlatform}
                    />
                );
            default:
                return null;
        }
    }, [
        modalStep,
        platforms,
        payment_gateway,
        selectedPlatform,
        selectedPaymentGateway,
        authUrl,
        setModalStep,
        handlePlatformSelected,
        handlePspComplete,
        handlePingPongComplete,
        handleRedirectComplete,
        handleAuthComplete
    ]);

    return <>{ActiveModal}</>;
};

export default PlatformMainModal;
