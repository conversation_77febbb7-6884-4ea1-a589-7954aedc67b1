import { Modal as Ant<PERSON><PERSON><PERSON>, Spin } from "antd";
import React, { useEffect, useState } from "react";
import { useMatomoContext } from "@/contexts/MatomoContext";
import { TRACKING_CATEGORIES, TRACKING_ACTIONS, TRACKING_EVENTS } from "@/components/shared/tracking/constants";
import styles from "./index.module.scss";
import redPocketBody from "@/assets-new/images/full_red_pocket_body.svg";
import activateButton from "@/assets-new/images/red_pocket_activate_button.svg";
import pocketUnavailable from "@/assets-new/images/pocket_unavailable.svg";
import alertIcon from "@/assets-new/icons/common/alert-circle.svg";
import { useRedPocketStore } from "@/store/redpocket.ts";
import { RED_POCKET_TYPE } from "@/constants/general.ts";
import appHelper from "@/utils/appHelper.ts";
import { useSelectedProduct } from "@/hooks/useSelectedProduct";
import { useNavigate } from "react-router-dom";
import { POSITION_STATUS } from "@/constants/position-status";
import { usePositionTracking } from "@/hooks/usePositionTracking";
import { useLimitStore } from "@/store/limits.ts";
import { formatNumberToFraction } from "@/utils/math";
import TwoButtonModal from "../shared/GenericModal";
import { OurWechatModal } from "../shared/OurWechatModal";

interface RedPocketModal500Props {
    open: boolean;
    onClose: () => void;
    style?: React.CSSProperties;
    width?: number | "auto";
    height?: number | string;
    backgroundColor?: string;
    closeText?: string;
    actionTitle?: string;
    descText?: string;
}

const RedPocketModal500: React.FC<RedPocketModal500Props> = ({ open, onClose, style, height = "auto", closeText }) => {
    const { trackEvent } = useMatomoContext();

    const handleClose = () => {
        setShowRules(false);
        onClose();
    };

    const navigate = useNavigate();
    const { trackPosition } = usePositionTracking();
    const product = useSelectedProduct();
    const routeToPage = async () => {
        if (!product) return;
        if (product === "xdj") {
            navigate(`/credit/hook/activate-shop`);
        } else if (product === "credit-approval-automation") {
            await trackPosition({
                path: `/credit/underwriting/increase-limit/authorize`,
                status: POSITION_STATUS.CAA_DRAWDOWN_ACCOUNT_DRAFT
            });
            navigate(`/credit/underwriting/increase-limit/authorize`);
            onClose();
        }
    };

    const RED_POCKET_NAME = RED_POCKET_TYPE.deduction.name;
    const RED_POCKET_ID = RED_POCKET_TYPE.deduction.id;

    const [remainingRedPocket, setRemainingRedPocket] = useState<number>(0);
    const [targetRedPocket, setTargetRedPocket] = useState<any>(null);
    const [actionBtn, setActionBtn] = useState<React.ReactNode>(null);
    const [statusText, setStatusText] = useState<React.ReactNode>(null);
    const { collect, loading, getRedPocketInfoById, redPockets } = useRedPocketStore();
    const [redPocketTitleStyle, setRedPocketTitleStyle] = useState<React.CSSProperties | undefined>(undefined);
    const { creditApplication } = useLimitStore();

    const handleCollect = async () => {
        trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.GET_500_RED_PACKET);
        if (redPockets.find(pocket => pocket.type === RED_POCKET_NAME)) return;

        try {
            const res = await collect(RED_POCKET_ID);
            if (res.code === 0) {
                const detail = await getRedPocketInfoById(RED_POCKET_ID);
                setRemainingRedPocket(detail?.remaining_quantity ?? 0);
                trackEvent(TRACKING_CATEGORIES.BUTTON, TRACKING_ACTIONS.CLICK, TRACKING_EVENTS.GOT_500_RED_PACKET);
            }
            if (res.code !== 0) {
                appHelper.msgApi.error("红包领取失败");
            }
        } catch (err) {
            console.error("Failed to collect red pocket:", err);
        }
    };

    useEffect(() => {
        const fetchRedPocketStatus = async () => {
            try {
                const detail = await getRedPocketInfoById(RED_POCKET_ID);
                setRemainingRedPocket(detail?.remaining_quantity ?? 0);
                setTargetRedPocket(detail);
            } catch (error) {
                console.error("Error fetching red pocket status:", error);
            }
        };
        void fetchRedPocketStatus();
    }, [getRedPocketInfoById]);

    const handleClickEvent = () => {
        routeToPage().then();
    };

    const preLimit = creditApplication?.pre_limit ?? 0;
    const preWml = creditApplication?.pre_wml ?? 0;

    const buttonText =
        product === "xdj" ? (
            <div className={styles.extraActionButtonText}>
                立即激活{preWml ? ` ${formatNumberToFraction(preWml)}` : ''}
            </div>
        ) : (
            <div className={styles.extraActionButtonText}>查看额度</div>
        );

    useEffect(() => {
        if (loading) {
            setActionBtn(
                <button className={styles.extraActionButton} disabled>
                    <Spin />
                </button>
            );
            setStatusText(null);
            setRedPocketTitleStyle({ display: "flex", alignItems: "center" });
            return;
        }
        const currentRedPocket = redPockets.find(pocket => pocket.type === RED_POCKET_NAME);
        if (!redPockets || !currentRedPocket) {
            if (
                targetRedPocket?.status === "expired" ||
                targetRedPocket?.status === "invalid" ||
                targetRedPocket?.status === "inactive"
            ) {
                setActionBtn(
                    <button
                        className={styles.extraActionButton}
                        onClick={() => {
                            routeToPage();
                        }}
                    >
                        <img src={activateButton} alt="Action" />
                        <div className={styles.extraActionButtonText}>{buttonText}</div>
                    </button>
                );
                setStatusText(
                    <button disabled className={styles.redPocketActionButton}>
                        <img src={pocketUnavailable} alt="Action" style={{ width: "50%" }} />
                        <div className={styles.redPocketActionButtonText}>已失效</div>
                    </button>
                );
                setRedPocketTitleStyle({
                    background: "grey",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    // @ts-ignore
                    textFillColor: "transparent",
                    display: "flex",
                    alignItems: "center"
                });
                return;
            }
            if (remainingRedPocket > 0) {
                setActionBtn(
                    <button className={styles.extraActionButton} onClick={handleCollect}>
                        <img src={activateButton} alt="Action" />
                        <div className={styles.extraActionButtonText}>立即领取</div>
                    </button>
                );
                setStatusText(null);
                setRedPocketTitleStyle({ display: "flex", alignItems: "center" });
                return;
            } else {
                setActionBtn(
                    <button
                        className={styles.extraActionButton}
                        onClick={() => {
                            routeToPage();
                        }}
                    >
                        <img src={activateButton} alt="Action" />
                        <div className={styles.extraActionButtonText}>{buttonText}</div>
                    </button>
                );
                setStatusText(
                    <button disabled className={styles.redPocketActionButton}>
                        <img src={pocketUnavailable} alt="Action" style={{ width: "50%" }} />
                        <div className={styles.redPocketActionButtonText}>已领完</div>
                    </button>
                );
                setRedPocketTitleStyle({
                    background: "grey",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    // @ts-ignore
                    textFillColor: "transparent",
                    display: "flex",
                    alignItems: "center"
                });
                return;
            }
        }

        // default cases
        switch (currentRedPocket.status) {
            case "active":
            case "used":
            case "claimed":
                setActionBtn(
                    <button className={styles.extraActionButton} onClick={handleClickEvent}>
                        <img src={activateButton} alt="Action" />
                        {buttonText}
                    </button>
                );
                setStatusText(
                    <button disabled className={styles.redPocketActionButton}>
                        <img src={pocketUnavailable} alt="Action" style={{ width: "50%" }} />
                        <div className={styles.redPocketActionButtonText}>已领取</div>
                    </button>
                );
                setRedPocketTitleStyle({ display: "flex", alignItems: "center" });
                break;
            case "expired":
            case "invalid":
            default:
                setActionBtn(
                    <button
                        className={styles.extraActionButton}
                        onClick={() => {
                            routeToPage();
                        }}
                    >
                        <img src={activateButton} alt="Action" />
                        <div className={styles.extraActionButtonText}>
                            立即激活 {`${formatNumberToFraction(creditApplication?.pre_wml ?? 0)}`}
                        </div>
                    </button>
                );
                setStatusText(
                    <button disabled className={styles.redPocketActionButton}>
                        <img src={pocketUnavailable} alt="Action" style={{ width: "50%" }} />
                        <div className={styles.redPocketActionButtonText}>已失效</div>
                    </button>
                );
                setRedPocketTitleStyle({
                    background: "grey",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    // @ts-ignore
                    textFillColor: "transparent",
                    display: "flex",
                    alignItems: "center"
                });
                break;
        }
    }, [redPockets, loading, remainingRedPocket, creditApplication, targetRedPocket]);

    const [showRules, setShowRules] = useState(false);
    const toggleRules = () => setShowRules(!showRules);
    const [twoButtonModal, setTwoButtonModal] = React.useState<boolean>(false);
    const [wechatModal, setWechatModal] = React.useState<boolean>(false);

    return (
        <div>
            <AntModal
                open={open}
                onCancel={handleClose}
                footer={null}
                className={`rounded-[24px] [&_.ant-modal-content]:!p-0 [&_.ant-modal-content]:!rounded-[24px] [&_.ant-modal-content]:!bg-transparent [&_.ant-modal-content]:!shadow-none [&_.ant-modal-content]:!border-none ${
                    !closeText
                        ? "[&_.ant-modal-close]:!w-8 [&_.ant-modal-close]:!h-8 [&_.ant-modal-close]:!bg-white/40 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:!rounded-full"
                        : "[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!shadow-none [&_.ant-modal-close]:!border-none hover:[&_.ant-modal-close]:!bg-[transparent] active:[&_.ant-modal-close]:!bg-[transparent] [&_.ant-modal-close]:!px-3 [&_.ant-modal-close]:!py-1 [&_.ant-modal-close]:!top-4 [&_.ant-modal-close]:!right-4 [&_.ant-modal-close]:before:!content-[''] [&_.ant-modal-close]:after:!content-[''] [&_.ant-modal-close-x]:!bg-[transparent] [&_.ant-modal-close]:mr-2 [&_.ant-modal-close]:!w-auto"
                }`}
                centered
                width={"300px"}
                style={{
                    ...style,
                    backgroundColor: "transparent",
                    boxShadow: "none",
                    border: "none"
                }}
                height={height}
            >
                <div style={{ padding: "8%" }} />
                <div className="h-full min-h-[500px] rounded-lg bg-transparent">
                    <div style={{ width: "100%" }}>
                        <img src={redPocketBody} alt="Red Pocket" style={{ width: "300px", display: "block" }} />
                        <div className={styles.redPocketTitle} style={redPocketTitleStyle}>
                            <span style={{ fontSize: "60px" }}>500</span>
                            <span style={{ fontSize: "24px" }}>美元</span>
                        </div>
                        {statusText}
                        <div className={styles.descText} style={{ width: "68%" }} />
                        <div
                            style={{
                                position: "absolute",
                                top: "43%",
                                width: "80%",
                                left: "10%",
                                textAlign: "center",
                                fontSize: "12px",
                                color: "#FFFFFF",
                                opacity: 0.6
                            }}
                        >
                            数量有限，先用先得，用完即止。
                            <br />
                            目前剩余: {remainingRedPocket}个
                        </div>
                        {actionBtn}
                        <button
                            style={{
                                display: "flex",
                                alignItems: "center",
                                background: "none",
                                border: "none",
                                padding: 0,
                                cursor: "pointer",
                                position: "absolute",
                                top: "63%",
                                left: "35%",
                                opacity: showRules ? 1 : 0.6,
                                transition: "opacity 150ms ease-in-out"
                            }}
                            onClick={toggleRules}
                        >
                            <img
                                src={alertIcon}
                                alt="alert"
                                style={{
                                    filter: "brightness(0) invert(1)",
                                    opacity: showRules ? 1 : 0.6
                                }}
                            />
                            <span
                                style={{
                                    fontSize: "12px",
                                    marginLeft: 4,
                                    color: showRules ? "#FFFFFF" : "rgba(255,255,255,0.6)",
                                    transition: "color 150ms ease-in-out"
                                }}
                            >
                                使用规则
                            </span>
                        </button>

                        {showRules && (
                            <div
                                style={{
                                    backgroundColor: "#000",
                                    color: "white",
                                    borderRadius: "24px",
                                    marginTop: "5%",
                                    padding: "10px",
                                    paddingLeft: "16px"
                                }}
                            >
                                <span
                                    style={{
                                        display: "block",
                                        paddingLeft: "1em",
                                        textIndent: "-1em",
                                        marginBottom: "0.5em"
                                    }}
                                >
                                    ・红包使用方式：首次用款时领取启用，还款时自动抵扣首笔利息，最高500美元
                                </span>
                                <span
                                    style={{
                                        display: "block",
                                        paddingLeft: "1em",
                                        textIndent: "-1em"
                                    }}
                                >
                                    ・优惠受条款及细则约束，丰泊国际对此有绝对决定权，可随时更改或中止而毋需通知
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </AntModal>
            <TwoButtonModal
                open={twoButtonModal}
                onClose={() => {
                    setTwoButtonModal(false);
                }}
                onComplete={() => {
                    setTwoButtonModal(false);
                    setWechatModal(true);
                }}
                content={
                    <>
                        尊敬的客户，您的专属客户经理将在30分钟内联系您，请注意接听来电。
                        <br />
                        <br />
                        其他问题请点击【联系客服】
                    </>
                }
            />
            <OurWechatModal
                open={wechatModal}
                onClose={() => {
                    setWechatModal(false);
                }}
                hasAlertIcon
                textAlign={"center"}
            />
        </div>
    );
};

export default RedPocketModal500;
