import { THEME_CONSTANTS } from "@/components/common";
import RedirectButton from "@/components/common/RedirectButton";
import { useTranslation } from "react-i18next";

const CreateAccount = () => {
    const { t } = useTranslation();

    return (
        <div className="text-left">
            <span style={{ color: THEME_CONSTANTS.PRIMARY_TEXT_COLOR }}>{t("login.noAccountYet")} </span>
            <RedirectButton to="/signup" text={t("login.createNow")} />
        </div>
    );
};

export default CreateAccount;
