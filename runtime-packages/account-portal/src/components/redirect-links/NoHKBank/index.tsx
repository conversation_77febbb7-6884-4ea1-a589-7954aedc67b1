import { useState } from "react";
import TextButton from "@/components/common/TextButton";
import { OurWechatModal } from "@/components/shared/OurWechatModal";
import { EmailNotifyType, sendEmailNotificationsByType } from "@fundpark/fp-api";

const NoHKBank = () => {
    const [showModal, setShowModal] = useState(false);

    const handleOnClick = async () => {
        setShowModal(true);
        await sendEmailNotificationsByType(EmailNotifyType.NO_HONG_KONG_BANK_ACCOUNT)();
    };

    const handleCloseModal = () => {
        setShowModal(false);
    };

    const weChatMessage = "亲爱的用户，当前仅支持中国香港银行账户或空中云汇、连连支付、寻汇的同名虚拟香港银行账户收款。如您尚未开通以上方式，烦请添加以下微信咨询办理，离用款只差一步了哦～"
    const noHKBankText = "没有中国香港银行账户？"

    return (
        <div className="text-left">
            <TextButton onClick={handleOnClick} text={noHKBankText} hasUnderline />
            <OurWechatModal 
                message={weChatMessage}
                open={showModal} 
                onClose={handleCloseModal} 
            />
        </div>
    );
};

export default NoHKBank;
