import { THEME_CONSTANTS } from "@/components/common";
import RedirectButton from "@/components/common/RedirectButton";
import { useTranslation } from "react-i18next";

const AlreadyHaveAccount = () => {
    const { t } = useTranslation();

    return (
        <div className="text-left">
            <span style={{ color: THEME_CONSTANTS.PRIMARY_TEXT_COLOR }}>{t("alreadyHaveAccount.alreadyHaveAccount")}</span>
            <RedirectButton to="/login" text={t("alreadyHaveAccount.signIn")} />
        </div>
    );
};

export default AlreadyHaveAccount;
