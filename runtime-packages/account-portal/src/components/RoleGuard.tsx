import { Navigate } from "react-router-dom";
import { useCommonStore } from "@/store/common";

interface RoleGuardProps {
    children: React.ReactNode;
    requiredRole: string;
}

const RoleGuard: React.FC<RoleGuardProps> = ({ children, requiredRole }) => {
    const { userRole } = useCommonStore();

    if (userRole !== requiredRole || !userRole) {
        return <Navigate to="/myProfile" replace />;
    }

    return <>{children}</>;
};

export default RoleGuard; 