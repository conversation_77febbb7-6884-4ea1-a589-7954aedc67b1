import { BusinessInfoRequest, CompanyInfoRequest } from "@fundpark/fp-api";

/**
 * Maps UI form data to API request format for business information
 */
export const mapBusinessInfoFormToApi = (formData: any): BusinessInfoRequest => {
  return {
    top_three_buyer_country: formData.topBuyerCountries?.map(Number) || [],
    top_three_suppliers_country: formData.topSupplierCountries?.map(Number) || [],
    funding_sources_country: Number(formData.fundSourceCountries) || 0,
    initial_wealth_source: formData.initialWealthSources?.map(Number) || [],
    continuous_wealth_income_source: formData.ongoingIncomeSources?.map(Number) || [],
    funding_sources: formData.fundingSources?.map(Number) || [],
    major_commodities: formData.mainProducts || "",
    industry_service: Number(formData.industry) || undefined
  };
};

/**
 * Maps API response data to UI form format for business information
 */
export const mapBusinessInfoApiToUi = (apiData: any): any => {
  const data = apiData.data || apiData;
  
  return {
    topBuyerCountries: data.top_three_buyer_country?.map(String) || ["229"],
    topSupplierCountries: data.top_three_suppliers_country?.map(String) || ["2"],
    fundSourceCountries: data.funding_sources_country || "2",
    initialWealthSources: data.initial_wealth_source || ["5"],
    ongoingIncomeSources: data.continuous_wealth_income_source || ["1"],
    fundingSources: data.funding_sources || ["1"],
    mainProducts: data.major_commodities || "",
    industry: data.industry_service?.toString() || undefined
  };
}; 

/**
 * Maps UI form data to API request format for company information
 */
export const mapCompanyInfoFormToApi = (formData: any): CompanyInfoRequest => {
  return {
    country: Number(formData.countryRegion),
    company_name_english: formData.countryRegion === "1"? String(formData.companyNameEn) : ""  ,
    company_name_chinese: formData.countryRegion === "2"? String(formData.companyNameCn) : ""  ,
    brn: String(formData.businessRegistrationNumber || '')
  }
};