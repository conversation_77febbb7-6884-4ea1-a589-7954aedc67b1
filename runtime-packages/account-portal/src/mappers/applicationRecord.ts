import { ApplicationRecordItem, ApplicationRecordListData, ApplicationRecordListRes, ApplicationRecordListReq } from '@fundpark/fp-api';
import { ApplicationRecord, ApplicationStatus } from '@/components/application-records/types';

/**
 * Map API application record item to UI application record format
 */
export const mapApplicationRecordApiToUi = (apiRecord: ApplicationRecordItem): ApplicationRecord => {
  // Map status from API to UI status enum
  let status: ApplicationStatus = 'In Progress';
  
  // This mapping will need to be adjusted based on the actual status values from the API
  switch (apiRecord.status) {
    case 'APPROVED':
      status = 'Approved';
      break;
    case 'REJECTED':
      status = 'Rejected';
      break;
    case 'WITHDRAW':
      status = 'Withdraw';
      break;
    default:
      status = 'In Progress';
  }
  
  return {
    id: apiRecord.sourceId, // Using sourceId as the unique identifier
    approvalId: apiRecord.approvalId,
    approvalName: apiRecord.approvalName,
    productName: apiRecord.productNameEn || apiRecord.productNameCn, // Use English name if available, otherwise Chinese
    status,
    createdTime: apiRecord.createTime
  };
};

/**
 * Map API response data to UI data format
 */
export const mapApplicationRecordListDataToUi = (apiData: ApplicationRecordListData) => {
  return {
    list: apiData.records.map(mapApplicationRecordApiToUi),
    total: apiData.total
  };
};

/**
 * Map API response to UI data format
 * This handles the HTTP response returned by the API
 */
export const mapApplicationRecordListResToUi = (apiResponse: any) => {
  if (apiResponse && apiResponse.data && apiResponse.data.records) {
    return mapApplicationRecordListDataToUi(apiResponse.data);
  }
  return { list: [], total: 0 };
};

/**
 * Map UI request parameters to API request parameters
 */
export const mapApplicationRecordParamsUiToApi = (uiParams: any): ApplicationRecordListReq => {
  return {
    pageSize: uiParams.pageSize || 20,
    pageNum: uiParams.pageNum || 1,
    approvalName: uiParams.approvalName,
    productName: uiParams.productName,
    createStartTime: uiParams.createStartTime,
    createEndTime: uiParams.createEndTime,
    companyId: uiParams.companyId,
    orderByColumn: uiParams.orderByColumn,
    isAsc: uiParams.isAsc
  };
}; 