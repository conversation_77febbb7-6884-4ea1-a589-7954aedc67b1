import { DrawdownAccountFormData } from "@/components/DrawdownForm/secondDrawdown/types";

export const mapBankAccountApiToUi = (apiResponse: any): DrawdownAccountFormData => {
    // Extract the data from response
    let data: any;
    console.log("start")

    // If the response contains data property (GetDebitAccountResponse structure)
    if (apiResponse && typeof apiResponse === "object") {
        console.log("Checking response structure:", {
            hasDataProperty: "data" in apiResponse,
            dataType: typeof apiResponse.data
        });

        if ("data" in apiResponse && apiResponse.data) {
            data = apiResponse.data;
        } else {
            data = apiResponse; // Use the response itself if it doesn't have a data property
        }
    } else {
        console.error("Invalid API response format:", apiResponse);
        data = {}; // Default to empty object if the response is invalid
    }

    console.log("Extracted data for mapping:", data);

    // Create UI data object with default values for all fields
    const uiData: DrawdownAccountFormData = {
        bankAccountID: data.id,
        drawdownCurrency: "USD",
        drawdownAmount: data.disburse_amount ? Number(data.disburse_amount).toFixed(2) : "",
        bankAccountNumber: data.bank_account_number || "",
        bankAccountName:data.bank_account_name,
        refNumber: ""
    };

    console.log("Final UI data:", uiData);
    return uiData;
};