import { FacilityDetailResponseData } from '@fundpark/fp-api';

/**
 * Maps API facility detail response to UI facility data format
 * @param apiResponse The API response from getFacilityDetail
 * @returns Formatted facility data for UI components
 */
export const mapFacilityDetailApiToUi = (apiResponse: any) => {
  // Extract the facility detail data from the response
  const facilityDetail = apiResponse.data;
  console.log('facilityDetailfacilityDetail', facilityDetail);
  
  if (!facilityDetail) {
    return {
      facilityLimit: 0,
      watermark: 0,
      outstanding: 0,
      pendingDrawdown: 0,
      frozenLimit: 0,
      availableFunding: 0,
      productCode: '',
      productType: '',
      facilityId: '',
      effectiveDate: '',
      nextReviewDate: '',
      currency: '',
      status: ''
    };
  }
  
  return {
    facilityLimit: parseFloat(facilityDetail.facilityLimitAmount),
    watermark: parseFloat(facilityDetail.watermarkAmount),
    outstanding: parseFloat(facilityDetail.currentOutstandingAmount),
    pendingDrawdown: parseFloat(facilityDetail.pendingDrawdownAmount),
    frozenLimit: parseFloat(facilityDetail.facilityFrozenLimitAmount),
    availableFunding: parseFloat(facilityDetail.facilityAvailableFundingAmount),
    productCode: facilityDetail.productCode,
    productType: facilityDetail.productType,
    facilityId: facilityDetail.creditFacilityId,
    effectiveDate: facilityDetail.effectiveDate,
    nextReviewDate: facilityDetail.nextReviewDate,
    currency: facilityDetail.facilityCurrency,
    status: facilityDetail.facilityStatus
  };
};

/**
 * Maps base rate type codes to display names
 * @param baseRateType The base rate type code from API
 * @returns The formatted display name for UI
 */
const mapBaseRateType = (baseRateType: string): string => {
  if (!baseRateType || baseRateType === '-') return '-';
  
  switch (baseRateType) {
    case 'CNHHIBOR':
      return 'CNH HIBOR';
    default:
      return baseRateType;
  }
};

/**
 * Maps API facility detail response to UI loan details format
 * @param apiResponse The API response from getFacilityDetail
 * @returns Array of formatted loan data for UI components
 */
export const mapFacilityLoanDetailsApiToUi = (apiResponse: any) => {
  // Extract the facility detail data from the response
  const facilityDetail = apiResponse.data;
  
  if (!facilityDetail || !facilityDetail.repaymentMethodList || facilityDetail.repaymentMethodList.length === 0) {
    return []; // Return empty array if no repayment methods
  }
  
  // Map each repayment method to loan details
  return facilityDetail.repaymentMethodList.map((repaymentMethod: any, index: number) => {
    // Map fee details to interest rate info
    const interestRateInfo = repaymentMethod.feeDetailList.map((fee: any) => ({
      interestRateType: fee.rateType === 'fixed' ? 'Fixed' : 
                         fee.rateType === 'floating' ? 'Floating' : 
                         fee.rateType === 'drawdownFloating' ? 'Drawdown Floating' : fee.rateType,
      currency: fee.currency,
      benchmark: mapBaseRateType(fee.baseRateType),
      interestRate: fee.rateType === 'fixed' ? `${fee.interestRate}%` : 
                    (fee.rateType === 'floating' || fee.rateType === 'drawdownFloating') ? 
                    `${mapBaseRateType(fee.baseRateType)} + ${fee.interestRate}%` : `${fee.interestRate}%`,
      accrualBasis: fee.accrualBasis || '-'
    }));
    
    return {
      tabKey: `${repaymentMethod.loanType.toLowerCase()}-${index}`,
      tabLabel: `${repaymentMethod.loanType}`,
      loanStructureId: repaymentMethod.repaymentTemplateId,
      loanType: repaymentMethod.loanType,
      maximumOutstanding: repaymentMethod.maxOutstanding ? parseFloat(repaymentMethod.maxOutstanding) : 0,
      maximumTenor: repaymentMethod.maxTenorValue ? `${repaymentMethod.maxTenorValue} ${repaymentMethod.maxTenorUnit}` : '',
      minimumInterestTenor: repaymentMethod.minTenorValue ? `${repaymentMethod.minTenorValue} ${repaymentMethod.minTenorUnit}` : '',
      gracePeriod: `${repaymentMethod.gracePeriodValue} ${repaymentMethod.gracePeriodUnit}`,
      repaymentMethod: repaymentMethod.repaymentMethod,
      repaymentAllocationRule: repaymentMethod.repaymentSequence,
      minimumRepaymentAmount: repaymentMethod.minRepaymentAmount,
      overdueInterestEnabled: repaymentMethod.allowOverdue === 'Y',
      overdueInterestMultiplier: repaymentMethod.overdueInterest,
      interestPaymentTiming: repaymentMethod.repaymentTiming,
      interestRateInfo
    };
  });
};

/**
 * Creates a request payload for the facility detail API
 * @param facilityId The ID of the facility to retrieve
 * @returns Request payload for getFacilityDetail API
 */
export const createFacilityDetailRequest = (facilityId: string) => {
  return {
    id: facilityId
  };
}; 