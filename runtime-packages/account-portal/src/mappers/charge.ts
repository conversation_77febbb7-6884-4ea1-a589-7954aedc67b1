import { Charge, SubCharge } from '@fundpark/fp-api/types/charge.js';
import { ChargeOrder, SubOrder, ChargeOrderStatus } from '../components/charge-order/types';

/**
 * Maps API charge data to UI ChargeOrder format
 */
export const mapChargeApiToUi = (apiCharge: Charge): ChargeOrder => {
  // If API provides orderStatus directly, use it, otherwise derive from amounts
  let status: ChargeOrderStatus = apiCharge.orderStatus as ChargeOrderStatus;

  return {
    id: apiCharge.id,
    chargeOrderId: apiCharge.orderCode,
    facilityId: apiCharge.companyCreditFacilityLimitId,
    type: apiCharge.feeType,
    feeRate: apiCharge.feeRate,
    status: status,
    collectionMethod: apiCharge.collectionMethod,
    totalOrderAmount: parseFloat(apiCharge.totalFeeAmount),
    outstandingAmount: parseFloat(apiCharge.outstandingAmount),
    pendingAmount: parseFloat(apiCharge.pendingAmount),
    receivedAmount: parseFloat(apiCharge.receivedAmount),
    createdTime: apiCharge.systemCreatedDate,
    subOrderCount: apiCharge.subChargeList?.length || 0
  };
};

/**
 * Maps API sub-charge data to UI SubOrder format
 */
export const mapSubChargeApiToUi = (apiSubCharge: SubCharge): SubOrder => {

  return {
    id: apiSubCharge.id,
    subOrderId: apiSubCharge.subOrderCode,
    drawdownId: apiSubCharge.sourceOrderId,
    collectionMethod: apiSubCharge.collectionMethod,
    createdTime: apiSubCharge.systemCreatedDate,
    totalOrderAmount: parseFloat(apiSubCharge.outstandingAmount) + parseFloat(apiSubCharge.receivedAmount) + parseFloat(apiSubCharge.pendingAmount),
    outstandingAmount: parseFloat(apiSubCharge.outstandingAmount),
    receivedAmount: parseFloat(apiSubCharge.receivedAmount),
    pendingAmount: parseFloat(apiSubCharge.pendingAmount),
    status: apiSubCharge.orderStatus as ChargeOrderStatus
  };
}; 