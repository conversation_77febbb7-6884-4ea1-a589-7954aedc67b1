import { DebitAccountRequest, GetDebitAccountResponse, GetSignerInfoData } from "@fundpark/fp-api";
import { DrawdownAccountFormData } from "../components/DrawdownForm/DrawdownAccount/types";
import { AgreementDetailStatus, Signing<PERSON>erson, SigningStatus } from "../components/DrawdownForm/Signing/types";

// Map UI form data to API request format for submitting debit account
export const mapDebitAccountFormToApi = (formData: any): DebitAccountRequest => {
    // Get the actual bank name instead of just the ID
    const bankValue = formData.bank;
    // ConfigDataSelect returns the key_id as a string
    const bankId = parseInt(bankValue);
    
    return {
        country: parseInt(formData.countryRegion) || 0,
        company_name_english: formData.companyNameEn || "",
        company_name_chinese: formData.companyNameCn || "",
        brn: formData.businessRegistrationNumber?.toString() || "",
        currency: formData.drawdownCurrency || "USD",
        disburse_amount:
            formData.drawdownAmount
                ? typeof formData.drawdownAmount === "string"
                    ? parseFloat(formData.drawdownAmount) || null
                    : formData.drawdownAmount
                : null,
        bank_account_name: formData.bankAccountName || "",
        bank_account_number: formData.bankAccountNumber || "",
        bank_name: bankValue || "", // Use the bank value directly as it might be a name or ID
        swift: formData.swiftCode || "",
        bank_address: formData.bankAddress || ""
    };
};

// Map API response data to UI form format for displaying debit account info
export const mapDebitAccountApiToUi = (apiResponse: GetDebitAccountResponse): DrawdownAccountFormData => {
    // Extract the data from response
    let data: any;

    // If the response contains data property (GetDebitAccountResponse structure)
    if (apiResponse && typeof apiResponse === "object") {

        if ("data" in apiResponse && apiResponse.data) {
            data = apiResponse.data;
        } else {
            data = apiResponse; // Use the response itself if it doesn't have a data property
        }
    } else {
        console.error("Invalid API response format:", apiResponse);
        data = {}; // Default to empty object if the response is invalid
    }

    console.log("Extracted data for mapping:", data);

    // Create UI data object with default values for all fields
    const uiData: DrawdownAccountFormData = {
        countryRegion: data.country ? data.country.toString() : null,
        companyNameEn: data.company_name_english || "",
        companyNameCn: data.company_name_chinese || "",
        businessRegistrationNumber: data.brn || "",
        drawdownCurrency: data.currency || "USD",
        drawdownAmount: data.disburse_amount ? Number(data.disburse_amount).toFixed(2) : "",
        bankAccountName: data.bank_account_name || "",
        bankAccountNumber: data.bank_account_number || "",
        // The API might return either a bank ID or name, so we need to handle both cases
        // ConfigDataSelect expects a string key_id
        bank: data.bank_name || null,
        swiftCode: data.swift || "",
        bankAddress: data.bank_address || ""
    };

    console.log("Final UI data:", uiData);
    return uiData;
};

// Map API signer info response to UI format for SigningForm component
export const mapSignerInfoApiToUi = (response: any): SigningPerson[] => {
    if (!response || !response.data) {
        return [];
    }

    // Handle the case when the response is wrapped in an IHttpResponse
    const signerData = Array.isArray(response.data) ? response.data : 
                      (response.data.data && Array.isArray(response.data.data)) ? response.data.data : 
                      [];

    return signerData.map((signer: GetSignerInfoData) => {
        // Map the sign_status from API to UI status
        let status: SigningStatus;
        let agreementDetailStatus: AgreementDetailStatus;
        let agreementFailedReason: string | null;

        agreementFailedReason = signer.company_agreement_failed_reason;
        
        switch (signer.sign_status) {
            case 0:
                status = 'pending'; // Not signed yet
                break;
            case 1:
                status = 'signing'; // In signing process
                break;
            case 2:
                status = 'signed'; // Successfully signed
                break;
            case 3:
                status = 'rejected'; // Rejected or Withdrawn
                break;
            case 4:
                status = 'expired';
                break;
            case 5:
                status = 'withdrawn';
                break;
            case 6:
                status = 'failed';
                break;
            default:
                status = 'pending';
        }

        switch (signer.company_agreement_detail_status) {
            case 0:
                agreementDetailStatus = 'draft';
                break;
            case 1:
                agreementDetailStatus = 'signing';
                break;
            case 2:
                agreementDetailStatus = 'expired';
                break;
            case 3:
                agreementDetailStatus = 'withdraw';
                break;
            case 4:
                agreementDetailStatus = 'reject';
                break;
            case 5:
                agreementDetailStatus = 'finished';
                break;
            case 6:
                agreementDetailStatus = 'failed';
                break;
            default:
                agreementDetailStatus = 'draft';
        }

        return {
            id: signer.id.toString(),
            englishName: signer.full_name_eng || '',
            chineseName: signer.full_name_chn || '',
            role: signer.position ? signer.position.length > 1 ? '董事' : signer.position[0] : '',
            status,
            personId: `FP${Math.floor(10000000 + Math.random() * 90000000)}`, // Generate a random ID for now
            nationality: signer.region_code, // Map region_code to nationality
            email: signer.email,
            mobilePhoneAreaCode: signer.phone_region_code,
            mobilePhoneNumber: signer.phone_number,
            papersType: signer.papers_type,
            frontFileId: signer.front_id_file_id,
            backFileId: signer.back_id_file_id,
            signRecords: signer.sign_record ? [signer.sign_record] : [],
            frontFileName: signer.front_id_file_name,
            backFileName: signer.back_id_file_name,
            needSign: signer.needSign,
            agreementDetailStatus: agreementDetailStatus,
            agreementFailedReason: agreementFailedReason,
            address: signer.address,

            frontPreviewLink: signer.frontIDPreviewLink,
            backPreviewLink: signer.backIDPreviewLink,

            extraFrontFileLink: signer.frontPreviewLink,
            extraBackFileLink: signer.backPreviewLink,
            extraFrontFileId: signer.front_file_id,
            extraBackFileId: signer.back_file_id
        };
    });
};

// frontPreviewLink : big6 front
// backPreviewLink : big6 back

// frontIDPreviewLink : hk front
// backIDPreviewLink : hk back