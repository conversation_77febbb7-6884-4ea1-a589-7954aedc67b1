import {lazy} from "react";
import type {RouteObject} from "react-router-dom";
import {Navigate} from "react-router-dom";
import RouteBoundary from "@/components/RouteBoundary";
// import XDJLayout from "@/layouts/XDJLayout";
import UnauthLayout from "@/layouts/UnauthLayout";
import Error from "@/pages/Error";
import NotAuth from "@/pages/NotAuth";
import NotFound from "@/pages/NotFound";
import {CreditApprovalAutomationLayoutGuard, RegisterGuard, UnauthGuard, XDJGuard} from "@/utils/auth.tsx";
import {withStatusProtection} from "@/components/shared/StatusProtectedRoute/withStatusProtection";
import {POSITION_STATUS, type PositionStatus} from "@/constants/position-status";
import PreventBackNavigation from "@/components/shared/PreventBackNavigation";

import XDJLayout from "./layouts/XDJLayout";
import CreditApprovalAutomationLayout from "@/layouts/CreditApprovalAutomationLayout";

// Helper function to create a protected lazy component
const protectedLazy = (
    importFn: () => Promise<any>,
    statusProtection?: {
        requiredStatus: PositionStatus | PositionStatus[];
        fallbackPath?: string;
    }
) => {
    const LazyComponent = lazy(importFn);
    return statusProtection ? withStatusProtection(LazyComponent, statusProtection) : LazyComponent;
};

// Helper function to wrap component with PreventBackNavigation for register pages

const withPreventBack = (Component: React.ComponentType) => {
    return (props: any) => (
        <PreventBackNavigation>
            <Component {...props} />
        </PreventBackNavigation>
    );
};

// Helper function to create a protected register component
const protectedRegisterComponent = (
    importFn: () => Promise<any>,
    statusProtection?: {
        requiredStatus: PositionStatus | PositionStatus[];
        fallbackPath?: string;
    }
) => {
    const ProtectedComponent = protectedLazy(importFn, statusProtection);
    return withPreventBack(ProtectedComponent);
};

const routes: RouteObject[] = [

    {
        path: "/",
        element: <UnauthGuard/>,
        errorElement: <RouteBoundary/>,
        children: [
            {
                element: <UnauthLayout/>,
                children: [
                    {index: true, Component: lazy(() => import("@/pages/home/<USER>"))},
                ],
            }
        ],
    },
    {
        path: "/credit",
        element: <Navigate to="/credit/hook" replace/>,
    },
    {
        path: "/credit/hook",
        element: <XDJLayout/>,
        errorElement: <RouteBoundary/>,
        children: [
            {
                element: <UnauthGuard/>,
                children: [
                    {
                        index: true,
                        Component: lazy(() => import("@/pages/sdg/index")),
                    },
                ],
            },
            {
                children: [
                    {
                        element: <XDJGuard/>,
                        children: [
                            {
                                path: "home",
                                Component: lazy(() => import("@/pages/sdg/home/<USER>")),
                            },
                            {
                                element: <RegisterGuard/>,
                                children: [
                                    {
                                        path: "register/drawdown-account",
                                        Component: protectedRegisterComponent(
                                            () => import("@/pages/sdg/register/drawdownAccount/index"),
                                        ),
                                    },
                                    {
                                        path: "register/business-info",
                                        Component: protectedRegisterComponent(
                                            () => import("@/pages/sdg/register/businessInfo/index"),
                                            {
                                                requiredStatus: [POSITION_STATUS.XDJ_DRAWDOWN_ACCOUNT_SUBMITTED]
                                            }
                                        ),
                                    },
                                    {
                                        path: "register/signing",
                                        children: [
                                            {
                                                index: true,
                                                Component: protectedRegisterComponent(
                                                    () => import("@/pages/sdg/register/signing/index"),
                                                    {
                                                        requiredStatus: [POSITION_STATUS.XDJ_BUSINESS_INFO_SUBMITTED, POSITION_STATUS.XDJ_SIGNING_INITIATED]
                                                    }
                                                )
                                            },
                                            {
                                                path: "loading",
                                                Component: protectedRegisterComponent(
                                                    () => import("@/pages/sdg/register/signing/loading/index"),
                                                    {
                                                        requiredStatus: [POSITION_STATUS.XDJ_BUSINESS_INFO_SUBMITTED, POSITION_STATUS.XDJ_SIGNING_INITIATED]
                                                    }
                                                )
                                            },
                                        ]
                                    },
                                ]
                            },
                            {
                                path: "second-drawdown",
                                Component: lazy(() => import("@/pages/secondDrawdown/index")),
                            },
                            {
                                path: "terms-and-condition",
                                Component: lazy(() => import("@/pages/termsAndCondition/index")),
                            },
                            {
                                path: "activate-shop",
                                Component: lazy(() => import("@/pages/activateShop/index"))
                            },
                            {
                                path: "activate-shop/signing",
                                children: [
                                    {
                                        index: true,
                                        Component: lazy(() => import("@/pages/activateShop/signing/index"))
                                    },
                                    {
                                        path: "loading",
                                        Component: lazy(() => import("@/pages/activateShop/signing/loading/index"))
                                    },
                                ]
                            },
                        ],
                    },
                ]
            }
        ],
    },
    {
        path: "/credit/underwriting",
        element: <CreditApprovalAutomationLayout/>,
        errorElement: <RouteBoundary/>,
        children: [
            {
                element: <UnauthGuard/>,
                children: [
                    {
                        index: true,
                        Component: lazy(() => import("@/pages/creditApprovalAutomation/index")),
                    },
                ],
            },
            {
                element: <CreditApprovalAutomationLayoutGuard/>,
                children: [
                    {
                        path: "home",
                        Component: lazy(() => import("@/pages/creditApprovalAutomation/home/<USER>")),
                    },
                    {
                        path: "register/drawdown-account",
                        Component: protectedRegisterComponent(
                            () => import("@/pages/creditApprovalAutomation/register/drawdownAccount/index"),
                        ),
                    },
                    {
                        path: "register/business-info",
                        Component: protectedRegisterComponent(
                            () => import("@/pages/creditApprovalAutomation/register/businessInfo/index"),
                            {
                                requiredStatus: [POSITION_STATUS.CAA_DRAWDOWN_ACCOUNT_SUBMITTED]
                            }
                        ),
                    },
                    {
                        path: "register/signing",
                        children: [
                            {
                                index: true,
                                Component: protectedRegisterComponent(
                                    () => import("@/pages/creditApprovalAutomation/register/signing/index"),
                                    {
                                        requiredStatus: [POSITION_STATUS.CAA_BUSINESS_INFO_SUBMITTED, POSITION_STATUS.CAA_SIGNING_INITIATED]
                                    }
                                ),
                            },
                            {
                                path: "loading",
                                Component: protectedRegisterComponent(
                                    () => import("@/pages/creditApprovalAutomation/register/signing/loading/index"),
                                    {
                                        requiredStatus: [POSITION_STATUS.CAA_BUSINESS_INFO_SUBMITTED, POSITION_STATUS.CAA_SIGNING_INITIATED]
                                    }
                                ),
                            },
                        ]
                    },
                    {
                        path: "second-drawdown",
                        Component: lazy(() => import("@/pages/secondDrawdown/index")),
                    },
                    {
                        path: "terms-and-condition",
                        Component: lazy(() => import("@/pages/termsAndCondition/index")),
                    },
                    {
                        path: "increase-limit/authorize",
                        Component: lazy(() => import("@/pages/caaAuthLimit/index")),
                    },
                    {
                        path: "activate-shop",
                        Component: lazy(() => import("@/pages/caaActivateShop/index"))
                    },
                    {
                        path: "activate-shop/signing",
                        children: [
                            {
                                index: true,
                                Component: lazy(() => import("@/pages/caaActivateShop/signing/index"))
                            },
                            {
                                path: "loading",
                                Component: lazy(() => import("@/pages/caaActivateShop/signing/loading/index"))
                            },
                        ]
                    },
                ],
            },
        ],
    },
    // {
    //     path: "/terms",
    //     element: <NoLoginLayout/>,
    //     errorElement: <RouteBoundary/>,
    //     children: [
    //         {
    //             index: true,
    //             Component: lazy(() => import("@/pages/terms/index"))
    //         }
    //     ]
    // },
    {
        path: "questions",
        children: [
            {
                index: true,
                Component: lazy(() => import("@/pages/questions/faq/index"))
            }
        ]
    },
    {
        path: "free-reports",
        children: [
            {
                index: true,
                Component: lazy(() => import("@/pages/reports/freeReports/index"))
            }
        ]
    },
    // {
    //     path: "messages",
    //     children: [
    //         {
    //             index: true,
    //             Component: lazy(() => import("@/pages/notifications/messages/index"))
    //         },
    //         {
    //             path: "detail",
    //             Component: lazy(() => import("@/pages/notifications/detail"))
    //         }
    //     ]
    // },
    {
        path: "/notAuth",
        element: <NotAuth/>
    },
    {
        path: "/error",
        element: <Error/>
    },
    {
        path: "*",
        element: <NotFound/>
    }
];

export default routes;
