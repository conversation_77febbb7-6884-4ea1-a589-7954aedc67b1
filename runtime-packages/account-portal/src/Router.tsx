import { Suspense } from "react";
import { Router<PERSON>rovider, createBrowserRouter, Outlet } from "react-router-dom";
import routes from "@/routes.tsx";
import { PROJECT_BASE_PATH } from "@/constants/routing";
import ExitTracker from "@/components/ExitTracker";

// Create a wrapper component that includes ExitTracker
const AppWithExitTracking = () => {
    return (
        <>
            <ExitTracker />
            <Outlet />
        </>
    );
};

// Add the wrapper to the routes
const routesWithExitTracking = [
    {
        path: "/",
        element: <AppWithExitTracking />,
        children: routes
    }
];

const router = createBrowserRouter(routesWithExitTracking, {
    basename: PROJECT_BASE_PATH,
    future: {
        v7_relativeSplatPath: true
    }
});

export default function Router() {
    return (
        <Suspense fallback={null}>
            <RouterProvider 
                router={router} 
                future={{ v7_startTransition: true }}
            />
        </Suspense>
    );
}
