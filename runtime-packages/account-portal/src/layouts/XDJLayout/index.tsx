import MainLayout from "../MainLayout";
import {useMatch, useOutlet} from "react-router-dom";
import hsbcFundParkFirstAd from "@/assets-new/images/hsbcFundParkAd.jpg";
import goldmanFundParkAd from "@/assets-new/images/goldmanFundParkAd.jpg";
import MatomoPageTracker from "@/components/MatomoPageTracker";
import unauthBackground from "@/assets-new/images/unauthBackground.jpg";
import styles from "./index.module.scss";
import AutoplayAdBanner from "@/components/adBanners/AutoplayAdBanner";
import {ConfigProvider} from "@/contexts/ConfigContext";
import { ALL_TEMPLATE_IDS } from "@/constants/templateIds";
import {useSearchParams} from "react-router-dom";

const XDJLayout: React.FC = () => {
    const outlet = useOutlet();
    const isPublicIndex = Boolean(useMatch({path: "/credit/hook", end: true}));
    const [searchParams] = useSearchParams();

    const content = isPublicIndex
        ? outlet
        : (
            <ConfigProvider>
                {outlet}
            </ConfigProvider>
        );

    if (isPublicIndex && ALL_TEMPLATE_IDS.includes(searchParams.get("tpid") || "")) {
        return (
            <>
                <MatomoPageTracker/>
                {content}
            </>
        );
    }

    return (
        <MainLayout>
            <MatomoPageTracker/>
            <AutoplayAdBanner
                ads={[
                    {name: 'HSBC & FundPark 2.5B', image: hsbcFundParkFirstAd},
                    {name: 'Goldman Sachs & FundPark 5B', image: goldmanFundParkAd}
                ]}
                intervalSeconds={5}
            />
            <div
                className={styles.backgroundContainer}
                style={{backgroundImage: `url(${unauthBackground})`}}
            >
                {content}
            </div>
        </MainLayout>
    );
};

export default XDJLayout;
