import { Layout } from "antd";
import Navbar from "@/components/Navbar.tsx";
import NavbarNoLogin from "@/components/NavbarNoLogin.tsx";
import { Footer } from "@/components/Footer";
import FloatingWidget from "@/components/FloatingWidget/SDGFloatingButtons";
import "./styles.scss";
import {useCommonStore} from "@/store/common.ts";
import { useEffect } from "react";
import {getCompanyNotificationsList} from "@fundpark/fp-api";
import appHelper from "@/utils/appHelper";
import { useLocation } from 'react-router-dom';

const { Content } = Layout;

const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { isLogin } = useCommonStore();
    const location = useLocation();

    useEffect(() => {
        if (isLogin) {
            refreshNotifications();
        };
    }, [location.pathname]);

    const refreshNotifications  = async () => {
        try {
            // get notifications
            const noti_res = await getCompanyNotificationsList({per_page:1});
            if (noti_res.message === "success") {
                console.log('called notifications', noti_res.data.data);
                appHelper.setNotifications(noti_res.data.data);
            };
        } catch (error) {
            console.error("Failed to retrieve notifications.", error);
        }
    }

    return (
        <div className="main-layout">
            <Layout className="min-h-screen bg-white">
                {isLogin ? (
                    <Navbar />
                ) : (
                    <NavbarNoLogin />
                )}
                <Content>{children}</Content>
                <FloatingWidget/>
                <Footer />
            </Layout>
        </div>
    );
};

export default MainLayout;