import MainLayout from "../MainLayout";
import { useOutlet } from "react-router-dom";
import hsbcFundParkFirstAd from "@/assets-new/images/hsbcFundParkAd.jpg"
import goldmanFundParkAd from "@/assets-new/images/goldmanFundParkAd.jpg"
import MatomoPageTracker from "@/components/MatomoPageTracker";
import unauthBackground from "@/assets-new/images/unauthBackground.jpg";
import styles from "./index.module.scss";
import AutoplayAdBanner from "@/components/adBanners/AutoplayAdBanner";

const UnauthLayout: React.FC = () => {
    const outlet = useOutlet();

    return (
        <MainLayout>
            <MatomoPageTracker />
            <AutoplayAdBanner 
                ads={[
                    { name: 'HSBC & FundPark 2.5B', image: hsbcFundParkFirstAd },
                    { name: 'Goldman Sachs & FundPark 5B', image: goldmanFundParkAd }
                ]}
                intervalSeconds={5}
            />
            <div 
                className={styles.backgroundContainer}
                style={{ backgroundImage: `url(${unauthBackground})` }}
            >
                {outlet}
            </div>
        </MainLayout>
    );
};

export default UnauthLayout;