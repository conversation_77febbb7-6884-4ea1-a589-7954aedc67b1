import React from "react";
import {Layout} from "antd";
import MatomoPageTracker from "@/components/MatomoPageTracker";
import FloatingWidget from "@/components/FloatingWidget/LandingPageFloatingWidget";
import {Footer} from "@/components/Footer";
import BlueBackground from "@/assets-new/images/landing/blue/bg.png";

const {Content} = Layout;

type FooterColorVariant = "default" | "white";

interface GenericLayoutProps {
    type?: "red" | "blue";
    children: React.ReactNode;
}

const GenericLayout: React.FC<GenericLayoutProps> = ({
                                                         type = "red",
                                                         children
                                                     }) => {
    let footerStyle: FooterColorVariant;
    let backgroundStyle: React.CSSProperties;

    switch (type) {
        case "blue":
            footerStyle = "default";
            backgroundStyle = {
                backgroundImage: `url(${BlueBackground})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
                minHeight: "100vh",
                minWidth: '1240px',
            };
            break;
        case "red":
        default:
            footerStyle = "white";
            backgroundStyle = {
                background: "linear-gradient(180deg, #FFA373 -1.43%, #FF5B51 85.41%, #FF3C39 122.63%)",
                minWidth: '1366px',
            };
            break;
    }

    return (
        <div className="main-layout">
            <Layout style={backgroundStyle}>
                <Content>
                    <MatomoPageTracker/>
                    {children}
                </Content>

                <FloatingWidget type={type}/>
                <Footer type={footerStyle}/>
            </Layout>
        </div>
    );
};

export default GenericLayout;
