import React from "react";
import { useOutlet } from "react-router-dom";

import { Layout } from "antd";
import { Footer } from "@/components/Footer";
import NavbarNoLogin from "@/components/NavbarNoLogin";
import Page from "@/components/common/Page";
import MatomoPageTracker from "@/components/MatomoPageTracker";

const { Content } = Layout;

const NoLoginLayout: React.FC = () => {
    const outlet = useOutlet();

    return (
        <Layout className="h-screen bg-white">
            <MatomoPageTracker />
            <NavbarNoLogin />
            <Content className="flex-1">
                <Page>{outlet}</Page>
            </Content>
            <Footer />
        </Layout>
    );
};

export default NoLoginLayout;
