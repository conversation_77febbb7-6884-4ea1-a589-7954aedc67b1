import { FC, PropsWithChildren } from "react";
import SideBarMenu from "@/components/SideBarMenu";
import { getNotificationsSideBarItems } from "@/components/SideBarMenu/constants";

const NotificationsLayout: FC<PropsWithChildren> = ({ children }) => {
    return (
        <div className="flex items-start h-full bg-white">
            <SideBarMenu items={getNotificationsSideBarItems()} />
            <div className="w-0 flex-1 mb-10">
                {children}
            </div>
        </div>
    );
};

export default NotificationsLayout;
