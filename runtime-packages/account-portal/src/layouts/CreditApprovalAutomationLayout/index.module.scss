@use "@/assets/styles/variables.module.scss" as *;

.unAuthContainer {
    :global {
        .ant-form-item-label > label {
            font-weight: 500;
        }

        .ant-input, .ant-input-password {
            border-radius: 4px;
        }

        .ant-btn {
            border-radius: 4px;
            height: 40px;
        }

        .ant-carousel {
            height: 100%;
            border-radius: 16px;
            overflow: hidden;

            .slick-slider, .slick-list, .slick-track {
                height: 100%;
                border-radius: 16px;
            }

            .slick-slide {
                > div {
                    height: 100%;
                }
            }

            .slick-dots {
                bottom: 40px;
                
                li {
                    margin: 0 4px;
                    
                    button {
                        width: 24px;
                        height: 4px;
                        border-radius: 2px;
                        background: rgba(255, 255, 255, 0.3);

                        &:hover {
                            background: rgba(255, 255, 255, 0.5);
                        }
                    }

                    &.slick-active button {
                        background: #fff;
                    }
                }
            }
        }

        .ant-form {
            .ant-select-selector {
                border-radius: 9999px !important;
                height: 36px !important;
                padding: 0 16px !important;
                display: flex;
                align-items: center;
                background-color: #f5f5f5 !important;
                border: none !important;
            }

            .ant-select-selection-item {
                font-size: 14px;
                color: black !important;
                display: flex !important;
                align-items: center;
                gap: 8px;
            }

            .ant-select-arrow {
                right: 12px;
                color: black !important;
            }
        }
    }
}

.scrollContainer {
    position: relative;
}

.fadeTop,
.fadeBottom {
    position: absolute;
    left: 0;
    right: 0;
    height: 40px;
    pointer-events: none;
    z-index: 10;
}

.fadeTop {
    top: 0;
    background: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 100%);
}

.fadeBottom {
    bottom: 0;
    background: linear-gradient(to top, white 0%, rgba(255, 255, 255, 0) 100%);
}

.customScroll {
    &::-webkit-scrollbar {
        width: 0;
        background: transparent;
    }
}

.cp-login {
	&-top-btn {
		font-size: 16px;
		color: $fp-text-color;
		&:hover {
			color: $fp-primary-color !important;
		}
	}
}

.backgroundContainer {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}