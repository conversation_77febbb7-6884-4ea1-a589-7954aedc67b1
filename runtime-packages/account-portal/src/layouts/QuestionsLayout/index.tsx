import { FC, PropsWithChildren } from "react";
import { useTranslation } from "react-i18next";
import Breadcrumb, { BreadcrumbItem } from "@/components/Breadcrumb";
import SideBarMenu from "@/components/SideBarMenu";
import { getFAQSideBarProducts } from "@/components/SideBarMenu/constants";
import "./styles.scss";
import React, { useState } from 'react';
import FAQTabs from "@/components/Tabs/FAQTabs";

interface QuestionsLayoutProps {
    questionTab: string;
}

const QuestionsLayout: FC<PropsWithChildren<QuestionsLayoutProps>> = ({ questionTab }) => {
    const { t } = useTranslation();

    const products = getFAQSideBarProducts();
    const [activeProduct, setActiveProduct] = useState(products[0].key);
    return (
        <>
            <div className="questions-title">
                常见问题
            </div>
            <div className="faq-tabs-container">
                <FAQTabs keyName={activeProduct} questionTab={questionTab} />
            </div>
        </>
    );
};

export default QuestionsLayout;