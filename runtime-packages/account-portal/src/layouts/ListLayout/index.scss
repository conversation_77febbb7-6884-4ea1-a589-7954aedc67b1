@use "@/assets/styles/variables.module.scss" as *;

$badge-all-color: #40B16E; // 成功绿
$badge-active-color: #2463EB; // 辅助蓝
$badge-process-color: #DD4C4C; // 预警红
$badge-reject-color: #DD4C4C; // 预警红


.container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: $fp-white-color;
    .page-header {
        text-align: left;
        padding: 16px 0;
        border-bottom: 1px solid #eaeaea;

        .page-title {
            font-size: 24px;
            font-weight: bolder;
            color: $fp-text-primary-color;
        }
    }

    .main-content {
        display: flex;
        height: calc(100% - 60px); // 减去顶部标题高度

        .content {
            flex: 1;
            padding-top: 24px;
            padding-bottom: 24px;
            padding-left: 24px;
            padding-right: 24px;
            background-color: #F6F7FA;
            border-radius: 20px;

            .header-tabs {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;

                .badge-all {
                    .ant-badge-dot {
                        background-color: $fp-accent-color;
                        color: $fp-text-emphasis-color;
                    }
                }

                .badge-active {
                    .ant-badge-dot {
                        background-color: $fp-success-color;
                        color: $fp-text-emphasis-color;
                    }
                }

                .badge-process {
                    .ant-badge-dot {
                        background-color: $fp-secondary-color;
                        color: $fp-text-emphasis-color;
                    }
                }

                .badge-reject {
                    .ant-badge-dot {
                        background-color: $fp-error-color;
                        color: $fp-text-emphasis-color;
                    }
                }

                .ant-tabs {
                    flex: 1;

                    .ant-tabs-tab {
                        font-size: 16px;
                        color: $fp-text-emphasis-color;

                        &.ant-tabs-tab-active .ant-tabs-tab-btn {
                            color: $fp-text-emphasis-color;
                        }
                    }
                }

                button {
                    width: 122px;
                    height: 40px;
                    border-radius: 30px;
                    background-color: $fp-accent-color;
                    border: none;
                    margin-left: 16px;

                    &:hover {
                        background-color: fade($fp-accent-color, 60%);
                    }
                }
            }

            .ant-list {
                .ant-list-items {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-around;
                }
            }

            .personnel-card {
                margin-bottom: 24px;
                width: auto;
                min-width: 281px;
                border-radius: 20px;
                
                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 16px;
                    padding-top: 16px;

                    .user-info {
                        display: flex;
                        gap: 12px;
                        align-items: center;

                        .user-details {
                            .user-id {
                                font-family: "Poppins";
                                font-weight: 700;
                                font-size: 14px;
                                line-height: 20px;
                                color: #282830;
                                margin-bottom: 4px;
                            }
                            .user-type {
                                color: #666666;
                                font-size: 12px;
                            }
                        }
                    }

                    .more-actions {
                        padding: 0;
                        font-weight: bold;
                    }
                }

                .user-contact-info {
                    padding-left: 16px;
                    padding-right: 16px;
                    .info-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 8px;
                        color: #666666;
                        font-size: 13px;

						span {
							color: #666666;
                        	font-size: 13px;
						}

                        svg {
                            width: 16px;
                            height: 16px;
                            color: #999999;
							flex: none;
                        }
                    }
                }

                .card-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-left: 16px;
                    padding-right: 16px;

                    .status-badge {
                        border-radius: 16px;
                        
                        &.active {
                            background-color: #E8F5E9;
                            color: #4CAF50;
                        }
                    }

                    .more-actions {
                        padding: 0;
                        font-weight: bold;
                    }
                }
            }
        }
    }
}