import { FC } from "react";
import SideBarMenu from "@/components/SideBarMenu";
import Page from "@/components/common/Page";
import type { BreadcrumbItem } from "@/components/Breadcrumb";

const SideBarMenuLayout: FC<{
    breakcrumbs?: BreadcrumbItem[];
    children: React.ReactNode;
}> = ({ children, breakcrumbs }) => {

    return (
        <Page breadcrumbs={breakcrumbs}>
            <div className="flex items-start h-full bg-white">
                <SideBarMenu />
                <div className="flex-1 mb-10">{children}</div>
            </div>
        </Page>
    );
};

export default SideBarMenuLayout;
