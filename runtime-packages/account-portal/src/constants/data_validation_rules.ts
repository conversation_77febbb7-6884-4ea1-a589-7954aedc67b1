// src/utils/pspRules.ts
import {validateEmail} from "@fundpark/ui-utils";
import type {TFunction} from "i18next";
import type {FormInstance} from "antd";

export function getEmailRules(t: TFunction) {
    return [
        {required: true, message: t("login.emailRequired")},
        {
            validator: async (_: any, value: string) => {
                if (!value) return;
                if (!validateEmail(value)) {
                    throw new Error(t("login.emailInvalid"));
                }
                  if (value && value.length > 255) {
                    return Promise.reject(new Error("邮箱长度不能超过255个字符"));
                }
            },
        },
    ];
}

export function getMobileRules(t: TFunction, form: FormInstance, countryCodeFieldName: string = "phoneCountryCode") {
    return [
        {required: true, message: t("login.mobileRequired")},
        {
            validator: (_: any, value: string) => {
                if (!value) return Promise.resolve();
                const countryCode = form.getFieldValue(countryCodeFieldName);
                let phonePattern: RegExp;
                switch (countryCode) {
                    case "+86":
                        phonePattern = /^1[3-9]\d{9}$/;
                        break;
                    case "+852":
                        phonePattern = /^[5689]\d{7}$/;
                        break;
                    case "+1":
                        phonePattern = /^[1-9]\d{9}$/;
                        break;
                    default:
                        return Promise.resolve();
                }
                if (!phonePattern.test(value)) {
                    return Promise.reject(new Error(t("login.mobileInvalid")));
                }
                return Promise.resolve();
            },
        },
    ];
}

export function getCiRules() {
    return [
        {required: true, message: "请输入公司注册证CI号码"},
        // if you want pattern‐checking, do it here:
        // { pattern: /^[A-Z0-9]{6,20}$/, message: t("login.ciInvalid") },
    ];
}

export function getWechatRules() {
    return [
        {
            validator: (_: any, value: string) => {
                if (value && value.length > 50) {
                    return Promise.reject(new Error("微信号长度不能超过50个字符"));
                }
                return Promise.resolve();
            },
        },
    ];
}