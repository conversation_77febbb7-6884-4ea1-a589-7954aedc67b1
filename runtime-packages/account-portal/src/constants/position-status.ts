/**
 * Constants for position status values used in route protection
 */
export const POSITION_STATUS = {
  XDJ_DRAWDOWN_ACCOUNT_DRAFT: 'XDJ Drawdown Account Draft',
  XDJ_DRAWDOWN_ACCOUNT_SUBMITTED: 'XDJ Drawdown Account Submitted',
  XDJ_BUSINESS_INFO_SUBMITTED: 'XDJ Business Info Submitted',
  XDJ_SIGNING_INITIATED: 'XDJ Signing Initiated',
  XDJ_SIGNING_COMPLETED: 'XDJ Signing Completed',

  CAA_DRAWDOWN_ACCOUNT_DRAFT: 'CAA Drawdown Account Draft',
  CAA_DRAWDOWN_ACCOUNT_SUBMITTED: 'CAA Drawdown Account Submitted',
  CAA_BUSINESS_INFO_SUBMITTED: 'CAA Business Info Submitted',
  CAA_SIGNING_INITIATED: 'CAA Signing Initiated',
  CAA_SIGNING_COMPLETED: 'CAA Signing Completed',
} as const;

// Type for all possible status values
export type PositionStatus = typeof POSITION_STATUS[keyof typeof POSITION_STATUS]; 