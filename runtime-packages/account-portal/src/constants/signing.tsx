/**
 * Status codes returned by the shareholder structure verification API
 */
export const VerificationStatusCodes = {
    SUCCESS: 0,           // Verification passed
    WAITING: 2000,        // Verification in progress
    MANUAL_REVIEW: 3000,  // Manual review required
    FAILED: 4000, // Verification failed
    NOT_ALLOWED_ACCESS: 4010, // Not allowed access
    PEOPLE_NOT_FOUND: 4020, // People not found
    MOBILE_ANT_FAILED: 4030, // Mobile ant failed
    BUSINESS_INFO_FAILED: 4040, // Business info failed

    SIGNER_INFO_MISMATCH: 200400,
    RPA_INFO_MISMATCH: 200401,
    ANTI_FRAUD_FAILED: 200402,
    RPA_INFO_NOT_FOUND: 200404,
    COMPANY_NOT_FOUND: 200405,
    MEMBER_NOT_FOUND: 200406,
    MEMBER_NOT_FOUND_IN_CURRENT_COMPANY: 200407,
    MEMBER_HAS_BEEN_SIGNED: 200408,
    FIND_MORE_THAN_ONE_CONNECTED_COMPANY: 200409,
    DIRECTOR_QUANTITY_NOT_MATCH_WITH_RPA_RESULT: 200410,
    SHAREHOLDER_QUANTITY_NOT_MATCH_WITH_RPA_RESULT: 200411,
    AGREEMENT_HAS_BEEN_SENT: 200412, 
    AGREEMENT_GENERATING: 200413, 
    COMPANY_CREDIT_REQUEST_NOT_FOUND: 200414,
  } as const;
  
  /**
   * Type definition for error messages that can be either string or JSX
   */
  export type ErrorMessageType = {
    [key: string]: string | JSX.Element;
  };
  
  /**
   * Error messages for the signing page
   */
  export const ErrorMessages: ErrorMessageType = {
    VERIFICATION_FAILED: '获取验证结果时发生错误，请稍后再试。',
    MANUAL_REVIEW_REQUIRED: (
      <div className="flex flex-col">
        <div>检测到您的账户存在风险，申请流程暂停了。</div>
        <div>申诉或其他问题，请联系客服</div>
      </div>
    ),
    NOT_ALLOWED_ACCESS: '您授权的店铺尚未完全达到要求，请联系我们换个（3个月正常经营）店铺重新申请吧～',
    PEOPLE_NOT_FOUND: '很抱歉，系统没有找到贵司董事及实控人信息，烦请联系我们继续补充，离用款只差一步了哦～',
    UNKNOWN_ERROR: '未知错误，请稍后再试。',
    FETCH_ERROR: '获取签署人信息失败，请稍后重试',
    VALIDATION_ERROR: '请填写所有必填项',
    INITIATE_ERROR: '启动签约流程失败，请稍后重试',
    URGE_ERROR: '发送签约提醒失败，请稍后重试',
    INFO_MISMATCH: '签署人信息与身份证件信息不匹配',
    RPA_MISMATCH: '您上传的身份信息似乎不太匹配呢，请按照显示的董事信息再上传一次',
    ANTI_FRAUD_FAILED: '反欺诈检测未通过',
    RPA_INFO_NOT_FOUND: '您上传的身份信息似乎不太匹配呢，请按照显示的董事信息再上传一次',
    COMPANY_NOT_FOUND: '公司不存在',
    MEMBER_NOT_FOUND: '成员不存在',
    MEMBER_NOT_FOUND_IN_CURRENT_COMPANY: '成员不在当前公司',
    MEMBER_HAS_BEEN_SIGNED: '成员已签署',
    FIND_MORE_THAN_ONE_CONNECTED_COMPANY: '存在多个关联公司',
    SUBMIT_ERROR: '提交申请失败',
    AGREEMENT_HAS_BEEN_SENT: '签约文件将发送至邮箱，请前往查看',
    AGREEMENT_GENERATING: '合同正在生成，请于30分钟后重试，谢谢',
    PSP_IS_NULL:'请编辑绑定收款公司',
    COMPANY_CREDIT_REQUEST_NOT_FOUND: '未找到该公司的信用申请',
  } as const;
  
  /**
   * Route paths for navigation
   */
  export const Routes = {
    LOADING_PAGE: '/credit/hook/register/signing/loading',
    HOME_PAGE: '/credit/hook/home',
    LOADING_PAGE_CAA: '/credit/underwriting/register/signing/loading',
    HOME_PAGE_CAA: '/credit/underwriting/home'
  } as const; 


  export const SuccessMessages = {
    INITIATE_SUCCESS: '签约流程已启动，请检查您的邮箱和手机',
    URGE_SUCCESS: '已成功发送签约提醒',
  };

  // Modal properties
export const ModalConfig = {
  ERROR_MODAL_WIDTH: 444,
  SUBMIT_MODAL_WIDTH: 380,
  INITIATE_SUCCESS_MODAL_WIDTH: 458,
};

// Button configurations
export const ButtonConfig = {
  PRIMARY_BUTTON_WIDTH: '320px',
  ACTION_BUTTON_WIDTH: '160px',
  CONFIRM_BUTTON_HEIGHT: '40px',
  CONFIRM_BUTTON_WIDTH: '88px',
};