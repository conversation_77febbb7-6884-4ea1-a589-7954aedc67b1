/**
 * Enum for configuration data types
 * Used for referencing configuration data throughout the application
 */
export enum ConfigType {
  COUNTRY_REGION = 'country_region',
  INDUSTRY_SERVICE = 'industry_service',
  MOBILE_AREA_CODE = 'mobile_area_code',
  ENTITY_TYPE = 'entity_type',
  BUSINESS_NATURE = 'business_nature',
  CHARACTER_TITLE = 'character_title',
  CONNECTED_PERSON_CAPACITY = 'connected_person_capacity',
  CONNECTED_COMPANY_CAPACITY = 'connected_company_capacity',
  FUNDING_SOURCE = 'funding_source',
  INITIAL_SOURCE_OF_WEALTH = 'initial_source_of_wealth',
  CONTINUOUS_SOURCE_OF_WEALTH = 'continuous_source_of_wealth',
  UBO_SOURCE_OF_WEALTH = 'ubo_source_of_wealth',
  CERTIFICATE_TYPE = 'certificate_type',
  MAJOR_COMMODITIES_TYPE = 'major_commodities_type',
  BANK_ACCOUNT = 'bank_account',
}

/**
 * Special country key IDs for backward compatibility
 */
export enum CountryId {
  CHINA = '2',
  HONG_KONG = '1',
}

/**
 * Legacy country codes used in the application
 */
export enum LegacyCountryCode {
  CHINA = 'china',
  HONG_KONG = 'hongkong',
}

/**
 * Maps legacy country codes to country names in the API
 */
export const LEGACY_COUNTRY_TO_API_NAME: Record<string, string> = {
  [LegacyCountryCode.CHINA]: 'China',
  [LegacyCountryCode.HONG_KONG]: 'Hong Kong',
};

/**
 * Maps country names from API to legacy country codes
 */
export const API_NAME_TO_LEGACY_COUNTRY: Record<string, string> = {
  'China': LegacyCountryCode.CHINA,
  'Hong Kong': LegacyCountryCode.HONG_KONG,
};

/**
 * Maps country IDs to legacy country codes
 */
export const COUNTRY_ID_TO_LEGACY_CODE: Record<string, string> = {
  [CountryId.CHINA]: LegacyCountryCode.CHINA,
  [CountryId.HONG_KONG]: LegacyCountryCode.HONG_KONG,
}; 