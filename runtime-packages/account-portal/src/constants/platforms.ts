import amazonIcon from "@/assets-new/icons/platforms/amazon.svg";
import walmartIcon from "@/assets-new/icons/platforms/walmart.svg";
import ebayIcon from "@/assets-new/icons/platforms/ebay.svg";
import aliexpressIcon from "@/assets-new/icons/platforms/aliexpress.svg";
import shopeeIcon from "@/assets-new/icons/platforms/shopee.svg";
import shopifyIcon from "@/assets-new/icons/platforms/shopify.svg";

import fruugoIcon from "@/assets-new/icons/platforms/fruugo.svg";
import cdiscountIcon from "@/assets-new/icons/platforms/cdiscount.svg";
import mercadoIcon from "@/assets-new/icons/platforms/mercado.svg";
import tiktokIcon from "@/assets-new/icons/platforms/tiktok.svg";
import temuIcon from "@/assets-new/icons/platforms/temu.svg";
import lazadaIcon from "@/assets-new/icons/platforms/lazada.svg";
import kauflandIcon from "@/assets-new/icons/platforms/kaufland.svg";
import sheinIcon from "@/assets-new/icons/platforms/shein.svg";
import wayfairIcon from "@/assets-new/icons/platforms/wayfair.svg";
import manomanoIcon from "@/assets-new/icons/platforms/manomano.svg";
import conforamaIcon from "@/assets-new/icons/platforms/conforama.svg";
import dewuIcon from "@/assets-new/icons/platforms/dewu.svg";
import tiktokCnIcon from "@/assets-new/icons/platforms/tiktok_cn.svg";
import ksIcon from "@/assets-new/icons/platforms/ks.svg";
import tfxIcon from "@/assets-new/icons/platforms/TFX.svg";
import jdIcon from "@/assets-new/icons/platforms/jd.svg";
import tmallIcon from "@/assets-new/icons/platforms/tmall.svg";
import pddIcon from "@/assets-new/icons/platforms/pdd.svg";
import redNoteIcon from "@/assets-new/icons/platforms/red_note.svg";

export const ICON_MAPPING: Record<string, string> = {
  "Amazon": amazonIcon,
  "Walmart": walmartIcon,
  "ebay": ebayIcon,
  "AliExpress": aliexpressIcon,
  "Shopee": shopeeIcon,
  "Shopify": shopifyIcon,
  "Fruugo": fruugoIcon,
  "Cdiscount": cdiscountIcon,
  "Mercado Libre": mercadoIcon,
  "TikTok": tiktokIcon,
  "Temu": temuIcon,
  "Lazada": lazadaIcon,
  "Kaufland": kauflandIcon,
  "SHEIN": sheinIcon,
  "Wayfair": wayfairIcon,
  "Manomano": manomanoIcon,
  "Conforama": conforamaIcon,
  "得物": dewuIcon,
  "抖音": tiktokCnIcon,
  "快手": ksIcon,
  "鲸芽（原淘分销）": tfxIcon,
  "京东": jdIcon,
  "天猫国际": tmallIcon,
  "拼多多": pddIcon,
  "小红书": redNoteIcon,
};


export enum PlatformActionEnum {
  auth_without_psp = 1,
  auth_with_psp = 2,
  auth_with_ping_pong = 3
}
