import { ICarouselBanner } from "@/types/banner/carouselBanner";
import xdjBanner1 from "@/assets-new/images/banners/xdj_banner1.png"
import xdjBanner2 from "@/assets-new/images/banners/xdj_banner2.jpg"
import caaBanner1 from "@/assets-new/images/banners/caa_banner1.jpg"
import caaBanner2 from "@/assets-new/images/banners/caa_banner2.jpg"



export const XDJBannerList: ICarouselBanner[] = [
    {
        id:1,
        banner: caaBanner1,
        banner_id: 'underwriting2506red',
        bannerEvent:(navigate)=>{
            navigate("/credit/underwriting", { replace: true });
        }
    },
    {
        id:2,
        banner: caaBanner2,
        banner_id: 'underwriting2506blue',
        bannerEvent:(navigate)=>{
            navigate("/credit/underwriting", { replace: true });
        }
    },

  ];

export const CAABannerList: ICarouselBanner[] = [
{
    id:1,
    banner: xdjBanner1,
    banner_id: 'hook2506red',
    bannerEvent:(navigate)=>{
        navigate("/credit/hook", { replace: true });
    }
},
{
    id:2,
    banner: xdjBanner2,
    banner_id: 'hook2506blue',
    bannerEvent:(navigate)=>{
        navigate("/credit/hook", { replace: true });
    }
},

];
  
