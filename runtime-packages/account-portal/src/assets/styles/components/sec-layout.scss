@use "@/assets/styles/variables.module.scss" as *;

.cp-sec-layout {
    max-width: 940px;
    &-header {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        &-title {
            flex: 1;
            font-size: 24px;
            line-height: 30px;
            font-weight: 700;
            font-family: "Poppins";
            color: $fp-text-color;
        }
        &-right {
            display: flex;
            align-items: center;
            gap: 6px;
        }
    }
    &-content {
        overflow: hidden;
        background: #f6f7fa;
        border-radius: 20px;
        padding: 24px;
        gap: 24px;
        display: flex;
        align-items: flex-start;
        flex-wrap: wrap;
        &-empty {
            overflow: hidden;
            border-radius: 20px;
            padding: 24px;
            gap: 24px;
            display: flex;
            align-items: flex-start;
            flex-wrap: wrap;
        }
    }
    &-item {
        overflow: hidden;
        border-radius: 20px;
        border: 1px solid #dddfe6;
        background: #fff;
        width: 434px;
        transition: all 0.3s;
        position: relative;
        &:hover {
            border-color: $fp-accent-color;
        }
        &-main {
            min-height: 197px;
            padding: 24px;
        }
        &-footer {
            height: 64px;
            border-top: 1px solid #dddfe6;
            padding: 16px 24px 16px 24px;
            display: flex;
            align-items: center;
            &-left {
                flex: 1;
                display: flex;
                align-items: center;
                gap: 4px;
            }
            &-right {
                display: flex;
                align-items: center;
                gap: 4px;
            }
        }
        &-first-wrap {
            display: flex;
            align-items: flex-start;
        }
        &-first-right {
            flex: 1;
            width: 0;
        }
        &-avator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 16px;
            background: #f6f7fa;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }
        &-name-wrap {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: 6px;
            min-height: 40px;
            margin-bottom: 6px;
        }
        &-name {
            color: #000;
            font-size: 16px;
            font-weight: 700;
            line-height: 22px;
        }
        &-icon {
            font-size: 28px;
        }
        &-type-wrap {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        &-type {
            display: flex;
            align-items: center;
            font-size: 12px;
            line-height: 20px;
            font-weight: 400;
            padding: 0 6px;
            border-radius: 50px;
            color: $fp-text2-color;
            background: #f6f7fa;
            svg {
                font-size: 16px;
                margin-right: 2px;
            }
        }
        &-number-wrap {
            margin-top: 24px;
            display: flex;
        }
        &-number {
            flex: 1;
        }
        &-number-divder {
            content: "|";
            margin: 6px 5px;
            width: 1px;
            height: 32px;
            background: #dddfe6;
            align-self: center;
        }
        &-number-name {
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            color: $fp-text-subtle-color;
            margin-bottom: 4px;
        }
        &-number-value {
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            color: $fp-text-color;
        }
        &-status {
            min-width: 88px;
            text-align: center;
            padding: 6px 12px;
            border-radius: 24px;
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            &.draft {
                cursor: pointer;
                color: $fp-text-color;
                background: #dddfe6;
            }
            &.rejected {
				cursor: pointer;
                color: $fp-error-color;
                background: #fceded;
            }
            &.return {
                cursor: pointer;
                color: #ff7633;
                background: #fff1eb;
            }
            &.approval {
                color: $fp-success-color;
                background: #ecf7f0;
            }
            &.inprogress {
                color: $fp-secondary-color;
                background: #e9effd;
            }
            &.authorized {
                color: $fp-secondary-color;
                background: #e9effd;
            }
            &.unauthorized {
                color: $fp-text-primary-color;
                background: #e9e8ed;
            }
            &.unverified {
                color: $fp-text-primary-color;
                background: #dddfe6;
            }
        }
        &-op-btn {
            font-size: 14px;
            width: 32px;
            height: 32px;
            background: #f6f7fa;
            color: $fp-text-color;
            &:not(:disabled):not(.ant-btn-disabled):hover {
                color: #fff;
                background: $fp-accent-color;
            }
        }
    }
}
