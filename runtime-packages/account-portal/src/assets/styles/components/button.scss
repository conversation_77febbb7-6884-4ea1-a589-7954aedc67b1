@use "@/assets/styles/variables.module.scss" as *;

.ant-btn-color-primary {
	box-shadow: none;
}

@mixin btn-round() {
    border-radius: 60px;
    padding: 10px 16px;
    border: none;
    font-size: 14px;
    line-height: 20px;
}

.cp-btn-default {
    @include btn-round();
    color: $fp-accent-color;
    border: 1px solid $fp-accent-color;
	background: #fff;
	&:not(:disabled):not(.ant-btn-disabled):hover {
		color: #fff;
		background: $fp-accent-color;
	}
	&:not(:disabled):not(.ant-btn-disabled):active {
		color: #fff;
		background: $fp-accent-active-bg-color;
	}
}

.cp-btn-default2 {
    @include btn-round();
    color: $fp-primary-color;
    border: 1px solid $fp-primary-color;
	background: #fff;
	&:not(:disabled):not(.ant-btn-disabled):hover {
		color: #fff;
		background: $fp-primary-color;
		border-color: $fp-primary-color;
	}
	&:not(:disabled):not(.ant-btn-disabled):active {
		color: #fff;
		background: $fp-primary-active-bg-color;
		border-color: $fp-primary-color;
	}
}

.cp-btn-primary-round {
    @include btn-round();
    height: 40px;
    border-radius: 60px;
    background: $fp-primary-color;
    color: #fff;
	&:not(:disabled):not(.ant-btn-disabled):hover {
		background: $fp-primary-hover-bg-color;
	}
	&:not(:disabled):not(.ant-btn-disabled):active {
		background: $fp-primary-active-bg-color;
	}
}

.cp-btn-primary2-round {
    @include btn-round();
    background: $fp-accent-color;
    color: #fff;
	&:not(:disabled):not(.ant-btn-disabled):hover {
		background: $fp-accent-hover-bg-color;
	}
	&:not(:disabled):not(.ant-btn-disabled):active {
		background: $fp-accent-active-bg-color;
	}
}

.cp-text-btn-primary {
	font-size: 14px;
	line-height: 20px;
	font-weight: 600;
	letter-spacing: 0;
	color: #64CCC9;
	&:not(:disabled):not(.ant-btn-disabled):hover {
		color: #83D6D4;
	}
	&:not(:disabled):not(.ant-btn-disabled):active {
		color: #55ADAB;
	}
}