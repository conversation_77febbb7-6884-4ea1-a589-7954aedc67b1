@use "@/assets/styles/variables.module.scss" as *;

.ant-table-wrapper {
    .ant-table-thead > tr > th,
    .ant-table-thead > tr > td {
        font-size: 14px;
        line-height: 20px;
        font-weight: 600;
        padding: 14px 24px;
        color: $fp-text-color;
        background-color: rgba(32, 23, 71, 0.04);
        border-bottom-color: rgba(143, 139, 163, 1);
    }
    .ant-table-tbody > tr > td {
        font-size: 14px;
        line-height: 20px;
        font-weight: 600;
        padding: 14px 24px;
        color: $fp-text-color;
        border-bottom-color: rgba(32, 23, 71, 0.08);
    }
}

.table-action {
    display: flex;
    align-items: center;
    button {
		gap: 0;
        & + button {
			&::before {
				content: "";
				display: block;
				width: 1px;
				height: 18px;
				background-color: $fp-border-color;
				margin: 0 16px;
			}
        }
    }
}
