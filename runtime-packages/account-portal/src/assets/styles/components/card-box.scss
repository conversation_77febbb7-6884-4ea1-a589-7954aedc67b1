@use "@/assets/styles/variables.module.scss" as *;

.card-box {
    border: 1px solid #cacad1;
    border-radius: 12px;
    overflow: hidden;
    padding: 20px 20px 0;
    transition: all 0.3s;
	margin-bottom: 24px;
    &:hover {
        border-color: $fp-text-color;
    }
    &-header {
        display: flex;
        align-items: flex-start;
        padding-bottom: 24px;
        &-title {
            flex: 1;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            color: #000;
            .collapse-btn {
                color: #000;
                margin-left: 8px;
                .ant-btn-icon {
                    transition: transform 0.3s;
                }
                &.rotate-status .ant-btn-icon {
                    transform: rotate(180deg);
                }
            }
        }
        &-extra {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #282830;
        }
    }
	&-content {
		transition: all 0.3s;
	}
}
