@use "./variables.module.scss" as *;

body {
    font-family:
        "Source Sans Pro",
        -apple-system,
        BlinkMacSystemFont,
        "Segoe UI",
        <PERSON><PERSON>,
        "Helvetica Neue",
        <PERSON><PERSON>,
        "Noto Sans",
        sans-serif,
        "Apple Color Emoji",
        "Segoe UI Emoji",
        "Segoe UI Symbol",
        "Noto Color Emoji" !important;
    color: $fp-text-color;
    font-size: 14px;
    line-height: 20px;
}

/* Override browser autofill styles */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
    background-color: transparent !important;
}

* {
    /* 自定义整个滚动条 */
    &::-webkit-scrollbar {
        width: 10px;
    }

    /* 自定义滚动条轨道 */
    &::-webkit-scrollbar-track {
        background: transparent;
    }

    /* 自定义滚动条的滑块（thumb） */
    &::-webkit-scrollbar-thumb {
        background: rgba(32, 23, 71, 0.3);
        border-radius: 40px;
    }

    /* 当滑块悬停或活动时，可以添加更多样式 */
    &::-webkit-scrollbar-thumb:hover {
        background: #888;
    }
}

svg {
    vertical-align: middle;
}

.fp-primary-color {
    color: $fp-primary-color;
}

.fp-accent-color {
    color: $fp-accent-color;
}

// Add these styles for the Navbar
.ant-menu {
    &.ant-menu-dark {
        background: transparent;

        .ant-menu-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .ant-menu-item-selected {
            background-color: rgba(255, 255, 255, 0.2);
        }
    }
}

.ant-layout-header {
    line-height: 1;
    padding: 0 24px;
}

.ant-select {
    .ant-select-arrow {
        transition: all 0.3s ease;
    }
    &.ant-select-open .ant-select-arrow {
        transform: rotate(180deg);
    }
}

// Global style for Form.Item labels
.ant-form-item .ant-form-item-label > label {
    color: $fp-text2-color;
}

.fp-compact {
	width: 100%;
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: #fff;
    border: 1px solid #dddfe6;
    border-radius: 10px;
    padding: 3px;
    & > .ant-select {
        background: #f6f7fa;
        border-radius: 8px;
        width: 150px;
        .ant-select-selection-item {
            color: $fp-text-color;
        }
        .ant-select-arrow {
            color: $fp-text-color;
        }
    }
}
