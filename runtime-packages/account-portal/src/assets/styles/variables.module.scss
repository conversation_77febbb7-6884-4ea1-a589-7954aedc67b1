/** 主要文字颜色 */
$fp-text-color: #282830;
/** 次要文字颜色 */
$fp-text2-color: #6e6e75;

/** 主色(主题青) */
$fp-primary-color: #64ccc9;
$fp-primary-hover-bg-color: #93dbd9;
$fp-primary-active-bg-color: #50a3a1;
/** 主色(主题藏青) */
$fp-accent-color: #201747;
$fp-accent-hover-bg-color: #4d456c;
$fp-accent-active-bg-color: #0a0715;
/** 辅助蓝 */
$fp-secondary-color: #2463eb;
/** 辅助白 */
$fp-white-color: #ffffff;
/** 辅助紫 */
$fp-tertiary-color: #bf79df;
/** 成功绿 */
$fp-success-color: #40b16e;
/** 预警红 */
$fp-error-color: #dd4c4c;
/** 警示橙 */
$fp-warning-color: #ff7633;
/** 提示黄 */
$fp-info-color: #f4a83c;

/** 文本颜色 */
$fp-text-emphasis-color: #000000; // 重点文字
$fp-text-primary-color: #282830; // 主要文字
$fp-text-secondary-color: #6e6e75; // 次要文字
$fp-text-subtle-color: #9e9ea3; // 辅助文字

/** 边框与背景 */
$fp-border-color: #cacad1; // 边框灰色
$fp-gray-border-color: #dddfe6; // 灰色/框线
$fp-divider-color: #dfe1e5; // 分割线色
$fp-bg-color: #f6f8fa; // 背景灰色

:export {
    fpTextColor: $fp-text-color;
    fpText2Color: $fp-text2-color;
    fpPrimaryColor: $fp-primary-color;
    fpPrimaryHoverBgColor: $fp-primary-hover-bg-color;
    fpPrimaryActiveBgColor: $fp-primary-active-bg-color;
    fpAccentColor: $fp-accent-color;
    fpAccentHoverBgColor: $fp-accent-hover-bg-color;
    fpAccentActiveBgColor: $fp-accent-active-bg-color;
    fpSecondaryColor: $fp-secondary-color;
    fpWhiteColor: $fp-white-color;
    fpTertiaryColor: $fp-tertiary-color;
    fpSuccessColor: $fp-success-color;
    fpErrorColor: $fp-error-color;
    fpWarningColor: $fp-warning-color;
    fpInfoColor: $fp-info-color;
    fpTextEmphasisColor: $fp-text-emphasis-color;
    fpTextPrimaryColor: $fp-text-primary-color;
    fpTextSecondaryColor: $fp-text-secondary-color;
    fpTextSubtleColor: $fp-text-subtle-color;
    fpBorderColor: $fp-border-color;
    fpGrayBorderColor: $fp-gray-border-color;
    fpDividerColor: $fp-divider-color;
    fpBgColor: $fp-bg-color;
}
