import type { ThemeConfig } from "antd";
import { hexToRgb } from "@fundpark/ui-utils";
import variables from "@/assets/styles/variables.module.scss";

const primaryRgbStr = hexToRgb(variables.fpAccentColor).join(", ");

function primaryToRgba(opacity: number) {
    return `rgba(${primaryRgbStr}, ${opacity})`;
}

const theme: ThemeConfig = {
    token: {
        fontFamily: "Source Sans Pro",
        colorPrimary: variables.fpAccentColor,
        colorBgContainerDisabled: primaryToRgba(0.04),
        colorTextDisabled: variables.fpTextColor,
        colorBorder: variables.fpGrayBorderColor,
		colorError: variables.fpErrorColor,
        fontSize: 14,
        borderRadius: 10,
        borderRadiusLG: 10
    },
    components: {
        Button: {
            algorithm: false
        },
        Input: {
            activeShadow: primaryToRgba(0.1)
        },
        DatePicker: {
            activeShadow: primaryToRgba(0.1)
        },
        Select: {
            activeOutlineColor: primaryToRgba(0.1),
            optionActiveBg: primaryToRgba(0.04),
            optionSelectedBg: primaryToRgba(0.08)
        }
    }
};

export default theme;
