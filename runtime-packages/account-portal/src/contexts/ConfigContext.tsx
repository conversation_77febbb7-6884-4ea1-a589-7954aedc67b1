import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getCompanyConfig } from '@fundpark/fp-api';
import { ConfigType } from '@/constants/configTypes';

// Define the structure of our config data
interface CountryRegion {
  key_id: number;
  name: string;
  name_chi: string;
}

interface BasicConfigItem {
  key_id: number;
  name: string;
  name_chi: string;
  bank_num?: string;
  swift?: string;
  address?: string;
  show_name?: string;
}

interface CompanyConfig {
  country_region: CountryRegion[];
  industry_service: BasicConfigItem[];
  mobile_area_code: BasicConfigItem[];
  entity_type: BasicConfigItem[];
  business_nature: BasicConfigItem[];
  character_title: BasicConfigItem[];
  connected_person_capacity: BasicConfigItem[];
  connected_company_capacity: BasicConfigItem[];
  funding_source: BasicConfigItem[];
  initial_source_of_wealth: BasicConfigItem[];
  continuous_source_of_wealth: BasicConfigItem[];
  ubo_source_of_wealth: BasicConfigItem[];
  certificate_type: BasicConfigItem[];
  major_commodities_type: BasicConfigItem[];
  bank_account: BasicConfigItem[];
}

interface ConfigContextType {
  companyConfig: CompanyConfig | null;
  loading: boolean;
  error: Error | null;
  refreshConfig: () => Promise<void>;
  getConfigLabel: (type: ConfigType, value: string) => string;
  getCountryLabel: (value: string) => string;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

interface ConfigProviderProps {
  children: ReactNode;
}

export const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const [companyConfig, setCompanyConfig] = useState<CompanyConfig | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchConfig = async () => {
    setLoading(true);
    try {
      const response = await getCompanyConfig();
      if (response && response.code === 0 && response.data) {
        setCompanyConfig(response.data as unknown as CompanyConfig);
        setError(null);
      } else {
        throw new Error('Failed to fetch config data');
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      console.error('Error fetching config:', err);
    } finally {
      setLoading(false);
    }
  };

  const refreshConfig = async () => {
    await fetchConfig();
  };

  // Helper function to get label for any config type
  const getConfigLabel = (type: ConfigType, value: string): string => {
    if (!companyConfig || !companyConfig[type]) {
      return value;
    }

    const options = companyConfig[type];
    const selectedOption = options.find((option: any) => option.key_id.toString() === value);
    
    // For bank accounts, use show_name if available, otherwise name_chi
    if (type === ConfigType.BANK_ACCOUNT && selectedOption && 'show_name' in selectedOption) {
      return (selectedOption as any).show_name as string;
    }
    
    return selectedOption ? selectedOption.name_chi : value;
  };

  // Specific helper for country labels (with filtering for China and Hong Kong)
  const getCountryLabel = (value: string): string => {
    if (!companyConfig || !companyConfig[ConfigType.COUNTRY_REGION]) {
      return value;
    }
    
    const countryOptions = companyConfig[ConfigType.COUNTRY_REGION].filter(
      (item: any) => item.key_id === 1 || item.key_id === 2
    );
    
    const selectedOption = countryOptions.find((option: any) => option.key_id.toString() === value);
    return selectedOption ? selectedOption.name_chi : value;
  };

  // Fetch config data on initial mount
  useEffect(() => {
    fetchConfig();
  }, []);

  const value = {
    companyConfig,
    loading,
    error,
    refreshConfig,
    getConfigLabel,
    getCountryLabel
  };

  return <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>;
};

export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
}; 