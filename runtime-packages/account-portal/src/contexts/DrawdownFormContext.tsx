import React, { createContext, useContext, useState, ReactNode, useRef } from 'react';

interface BusinessInfo {
  companyName: string;
  businessType: string;
  registrationNumber: string;
  // Add more business fields as needed
}

interface DrawdownAccount {
  accountType: string;
  accountNumber: string;
  amount: number;
  currency: string;
  countryRegion?: string;
  // Add more account fields as needed
}

interface DrawdownFormData {
  drawdownAccount: DrawdownAccount;
  businessInfo: BusinessInfo;
}

interface DrawdownFormContextType {
  formData: DrawdownFormData;
  updateDrawdownAccount: (data: Partial<DrawdownAccount>) => void;
  updateBusinessInfo: (data: Partial<BusinessInfo>) => void;
  resetForm: () => void;
  submitForm: () => Promise<boolean>;
  isSubmitting: boolean;
  submissionError: string | null;
  validateDrawdownAccount: (formInstance: any) => Promise<boolean>;
  validateBusinessInfo: (formInstance: any) => Promise<boolean>;
  registerFormInstance: (step: string, form: any) => void;
}

const initialFormData: DrawdownFormData = {
  drawdownAccount: {
    accountType: '',
    accountNumber: '',
    amount: 0,
    currency: '',
  },
  businessInfo: {
    companyName: '',
    businessType: '',
    registrationNumber: '',
  }
};

const DrawdownFormContext = createContext<DrawdownFormContextType | undefined>(undefined);

interface DrawdownFormProviderProps {
  children: ReactNode;
}

export const DrawdownFormProvider: React.FC<DrawdownFormProviderProps> = ({ children }) => {
  const [formData, setFormData] = useState<DrawdownFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const formInstances = useRef<{ [key: string]: any }>({});

  const updateDrawdownAccount = (data: Partial<DrawdownAccount>) => {
    setFormData(prev => ({
      ...prev,
      drawdownAccount: {
        ...prev.drawdownAccount,
        ...data
      }
    }));
  };

  const updateBusinessInfo = (data: Partial<BusinessInfo>) => {
    setFormData(prev => ({
      ...prev,
      businessInfo: {
        ...prev.businessInfo,
        ...data
      }
    }));
  };

  const registerFormInstance = (step: string, form: any) => {
    formInstances.current[step] = form;
  };

  const validateDrawdownAccount = async (form: any): Promise<boolean> => {
    try {
      if (form) {
        await form.validateFields();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Drawdown account validation error:', error);
      return false;
    }
  };

  const validateBusinessInfo = async (form: any): Promise<boolean> => {
    try {
      if (form) {
        await form.validateFields();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Business info validation error:', error);
      return false;
    }
  };

  const resetForm = () => {
    setFormData(initialFormData);
  };

  const submitForm = async (): Promise<boolean> => {
    // Validate business info before submission
    const businessInfoForm = formInstances.current['businessInfo'];
    
    if (businessInfoForm) {
      try {
        await businessInfoForm.validateFields();
      } catch (error) {
        console.error('Business info validation failed:', error);
        return false;
      }
    }
    
    setIsSubmitting(true);
    setSubmissionError(null);
    
    try {
      // Simulate API call to submit the form
      // Replace this with your actual API submission logic
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Example API call:
      // const response = await fetch('/api/drawdown-request', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(formData),
      // });
      
      // if (!response.ok) {
      //   throw new Error('Failed to submit drawdown request');
      // }
      
      console.log('Form submitted successfully:', formData);
      setIsSubmitting(false);
      return true;
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmissionError(error instanceof Error ? error.message : 'Unknown error occurred');
      setIsSubmitting(false);
      return false;
    }
  };

  const value = {
    formData,
    updateDrawdownAccount,
    updateBusinessInfo,
    resetForm,
    submitForm,
    isSubmitting,
    submissionError,
    validateDrawdownAccount,
    validateBusinessInfo,
    registerFormInstance
  };

  return (
    <DrawdownFormContext.Provider value={value}>
      {children}
    </DrawdownFormContext.Provider>
  );
};

export const useDrawdownForm = (): DrawdownFormContextType => {
  const context = useContext(DrawdownFormContext);
  if (context === undefined) {
    throw new Error('useDrawdownForm must be used within a DrawdownFormProvider');
  }
  return context;
}; 