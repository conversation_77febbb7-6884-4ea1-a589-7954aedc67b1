import React, { createContext, useContext, ReactNode } from 'react';
import useMatomo, { TrackEventParams } from '@/hooks/useMatomo';
import matomoConfig from '@/config/matomo';

// Define the context type
interface MatomoContextType {
  trackEvent: (category: string, action: string, name?: string, value?: number) => void;
  trackEventWithDimensions: (params: TrackEventParams) => void;
  trackPageView: (customTitle?: string, customUrl?: string) => void;
  trackSiteSearch: (keyword: string, category?: string, resultsCount?: number) => void;
  trackGoal: (goalId: number, conversionValue?: number) => void;
  setCustomDimension: (dimensionId: number, dimensionValue: string) => void;
  setUserId: (userId: string) => void;
  resetUserId: () => void;
  forceTrackingDispatch: () => void;
  getPreviousUrl: () => string | null;
  getCurrentUrl: () => string | null;
  isEnabled: boolean;
}

// Create the context with default values
const MatomoContext = createContext<MatomoContextType>({
  trackEvent: () => {},
  trackEventWithDimensions: () => {},
  trackPageView: () => {},
  trackSiteSearch: () => {},
  trackGoal: () => {},
  setCustomDimension: () => {},
  setUserId: () => {},
  resetUserId: () => {},
  forceTrackingDispatch: () => {},
  getPreviousUrl: () => null,
  getCurrentUrl: () => null,
  isEnabled: false
});

// Provider component
interface MatomoProviderProps {
  children: ReactNode;
  enabled?: boolean;
}

export const MatomoProvider: React.FC<MatomoProviderProps> = ({ children, enabled = true }) => {
  const matomo = useMatomo();
  
  // Check if Matomo is enabled based on configuration and props
  const isEnabled = enabled && !matomoConfig.disableInDevelopment;
  
  // Create context value
  const contextValue: MatomoContextType = {
    ...matomo,
    isEnabled
  };
  
  return (
    <MatomoContext.Provider value={contextValue}>
      {children}
    </MatomoContext.Provider>
  );
};

// Custom hook to use the Matomo context
export const useMatomoContext = () => useContext(MatomoContext);

export default MatomoContext; 