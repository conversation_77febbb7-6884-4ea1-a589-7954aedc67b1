# Build stage
FROM node:20-alpine as builder

# Install pnpm first
RUN npm install -g pnpm

ARG BUILD_MODE=development
ENV BUILD_MODE=${BUILD_MODE}

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./
COPY base-packages/ ./base-packages/
COPY api-packages/ ./api-packages/
COPY runtime-packages/ ./runtime-packages/
COPY build-packages/ ./build-packages/
COPY ui-packages/ ./ui-packages/
COPY tsconfig/ ./tsconfig/

# Install dependencies and build
RUN pnpm install --no-frozen-lockfile
RUN pnpm build:${BUILD_MODE}

# Production stage
FROM nginx:alpine

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built files from builder stage
COPY --from=builder /app/runtime-packages/account-portal/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]