import fs from 'fs';
import path from 'path';
import xlsx from 'xlsx';

export function generateExcel(i18nDir, outputExcel) {
  const languages = fs.readdirSync(i18nDir).filter(file => fs.statSync(path.join(i18nDir, file)).isDirectory());
  const namespaces = fs.readdirSync(path.join(i18nDir, languages[0])).map(file => path.basename(file, '.json'));

  const data = {};

  namespaces.forEach(ns => {
    data[ns] = {};
    languages.forEach(lang => {
      const filePath = path.join(i18nDir, lang, `${ns}.json`);
      if (fs.existsSync(filePath)) {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        data[ns][lang] = content;
      }
    });
  });

  const flattenObject = (obj, prefix = '') =>
    Object.keys(obj).reduce((acc, k) => {
      const pre = prefix.length ? `${prefix}.` : '';
      if (typeof obj[k] === 'object' && obj[k] !== null) {
        Object.assign(acc, flattenObject(obj[k], pre + k));
      } else {
        acc[pre + k] = obj[k];
      }
      return acc;
    }, {});

  const workbook = xlsx.utils.book_new();
  namespaces.forEach(ns => {
    const sheetData = [['key', ...languages]];
    const keys = new Set();
    languages.forEach(lang => {
      const flattenedData = flattenObject(data[ns][lang] || {});
      Object.keys(flattenedData).forEach(key => keys.add(key));
    });
    keys.forEach(key => {
      const row = [key];
      languages.forEach(lang => {
        const flattenedData = flattenObject(data[ns][lang] || {});
        row.push(flattenedData[key] || '');
      });
      sheetData.push(row);
    });
    const worksheet = xlsx.utils.aoa_to_sheet(sheetData);

    // 设置列宽
    const colWidths = [{ wch: 40 }, ...languages.map(() => ({ wch: 80 }))];
    worksheet['!cols'] = colWidths;

    xlsx.utils.book_append_sheet(workbook, worksheet, ns);
  });

  xlsx.writeFile(workbook, outputExcel);
  console.log(`Excel file generated at ${outputExcel}`);
}
