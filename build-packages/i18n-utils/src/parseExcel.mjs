import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import xlsx from 'xlsx';
import prettier from 'prettier';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export function parseExcel(i18nDir, inputExcel) {
  const workbook = xlsx.readFile(inputExcel);

  const unflattenObject = (data) => {
    const result = {};
    for (const i in data) {
      const keys = i.split('.');
      keys.reduce((r, e, j) => {
        return r[e] || (r[e] = isNaN(Number(keys[j + 1])) ? (keys.length - 1 === j ? data[i] : {}) : []);
      }, result);
    }
    return result;
  };

  const mergeDeep = (target, source) => {
    for (const key of Object.keys(source)) {
      if (source[key] instanceof Object && key in target) {
        Object.assign(source[key], mergeDeep(target[key], source[key]));
      }
    }
    Object.assign(target || {}, source);
    return target;
  };

  workbook.SheetNames.forEach(ns => {
    const worksheet = workbook.Sheets[ns];
    const sheetData = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

    const headers = sheetData[0];
    const languages = headers.slice(1);

    const data = {};
    languages.forEach(lang => {
      data[lang] = {};
    });

    sheetData.slice(1).forEach(row => {
      const key = row[0];
      languages.forEach((lang, index) => {
        data[lang][key] = row[index + 1];
      });
    });

    languages.forEach(lang => {
      const langDir = path.join(i18nDir, lang);
      if (!fs.existsSync(langDir)) {
        fs.mkdirSync(langDir);
      }
      const filePath = path.join(langDir, `${ns}.json`);
      let existingData = {};
      if (fs.existsSync(filePath)) {
        existingData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      }
      const unflattenedData = unflattenObject(data[lang]);
      const mergedData = mergeDeep(existingData, unflattenedData);
      prettier.format(JSON.stringify(mergedData), { parser: 'json', tabWidth: 4 }).then(formattedData => {
        fs.writeFileSync(filePath, formattedData, 'utf8');
      });
    });
  });

  console.log('JSON files regenerated from Excel');
}
