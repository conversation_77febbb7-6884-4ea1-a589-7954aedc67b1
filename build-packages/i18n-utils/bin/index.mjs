#!/usr/bin/env node

import { program } from 'commander';
import path from 'path';
import { generateExcel } from '../src/generateExcel.mjs';
import { parseExcel } from '../src/parseExcel.mjs';

program
  .command('generate <dir>')
  .description('Generate Excel from i18n JSON files')
  .option('-f, --file <filename>', 'Specify the output Excel file name', 'i18n_translations.xlsx')
  .action((dir, options) => {
    const i18nDir = path.resolve(dir, 'i18n');
    const outputFile = path.resolve(process.cwd(), options.file);
    generateExcel(i18nDir, outputFile);
  });

program
  .command('parse <dir>')
  .description('Parse Excel to i18n JSON files')
  .option('-f, --file <filename>', 'Specify the input Excel file name', 'i18n_translations.xlsx')
  .action((dir, options) => {
    const i18nDir = path.resolve(dir, 'i18n');
    const inputFile = path.resolve(process.cwd(), options.file);
    parseExcel(i18nDir, inputFile);
  });

program.parse(process.argv);
