export function parseQuery<T extends object>(query: string): T {
    const str = (query || "").split("?")[1];
    if (!str) return {} as T;

    return str.split("&").reduce((acc, cur) => {
        const [key, value] = cur.split("=");
        acc[key] = decodeURIComponent(value);
        return acc;
    }, {} as Record<string, string>) as T;
}

export function serializeQuery(query: Record<string, any>): string {
    const str = Object.keys(query)
        .reduce<string[]>((all, key) => {
            if (query[key] !== undefined && query[key] !== null) {
                all.push(`${key}=${encodeURIComponent(query[key])}`);
            }
            return all;
        }, [])
        .join("&");
    return str ? `?${str}` : "";
}
