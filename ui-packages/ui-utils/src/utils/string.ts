export function trimSearch(search: any) {
    for (const key in search) {
        if (typeof search[key] === "string") {
            search[key] = search[key].trim();
        }
    }
    return search;
}

export function removeEmpty(search: any) {
    for (const key in search) {
        const v = search[key];
        if (v === "" || v === undefined || v === null) {
            delete search[key];
        }
    }
    return search;
}

/** 文本枚举映射 */
export function formatEnum<T extends Record<any, any>>(
    key: string | number | undefined | null,
    map: T,
    defaultValue?: any
) {
    const value = key !== null && key !== undefined ? map[key] : key;
    return value ?? defaultValue;
}

export function getSearchObj<T = any>(url?: string) {
    if (!url) url = location.search;
    const urlArr = url.split("?")[1]?.split("&") || [];
    const obj = {} as T;
    for (const item of urlArr) {
        const [key, val] = item.split("=");
        (obj as any)[key] = decodeURIComponent(val);
    }
    return obj;
}

/**
 * 字符串数据脱敏函数
 * @param {string} str - 原始字符串
 * @param {number} [startLen=0] - 开头保留长度
 * @param {number} [endLen=0] - 结尾保留长度
 * @param {string} [maskChar='*'] - 替换字符
 * @returns {string} 脱敏后的字符串
 */
export function desensitize(str?: string, startLen: number = 0, endLen: number = 0, maskChar: string = "*"): string {
    if (typeof str !== "string") return "";

    const len = str.length;
    startLen = Math.max(0, startLen);
    endLen = Math.max(0, endLen);

    // 调整结尾保留长度防止溢出
    if (startLen + endLen > len) {
        endLen = len - startLen;
        if (endLen < 0) endLen = 0;
    }

    const replaceLen = len - startLen - endLen;

    // 当无需替换时直接返回原字符串
    if (replaceLen <= 0) {
        return str.slice(0, startLen) + str.slice(-endLen || len);
    }

    return str.substring(0, startLen) + maskChar.repeat(replaceLen) + str.substring(len - endLen);
}

/**
 * 数字转换为千分位格式
 * @param {number} numStr 格式化目标数字
 * @returns {string}
 */
export function moneyFormat(numStr: number | string): string {
    if (numStr === undefined || numStr === null) return "-";

    numStr = numStr.toString();
    // 处理负号
    let sign = "";
    if (numStr.startsWith("-")) {
        sign = "-";
        numStr = numStr.slice(1);
    }

    // 分割整数和小数部分
    const parts = numStr.split(".");
    let integerPart = parts[0] || "0";
    const decimalPart = parts.length > 1 ? `.${parts[1]}` : "";

    // 去除前导零，并处理全零的情况
    integerPart = integerPart.replace(/^0+/, "") || "0";
    if (integerPart === "") integerPart = "0";

    // 添加千分位逗号
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    // 组合结果
    return sign + integerPart + decimalPart;
}

export function hexToRgb(hex: string) {
    if (!hex) return [0, 0, 0];
    hex = hex.replace("#", "");
    if (hex.length === 3) {
        hex = hex
            .split("")
            .map(item => item + item)
            .join("");
    }
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return [r, g, b];
}
