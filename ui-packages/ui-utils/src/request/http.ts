import axios from "axios";
import type { AxiosRequestConfig, Method, AxiosInstance } from "axios";
import { message } from "antd";
import fileDownload from "js-file-download";
import { IHttpResponse, HttpResCode } from "./types";
import { getLang, Locale } from "../utils/getLang";
import { trimSearch, removeEmpty } from "../utils/string";
import i18n from "../i18n";

interface IHttpRequestConfig {
    baseURL?: string;
	headers?: AxiosRequestConfig["headers"];
    /** 错误码的i18n实例 */
    i18n?: {
        t: (key: any) => string;
        language: string;
        changeLanguage: (locale: string) => void;
    };
    /** 错误码对应的i18n message key */
    errorCodes?: Record<string, string>;
    /** 过滤对应的错误码不弹出提示信息 */
    filterErrorTips?: number[];
}

interface IMyAxiosRequestConfig<T> extends AxiosRequestConfig<T> {
    params?: T;
}

interface ErrorMessage {
    code: number;
    messagePath: string; // 错误信息i18n路径
}

interface ICreateServiceParams<T, R> {
    /** 请求地址 */
    url: string;
    /** 请求方法 */
    method: Method;
    /** 请求头 */
    headers?: AxiosRequestConfig["headers"];
    /** 请求前请求体修改回调 */
    beforeRequest?: (axiosParam: IMyAxiosRequestConfig<T>) => typeof axiosParam;
    asyncBeforeRequest?: (axiosParam: IMyAxiosRequestConfig<T>) => Promise<typeof axiosParam>;
    /** 请求后报文修改回调 */
    transformResponse?: (res: IHttpResponse<R>) => IHttpResponse<any>;
    /** 是否清除查询参数的前后空格 */
    trimParams?: boolean;
    /** 是否移除空参数 */
    removeEmptyParams?: boolean;
    /** 是否弹出失败信息框, 默认true */
    showFailTips?: boolean;
    /** 超时时长, 默认: 20S */
    timeout?: number;
    /** 返回数据类型，默认json，导出文件流传blob */
    responseType?: AxiosRequestConfig["responseType"];
    /** 是否鉴权，默认true */
    useAuth?: boolean;
    /** 通用错误信息 */
    genericErrorMessage?: string;
    /** 自定义错误信息 */
    customErrorMessage?: Record<string, string>;
}

export class HttpRequest {
    protected instance: AxiosInstance;
    protected locale: Locale = getLang();
    private token: string | null = null;
    private customI18n: IHttpRequestConfig["i18n"];
    private errorCodes: Record<string, string> = {};
    private filterErrorTips: number[] = [];

    private onResponseCallbacks: ((res: IHttpResponse<unknown>) => void)[] = [];

    constructor(config: Partial<IHttpRequestConfig> = {}) {
        const { baseURL = "/", headers, i18n: customI18n, errorCodes = {}, filterErrorTips = [] } = config;
        this.customI18n = customI18n;
        this.errorCodes = errorCodes;
        this.filterErrorTips = filterErrorTips;
        const instance = axios.create({
            baseURL,
			headers
        });
        this.instance = instance;
    }

    public setBaseUrl(baseURL: string) {
        this.instance.defaults.baseURL = baseURL;
    }

    public setLocale(locale: Locale) {
        this.locale = locale;
        i18n.changeLanguage(locale);
        this.customI18n?.changeLanguage(locale);
    }

    public setToken(token: string | null) {
        this.token = token;
    }

    public onResponse(callback: (res: IHttpResponse<unknown>) => void) {
        this.onResponseCallbacks.push(callback);

        return () => {
            const index = this.onResponseCallbacks.indexOf(callback);
            if (index > -1) {
                this.onResponseCallbacks.splice(index, 1);
            }
        };
    }

    public createService = <T = void, R = any>(options: ICreateServiceParams<T, R>) => {
        return async (req: T): Promise<IHttpResponse<R>> => {
            const method = options.method.toLocaleLowerCase() as Method;
            const useAuth = options.useAuth !== false;
            const isGet = method === "get";

            if (options.trimParams) {
                trimSearch(req);
            }
            if (options.removeEmptyParams) {
                removeEmpty(req);
            }

            let param: IMyAxiosRequestConfig<T> = {
                url: options.url,
                method,
                data: isGet ? undefined : req,
                params: isGet ? req : undefined,
                timeout: options.timeout,
                responseType: options.responseType || "json",
                headers: options.headers
            };
            if (method === "post") {
                param.headers = {
                    "Content-Type": "application/json",
                    ...param.headers
                };
            }
            if (options.beforeRequest) {
                param = options.beforeRequest(param);
            }
            if (options.asyncBeforeRequest) {
                param = await options.asyncBeforeRequest(param);
            }
            if (useAuth && this.token) {
                param.headers = {
                    ...param.headers,
                    Authorization: this.token
                };
            }

            try {
                const res = await this.instance.request<IHttpResponse<R>>(param);
                const cntAttach = res.headers["content-disposition"];
                if (cntAttach) {
                    let filename = "";
                    const matchUtf8 = cntAttach.match(/filename\*=UTF-8'*([^;]+)/i);
                    if (matchUtf8) {
                        filename = matchUtf8[1];
                    } else {
                        const match = cntAttach.match(/filename="([^"]+)"/);
                        if (match) {
                            filename = match[1];
                        }
                    }
                    filename = decodeURIComponent(filename);
                    return fileDownload(res.data as any, filename || "unknown") as any;
                }

                if (res.data !== null && typeof res.data === "object") {
                    const code = res.data.code;
                    res.data.success = code === HttpResCode.Success;
                    if (!res.data.success && options.showFailTips !== false && !this.filterErrorTips.includes(code)) {
                        const customErrorMessage = options.customErrorMessage?.[code];
                        const msg = res.data.msg || "-";
                        const errorMsgKey = this.errorCodes[code];
                        const genericErrorMessage = options.genericErrorMessage;
                        if (customErrorMessage) {
                            this.showWarnTips(this.customI18n ? this.customI18n.t(customErrorMessage) : msg, false);
                        } else if (errorMsgKey) {
                            // prettier-ignore
                            this.showWarnTips(`${this.customI18n ? this.customI18n.t(errorMsgKey) : msg}`, false);
                        } else if (genericErrorMessage) {
                            this.showWarnTips(this.customI18n ? this.customI18n.t(genericErrorMessage) : msg, false);
                        } else {
                            this.showWarnTips(`[${code ?? "-"}] ${msg}`);
                        }
                    }
                }
				
                this.onResponseCallbacks.forEach(cb => cb(res.data));

                if (res.data.success && options.transformResponse) {
                    return options.transformResponse(res.data);
                }
                return res.data;
            } catch (err: any) {
                let errMsg;
                if (err.response.data !== null && typeof err.response.data === "object") {
                    // prettier-ignore
                    errMsg = `[${err.response.data.code ?? '-'}] ${err.response.data.msg || '-'}`;
                } else {
                    errMsg = `${err.response.status} ${err.message}`;
                }
                this.showErrorTips(i18n.t("translation:Http.requestError"), false);
                throw err;
            }
        };
    };

    public showWarnTips(content: string, showType: boolean = true) {
        // message.warning(showType ? `${i18n.t("translation:Http.requestFail")}: ${content}` : content);
    }

    public showErrorTips(content: string, showType: boolean = true) {
        message.error(showType ? `${i18n.t("translation:Http.requestError")}: ${content}` : content);
    }
}
