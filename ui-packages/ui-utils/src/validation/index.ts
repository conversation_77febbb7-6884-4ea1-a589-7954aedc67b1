export function validateEmail(email: string): boolean {
    return /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/.test(email);
}

export function validateMobilePhone(phone: string, countryCode: string): boolean {
    if (countryCode === "+86") {
        return /^1[3-9]\d{9}$/.test(phone);
    }

    else if (countryCode === "+852") {
        return /^[5689]\d{7}$/.test(phone);
    }

    else if (countryCode === "+1") {
        return /^[1-9]\d{9}$/.test(phone);
    }

    return false;
}

type ErrorMessage = 'validation.password.length' | 'validation.password.format';

export function validatePassword(password: string): { isValid: boolean; message?: ErrorMessage } {
    const lengthValid = password.length >= 8 && password.length <= 16;
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?\`~]/.test(password);
    const compositionValid = hasLowerCase && hasUpperCase && hasNumber && hasSpecialChar;

    if (!lengthValid && !compositionValid) {
        return {
            isValid: false,
            message: 'validation.password.length'
        };
    }

    else if (!lengthValid) {
        return { isValid: false, message: 'validation.password.length' };
    }

    else if (!compositionValid) {
        return { isValid: false, message: 'validation.password.format' };
    }

    return { isValid: true };
}