import i18n from "i18next";
import { getLang } from "../utils/getLang";
import enUS from "./en_US/index.json";
import zhCN from "./zh_CN/index.json";
import zhHK from "./zh_HK/index.json";

export const defaultNS = "translation";
export const resources = {
    en_US: {
        [defaultNS]: enUS
    },
    zh_CN: {
        [defaultNS]: zhCN
    },
    zh_HK: {
        [defaultNS]: zhHK
    }
} as const;

const customI18n = i18n.createInstance(
    {
        defaultNS,
        resources,
        lng: getLang(),
        fallbackLng: "zh_CN",
        debug: false,
        interpolation: {
            escapeValue: false
        }
    },
    () => {}
);

export default customI18n;
