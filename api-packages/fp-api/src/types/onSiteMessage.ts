export type MessageStatus = 'Read' | 'Unread';
export type OwnerType = 'Client_User' | 'Internal_User';
export type MessageCategory = string

export interface OnSiteMessage {
  id: string;
  noticeId: string | null;
  ownerId: string;
  ownerName: string;
  ownerType: OwnerType;
  title: string;
  content: string;
  messageStatus: MessageStatus;
  category: MessageCategory;
  sentBy: string;
  sentTime: string;
  receivedTime: string;
  readTime: string | null;
  url: string;
  extId: string;
  messagetype: "Announcement" | "Notification";
}

export interface OnSiteMessageListRequest {
  pageSize: string;
  pageNum: string;
  messageStatus?: MessageStatus;
  category?: MessageCategory;
  content?: string;
  orderByColumn?: "senttime";
  isAsc?: "ascending" | "descending";
  messagetype?: "Announcement" | "Notification";
}

export interface OnSiteMessageListResponse {
  records: OnSiteMessage[];
  total: string;
  size: string;
  current: string;
  pages: string;
}

export interface OnSiteMessageReadRequest {
  id: string;
  messageStatus: MessageStatus;
}

export interface CompanyUserAffiliationRequest {
  companyId: string;
}

export interface RejectAffiliationRequest {
  companyid: number;
} 