export interface SendEmailByForgotReq {
    email: string;
}

export interface SendEmailByForgotRes {
    token: string;
}

export interface ResetPwdByForgotReq {
    /** 邮箱 */
    userName?: string;
    /** 手机区号 */
    mobilePhoneAreaCode?: string;
    /** 手机号 */
    mobilePhone?: string;
    /** 新密码 */
    newPassword: string;
    /** 发送邮箱验证码后获取到的token */
    token: string;
    /** 邮箱验证码 */
    verificationCode: string;
}

export interface InitPwdByForgotReq {
    newPassword: string;
    token: string;
}

export interface SendMobileByForgotReq {
    mobile: string;
    countryCode: string;
}

export interface SendMobileByForgotRes {
    token: string;
}

export interface ResetPwdByMobileReq {
    newPassword: string;
    token: string;
    verificationCode: string;
}

