export interface Platform {
    id: number;
    platform: string;
    icon_path: string | null;
    partner_abbr: string;
    action: number;
}

export interface GetPlatformListReq {

}

export interface GetPlatformListRes {
    status: number;
    data: Platform[];
    message: string;

    map(param: (item: any) => { id: any; platform: any; partner_abbr: any; action: any; icon: string; sites: object }): any;
}

export interface PaymentGatewayEnum {
    'auth': 1;
}

export interface PaymentGateway {
    id: number;
    icon_path: string | null;
    partner_abbr: string;
    platform: string;
    action: PaymentGatewayEnum | number;
}

export interface GetPaymentGatewayListReq {

}

export interface GetPaymentGatewayListRes {
    status: number;
    data: PaymentGateway[];
    message: string;

    map(param: (item: any) => { id: any; platform: any; partner_abbr: any; action: any; icon: string }): any;
}

export interface GetAuthURLReq {
    platform_shop_id: number | null;
    platform_psp_id: number | null;
    redirect_url: string | null;
    meta_data: {
        scene?: string;
    }
}

export interface GetAuthURLRes {
    oauth_url: string;
    id_token: string;
    status: number;
    data: {
        oauth_url: string;
        id_token: string;
        oauth_request_id: number;
    };
    message: string;
}

export interface GetAuthCallbackResultReq {
    id_token: string;
}

export interface GetAuthCallbackResultRes {
    status: number;
    data: object;
    message: string;
}

export interface OauthShopItem {
    id: number;
    user_id: number;
    seller_id: string;
    platform_id: number;
    platform: string;
    choose_psp: {
        account_id: string;
        oauth_psp_id: number;
        platform: string;
        platform_id: number;
        oauth_status: number;
    };
    is_occupation: number;
    oauth_status: number;
    created_at: string;
    updated_at: string;
    can_delete: number;
    credit_status: number;
}

export interface OauthPspItem {
    icon?: string | undefined;
    id: number;
    user_id: number
    account_id: string
    platform_id: number;
    platform: string;
    oauth_status: string;
    created_at: string;
    updated_at: string;
}

export interface GetOauthShopListReq {

}

export interface GetOauthShopListRes {
    shop: OauthShopItem[];
    psp: OauthPspItem[];
    ouath_change: number;
}

export interface bindPSPtoShopReq {
    platform_shop_id?: number;
    shop_id?: number;
    bind_psp_id: number;
}

export interface bindPSPtoShopRes {
    status: number;
    data: bindPSPtoShopReq[];
    message: string;
}