export interface BankAccount {
    id?: string;
    companyId: string;
    companyName?: string;
    region?: string;
    relationType: string;
    bankName: string;
    accountName: string;
    accountNumber: string;
    branchName?: string;
    swiftCode: string;
    currency: string;
    address?: string;
    verifiedStatus?: string;
    type?: "CP";
}

export interface PSPAccount {
    id?: string;
    companyId: string;
    companyName?: string;
    region: string;
    relationType: string;
    platformName: string;
    accountName: string;
    accountNumber: string;
    currency: string;
    verifiedStatus?: string;
    authorizedStatus?: string;
    type?: "CP";
}
