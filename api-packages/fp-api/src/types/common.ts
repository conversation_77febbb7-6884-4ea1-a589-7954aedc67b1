export interface PaginationRequest {
    pageSize: number;
    pageNum: number;
	orderByColumn?: string;
    isAsc?: string | boolean;
}

export interface PaginationResponse<T> {
    total: number;
    size: number;
    current: number;
    pages: number;
    records: T[];
}

export interface GetRegionDataReq {
    /** 1: region, 27: currency, 28: bankName */
    typeId: string;
}

export interface CommonRegionData {
    id: string;
    name: string;
    nameChi: string;
    riskPoint: string;
    status: string;
}

export type GetRegionDataRes = CommonRegionData[];

export interface GetCurrencyRateReq extends PaginationRequest {
    orderByColumn?: string;
    isAsc?: string;
    fxType?: string;
    fxStandard?: string;
    currency?: string;
    startDate?: string;
    endDate?: string;
}

export interface CurrencyRateVO {
    date: string;
    fxStandard: string;
    fxType: string;
    sourceType: string;
    currency: string;
    dynamicCurrencyList: {
        currency: string;
        rate: string;
    }[];
    updateTime: string;
}

export type GetCurrencyRateRes = PaginationResponse<CurrencyRateVO>;
