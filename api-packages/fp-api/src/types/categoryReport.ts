export interface getReportKeywordRes {
    code: number;
    msg: string | null;
    data: [string];
}

export interface postReportReq {
    keywords: [string];
}

export interface postReportRes {
    code: number;
    msg: string | null;
    data: []
}

export interface reportResult {
    data: [reports];
    status_result: boolean;
}
export interface reports {
    no: number; // 序号
    keyword: string; // 关键词
    report_number: string; //报告编号
    report_link: string;
}

export interface getReportRes {
    code: number;
    data: reportResult;
}
