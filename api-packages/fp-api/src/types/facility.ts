export type FacilityDetailReq = {
  id: string;
};

export type FeeDetail = {
  id: string;
  rateType: string;
  currency: string;
  baseRateType: string;
  accrualBasis: string | null;
  interestRate: string;
  minInterestRate: string | null;
  maxInterestRate: string | null;
};

export type RepaymentMethod = {
  id: string;
  templateName: string;
  templateId: string;
  repaymentTemplateId: string;
  loanType: string;
  repaymentMethod: string;
  minRepaymentAmount: string;
  repaymentSequence: string;
  maxTenorUnit: string;
  maxTenorValue: string | null;
  accrualFrequency: string;
  accrualBasis: string;
  minTenorUnit: string;
  minTenorValue: string | null;
  repaymentTiming: string;
  allowOverdue: string;
  gracePeriodUnit: string;
  gracePeriodValue: number;
  overdueInterest: string;
  baseRateType: string;
  maxOutstanding: string | null;
  feeDetailList: FeeDetail[];
};

export type FacilityDetailRes = {
  id: string;
  creditFacilityId: string;
  facilityStatus: string;
  effectiveDate: string;
  nextReviewDate: string;
  companyCode: string;
  companyName: string;
  productCode: string;
  productNameCn: string;
  productNameEn: string | null;
  productType: string;
  facilityCurrency: string;
  facilityLimitAmount: string;
  watermarkAmount: string;
  facilityFrozenLimitAmount: string;
  currentOutstandingAmount: string;
  pendingDrawdownAmount: string;
  facilityAvailableFundingAmount: string;
  repaymentMethodList: RepaymentMethod[];
};

export type FacilityDetailResponseData = {
  code: number;
  msg: string | null;
  data: FacilityDetailRes;
}; 