export interface GetCompanyUsersReq {
    pageNum: number;
    pageSize: number;
    companyId: string;
	/** CP/UP */
	queryChannel: string;
}

export interface GetCompanyUsersRes {
    records: CompanyUser[];
    total: string;
    size: string;
    current: string;
    pages: string;
}

export interface CompanyUser {
    id: string;
    userId: string;
    userName: string;
    companyId: string;
    companyNameEn: string;
    companyNameCn: string;
    companyIdvLevel: string;
    regScenario: string;
    email: string;
	mobilePhoneAreaCode: string;
    mobile: string;
    affiliationType: string;
    updateByName: string;
    updatedDate: string;
    createdDate: string;
    joinTime: string;
    status: "Active" | "Confirming";
    roleList: {
        roleId: string;
        roleName: string;
    }[];
}

export interface SearchUsersReq {
    mobilePhoneAreaCode?: string;
    mobilePhoneNumber?: string;
    email?: string;
}

export interface SearchUserRes {
    id: string;
    userName: string;
    email: string;
    mobilePhoneAreaCode: string;
    mobilePhoneNumber: string;
    mobile: string | null;
    externalId: string;
    companyId: string;
    registeredScenario: string;
    registeredChannel: string;
    status: string;
    userId: string;
    changedDate: string | null;
    systemOwner: string | null;
    systemOwnerName: string | null;
    systemChangedBy: string | null;
    systemChangedByName: string | null;
    createdDate: string | null;
    companyList: string[] | null;
}

export interface RemoveCompanyUserReq {
    companyId: string;
    userId: string;
}

export interface RoleInfo {
    roleId: string;
    roleName: string;
}

export interface UpdateUserRoleReq {
    roleId: string;
    clientUserCompanyId: string;
}

export interface SetDefaultCompanyReq {
    userId: string;
    companyId: string;
}

export interface AddUserToCompanyReq {
    userId: string;
    companyId: string;
}
