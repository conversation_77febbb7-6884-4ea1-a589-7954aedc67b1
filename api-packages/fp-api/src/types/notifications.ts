export interface CompanyNotification {
  id: number;
  title: string;
  content: string;
  level: string;
  is_read?: boolean;
  read_at?: string | null;
  link?: string | null;
  code?:string;
}

export interface CompanyNotificationsListResponse {
  code: 0;
  data: {
    current_page: number;
    data: CompanyNotification[]; // Array of CompanyNotification objects
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: Array<
      {
        url: string | null;
        label: string;
        active: boolean
      }>;
    next_page_url: null;
    path: string;
    per_page: number;
    prev_page_url: null;
    to: number;
    total: number
    };
    message: string
}

export interface deleteNotificationResponse {
  code: number;
  data:any;
  message: string
  
}