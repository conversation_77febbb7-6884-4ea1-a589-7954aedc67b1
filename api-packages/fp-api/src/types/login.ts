export interface UserInfo {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    phone_country_code: string;
    phone_number: string;
    company_name: string;
    password: string;
    status: number;
    ap_cid: number | null;
    api_token: string;
    token_expiration: string;
    created_by: number;
    updated_by: number;
    created_at: string;
    updated_at: string;
    is_manage: number;
    email_verified: number;
    company_main_body: string | null;
    company_register_number: string;
    register_source: string;
    ap_account: number;
    follow_up_status: number;
    campaign_id: number;
    apply_no: string;
    apply_status: number;
    company_id: number;
    walmart_shop_count: number;
    route: any[];
    is_activated?: number;
}

export interface LoginReq {
    /** 账号 */
    email?: string;
    /** 密码 */
    password: string;
    /** 验证码 */
    validateCode?: string;
    /** 验证码uuid */
    uuid?: string;
    /** 手机区号 */
    mobilePhoneAreaCode?: string;
    /** 手机号 */
    mobilePhone?: string;
	/** 用户当前设备的时区 */
	timeZone?: string;
}

export interface LoginRes {
    token: string
    user: UserInfo,
    menu: any,
    access_token: string,
    source_index_path: string,
    is_skip: boolean
}

export interface ReSendInitEmailReq {
    email?: string;
    mobilePhone?: string;
    mobilePhoneAreaCode?: string;
	/** 图片验证码参数 */
	uuid?: string;
	code?: string;
}

export interface SendResetPasswordEmailReq{
    email: string;
    host_name: string;
    reset_page: string;
}