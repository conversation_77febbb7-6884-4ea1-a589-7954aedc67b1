import {UserInfo} from "./login";

export interface SignupReq {
    phone_country_code: string;
    phone_number: string;
    phone_code: string;
    email: string;
    password: string;
    password_confirmation: string;
    register_source: string;
}

export interface SignupValidateTokenReq {
    phone_country_code: string;
    phone_number: string;
}

export interface SignupValidateTokenRes {
    code: number;
    data: any;
    message: string;
}

export type SignupRes = {
    menu: Array<any>;
    user: {
        id: string;
        status: string;
        email: string;
        first_name: string;
        last_name: string;
        phone_country_code: string;
        register_source: string;
        campaign_id: number;
        created_at: string;
        updated_at: string;
        login:{
            token: string;
            user: UserInfo
        }
    };
    token: string;
}