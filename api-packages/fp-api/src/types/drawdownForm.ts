export interface CountryRegion {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface IndustryService {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface MobileAreaCode {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface EntityType {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface BusinessNature {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface BankAccount {
  key_id: number;
  name: string;
  name_chi: string;
  show_name: string;
}

export interface CharacterTitle {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface ConnectedPersonCapacity {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface ConnectedCompanyCapacity {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface FundingSource {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface InitialSourceOfWealth {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface ContinuousSourceOfWealth {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface UboSourceOfWealth {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface CertificateType {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface MajorCommoditiesType {
  key_id: number;
  name: string;
  name_chi: string;
}

export interface CompanyConfigData {
  country_region: CountryRegion[];
  industry_service: IndustryService[];
  mobile_area_code: MobileAreaCode[];
  entity_type: EntityType[];
  business_nature: BusinessNature[];
  character_title: CharacterTitle[];
  connected_person_capacity: ConnectedPersonCapacity[];
  connected_company_capacity: ConnectedCompanyCapacity[];
  funding_source: FundingSource[];
  initial_source_of_wealth: InitialSourceOfWealth[];
  continuous_source_of_wealth: ContinuousSourceOfWealth[];
  ubo_source_of_wealth: UboSourceOfWealth[];
  certificate_type: CertificateType[];
  major_commodities_type: MajorCommoditiesType[];
  bank_account: BankAccount[];
}

export interface GetCompanyConfigRes {
  code: number;
  data: CompanyConfigData;
  message: string;
}

export interface DebitAccountRequest {
  country: number;
  company_name_english: string;
  company_name_chinese: string;
  brn: string;
  currency: string;
  disburse_amount: number | null;
  bank_account_name: string;
  bank_account_number: string;
  bank_name: string;
  swift: string;
  bank_address: string;
}

export interface DebitAccountResponse {
  status: number;
  data: any[];
  message: string;
}

export interface GetDebitAccountResponse {
  country: string;
  company_name_english: string;
  company_name_chinese: string;
  brn: string;
  currency: string;
  disburse_amount: string;
  bank_account_name: string;
  bank_account_number: string;
  bank_name: string;
  swift: string;
  bank_address: string;
}

export interface BusinessInfoRequest {
  top_three_buyer_country: number[];
  top_three_suppliers_country: number[];
  funding_sources_country: number;
  initial_wealth_source: number[];
  continuous_wealth_income_source: number[];
  funding_sources: number[];
  major_commodities?: string;
  industry_service?: number;
}

export interface BusinessInfoData {
  top_three_buyer_country: number[];
  top_three_suppliers_country: number[];
  funding_sources_country: string;
  initial_wealth_source: string[];
  continuous_wealth_income_source: string[];
  funding_sources: string[];
  major_commodities?: string;
  industry_service?: string;
}

export interface BusinessInfoResponse {
  status: number;
  data: BusinessInfoData;
  message: string;
}

export interface UploadFileRequest {
  files: File | File[];
  dst: string;
}

export interface UploadFileResponse {
  code: number;
  message: string;
  data: {
    file_id: number;
    file_path: string;
    file_name: string;
    file_size: string;
    file_mime: string;
    file_link: string;
  };
}

export interface SaveSignerInfoRequest {
  front_id_file_id: number;
  back_id_file_id: number;
  region_code: number;
  phone_region_code: string;
  phone_number: string;
  email: string;
  front_file_id?: number;
  back_file_id?: number;
  papers_type?: string;
}

export interface DeleteSignerFileRequest {
  front_id_file_id?: string;
  back_id_file_id?: string;
  front_file_id?: string;
  back_file_id?: string;
}

export interface SignerInfo {
  seq: string;
  position: string;
  full_name_chn: string;
  full_name_eng: string;
  region_code: number;
  papers_type: string;
  phone_region_code: string;
  phone_number: string;
  email: string;
  front_id_file_id: number;
  back_id_file_id: number;
  id_number: string;
  id_name: string;
  id_name_en: string;
  front_file_id?: number;
  back_file_id?: number;
  papers_number: string;
  papers_name: string;
  papers_name_en: string;
  papers_data: Record<string, any>;
}

export interface SaveSignerInfoResponse {
  code: number;
  message: string;
  data: SignerInfo;
}

export interface InitiateSignErrorData {
  seq: number;
  field: string;
  want: string | number;
  got: string | number;
}

export interface InitiateSignRpaErrorData {
  full_name_chn: string;
  full_name_eng: string;
  rpa_check_result: string;
}

export interface InitiateSignResponse {
  code: number;
  message: string;
  data: InitiateSignErrorData[] | InitiateSignRpaErrorData[] | null;
}

export interface UrgeSignResponse {
  code: number;
  message: string;
  data: any[] | null;
}

export interface SignRecord {
  id: number;
  company_id: number;
  company_agreement_id: number;
  member_id: number;
  docs_num: number;
  sign_status: number;
  sign_date: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface GetSignerInfoData {
  id: number;
  company_id: number;
  seq: number;
  position: string[];
  full_name_chn: string;
  full_name_eng: string;
  region_code: string;
  papers_type: string;
  phone_region_code: string;
  phone_number: string;
  email: string;
  front_id_file_id: number;
  frontIDPreviewLink: string;
  back_id_file_id: number;
  backIDPreviewLink: string;
  front_file_id: number;
  frontPreviewLink: string;
  back_file_id: number;
  backPreviewLink: string;
  papers_number: string;
  papers_name: string;
  papers_name_en: string;
  papers_data: Record<string, any>;
  counter_fraud_status: number;
  counter_fraud_time: string;
  rpa_check_result: number;
  sign_record: SignRecord;
  sign_status: number;
  front_id_file_name: string;
  back_id_file_name: string;
  front_file_name: string;
  back_file_name: string;
  needSign: boolean;
  company_agreement_detail_status: number;
  company_agreement_failed_reason: string | null;
  address: string;
}

export interface GetSignerInfoResponse {
  code: number;
  message: string;
  data: GetSignerInfoData[];
}

/**
 * Response for email notification settings
 * Type values:
 * - 1: NO_HONG_KONG_COMPANY (没有香港公司)
 * - 2: NO_HONG_KONG_BANK_ACCOUNT (没有香港银行)
 */
export interface GetEmailNotifyResponse {
  status: number;
  data: any[];
  message: string;
}

export interface GetShareholderStructureResultResponse {
  code: number;
  message: string;
  data: {
    telphone: "Y" | "N";
    psp: "Y" | "N";
    qcc: "Y" | "N";
    qcc_sharehold: "Y" | "N";
  }
}

export enum EmailNotifyType {
  NO_HONG_KONG_COMPANY = 1,
  NO_HONG_KONG_BANK_ACCOUNT = 2
}

export interface CompanyInfoRequest {
  country: number,
  company_name_english?: string;
  company_name_chinese?: string;
  brn?: string;
}

export interface CompanyInfoResponse {
  status: number;
  data: any[];
  message: string;
}

/**
 * Response type for RPA email API
 */
export interface SendRpaEmailResponse {
  code: number;
  message: string;
  data?: any;
}

/**
 * Response type for retry-sign API
 * code: 0 = success, 1 = failure
 * message: Success message or error description
 * data: Optional array of additional data
 */
export interface RetrySignResponse {
  code: number;
  message: string;
  data?: any[];
}

export interface ChangePhoneNumberRequest {
  phone_number: string;
  phone_country_code: string;
  phone_code: string;
}

export interface ChangePhoneNumberResponse {
  code: number;
  message: string;
  data?: any[];
}