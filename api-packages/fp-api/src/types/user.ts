export interface GetUserInfoRes {
    id: string;
    userId: string;
    userName: string;
    email: string;
    mobilePhoneAreaCode?: string;
    mobilePhoneNumber?: string;
	language?: string;
}

export interface UpdateUserInfoReq {
    id: string;
    userName?: string;
    mobilePhoneAreaCode?: string;
    mobilePhoneNumber?: string;
    phoneCode?: string;
    email?: string;
    emailCode?: string;
    language?: string;
	uuid?: string;
    type: ("username" | "password" | "mobile" | "email" | "language")[];
}

export type UpdateUserInfoRes = boolean

export interface UpdateUserPasswordReq {
    id: string;
    oldPassword: string;
    newPassword: string;
}

export type UpdateUserPasswordRes = {
    [key: string]: any;
}

export type GetUserRedirectToTABFTokenRes = {
    links: string;
}