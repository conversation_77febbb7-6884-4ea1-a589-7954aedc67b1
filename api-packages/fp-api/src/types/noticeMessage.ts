import { PaginationRequest, PaginationResponse } from './common';

export enum MessageStatus {
  Read = 'Read',
  Unread = 'Unread'
}

export enum OwnerType {
  Client_User = 'Client_User',
  Internal_User = 'Internal_User'
}

export enum MessageCategory {
  Verification = 'Verification'
  // Add other categories as needed
}

export interface NoticeMessage {
  id: string;
  noticeId: string | null;
  ownerId: string;
  ownerName: string;
  ownerType: OwnerType;
  title: string;
  content: string;
  messageStatus: MessageStatus;
  category: MessageCategory;
  sentBy: string;
  sentTime: string;
  receivedTime: string;
  readTime: string | null;
  url: string;
  extId: string;
}

export interface NoticeMessageListRequest extends PaginationRequest {
  messageStatus?: MessageStatus;
  category?: OwnerType;
}

export type NoticeMessageListResponse = PaginationResponse<NoticeMessage>;

export interface NoticeMessageReadRequest {
  id: string;
  messageStatus: MessageStatus;
}

export interface NoticeMessageSearchRequest {
  pageSize: string;
  pageNum: string;
  messageStatus?: MessageStatus;
  category?: MessageCategory;
  content?: string;
  orderByColumn?: "senttime";
  isAsc?: "ascending" | "descending";
} 