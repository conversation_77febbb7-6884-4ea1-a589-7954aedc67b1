export interface GetLimitRes {
  approval_limit: number;
  available_limit: number;
}

export interface GetCreditApplicationRes {
  credit_no: string;              // 申请编号
  stage: 'calculating' | 'account_info' | 'info_confirm' | 'info_processing' | 'signing' | 'waiting_result' | 'loaning' | 'finished'|'psp_checking_done';
  type: 'xdj' | 'automation' | 'limit_increase';
  pre_limit: number;              // 预授信额度
  approval_limit: number;         // 审批额度
  pre_wml: number;                // 预授信水位
  is_first: boolean;              // 是否首次申请
  need_sign_contract: boolean;    // 是否需要签约
  status: 'active' | 'pending' | 'submitted' | 'failed' | 'deprecated'; // 状态
  reason: string;                 // 原因
  psp_check_status:number;
  control_check_result:boolean;
  thirdparty_check_result:boolean
}
export interface SubmitCreditApplicationRes {
  credit_no: string;              // 申请编号
  stage: 'calculating' | 'account_info' | 'info_confirm' | 'info_processing' | 'signing' | 'waiting_result' | 'loaning' | 'finished'|'psp_checking_done';
  type: 'xdj' | 'automation' | 'limit_increase'; // 类型
  pre_limit: number;              // 预授信额度
  approval_limit: number;         // 审批额度
  is_first: boolean;              // 是否首次申请
  need_sign_contract: boolean;    // 是否需要签约
  status: 'active' | 'pending' | 'submitted' | 'failed' | 'deprecated'; // 状态
  reason: string;                 // 原因
  pre_wml?: number;
}

export interface ActivateLimitRes {
  credit_no: string;              // 申请编号
  stage: 'calculating' | 'account_info' | 'info_confirm' | 'info_processing' | 'signing' | 'waiting_result' | 'loaning' | 'finished'|'psp_checking_done';
  type: 'xdj' | 'automation' | 'limit_increase'; // 类型
  pre_limit: number;              // 预授信额度
  approval_limit: number;         // 审批额度
  is_first: boolean;              // 是否首次申请
  need_sign_contract: boolean;    // 是否需要签约
  status: 'active' | 'pending' | 'submitted' | 'failed' | 'deprecated'; // 状态
  reason: string;                 // 原因
}
