import type { PaginationRequest, PaginationResponse } from "./common";

export interface PlatformVO {
    id: string;
    img: string;
    platform: string;
    type: string;
    authType: "api" | "account";
}

export interface AuthorizedShopByOpenAPIReq {
    id?: string;
    platform: string;
    pspType: string;
    shopName: string;
}

export interface AuthorizedShopByOpenAPIRes {
    directUrl: string;
}

export interface GetShopListReq extends PaginationRequest {
	/** UP/CP */
	queryChannel?: string;
    shopName?: string;
    shopId?: string;
    platform?: string;
    pspType?: string;
    authorizedStatus?: string;
    authorizedDateStart?: string;
    authorizedDateEnd?: string;
    expiredDateStart?: string;
    expiredDateEnd?: string;
}

export interface ShopVO {
    id: string;
    shopId: string;
    shopName: string;
    platform: string;
    pspType: string;
    authorizedStatus: string;
    authorizedTime: string;
    expiryDate: string;
}

export type GetShopListRes = PaginationResponse<ShopVO>;

export interface AddShopAccountReq {
    platform: string;
    accountList: {
        // id?: string;
        shopName: string;
        accountLoginName: string;
        accountLoginPwd: string;
        accountLoginUrl: string;
    }[];
}

export interface EditShopAccountReq {
    id: string;
    shopName: string;
    accountLoginName: string;
    accountLoginPwd: string;
    accountLoginUrl: string;
}

export interface GetShopAccountListReq extends PaginationRequest {
    orderByColumn?: string;
    isAsc?: string|boolean;
    shopName?: string;
    platform?: string;
    accountLoginName?: string;
    authorizedStatus?: string;
}

export interface ShopAccountVO {
    id: string;
    shopName: string;
    accountLoginName: string;
    accountLoginPwd: string;
    accountLoginUrl: string;
    platform: string;
    authorizedStatus: string;
    systemChangedDate: string;
}

export type GetShopAccountListRes = PaginationResponse<ShopAccountVO>;

export interface MigrateAuthorizedShopReq {
    id: string;
    companyId: string;
}
