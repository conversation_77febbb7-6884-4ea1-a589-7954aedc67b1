export interface GetRedPocketListReq {
    status?: RedPocketStatus | string
}

export type RedPocketStatus = "claimed" | "active" | "used" | "expired" | "invalid";

export interface RedPocket {
  id: number;
  type: string;
  value: number;
  currency: string;
  expired_at: string;
  status: string;
  claimed_at: string;
  activated_at: string;
  used_at: string;
  remaining_quantity?: number;
  can_use?: boolean;
}

export type GetRedPocketListRes = RedPocket[];


export interface GetRedPocketByIdRes {
    remaining_quantity: number;
    type: string;
    valid_from: string;
    valid_to: string;
}

export interface ClaimRedPocketRes {
    result: string;
}