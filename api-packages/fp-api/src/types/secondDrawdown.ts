export interface BankListResponse{
    id: number,
    bank_name: string,
    bank_account_name: string,
    bank_account_number: string,
    bank_account_currency: string,
    bank_address: string,
    swift: string,
    bank_country_code: string

}
export type BankList = BankListResponse[];



export interface DrawdownRequest {
    currency:string,
    amount:number,
    bank_id:number,
    reference_number:string

}


export interface DrawdownResponse {
    code: number;
    data: any;
    message: string;
  }
  
export interface InterestRateResponse{
    interest_rate: number;
    sofr_rate:number;
    date:string;
    currency:string;
    product_name:string;
    tenor_day:number;
    floating_rate_type: number,
    minimum_drawdown_amount: number
}

export interface DirectorInfoResponse{
    directors_name:string,
    directors_name_chi:string
}