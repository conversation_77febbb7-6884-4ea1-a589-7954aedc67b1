import type { PaginationRequest, PaginationResponse } from "./common";

export interface GetFacilitySummaryReq {
    id: string;
}

export interface GetFacilitySummaryRes {
    userId: string;
    facilityCurrency: string;
    facilityLimitAmount: string;
    facilityUsedLimitAmount: string;
    facilityAvailableFundingAmount: string;
    watermarkAmount: string;
    currentOutstandingAmount: string;
}

export interface GetFacilityListReq extends PaginationRequest {
    orderByColumn?: string;
    isAsc?: string;
    creditFacilityId?: string;
    companyName?: string;
    productName?: string;
    effectiveDateStart?: string;
    effectiveDateEnd?: string;
}

export interface FacilityListVO {
    id: string;
    facilityStatus: string;
    companyId: string;
    productCode: string;
    financingType: string;
    facilityCurrency: string;
    facilityLimitAmount: string;
    watermarkAmount: string;
    currentOutstandingAmount: string;
    facilityAvailableFundingAmount: string;
}

export type GetFacilityListRes = PaginationResponse<FacilityListVO>;
