import { PaginationRequest, PaginationResponse } from "./common";

// Charge Listing Types
export interface ChargeListingRequest extends PaginationRequest {
  orderByColumn?: string;
  isAsc?: string;
  companyId: string;
}

export interface SubCharge {
  id: string;
  subOrderCode: string;
  sourceOrderId: string;
  companyCreditFacilityLimitId: string;
  companyCreditFacilityLimitCode: string;
  collectionMethod: string;
  receivedAmount: string;
  outstandingAmount: string;
  pendingAmount: string;
  orderStatus: string;
  invoiceStatus: string;
  systemCreatedDate: string;
}

export interface Charge {
  id: string;
  orderCode: string;
  sourceOrderId: string;
  creditUnderwritingId: string;
  companyCreditFacilityLimitId: string;
  companyCreditFacilityLimitCode: string;
  collectionMethod: string;
  description: string;
  feeType: string;
  feeRate: string;
  feeCurrency: string;
  totalFeeAmount: string;
  outstandingAmount: string;
  receivedAmount: string;
  pendingAmount: string;
  orderStatus: string;
  systemCreatedDate: string;
  subChargeList: SubCharge[];
}

export interface ChargeListingResponse {
  total: number;
  size: number;
  current: number;
  optimizeCountSql: string;
  searchCount: string;
  pages: number;
  records: Charge[];
}

// Download Invoice Types
export interface DownloadInvoiceRequest {
  id?: string;
  ids?: string[];
}

export interface DownloadInvoiceResponse {
  data: string;
} 