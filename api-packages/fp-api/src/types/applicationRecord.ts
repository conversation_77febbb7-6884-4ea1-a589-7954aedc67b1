export interface WithdrawApplicationRecordReq {
  sourceId: string;
  applyCategory?: string;
  applyRemark?: string;
  approvalId: string;
  source: string;
  applyUserId?: string;
  applyUserName?: string;
}

export interface WithdrawApplicationRecordRes {
  code: number;
  msg: string;
  data: boolean;
}

export interface ApplicationRecordListReq {
  pageSize: number;
  pageNum: number;
  orderByColumn?: string;
  isAsc?: string;
  companyId?: string;
  approvalName?: string;
  productName?: string;
  productId?: string;
  createStartTime?: string;
  createEndTime?: string;
}

export interface ApplicationRecordItem {
  sourceId: string;
  source: string;
  approvalId: string;
  approvalName: string;
  productNameCn: string;
  productNameEn: string;
  productCode: string;
  productId: string;
  productVersion: number;
  status: string;
  createTime: string;
}

export interface ApplicationRecordListData {
  records: ApplicationRecordItem[];
  total: number;
  size: number;
  current: number;
  optimizeCountSql: string;
  searchCount: string;
  pages: number;
}

export interface ApplicationRecordListRes {
  code: number;
  msg: string;
  data: ApplicationRecordListData;
} 