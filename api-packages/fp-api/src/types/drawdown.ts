export interface GetDebitAccountRes {
    status: number;
    message?: string;
    data?: {
        countryRegion: string;
        drawdownCurrency: string;
        drawdownAmount: string;
        bankAccountName: string;
        bankAccountNumber: string;
        bank: string;
        swiftCode: string;
        bankAddress: string;
    };
}

export interface SubmitDebitAccountReq {
    countryRegion: string;
    drawdownCurrency: string;
    drawdownAmount: string;
    bankAccountName: string;
    bankAccountNumber: string;
    bank: string;
    swiftCode: string;
    bankAddress: string;
} 