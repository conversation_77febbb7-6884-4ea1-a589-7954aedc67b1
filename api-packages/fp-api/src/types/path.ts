/**
 * Types for path-related API endpoints
 */

/** 
 * Request type for storing user's last visit path
 */
export interface StorePathReq {
  path: string;
  path_name: string;
}

/**
 * Response data for path storage
 */
export interface PathData {
  path: string;
  path_name: string;
  created_at: string;
}

/**
 * Response type for store path API
 */
export interface StorePathRes {
  status: number;
  data: PathData;
  message: string;
}

/**
 * Response type for get path API
 */
export interface GetPathRes {
  status: number;
  data: PathData;
  message: string;
} 