export interface GetUserCompanyReq {
    userId: string;
}

export interface CompanyInfo {
    brn: string;
    companyId: string;
    cr: string;
    id: string;
    isDefault: boolean;
    kycCompanyType: string;
    kycStatus: string;
    region: string;
    registerNameCn: string;
    registerNameEn: string;
    roleId: string | null;
    roleName: string | null;
    uscc: string;
}

export type GetUserCompanyRes = CompanyInfo[];

export interface ChangeDefaultCompanyReq {
    userId: string;
    companyId: string;
}

export interface AddOrDelCompanyReq {
    clientUserEditDTO: {
        userId: string;
    };
    affiliatedCompanyEditDTOList?: {
        /** add empty */
        id?: string;
        companyId?: string;
        userId: string;
        region?: string;
        uscc?: string;
        brn?: string;
        cr?: string;
        registerNameEn?: string;
        registerNameCn?: string;
        incorporationDate?: string | null;
    }[];
    /** need delete companyId */
    deletedIds?: string[];
    /** CP */
    applyChannel: string;
    opType: "create" | "delete";
}

export type AddOrDelCompanyRes = string[];

export interface KycApplicationVO {
    id: string;
    approveId: string;
    kycBizId: string;
    kycApplyType: string;
    riskRating: string;
    kycNextReviewDate: string;
    approvalDate: string;
    createdTime: string;
    status: string;
}

export interface GetCompanyProfileRes {
    companyVO: {
        id: string;
        companyId: string;
        companyName: string;
        status: boolean;
        region: string;
        uscc: string;
        brn: string;
        cr: string;
        registerNameEn: string;
        registerNameCn: string;
        kycStatus: string;
        legalId: string;
        kycNextReviewDate: string;
        creditNextReviewDate: string;
        companyIdvLevel: string;
        lastKycId?: string;
    };
    kycApplicationList: KycApplicationVO[];
}

export interface CreateKYCDataReq {
    companyId: string;
    /** new: KYC01 */
    kycApplicationType: string;
    /** CP */
    kycApplicationChannel: string;
}

export interface CreateKYCDataRes {
    kycId: string;
}

export interface KYCCommonData {
    riskPointId: string;
    riskPointName: string;
    riskPointNameCN: string;
    riskPointItemId: string;
    riskPointItemName: string;
    riskPointItemNameCN: string;
    riskPointItemNameHongKong: string;
    sortNo: string;
}

export type GetKYCCommonDataRes = KYCCommonData[];

export interface SubmitKYCDataReq {
    id: string;
    /** CP */
    submitChannel: string;
}

export interface SubmitKYCDataRes {
    kycId: string;
    /** json key value格式 */
    validateFields?: string;
}

export interface SaveKYCDataReq {
    kycId: string;
    savedType: string;
    /** CP */
    applyChannel: string;
    nextStep?: string;
    generalInformationDTO?: GeneralInfo;
    businessInformationDTO?: BusinessInfo;
    connectedPartiesDTO?: {
        connectedPersonDTOList?: ConnectedParties["connectedPersonDTOList"];
        connectedCompanyDTOList?: ConnectedParties["connectedCompanyDTOList"];
    };
    riskAssessmentDTO?: {
        amlscreeningDTOList?: RiskAssessment["amlscreeningVOList"];
    };
    documentsDTO?: DocumentDataDTO;
}

export interface SaveKYCDataRes {
    /** json key value格式 */
    validateFields?: string;
}

interface GeneralInfo {
    id?: string;
    companyId?: string;
    companyType?: string;
    companyName?: string;
    region?: string;
    uscc?: string;
    brn?: string;
    cr?: string;
    kycApplyType?: string;
    registerNameEn?: string;
    registerNameCn?: string;
    incorporationDate?: string | null;
    registeredAddressRegin?: string;
    registeredAddressDetail?: string;
    principleBusiAddressType?: string;
    principleBusiAddressRegion?: string;
    principleBusiAddressDetail?: string;
    correspondenceAddressType?: string;
    correspondenceAddressRegion?: string;
    correspondenceAddressDetail?: string;
    kycStatus?: string;
    creditNextReviewDate?: string;
    amlScreeningSummary?: string;
    kycApplyChannel?: string;
}

interface BusinessInfo {
    id?: string;
    kycId?: string;
    latestAnnualSalesAmount?: number;
    latestAnnualSalesCurrency?: string;
    businessYear?: string;
    employeeNumber?: number;
    businessNatureCode?: string;
    businessNatureName?: string;
    goodsType?: string;
    goodsOther?: string;
    initialSourceResult?: string;
    ongoingSourceResult?: string;
    corporateSourceResult?: string;
    supplierThreeList?: Array<{
        id: string;
        kycId: string;
        partnerType: string;
        region: string;
        partnerName: string;
        status: number;
        sortNo: number;
    }>;
    buyerThreeList?: Array<{
        id: string;
        kycId: string;
        partnerType: string;
        region: string;
        partnerName: string;
        status: number;
        sortNo: number;
    }>;
    corporateFundsRegion?: string;
    industry?: string;
}

interface ConnectedParties {
    connectedPersonDTOList?: Array<{
        /** "add" | "update" | "delete" */
        opType: string;
        id?: string;
        kycId?: string;
        fullNameEn?: string;
        fullNameCn?: string;
        capacityRiskPointId?: string;
        capacityResult?: string;
        ownershipPercent?: string;
        position?: string;
        idType?: string;
        idTypeName?: string;
        idNumber?: string;
        birthday?: string | null;
        nationality?: string;
        currentResidentialRegion?: string;
        currentResidentialAddress?: string;
        permanentAddressType?: string;
        permanentAddressRegion?: string;
        permanentAddressDetail?: string;
        mobilePhoneAreaCode?: string;
        mobilePhoneNumber?: string;
        email?: string;
        experienceYearType?: string;
        uboSourceResult?: string;
        uboRegion?: string;
        /** 1: director, 2: shareholder */
        corporateParticipantType?: string;
    }>;
    connectedCompanyDTOList?: Array<{
        /** "add" | "update" | "delete" */
        opType: string;
        id?: string;
        kycId?: string;
        entityType?: string;
        incorporationRegion?: string;
        registeredNameEn?: string;
        registeredNameCn?: string;
        capacityResult?: string;
        idvType?: string;
        ownershipPercent?: string;
        brn?: string;
        cr?: string;
        uscc?: string;
        registeredAddressRegion?: string;
        registeredAddressDetail?: string;
        principleBusiAddressType?: string;
        principleBusiAddressRegion?: string;
        principleBusiAddressDetail?: string;
        correspondenceAddressType?: string;
        correspondenceAddressRegion?: string;
        correspondenceAddressDetail?: string;
        businessNature?: string;
        businessRegion?: string;
        industry?: string;
    }>;
}

export interface RiskAssessment {
    amlScreeningSummaryVO: {
        id?: string;
        kycId?: string;
        layerNumber?: number;
        jurisdictionNumber?: number;
        rationaleNumber?: number;
        answerResult?: string;
        amlScreeningSummary?: string;
    };
    amlscreeningVOList?: Array<{
        id?: string;
        kycId?: string;
        ownerType?: "KYC" | "Person" | "Entity";
        connectedId?: string;
        result?: string;
        searchDate?: string;
        isPep?: number;
        source?: string;
    }>;
    riskAnswerResultVOList?: Array<{
        id?: string;
        kycId?: string;
        connectedId?: string;
        riskPointId: string;
        riskPointItemId: string;
        answer?: string;
        status?: number;
    }>;
}

export interface CommonDocumentData {
    id?: string;
    kycId?: string;
    ownerType?: "KYC" | "Person" | "Entity";
    connectedId?: string;
    kycRiskPointItemId: string;
    kycRiskPointId: string;
    expiryDate?: string;
    docType?: string;
    docUrl?: string;
    docName?: string;
    fileSize?: number;
}

export interface DocumentDataDTO {
    companyDocumentsDTO?: {
        companyDocumentsDTOList?: CommonDocumentData[];
        amlScreeningResultDTOList?: CommonDocumentData[];
    };
    personDocumentsDTO?: {
        personDocumentsDTOList?: CommonDocumentData[];
        amlScreeningResultDTOList?: CommonDocumentData[];
    };
    entityDocumentsDTO?: {
        entityDocumentsDTOList?: CommonDocumentData[];
        amlScreeningResultDTOList?: CommonDocumentData[];
    };
}

export interface DocumentDataVO {
    companyDocumentsVO?: {
        companyDocumentsVOList?: CommonDocumentData[];
        amlScreeningResultVOList?: CommonDocumentData[];
    };
    personDocumentsVO?: {
        personDocumentsVOList?: CommonDocumentData[];
        amlScreeningResultVOList?: CommonDocumentData[];
    };
    entityDocumentsVO?: {
        entityDocumentsVOList?: CommonDocumentData[];
        amlScreeningResultVOList?: CommonDocumentData[];
    };
}

export interface GetKYCDataRes {
    kycId: string;
    /** json key value格式 */
    validateFields?: string;
    generalInformationVO: GeneralInfo;
    businessInformationVO: BusinessInfo;
    connectedPartiesVO: {
        connectedPersonVOList: ConnectedParties["connectedPersonDTOList"];
        connectedCompanyVOList: ConnectedParties["connectedCompanyDTOList"];
    };
    riskAssessmentVO: RiskAssessment;
    documentsVO: DocumentDataVO;
}

export interface GetKYCDesensitizeDataReq {
    kycId: string;
    applyChannel: string;
}
