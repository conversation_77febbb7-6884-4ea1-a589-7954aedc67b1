import request from "../request";
import { SignupReq, SignupValidateTokenReq, SignupValidateTokenRes, SignupRes } from "../types/signup";

export const SignupValidateToken = request.createService<SignupValidateTokenReq, SignupValidateTokenRes>({
    url: "api/user/phoneVerified",
    method: "post",
    useAuth: false,
    customErrorMessage: {
        "80004": "forgotPassword.userNotExist"
    }
});

export const Signup = request.createService<SignupReq, SignupRes>({
    url: "api/register",
    method: "post",
    useAuth: false,
});
