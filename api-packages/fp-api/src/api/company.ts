import request from "../request";
import {
    GetUserCompanyReq,
    GetUserCompanyRes,
    AddOrDelCompanyReq,
    AddOrDelCompanyRes,
    GetKYCCommonDataRes,
    CreateKYCDataReq,
    CreateKYCDataRes,
    GetKYCDataRes,
    SubmitKYCDataReq,
    SubmitKYCDataRes,
    SaveKYCDataReq,
    SaveKYCDataRes,
    GetCompanyProfileRes,
    ChangeDefaultCompanyReq,
    GetKYCDesensitizeDataReq
} from "../types/company";

export const GetUserCompany = request.createService<GetUserCompanyReq, GetUserCompanyRes>({
    url: "/metadata/client/user/affiliation/user/list",
    method: "post"
});

export const ChangeDefaultCompany = request.createService<ChangeDefaultCompanyReq, boolean>({
    url: "/metadata/clientuserCompany/set/default",
    method: "post"
});

export const AddOrDelCompany = request.createService<AddOrDelCompanyReq, AddOrDelCompanyRes>({
    url: "/metadata/clientuser/thirdparty/company/profile",
    method: "post"
});

export const GetCompanyProfile = request.createService<{ id: string }, GetCompanyProfileRes>({
    url: "/metadata/client/company/profile",
    method: "get"
});

export const CreateKYCData = request.createService<CreateKYCDataReq, CreateKYCDataRes>({
    url: "/sme/kyc/create",
    method: "post"
});

export const GetKYCData = request.createService<{ id: string }, GetKYCDataRes>({
    url: "/sme/kyc/detail",
    method: "get",
    beforeRequest(axiosParam) {
        axiosParam.url = `${axiosParam.url}/${axiosParam.params!.id}`;
        delete axiosParam.params;
        return axiosParam;
    }
});

export const GetKYCCommonData = request.createService<void, GetKYCCommonDataRes>({
    url: "/sme/risk/point",
    method: "get"
});

export const SaveKYCData = request.createService<SaveKYCDataReq, SaveKYCDataRes>({
    url: "/sme/kyc",
    method: "post"
});

export const SubmitKYCData = request.createService<SubmitKYCDataReq, SubmitKYCDataRes>({
    url: "/sme/kyc",
    method: "post",
    beforeRequest(axiosParam) {
        axiosParam.url = `${axiosParam.url}/${axiosParam.data!.id}`;
        // @ts-ignore
        delete axiosParam.data!.id;
        return axiosParam;
    }
});

export const GetKYCDesensitizeData = request.createService<GetKYCDesensitizeDataReq, GetKYCDataRes>({
    url: "/sme/kyc/detail/desensitize",
    method: "post"
});
