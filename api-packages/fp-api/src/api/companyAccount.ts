import request from "../request";
import type { BankAccount, PSPAccount } from "../types/companyAccount";

// Bank Account APIs
export const getBankAccountList = (companyId: string) => {
    return request.createService<{ companyId: string; type: "cp" | "up" }, BankAccount[]>({
        url: "/metadata/company/account/bank/list",
        method: "post"
    })({ companyId, type: "cp" });
};

export const saveBankAccount = (data: BankAccount) => {
    return request.createService<BankAccount, boolean>({
        url: "/metadata/company/account/bank/save",
        method: "post",
		trimParams: true
    })({ ...data, type: "CP" });
};

export const getBankAccountDetail = (id: string) => {
    return request.createService<void, BankAccount>({
        url: `/metadata/company/account/bank/get/${id}`,
        method: "post"
    })();
};

export const deleteBankAccount = (id: string) => {
    return request.createService<void, void>({
        url: `/metadata/company/account/bank/delete/${id}`,
        method: "delete"
    })();
};

// PSP Account APIs
export const getPSPAccountList = (companyId: string) => {
    return request.createService<{ companyId: string; type: "cp" | "up" }, PSPAccount[]>({
        url: "/metadata/company/account/psp/list",
        method: "post"
    })({ companyId, type: "cp" });
};

export const savePSPAccount = (data: PSPAccount) => {
    return request.createService<PSPAccount, boolean>({
        url: "/metadata/company/account/psp/save",
        method: "post",
		trimParams: true
    })({ ...data, type: "CP" });
};

export const getPSPAccountDetail = (id: string) => {
    return request.createService<void, PSPAccount>({
        url: `/metadata/company/account/psp/get/${id}`,
        method: "post"
    })();
};

export const deletePSPAccount = (id: string) => {
    return request.createService<void, void>({
        url: `/metadata/company/account/psp/delete/${id}`,
        method: "delete"
    })();
};
