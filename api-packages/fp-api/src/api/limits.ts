import request from "../request";
import {GetLimitRes, GetCreditApplicationRes, SubmitCreditApplicationRes, ActivateLimitRes} from "../types/limits";

/**
 * 取当前公司可用額度
 */
export const getUserLimits = request.createService<null, GetLimitRes>({
    url: "/api/credit/limit",
    method: "get",
});

/**
 * 获取客户当前的授信申请单
 */
export const getUserCreditApplication = request.createService<null, GetCreditApplicationRes>({
    url: "/api/credit/application",
    method: "get",
});


/**
 * 提交新的授信申请单
 */
export const submitUserCreditApplication = request.createService<void, SubmitCreditApplicationRes>({
    url: "/api/credit/limit/submit",
    method: "post",
});


/**
 * 立即激活额度
 */
export const activateLimit = (id: string) => {
    return request.createService<void, ActivateLimitRes>({
        url: `/api/credit/applications/${id}/active`,
        method: "post"
    })();
};

/**
 * 同步店鋪列表信息
 */
export const sync_auth_shop_status = request.createService<void, void>({
    url: "/api/oauth/sync/pgid",
    method: "post",
});
