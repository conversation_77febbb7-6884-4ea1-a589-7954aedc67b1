import request from "../request";
import {ClaimRedPocketRes, GetRedPocketByIdRes, GetRedPocketListReq, GetRedPocketListRes} from "../types/redPocket";

/**
 * 取当前公司可用/已领取的红包列表
 */
export const getRedPocketList = request.createService<GetRedPocketListReq, GetRedPocketListRes>({
    url: "/api/company/red-packets",
    method: "get",
});

/**
 * 获取红包信息
 */
export const getRedPocketById =
    request.createService<{ id: number }, GetRedPocketByIdRes>({
        url: "/api/red-packets/{id}",
        method: "get",
        beforeRequest: (axiosParam) => {
            const id = (axiosParam.params as any)?.id;
            if (id == null) {
                throw new Error("Missing `id` for `GetRedPocketById`");
            }
            axiosParam.url = axiosParam.url!.replace("{id}", encodeURIComponent(String(id)));
            const {id: _, ...rest} = (axiosParam.params as any);
            axiosParam.params = rest as any;
            return axiosParam;
        },
    });


/**
 * 领取红包
 */
export const claimRedPocket =
    request.createService<{ id: number }, ClaimRedPocketRes>({
        url: "/api/red-packets/{id}",
        method: "post",
        beforeRequest: (param) => {
            const source = (param.params ?? param.data) as any;
            const id = source?.id;
            if (id == null) {
                throw new Error("Missing `id` for `ClaimRedPocket`");
            }

            param.url = param.url!.replace("{id}", encodeURIComponent(String(id)));

            if (param.params) {
                const {id: _, ...rest} = source;
                param.params = rest;
            }
            if (param.data) {
                const {id: _, ...rest} = source;
                param.data = rest;
            }

            return param;
        },

    });
