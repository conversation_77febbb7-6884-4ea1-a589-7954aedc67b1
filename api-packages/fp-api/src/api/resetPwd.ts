import request from "../request";
import { SendEmailByForgotReq, SendEmailByForgotRes, ResetPwdByForgotReq, InitPwdByForgotReq, SendMobileByForgotReq, SendMobileByForgotRes, ResetPwdByMobileReq } from "../types/resetPwd";

/**
 * 老用户忘记密码发送邮件验证
 */
export const SendEmailByForgot = request.createService<SendEmailByForgotReq, SendEmailByForgotRes>({
    url: "/auth/clientUser/getResetEmail",
    method: "get",
    useAuth: false,
    customErrorMessage: {
        "80004": "forgotPassword.userNotExist"
    }
});

/**
 * 老用户通过邮箱验证修改密码
 */
export const ResetPwdByForgot = request.createService<ResetPwdByForgotReq>({
    url: "/auth/clientUser/resetPassword",
    method: "post",
    useAuth: false,
});

/**
 * 新用户初始化密码
 */
export const ResetPwdByInit = request.createService<InitPwdByForgotReq>({
    url: "/auth/clientUser/initPassword",
    method: "post",
    useAuth: false,
});