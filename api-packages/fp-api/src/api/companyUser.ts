import request from "../request";
import { 
    GetCompanyUsersReq, 
    GetCompanyUsersRes, 
    SearchUsersReq,
    SearchUserRes,
    RemoveCompanyUserReq,
    RoleInfo,
    UpdateUserRoleReq,
    SetDefaultCompanyReq,
    AddUserToCompanyReq
} from "../types/companyUser";

export const GetCompanyUsers = request.createService<GetCompanyUsersReq, GetCompanyUsersRes>({
    url: "/metadata/client/user/affiliation/list",
    method: "post"
});

export const SearchUsers = request.createService<SearchUsersReq, SearchUserRes[]>({
    url: "/metadata/clientuser/list/all",
    method: "post"
});

export const RemoveCompanyUser = request.createService<RemoveCompanyUserReq, boolean>({
    url: "/metadata/client/user/affiliation/remove",
    method: "post"
});

export const GetRoles = request.createService<void, RoleInfo[]>({
    url: "/metadata/clientuserRole/role/list",
    method: "get"
});

export const UpdateUserRole = request.createService<UpdateUserRoleReq, boolean>({
    url: "/metadata/clientuserRole/role/update",
    method: "post"
});

export const SetDefaultCompany = request.createService<SetDefaultCompanyReq, boolean>({
    url: "/metadata/clientuserCompany/set/default",
    method: "post"
});

export const AddUserToCompany = request.createService<AddUserToCompanyReq, void>({
    url: "/metadata/clientuser/add",
    method: "post",
    genericErrorMessage: "addCompanyUser.genericError",
    customErrorMessage: {
        "20017": "addCompanyUser.userAlreadyAdded"
    }
});
