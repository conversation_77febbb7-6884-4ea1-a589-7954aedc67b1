import request from "../request";
import {
  OnSiteMessageListRequest,
  OnSiteMessageListResponse,
  OnSiteMessageReadRequest,
  CompanyUserAffiliationRequest,
  RejectAffiliationRequest
} from "../types/onSiteMessage";

/**
 * Get on-site message list
 */
export const getOnSiteMessageList = request.createService<OnSiteMessageListRequest, OnSiteMessageListResponse>({
  url: "/mendixhelper/mymessage/search",
  method: "post",
});

/**
 * Mark message as read/unread
 */
export const updateMessageReadStatus = request.createService<OnSiteMessageReadRequest, boolean>({
  url: "/mendixhelper/mymessage/read",
  method: "post",
});

/**
 * Activate company user affiliation
 */
export const activateCompanyUserAffiliation = request.createService<CompanyUserAffiliationRequest, string>({
  url: "/metadata/client/user/affiliation/active",
  method: "post",
});

/**
 * Reject company user affiliation
 */
export const rejectAffiliation = request.createService<RejectAffiliationRequest, boolean>({
  url: '/metadata/client/user/affiliation/reject',
  method: 'post'
}); 