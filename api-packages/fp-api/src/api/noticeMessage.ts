import request from '../request';
import {
  NoticeMessageListRequest,
  NoticeMessageListResponse,
  NoticeMessageReadRequest,
  NoticeMessageSearchRequest
} from '../types/noticeMessage';

const BASE_URL = '/mendixhelper/mymessage/announcement';

export const getNoticeMessageList = request.createService<NoticeMessageListRequest, NoticeMessageListResponse>({
  url: `${BASE_URL}/list`,
  method: 'post'
});

export const markNoticeMessageRead = request.createService<NoticeMessageReadRequest, boolean>({
  url: `${BASE_URL}/read`,
  method: 'post'
});

export const searchNoticeMessages = request.createService<NoticeMessageSearchRequest, NoticeMessageListResponse>({
  url: `${BASE_URL}/search`,
  method: 'post'
}); 