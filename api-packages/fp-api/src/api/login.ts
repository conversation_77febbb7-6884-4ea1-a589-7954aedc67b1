import request from "../request";
import {LoginReq, LoginRes, ReSendInitEmailReq} from "../types/login";

/**
 * 邮箱登录
 */
export const Login = request.createService<LoginReq, LoginRes>({
    url: "/api/login",
    method: "post",
    useAuth: false,
    genericErrorMessage: "login.genericError",
});

/**
 * Get user info
 */
export const GetUserInfo = request.createService<null, LoginRes>({
    url: "/api/user",
    method: "get",
    genericErrorMessage: "login.genericError",
});

/**
 * 登录验证码
 * {
 "code": 200,
 "msg": "",
 "img": "xxx",
 "uuid": "xx"
 }
 */
export const GetCaptchaImg = request.createService({
    url: "/code",
    method: "get",
    useAuth: false,
});

/**
 * 退出登录
 */
export const Logout = request.createService({
    url: "/auth/clientUser/logout",
    method: "delete",
});

/**
 * 重新发送激活账号邮件
 */
export const ReSendInitEmail = request.createService<ReSendInitEmailReq>({
    url: "/auth/clientUser/resend/email",
    method: "post",
    useAuth: false,
});


/**
 * 通过accessToken获取登录用户信息（GET /api/accessToken/login）
 */

export const GetUserInfoByAccessToken =
    request.createService<{ accessToken: string }, LoginRes>({
        url: "/api/accessToken/login",
        method: "get",
        responseType: "json",
        beforeRequest: (config) => {
            const {accessToken, ...otherData} = (config.params! as { accessToken: string });

            config.headers = {
                ...(config.headers || {}),
                AccessToken: accessToken,
            };

            delete config.params;

            return config;
        },
    });