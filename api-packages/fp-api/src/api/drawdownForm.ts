import request from "../request";
import { 
  GetCompanyConfigRes, 
  DebitAccountRequest, 
  DebitAccountResponse,
  GetDebitAccountResponse,
  BusinessInfoRequest,
  BusinessInfoResponse,
  UploadFileRequest,
  UploadFileResponse,
  SaveSignerInfoRequest,
  SaveSignerInfoResponse,
  InitiateSignResponse,
  UrgeSignResponse,
  GetSignerInfoResponse,
  GetEmailNotifyResponse,
  SignerInfo,
  GetShareholderStructureResultResponse,
  DeleteSignerFileRequest,
  CompanyInfoRequest,
  CompanyInfoResponse,
  SendRpaEmailResponse,
  RetrySignResponse,
  ChangePhoneNumberRequest,
  ChangePhoneNumberResponse
} from "../types/drawdownForm";

export const getCompanyConfig = request.createService<void, GetCompanyConfigRes>({
    url: "/api/company/config",
    method: "get"
});

export const submitDebitAccount = request.createService<DebitAccountRequest, DebitAccountResponse>({
    url: "/api/information/debitAccount",
    method: "post"
});

export const getDebitAccount = request.createService<void, GetDebitAccountResponse>({
    url: "/api/information/debitAccount",
    method: "get"
});

export const submitBusinessInfo = request.createService<BusinessInfoRequest, BusinessInfoResponse>({
    url: "/api/information/business",
    method: "post"
});

export const getBusinessInfo = request.createService<void, BusinessInfoResponse>({
    url: "/api/information/business",
    method: "get"
});

export const submitCompanyInfo = request.createService<CompanyInfoRequest, CompanyInfoResponse>({
    url: "/api/information/company",
    method: "post"
});

// Agreement Signature APIs

/**
 * Upload a file for agreement signing
 * Endpoint: POST /api/uploadFiles
 * Authentication: Token (Bearer)
 *
 * Request Parameters:
 * - files: File[] (Required) - Stream of files to upload (single or multiple)
 * - dst: string (Required) - Must be "papers" for file storage path
 *
 * Response:
 * - code: 0 = success, non-zero = failure
 * - message: Response message
 * - data: File metadata including file_id, file_path, file_name, file_size, file_mime, file_link
 *         (Pre-signed link expires after 30 minutes)
 */
export const uploadFile = request.createService<UploadFileRequest, UploadFileResponse>({
    url: "/api/uploadFiles",
    method: "post",
    headers: {
        "Content-Type": "multipart/form-data"
    }
});

/**
 * Save signer information
 * Endpoint: POST /api/agreements/signers/{id}
 * Authentication: Token (Bearer)
 *
 * Request Parameters:
 * - front_id_file_id: int (Required) - File ID of front side of ID card/passport
 * - back_id_file_id: int (Required) - File ID of back side of ID card/passport
 * - region_code: int (Required) - Region code
 * - phone_region_code: string (Required) - Mobile phone country code
 * - phone_number: string (Required) - Mobile phone number
 * - email: string (Required) - Email address
 * - front_file_id: int (Optional) - File ID of other document front side
 * - back_file_id: int (Optional) - File ID of other document back side
 * - papers_type: string (Conditional) - Document type, required if other ID files provided
 *   Allowed values: idcard, hkidcard, passport, chinesepassport, exitentrypermittomainland
 *
 * Response:
 * - code: 0 = success, 1 = failure
 * - message: Response message
 * - data: Array of signer info records including seq, position, names, document info, etc.
 */
export const saveSignerInfo = (id: string) =>
    request.createService<SaveSignerInfoRequest, SignerInfo>({
        url: `/api/agreements/signers/${id}`,
        method: "post"
    });

/**
 * Delete uploaded signer documents
 * Endpoint: POST /api/agreements/delete-signer-file/{id}
 * Authentication: Token (Bearer)
 *
 * Request Parameters:
 * - front_id_file_id: string (Optional) - File ID of front side of ID card/passport to delete
 * - back_id_file_id: string (Optional) - File ID of back side of ID card/passport to delete
 * - front_file_id: string (Optional) - File ID of other document front side to delete
 * - back_file_id: string (Optional) - File ID of other document back side to delete
 *
 * Response:
 * - code: 0 = success, 1 = failure
 * - message: Response message
 * - data: Empty or omitted
 */
export const deleteSignerFile = (id: string) =>
    request.createService<DeleteSignerFileRequest, {code: number, message: string, data: any}>({
        url: `/api/agreements/delete-signer-file/${id}`,
        method: "post"
    });

/**
 * Initiate document signing process
 * Endpoint: POST /api/agreements/initiate-sign
 * Authentication: Token (Bearer)
 *
 * Request: No request body parameters
 *
 * Response:
 * - code: Status code
 *   0: success
 *   1: general failure
 *   200400: signer info mismatch
 *   200401: RPA check mismatch
 * - message: Response message
 * - data: On success: null or omitted
 *         On error: Contains details about mismatches
 *         When code=200400: {seq, field, want, got}
 *         When code=200401: [{full_name_chn, full_name_eng, rpa_check_result}]
 */
export const initiateSign = request.createService<void, InitiateSignResponse>({
    url: "/api/agreements/initiate-sign",
    method: "post"
});

/**
 * Send an urgent reminder to signers
 * Endpoint: POST /api/agreements/urge
 * Authentication: Token (Bearer)
 *
 * Request: No request body parameters
 *
 * Response:
 * - code: 0 = success, 1 = failure
 * - message: Response message (success by default)
 * - data: Empty array or omitted
 */
export const urgeSign = request.createService<void, UrgeSignResponse>({
    url: "/api/agreements/urge",
    method: "post"
});

/**
 * Get signer information and status
 * Endpoint: POST /api/agreements/signer
 * Authentication: Token (Bearer)
 *
 * Request: No request body parameters
 *
 * Response:
 * - code: 0 = success, 1 = failure
 * - message: Response message
 * - data: Array of signer records including:
 *   - Basic info: id, company_id, seq, position, names, contact info
 *   - Document info: papers_type, papers_number, file IDs
 *   - Preview links: Pre-signed URLs for document viewing (expire after 30 mins)
 *   - Status info: counter_fraud_status, rpa_check_result
 *   - sign_record: Detailed signing record including status codes:
 *     - sign_status: 0=Draft, 1=Signing, 2=Expired, 3=Withdraw, 4=Reject, 5=Finished
 *     - Record timestamps: sign_date, created_at, updated_at
 */
export const getSignerInfo = request.createService<void, GetSignerInfoResponse>({
    url: "/api/agreements/signer",
    method: "post"
});

/**
 * Get shareholder structure verification result
 * Endpoint: GET /api/information/shareholderStructure/result
 * Authentication: Token (Bearer)
 * 
 * Request: No request body parameters
 * 
 * Response:
 * - code: Status code
 *   0: Success - Verification passed, proceed to signing page
 *   2000: Waiting - Continue polling for results
 *   3000: Manual review - Display QR code for human review
 *   4000: Failed - Anti-fraud check failed
 * - message: Response message
 * - data: Verification result details with status flags
 *   - telphone: "Y" or "N" - Telephone verification status
 *   - psp: "Y" or "N" - PSP verification status 
 *   - qcc: "Y" or "N" - QCC verification status
 *   - qcc_sharehold: "Y" or "N" - QCC shareholder verification status
 */
export const getShareholderStructureResult = request.createService<void, GetShareholderStructureResultResponse>({
  url: "/api/information/shareholderStructure/result",
  method: "get"
});

export const sendEmailNotificationsByType = (type: number) =>
    request.createService<void, GetEmailNotifyResponse>({
        url: `/api/notifi/email/occ?type=${type}`,
        method: "get"
    });

/**
 * Send RPA email with image
 * Endpoint: POST /api/agreements/send-rpa-email
 * Authentication: Token (Bearer)
 *
 * Request: No request body parameters required
 *
 * Response:
 * - code: 0 = success, 1 = failure
 * - message: Response message
 * - data: Empty or response data if any
 */
export const sendRpaEmail = request.createService<void, SendRpaEmailResponse>({
    url: "/api/agreements/send-rpa-email",
    method: "post"
});

/**
 * Retry failed agreement signing
 * Endpoint: POST /api/agreements/retry-sign
 * Authentication: Token (Bearer)
 *
 * Request: No request body parameters
 *
 * Response:
 * - code: 0 = success, 1 = failure
 * - message: Response message
 *   - success: "success"
 *   - failure: Error description
 * - data: Array (Optional) - Additional data if any
 */
export const retrySign = request.createService<void, RetrySignResponse>({
    url: "/api/agreements/retry-sign",
    method: "post"
});


export const changePhoneNumber = request.createService<ChangePhoneNumberRequest, ChangePhoneNumberResponse>({
    url: "/api/user/change-phone",
    method: "post"
});