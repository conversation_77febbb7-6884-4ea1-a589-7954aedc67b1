import request from '../request';
import type { 
  FacilityDetailReq, 
  FacilityDetailResponseData 
} from '../types/facility';

/**
 * Get facility detail by id
 * @param data Facility detail request parameters
 * @returns Facility detail response
 */
export const getFacilityDetail = request.createService<FacilityDetailReq, FacilityDetailResponseData>({
  url: '/metadata/credit/detail',
  method: 'post'
}); 