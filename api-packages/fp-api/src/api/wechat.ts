import request from "../request";
import {GetCpidInfoRes} from "../types/wechat";

/**
 * 获取CPID数据
 */
export const getCpidInfo =
    request.createService<{ id: string }, GetCpidInfoRes>({
        url: "/api/campaign-tag/{id}",
        method: "get",
        beforeRequest: (param) => {
            const source = (param.params ?? param.data) as any;
            const id = source?.id;
            if (id == null) {
                throw new Error("Missing `id` for `getCpidInfo`");
            }

            param.url = param.url!.replace("{id}", encodeURIComponent(String(id)));

            if (param.params) {
                const {id: _, ...rest} = source;
                param.params = rest;
            }
            if (param.data) {
                const {id: _, ...rest} = source;
                param.data = rest;
            }

            return param;
        },

    });
