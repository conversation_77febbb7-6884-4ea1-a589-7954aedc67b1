import request from "../request";
import {
  ChargeListingRequest,
  ChargeListingResponse,
  DownloadInvoiceRequest,
  DownloadInvoiceResponse
} from "../types/charge";

/**
 * Get charge listing
 */
export const getChargeList = request.createService<ChargeListingRequest, ChargeListingResponse>({
  url: "/transaction/company/charge/list",
  method: "post"
});

/**
 * Download invoice
 */
export const downloadInvoice = request.createService<DownloadInvoiceRequest, DownloadInvoiceResponse>({
  url: "/transaction/company/charge/invoice/offline",
  method: "post"
}); 