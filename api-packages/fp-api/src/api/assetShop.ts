import request from "../request";
import type {
    PlatformVO,
    AuthorizedShopByOpenAPIReq,
    AuthorizedShopByOpenAPIRes,
    GetShopListReq,
    GetShopListRes,
    AddShopAccountReq,
    EditShopAccountReq,
    GetShopAccountListReq,
    GetShopAccountListRes,
    MigrateAuthorizedShopReq
} from "../types/assetShop";

// TODO: 后续开发Platform迭代再替换API
export const GetPlatformList = async () => {
    const res = await fetch(`/resources/platform/config.json?t=${Date.now()}`);
    const list: PlatformVO[] = await res.json();
    return {
        success: true,
        code: 200,
        msg: "ok",
        data: list
    };
};

/**
 * 店铺授权-第三方平台API
 */
export const AuthorizedShopByOpenAPI = request.createService<AuthorizedShopByOpenAPIReq, AuthorizedShopByOpenAPIRes>({
    url: "/metadata/authorized/shop/add",
    method: "post"
});

/**
 * 获取店铺列表
 */
export const GetShopList = request.createService<GetShopListReq, GetShopListRes>({
    url: "/metadata/authorized/shop/page",
    method: "post",
    trimParams: true,
    removeEmptyParams: true
});

/**
 * 获取店铺账号列表
 */
export const GetShopAccountList = request.createService<GetShopAccountListReq, GetShopAccountListRes>({
    url: "/metadata/authorized/shop/account/page",
    method: "post",
    trimParams: true,
    removeEmptyParams: true
});

/**
 * 添加店铺账号
 */
export const AddShopAccount = request.createService<AddShopAccountReq, boolean>({
    url: "/metadata/authorized/shop/account/add",
    method: "post"
});

/**
 * 编辑店铺账号
 */
export const EditShopAccount = request.createService<EditShopAccountReq, boolean>({
    url: "/metadata/authorized/shop/account/edit",
    method: "post",
	trimParams: true,
});

/**
 * 删除店铺账号
 */
export const DeleteShopAccount = request.createService<{ id: string }, boolean>({
    url: "/metadata/authorized/shop/account/delete",
    method: "post"
});

/**
 * 删除授权的店铺
 */
export const DeleteAuthorizedShop = request.createService<{ id: string }, boolean>({
    url: "/metadata/authorized/shop/delete",
    method: "post"
});

/**
 * 迁移授权的店铺
 */
export const MigrateAuthorizedShop = request.createService<MigrateAuthorizedShopReq, boolean>({
    url: "/metadata/authorized/shop/migrate",
    method: "post"
});

/**
 * 获取店铺账号原文密码
 */
export const GetShopAccountPassword = request.createService<{ id: string }, string>({
    url: "/metadata/authorized/shop/account/view/pwd",
    method: "post"
});
