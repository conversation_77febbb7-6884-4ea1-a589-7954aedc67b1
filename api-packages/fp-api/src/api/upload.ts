import * as crc32 from "crc-32";
import request from "../request";

async function calculateCRC(file: File) {
    return new Promise<number>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => {
            try {
                const bytes = new Uint8Array(e.target!.result as ArrayBuffer);
                const crcValue = crc32.buf(bytes);
                resolve(crcValue >>> 0);
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error("Failed to read file"));
        reader.readAsArrayBuffer(file);
    });
}

export const UploadMultiFile = request.createService<{ file: File }, string>({
    url: "/thirdparty/file/storage/uploadWithMultipartFile",
    method: "post",
    headers: {
        "Content-Type": "multipart/form-data"
    },
    async asyncBeforeRequest(data) {
        const { file } = data.data!;
        const formData = new FormData();
        const fileCRC = await calculateCRC(file);
        formData.append("fileName", file.name);
        formData.append("fileExtensions", file.name.split(".").pop() || "");
        formData.append("fileCRC", fileCRC.toString());
        formData.append("content", file);

        // @ts-ignore
        data.data = formData;
        return data;
    }
});

export const GetFileOnlineUrl = request.createService<{ resourceId: string }, string>({
    url: "/thirdparty/file/storage/getFileOnlineURL",
    method: "post"
});
