import request from "../request";
import { 
  WithdrawApplicationRecordReq, 
  WithdrawApplicationRecordRes, 
  ApplicationRecordListReq, 
  ApplicationRecordListRes 
} from "../types/applicationRecord";

/**
 * Withdraw application record
 */
export const WithdrawApplicationRecord = request.createService<WithdrawApplicationRecordReq, WithdrawApplicationRecordRes>({
  url: "/mendixhelper/approval/withdraw",
  method: "post"
}); 

/**
 * Get application record list with pagination
 */
export const getApplicationRecordList = request.createService<ApplicationRecordListReq, ApplicationRecordListRes>({
  url: "/mendixhelper/approval/cp/page/list",
  method: "post"
}); 