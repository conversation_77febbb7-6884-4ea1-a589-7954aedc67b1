import request from "../request";
import { GetRegionDataReq, GetRegionDataRes, GetCurrencyRateReq, GetCurrencyRateRes } from "../types/common";

// TODO: rename
export const GetRegionData = request.createService<GetRegionDataReq, GetRegionDataRes>({
    url: "/common/common/risk/point/all",
    method: "post"
});

export const GetIPAddress = request.createService<void, string>({
    url: "/auth/upUser/ip",
    method: "get"
});

export const GetCurrencyRate = request.createService<GetCurrencyRateReq, GetCurrencyRateRes>({
    url: "/common/currency/rate/list/matrix",
    method: "post"
});
