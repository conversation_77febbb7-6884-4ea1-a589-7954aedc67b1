import request from "../request";
import { getReportKeywordRes, postReportReq, postReportRes, getReportRes } from "../types/categoryReport";

export const getCategoryReportKeyword = request.createService<{ keywords: string }, getReportKeywordRes>({
    url: "api/category-report/categoryList/{keywords}",
    method: "get",
    beforeRequest: (axiosParam) => {
        const keywords = (axiosParam.params as any)?.keywords;
        if (keywords == null) {
            throw new Error("Missing `keywords` for `getCategoryReportKeyword`");
        }
        axiosParam.url = axiosParam.url!.replace("{keywords}", encodeURIComponent(String(keywords)));
        const {keywords: _, ...rest} = (axiosParam.params as any);
        axiosParam.params = rest as any;
        return axiosParam;
    },
});

export const getCategoryReportResult = request.createService<postReportReq,postReportRes>({
    url: "api/category-report/store",
    method: "post"
});

export const getCategoryReports = request.createService<void, getReportRes>({
    url: "api/category-report",
    method: "get"
});
