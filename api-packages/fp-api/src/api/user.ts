import request from "../request";
import { GetUserInfoRes, UpdateUserInfoReq, UpdateUserInfoRes, UpdateUserPasswordReq, UpdateUserPasswordRes, GetUserRedirectToTABFTokenRes } from "../types/user";

export const UpdateUserInfo = request.createService<UpdateUserInfoReq, UpdateUserInfoRes>({
    url: "/metadata/clientuser/updateBasicClientUser",
    method: "post",
});

export const UpdateUserPassword = request.createService<UpdateUserPasswordReq, UpdateUserPasswordRes>({
    url: "/auth/clientUser/updatePassword",
    method: "post",
    customErrorMessage: {
        "800003": "updatePassword.oldPasswordError"
    }
});

export const GetUserRedirectToTABFToken =
  request.createService<{ id: number }, GetUserRedirectToTABFTokenRes>({
    url: "/api/tabf/link/{id}",
    method: "get",
    beforeRequest: (axiosParam) => {
      const id = (axiosParam.params as any)?.id;
      if (id == null) {
        throw new Error("Missing `id` for TABF token link");
      }
      axiosParam.url = axiosParam.url!.replace("{id}", encodeURIComponent(String(id)));
      const { id: _, ...rest } = (axiosParam.params as any);
      axiosParam.params = rest as any;
      return axiosParam;
    },
  });
