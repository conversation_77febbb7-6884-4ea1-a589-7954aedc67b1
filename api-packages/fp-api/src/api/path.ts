import request from "../request";
import { StorePathReq, StorePathRes, GetPathRes } from "../types/path";

/**
 * Store user's last visit path
 * @param data Request with path and path_name
 * @returns Success response with stored path data
 */
export const storePath = request.createService<StorePathReq, StorePathRes>({
  url: "/api/path/store",
  method: "post"
});

/**
 * Get user's last visit path
 * @returns Response with path data
 */
export const getPath = request.createService<void, GetPathRes>({
  url: "/api/path",
  method: "get"
});
