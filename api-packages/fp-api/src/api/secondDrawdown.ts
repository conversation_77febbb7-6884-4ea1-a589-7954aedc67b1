import request from "../request";
import {
    BankList,
    BankListResponse,
    DirectorInfoResponse,
    DrawdownRequest,
    DrawdownResponse,
    InterestRateResponse
} from "../types/secondDrawdown";

export const getValidBankList = request.createService<void, BankList>({
    url: "api/information/bank/list",
    method: "get"
});

export const postDrawdown = request.createService<DrawdownRequest, DrawdownResponse>({
    url: "api/information/drawdown/submit",
    method: "post"
});

export const getInterestRate = request.createService<void, InterestRateResponse>({
    url: "api/information/interest_rate/sofr",
    method: "get"
});

export const getDirectorInfo = request.createService<void, DirectorInfoResponse>({
    url: "api/information/directors/name",
    method: "get"
});
