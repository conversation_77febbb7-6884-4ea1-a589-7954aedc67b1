import request from "../request";
import { CompanyNotificationsListResponse, deleteNotificationResponse } from "../types/notifications";

export const getCompanyNotificationsList = request.createService<
    { per_page: number },
    CompanyNotificationsListResponse
>({
    url: `/api/company/notifications?per_page={per_page}`,
    method: "get",
    beforeRequest: axiosParam => {
        const per_page = (axiosParam.params as any)?.per_paged | 1;
        axiosParam.url = axiosParam.url!.replace("{per_page}", encodeURIComponent(String(per_page)));
        const { per_page: _, ...rest } = axiosParam.params as any;
        axiosParam.params = rest as any;
        return axiosParam;
    }
});

export const deleteNotification = request.createService<{ id: number }, deleteNotificationResponse>({
    url: `/api/company/notifications/{id}`,
    method: "delete",

    beforeRequest: axiosParam => {
        console.log("axiosParam", axiosParam);
        console.log;
        const id = (axiosParam as any)?.data?.id;
        if (id == null) {
            throw new Error("Missing `id`");
        }
        axiosParam.url = axiosParam.url!.replace("{id}", encodeURIComponent(String(id)));
        const { id: _, ...rest } = axiosParam as any;
        axiosParam = rest as any;
        return axiosParam;
    }
});
