import request from "../../request";
import {
    GetPlatformListReq,
    GetPlatformListRes,
    GetPaymentGatewayListReq,
    GetPaymentGatewayListRes,
    GetAuthURLReq,
    GetAuthURLRes,
    GetAuthCallbackResultReq,
    GetAuthCallbackResultRes,
    GetOauthShopListReq,
    GetOauthShopListRes,
    bindPSPtoShopReq,
    bindPSPtoShopRes
} from "../../types/platform/platform";

/**
 * Get platform list
 */
export const getPlatformList = request.createService<GetPlatformListReq, GetPlatformListRes>({
    url: "/api/platform/shop/list",
    method: "get"
});

/**
 * Get psp list
 */
export const getPaymentGatewayList = request.createService<GetPaymentGatewayListReq, GetPaymentGatewayListRes>({
    url: "/api/platform/psp/list",
    method: "get"
});

/**
 * Get auth url
 */
export const getAuthURL = request.createService<GetAuthURLReq, GetAuthURLRes>({
    url: "/api/oauth/url",
    method: "post"
});

/**
 * Get auth callback result
 */
export const getAuthCallbackResult = request.createService<GetAuthCallbackResultReq, GetAuthCallbackResultRes>({
    url: "/api/oauth/callback",
    method: "post"
});

/**
 * Get client authed shop list
 */
export const getOauthShopList = request.createService<GetOauthShopListReq, GetOauthShopListRes>({
    url: "/api/oauth/list",
    method: "get"
});

export const bindPSPtoShop = request.createService<bindPSPtoShopReq, bindPSPtoShopRes>({
    url: "/api/oauth/relevance/psp",
    method: "post"
});

export const deleteAuthedShop = (id: string) => {
    return request.createService<void, void>({
        url: `/api/oauth/shop/${id}`,
        method: "delete"
    })();
};