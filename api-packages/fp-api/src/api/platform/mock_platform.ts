// mockPlatformService.ts

import {
    GetPlatformListReq,
    GetPlatformListRes,
    GetPaymentGatewayListReq,
    GetPaymentGatewayListRes,
    GetAuthURLReq,
    GetAuthURLRes,
    GetAuthCallbackResultReq,
    GetAuthCallbackResultRes,
    GetOauthShopListReq,
    GetOauthShopListRes
} from "../../types/platform/platform";

// Mock: Get platform list
export const mockGetPlatformList = async (
    req: GetPlatformListReq
): Promise<GetPlatformListRes> => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    return {
        "status": 0,
        "data": [
            {
                "id": 1,
                "platform": "Amazon",
                "icon_path": null,
                "partner_abbr": "",
                "action": 1    // 1可授权， 2没有api需要授权psp
            },
            {
                "id": 2,
                "platform": "Walmart",
                "icon_path": null,
                "partner_abbr": "",
                "action": 1
            },
            {
                "id": 3,
                "platform": "ebay",
                "icon_path": null,
                "partner_abbr": "",
                "action": 1
            },
            {
                "id": 4,
                "platform": "AliExpress",
                "icon_path": null,
                "partner_abbr": "",
                "action": 1
            },
            {
                "id": 5,
                "platform": "Shopee",
                "icon_path": null,
                "partner_abbr": "",
                "action": 1
            },
            {
                "id": 6,
                "platform": "Shopify",
                "icon_path": null,
                "partner_abbr": "",
                "action": 1
            },
            {
                "id": 7,
                "platform": "Fruugo",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 8,
                "platform": "Cdiscount",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 9,
                "platform": "Mercado Libre",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 10,
                "platform": "TikTok",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 11,
                "platform": "Temu",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 12,
                "platform": "Lazada",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 13,
                "platform": "Kaufland",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 14,
                "platform": "SHEIN",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 15,
                "platform": "Wayfair",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 16,
                "platform": "Manomano",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 17,
                "platform": "Conforama",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 18,
                "platform": "Dewu (\u5f97\u7269)",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 19,
                "platform": "\u6296\u97f3",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 20,
                "platform": "KuaiShou (\u5feb\u624b)",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 21,
                "platform": "\u6dd8\u5206\u9500\uff08\u9cb8\u82bd\uff09",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 22,
                "platform": "JD.COM (\u4eac\u4e1c)",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 23,
                "platform": "Tmall (\u5929\u732b)",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 24,
                "platform": "PDD (\u62fc\u591a\u591a)",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            },
            {
                "id": 25,
                "platform": "\u5c0f\u7ea2\u4e66",
                "icon_path": null,
                "partner_abbr": "",
                "action": 2
            }
        ],
        "message": "success"
    }


        ;
};

// Mock: Get payment gateway list
export const mockGetPaymentGatewayList = async (
    req: GetPaymentGatewayListReq
): Promise<GetPaymentGatewayListRes> => {
    await new Promise(resolve => setTimeout(resolve, 2000));
    return {
        "status": 0,
        "data": [
            {
                "id": 26,
                "platform": "Airwallex",
                "icon_path": null,
                "partner_abbr": "awx_rev",
                "action": 1
            },
            {
                "id": 27,
                "platform": "LianLian",
                "icon_path": null,
                "partner_abbr": "lianlian",
                "action": 1
            },
            {
                "id": 28,
                "platform": "Payoneer",
                "icon_path": null,
                "partner_abbr": "payo",
                "action": 1
            },
            {
                "id": 29,
                "platform": "PingPong",
                "icon_path": null,
                "partner_abbr": "pingpong",
                "action": 1
            },
            {
                "id": 30,
                "platform": "Sunrate",
                "icon_path": null,
                "partner_abbr": "sunrate",
                "action": 1
            }
        ],
        "message": "success"
    }


        ;
};

// Mock: Get auth URL
export const mockGetAuthURL = async (
    req: GetAuthURLReq
): Promise<GetAuthURLRes> => {
    return {
        "status": 0,
        "data": {
            "oauth_url": "http://www.baidu.com?id_token=oauth_7ce55ee8-f51d-2c75-1eb8-8bb0bcd204ce&pid=pingpong&app_id=PingPong_app_id&redirect_url=/oauth&metadata=eyJzY2VuZSI6InBzcCJ9",
            "id_token": "oauth_7ce55ee8-f51d-2c75-1eb8-8bb0bcd204ce",
            "oauth_request_id": 3
        },
        "message": "success"
    }
        ;
};

// Mock: Get auth callback result
export const mockGetAuthCallbackResult = async (
    req: GetAuthCallbackResultReq
): Promise<GetAuthCallbackResultRes> => {
    return {
        status: 0,
        data: {
            "status": 0,
            "data": [],
            "message": "success"
        },
        message: "callback received"
    };
};

// Mock: Get OAuth shop list
export const mockGetOauthShopList = async (
    req: GetOauthShopListReq
): Promise<GetOauthShopListRes> => {
    return {
        "status": 0,
        "data": {
            "shop": [
                {
                    "user_shop_id": 1,
                    "user_id": 359,
                    "oauth_shop": {
                        "seller_id": "shop_seller_id_222",
                        "platform_id": 2,
                        "platform": "Walmart"
                    },
                    "choose_psp": [],
                    "oauth_status": 0,
                    "created_at": "2025-04-15 21:07:49",
                    "updated_at": "2025-04-15 21:07:49"
                }
            ],
            "psp": [
                {
                    "user_psp_id": 1,
                    "user_id": 359,
                    "oauth_psp": {
                        "account_id": "psp_account_id_111",
                        "platform_id": 27,
                        "platform": "LianLian"
                    },
                    "choose_shop": {
                        "platform_id": 17,
                        "platform": "Conforama"
                    },
                    "oauth_status": 0,
                    "created_at": "2025-04-15 21:03:56",
                    "updated_at": "2025-04-15 21:03:56"
                }
            ]
        },
        "message": "success"
    };
};
