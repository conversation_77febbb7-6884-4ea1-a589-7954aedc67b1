import { HttpRequest } from "@fundpark/ui-utils";
import i18n from "./i18n";
import { ErrorCode } from "./error-code/code";
import errorCodes from "./error-code";

const baseURL = import.meta.env.VITE_API_BASE_URL as string;
const http = new HttpRequest({
    baseURL,
    i18n,
    errorCodes,
    headers: {
        "user_login_type": "CLIENT_USER"
    },
    filterErrorTips: [ErrorCode.$80016, ErrorCode.$80019, ErrorCode.$80001, ErrorCode.$80800001]
});

export default http;
