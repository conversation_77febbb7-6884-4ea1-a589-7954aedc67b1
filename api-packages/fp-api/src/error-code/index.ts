import { ErrorCode } from "./code";

const errorCodes: Record<number, string> = {
    [ErrorCode.$502]: "errorMsg.businessException",
    [ErrorCode.$503]: "errorMsg.systemExecutionException",
    [ErrorCode.$504]: "errorMsg.parameterEmpty",
    [ErrorCode.$505]: "errorMsg.returnResultEmpty",
    [ErrorCode.$506]: "errorMsg.networkError",
    [ErrorCode.$507]: "errorMsg.systemError",
    [ErrorCode.$508]: "errorMsg.batchDataTooMuchAdded",
    [ErrorCode.$509]: "errorMsg.batchDataTooMuchUpdated",
    [ErrorCode.$510]: "errorMsg.parameterMissingException",
    [ErrorCode.$511]: "errorMsg.queryDataOverloadException",
    [ErrorCode.$512]: "errorMsg.dataNotExistException",
    [ErrorCode.$513]: "errorMsg.systemDataException",
    [ErrorCode.$514]: "errorMsg.invalidOrNotExist",
    [ErrorCode.$515]: "errorMsg.verificationCodeIncorrect",
    [ErrorCode.$200]: "errorMsg.success",
    [ErrorCode.$500]: "errorMsg.failure",

    [ErrorCode.$20001]: "errorMsg.addedSuccessfully",
    [ErrorCode.$20002]: "errorMsg.addedFailed",
    [ErrorCode.$20003]: "errorMsg.updatedSuccessfully",
    [ErrorCode.$20004]: "errorMsg.updatedFailed",
    [ErrorCode.$20005]: "errorMsg.deletedSuccessfully",
    [ErrorCode.$20006]: "errorMsg.deletedFailed",
    [ErrorCode.$20007]: "errorMsg.verificationFailed",
    [ErrorCode.$20008]: "errorMsg.parameterMissingOrIncorrect",
    [ErrorCode.$20009]: "errorMsg.processing",
    [ErrorCode.$20010]: "errorMsg.doNotRepeatOperation",
    [ErrorCode.$20011]: "errorMsg.jsonFormatError",
    [ErrorCode.$20012]: "errorMsg.dateFormatIncorrect",
    [ErrorCode.$20013]: "errorMsg.databaseOperationFailed",
    [ErrorCode.$20015]: "errorMsg.processedDataExceedsLimit",
    [ErrorCode.$20016]: "errorMsg.operationTypeNotExistOrUnavailable",
    [ErrorCode.$20017]: "errorMsg.dataAlreadyExists",
    [ErrorCode.$20018]: "errorMsg.dataNotExist",

    [ErrorCode.$30001]: "errorMsg.templateUnavailable",
    [ErrorCode.$30002]: "errorMsg.enterBusinessRegistrationNumber",
    [ErrorCode.$30003]: "errorMsg.enterEnglishCompanyName",
    [ErrorCode.$30004]: "errorMsg.enterUnifiedSocialCreditCode",
    [ErrorCode.$30005]: "errorMsg.enterChineseCompanyName",
    [ErrorCode.$30006]: "errorMsg.failedToGenerateCompanyNumber",
    [ErrorCode.$30007]: "errorMsg.userEmailAlreadyExists",
    [ErrorCode.$30008]: "errorMsg.failedToGenerateCustomerUserNumber",
    [ErrorCode.$30009]: "errorMsg.mailboxVerifiedCannotModify",
    [ErrorCode.$30010]: "errorMsg.unlockTimesExceedLimit",
    [ErrorCode.$30011]: "errorMsg.regionInvalidOrNotExist",
    [ErrorCode.$30012]: "errorMsg.userPhoneAlreadyExists",
    [ErrorCode.$30013]: "errorMsg.associatedCompanyBRN",
    [ErrorCode.$30014]: "errorMsg.companyCREmpty",
    [ErrorCode.$30015]: "errorMsg.contactAdminForCompanyInvitation",
    [ErrorCode.$30016]: "errorMsg.userAlreadyHasSameBRN",
    [ErrorCode.$30017]: "errorMsg.companyAddUserLinkExpired",
    [ErrorCode.$30033]: "errorMsg.checkBrnRepeated",


    [ErrorCode.$40001]: "errorMsg.newApplicationForKYC",
    [ErrorCode.$40002]: "errorMsg.approvedKYCExists",
    [ErrorCode.$40003]: "errorMsg.smeSystemError",
    [ErrorCode.$40004]: "errorMsg.kycProcessingCheckStatus",
    [ErrorCode.$40005]: "errorMsg.invalidRiskLevel",

    [ErrorCode.$50001]: "errorMsg.downloadFileError",
    [ErrorCode.$50002]: "errorMsg.crcCheckError",
    [ErrorCode.$50003]: "errorMsg.fileNotExist",

    [ErrorCode.$60001]: "errorMsg.verificationCodeEmpty",
    [ErrorCode.$60002]: "errorMsg.verificationCodeExpired",
    [ErrorCode.$60003]: "errorMsg.verificationCodeIncorrect",

    [ErrorCode.$70001]: "errorMsg.ruleCodeInvalid",

    [ErrorCode.$80001]: "errorMsg.loginErrorWrongPassword",
    [ErrorCode.$80002]: "errorMsg.passwordErrorAccountLocked",
    [ErrorCode.$80003]: "errorMsg.userNotExistOrPasswordError",
    [ErrorCode.$80004]: "errorMsg.userNotExist",
    [ErrorCode.$80005]: "errorMsg.userNotActivated",
    [ErrorCode.$80006]: "errorMsg.initialPasswordTokenInvalid",
    [ErrorCode.$80007]: "errorMsg.userStatusError",
    [ErrorCode.$80008]: "errorMsg.clientUserMailboxEmpty",
    [ErrorCode.$80009]: "errorMsg.verificationCodeError",
    [ErrorCode.$80010]: "errorMsg.verificationCodeExpired",
    [ErrorCode.$80011]: "errorMsg.mendixPasswordResetFailed",
    [ErrorCode.$80012]: "errorMsg.mobileVerificationCodeError",
    [ErrorCode.$80013]: "errorMsg.emailVerificationCodeError",
    [ErrorCode.$80014]: "errorMsg.waitBeforeResendingVerificationCode",
    [ErrorCode.$80015]: "errorMsg.maxVerificationCodeAttemptsReached",
    [ErrorCode.$80016]: "errorMsg.clientUserEmailNotVerified",
    [ErrorCode.$80017]: "errorMsg.maxActiveEmailsReached",
    [ErrorCode.$80018]: "errorMsg.userStatusUnverifiedOrDisabled",
    [ErrorCode.$80019]: "errorMsg.maxVerificationCodesReachedTryPicCode",

    [ErrorCode.$800003]: "errorMsg.userNotFound",
    
    [ErrorCode.$80800003]: "errorMsg.invalidValidateCode",
    [ErrorCode.$80810004]: "errorMsg.invalidCredentials",

    [ErrorCode.$90010001]: "errorMsg.enterBusinessRegistrationNumber",
    [ErrorCode.$90010002]: "errorMsg.enterEnglishCompanyName",
    [ErrorCode.$90010003]: "errorMsg.enterUnifiedSocialCreditCode",
    [ErrorCode.$90010004]: "errorMsg.enterChineseCompanyName",
    [ErrorCode.$90010005]: "errorMsg.failedToGenerateCompanyID",
    [ErrorCode.$90010006]: "errorMsg.userEmailAlreadyExists",
    [ErrorCode.$90010007]: "errorMsg.failedToGenerateCustomerUserID",
    [ErrorCode.$90010008]: "errorMsg.invalidMobileNumber",
    [ErrorCode.$90010009]: "errorMsg.invalidInternalEmail",
    [ErrorCode.$90010010]: "errorMsg.emailVerifiedCannotModify",
    [ErrorCode.$90010011]: "errorMsg.unlockTimesExceededLimit",
    [ErrorCode.$90010012]: "errorMsg.languageInvalid",
    [ErrorCode.$90020001]: "errorMsg.companyHasProcessedKYC",
    [ErrorCode.$90020002]: "errorMsg.companyKYCIDNotExist",
    [ErrorCode.$90020003]: "errorMsg.companyBRNApproved",
};

export default errorCodes;
