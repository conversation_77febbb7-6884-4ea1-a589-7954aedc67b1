{"errorMsg": {"userNotFound": "User does not exist | Password is incorrect", "businessException": "Business exception.", "systemExecutionException": "System execution exception.", "parameterEmpty": "Parameter is empty.", "returnResultEmpty": "Return result is empty.", "networkError": "Network error.", "systemError": "System error.", "batchDataTooMuchAdded": "Too much batch data added.", "batchDataTooMuchUpdated": "Too much batch data updated.", "parameterMissingException": "Parameter missing exception.", "queryDataOverloadException": "Query data overload exception.", "dataNotExistException": "Data does not exist exception.", "systemDataException": "System data exception.", "invalid": "{} invalid.", "notExist": "{} does not exist.", "invalidOrNotExist": "{} invalid |Does not exist.", "success": "Success.", "failure": "Failure.", "addedSuccessfully": "Added successfully.", "addedFailed": "Added failed.", "updatedSuccessfully": "Updated successfully.", "updatedFailed": "Updated failed.", "deletedSuccessfully": "Deleted successfully.", "deletedFailed": "Deleted failed.", "verificationFailed": "Verification failed.", "parameterMissingOrIncorrect": "Parameter missing or incorrect.", "processing": "Processing.", "doNotRepeatOperation": "Do not repeat the operation.", "jsonFormatError": "JSON format error.", "dateFormatIncorrect": "Date format is incorrect.", "databaseOperationFailed": "Database operation failed.", "processedDataExceedsLimit": "The processed data cannot exceed 1000.", "operationTypeNotExistOrUnavailable": "Operation type does not exist or is unavailable.", "dataAlreadyExists": "Data already exists.", "dataNotExist": "Data does not exist.", "templateUnavailable": "Template is unavailable.", "enterBusinessRegistrationNumber": "Please enter the business registration number.", "enterEnglishCompanyName": "Please enter the English company name.", "enterUnifiedSocialCreditCode": "Please enter the unified social credit code.", "enterChineseCompanyName": "Please enter the Chinese company name.", "failedToGenerateCompanyNumber": "Failed to generate company number.", "userEmailAlreadyExists": "User Email already exists.", "failedToGenerateCustomerUserNumber": "Failed to generate customer user number.", "mailboxVerifiedCannotModify": "Mailbox has been verified and cannot be modified.", "unlockTimesExceedLimit": "Unlock times exceed limit.", "regionInvalidOrNotExist": "Region is invalid, region does not exist.", "userPhoneAlreadyExists": "User phone already exists.", "associatedCompanyBRN": "You have associated company BRN: %s information, please continue business operations.", "companyCREmpty": "Company CR is empty.", "contactAdminForCompanyInvitation": "Please contact the system administrator to invite you to join the company.", "checkBrnRepeated": "Please check if the brn you entered is repeated.", "userAlreadyHasSameBRN": "The current user already has a company with the same BRN: %s.", "companyAddUserLinkExpired": "The company add user link has expired.", "newApplicationForKYC": "The company has a new application for processing kyc.", "approvedKYCExists": "The company already has an approved kyc, please select another kyc type.", "smeSystemError": "sme system error.", "kycProcessingCheckStatus": "During processing, please check the kyc status.", "invalidRiskLevel": "Invalid risk level.", "downloadFileError": "Download file error.", "crcCheckError": "CRC check error.", "fileNotExist": "File does not exist.", "ruleCodeInvalid": "Rule code is invalid.", "loginErrorWrongPassword": "Login error, you have entered the wrong password %d times, please enter the verification code.", "passwordErrorAccountLocked": "Password input error %d times, account is locked, please contact the system administrator or try again the next day.", "userNotExistOrPasswordError": "User does not exist | Password error.", "userNotExist": "Login user does not exist.", "userNotActivated": "Login user is not activated, please contact the system administrator.", "initialPasswordTokenInvalid": "Initial password token is invalid, token does not exist or token has expired, please contact the system administrator.", "userStatusError": "User status error.", "clientUserMailboxEmpty": "Client user mailbox is empty.", "verificationCodeEmpty": "Verification code cannot be empty.", "verificationCodeIncorrect": "Verification code is incorrect.", "verificationCodeError": "Verification code error.", "verificationCodeExpired": "Verification code expired.", "invalidValidateCode": "Invalid verification code", "invalidCredentials": "Invalid credentials", "mendixPasswordResetFailed": "Mendix password reset failed.", "mobileVerificationCodeError": "Mobile verification code error.", "emailVerificationCodeError": "Email verification code error.", "waitBeforeResendingVerificationCode": "If you need to send the verification code again, please wait one minute before continuing.", "maxVerificationCodeAttemptsReached": "The maximum number of verification codes has been reached, please try again tomorrow.", "clientUserEmailNotVerified": "The client user email is not verified.", "maxActiveEmailsReached": "The maximum number of active emails has been reached, please try again tomorrow.", "userStatusUnverifiedOrDisabled": "The user status is unverified | disabled.", "maxVerificationCodesReachedTryPicCode": "The maximum number of verification codes has been reached, please try pic code.", "failedToGenerateCompanyID": "Failed to generate company ID.", "failedToGenerateCustomerUserID": "Failed to generate customer user ID.", "invalidMobileNumber": "Mobile number is invalid, please recheck.", "invalidInternalEmail": "Internal email is invalid, <NAME_EMAIL>.", "emailVerifiedCannotModify": "Email has been verified and cannot be modified.", "unlockTimesExceededLimit": "Unlock times exceeded the limit.", "languageInvalid": "Language is invalid, please recheck.", "companyHasProcessedKYC": "The company already has %s processed kyc, please select another kyc type.", "companyKYCIDNotExist": "The company kyc id does not exist.", "companyBRNApproved": "The company brn %s has been approved"}}