{"errorMsg": {"userNotFound": "用户不存在 | 密码错误", "businessException": "业务异常。", "systemExecutionException": "系统执行异常。", "parameterEmpty": "参数为空。", "returnResultEmpty": "返回结果为空。", "networkError": "网络错误。", "systemError": "系统错误。", "batchDataTooMuchAdded": "批量新增数据过多。", "batchDataTooMuchUpdated": "批量更新数据过多。", "parameterMissingException": "参数缺失异常。", "queryDataOverloadException": "查询数据超载异常。", "dataNotExistException": "数据不存在异常。", "systemDataException": "系统数据异常。", "invalid": "{} 无效。", "notExist": "{} 不存在。", "invalidOrNotExist": "{} 无效 | 不存在。", "success": "成功。", "failure": "失败。", "addedSuccessfully": "新增成功。", "addedFailed": "新增失败。", "updatedSuccessfully": "更新成功。", "updatedFailed": "更新失败。", "deletedSuccessfully": "删除成功。", "deletedFailed": "删除失败。", "verificationFailed": "验证失败。", "parameterMissingOrIncorrect": "参数缺失或错误。", "processing": "处理中。", "doNotRepeatOperation": "请勿重复操作。", "jsonFormatError": "JSON 格式错误。", "dateFormatIncorrect": "日期格式错误。", "databaseOperationFailed": "数据库操作失败。", "processedDataExceedsLimit": "处理的数据不能超过 1000 条。", "operationTypeNotExistOrUnavailable": "操作类型不存在或不可用。", "dataAlreadyExists": "数据已存在。", "dataNotExist": "数据不存在。", "templateUnavailable": "模板不可用。", "enterBusinessRegistrationNumber": "请输入商业登记号码。", "enterEnglishCompanyName": "请输入公司英文名称。", "enterUnifiedSocialCreditCode": "请输入统一社会信用代码。", "enterChineseCompanyName": "请输入公司中文名称。", "failedToGenerateCompanyNumber": "生成公司编号失败。", "userEmailAlreadyExists": "用户邮箱已存在。", "failedToGenerateCustomerUserNumber": "生成客户用户编号失败。", "mailboxVerifiedCannotModify": "邮箱已验证，无法修改。", "unlockTimesExceedLimit": "解锁次数超限。", "regionInvalidOrNotExist": "地区无效或不存在。", "userPhoneAlreadyExists": "用户手机号已存在。", "associatedCompanyBRN": "您已关联公司 BRN：%s 信息，请继续业务操作。", "companyCREmpty": "公司 CR 为空。", "contactAdminForCompanyInvitation": "请联系系统管理员邀请您加入公司。", "checkBrnRepeated": "请检查输入的 BRN 是否重复。", "userAlreadyHasSameBRN": "当前用户已拥有相同 BRN 的公司：%s。", "companyAddUserLinkExpired": "公司添加用户链接已过期。", "newApplicationForKYC": "该公司有新的 KYC 申请待处理。", "approvedKYCExists": "该公司已有已批准的 KYC，请选择其他 KYC 类型。", "smeSystemError": "SME 系统错误。", "kycProcessingCheckStatus": "处理中，请检查 KYC 状态。", "invalidRiskLevel": "风险等级无效。", "downloadFileError": "文件下载错误。", "crcCheckError": "CRC 校验错误。", "fileNotExist": "文件不存在。", "ruleCodeInvalid": "规则代码无效。", "loginErrorWrongPassword": "登录错误，您已输入错误密码 %d 次，请输入验证码。", "passwordErrorAccountLocked": "密码输入错误 %d 次，账户已锁定，请联系系统管理员或次日再试。", "userNotExistOrPasswordError": "用户不存在 | 密码错误。", "userNotExist": "登录用户不存在。", "userNotActivated": "登录用户未激活，请联系系统管理员。", "initialPasswordTokenInvalid": "初始密码令牌无效，不存在或已过期，请联系系统管理员。", "userStatusError": "用户状态错误。", "clientUserMailboxEmpty": "客户用户邮箱为空。", "verificationCodeEmpty": "验证码不能为空。", "verificationCodeIncorrect": "验证码不正确。", "verificationCodeError": "验证码错误。", "verificationCodeExpired": "验证码已过期。", "invalidValidateCode": "验证码无效。", "invalidCredentials": "凭据无效。", "mendixPasswordResetFailed": "Mendix 密码重置失败。", "mobileVerificationCodeError": "手机验证码错误。", "emailVerificationCodeError": "邮箱验证码错误。", "waitBeforeResendingVerificationCode": "如需重新发送验证码，请等待一分钟后再试。", "maxVerificationCodeAttemptsReached": "已达最大验证码次数，请明日再试。", "clientUserEmailNotVerified": "客户用户邮箱未验证。", "maxActiveEmailsReached": "已达最大活跃邮箱数量，请明日再试。", "userStatusUnverifiedOrDisabled": "用户状态未验证 | 已禁用。", "maxVerificationCodesReachedTryPicCode": "已达最大验证码次数，请尝试图片验证码。", "failedToGenerateCompanyID": "生成公司 ID 失败。", "failedToGenerateCustomerUserID": "生成客户用户 ID 失败。", "invalidMobileNumber": "手机号无效，请检查。", "invalidInternalEmail": "内部邮箱无效，请输入 <EMAIL>。", "emailVerifiedCannotModify": "邮箱已验证，无法修改。", "unlockTimesExceededLimit": "解锁次数超限。", "languageInvalid": "语言无效，请检查。", "companyHasProcessedKYC": "该公司已有 %s 已处理的 KYC，请选择其他 KYC 类型。", "companyKYCIDNotExist": "公司 KYC ID 不存在。", "companyBRNApproved": "公司 BRN %s 已批准。"}}