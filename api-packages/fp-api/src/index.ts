export { default as request } from "./request";
export * from "./api/login";
export * from "./api/resetPwd";
export * from "./api/user";
export * from "./api/signup";
export * from "./api/common";
export * from "./api/company";
export * from "./api/upload";
export * from "./api/companyUser";
export * from "./api/companyAccount";
export * from "./api/onSiteMessage";
export * from "./api/noticeMessage";
export * from "./api/assetShop";
export * from "./api/charge";
export * from "./api/home";
export * from "./api/applicationRecord";
export * from "./types/applicationRecord";
export * from "./api/facility";
export * from "./types/facility";
export * from "./api/platform/platform";
export * from "./types/platform/platform";
export * from "./api/drawdownForm";
export * from "./types/drawdownForm";
export * from "./api/path";
export * from "./types/path";
export * from "./api/openapi/company";
export * from "./api/redPocket";
export * from "./api/limits";
export * from './api/secondDrawdown'
export * from "./api/notifications";
export * from "./api/categoryReport";
export * from "./api/wechat";

// Export all the drawdown form related functions
export {
  getCompanyConfig,
  submitDebitAccount,
  getDebitAccount,
  submitBusinessInfo,
  getBusinessInfo,
  uploadFile,
  saveSignerInfo,
  getSignerInfo,
  initiateSign,
  urgeSign,
  getShareholderStructureResult,
} from './api/drawdownForm';

// Re-export types
export type { GetDebitAccountRes, SubmitDebitAccountReq } from './types/drawdown';