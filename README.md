# FP-Account-Portal

Fundpark Account Portal Frontend Application.

## About

A React-based web application for Fundpark's Account Portal, featuring:
- Multi-language support (English, Simplified Chinese, Traditional Chinese)
- Environment-specific configurations
- Modern UI with Ant Design and Tailwind CSS
- Type-safe development with TypeScript
- Monorepo architecture with pnpm workspaces

## Getting Started

### Prerequisites

- Node.js >= 20.0.0
- pnpm >= 9.12.0

### Installation

1. Clone the repository:
   ```bash
   git clone http://source.fundpark.com/FP-IT/account-portal.git
   ```
2. Install dependencies:
   ```bash
   pnpm install
   ```
3. Start the development server:
   ```bash
   pnpm dev
   ```

### Development Scripts

```bash
# Start development server
pnpm dev

# Development build
pnpm build:development

# SIT environment build
pnpm build:sit

# CI build (uses build script)
pnpm build:ci
```

### Building

To build the application for production:
```bash
pnpm build:sit
```

## Project Structure

```
├── api-packages        # API integration packages
│   └── fp-api          # Fundpark API integration
├── base-packages       # Shared type definitions and utilities
│   └── base-types      # Global TypeScript types
├── runtime-packages    # Main application packages
│   └── account-portal  # Account Portal UI application
├── ui-packages         # Shared UI components and utilities
│   └── ui-utils        # UI utility functions and helpers
├── scripts             # Build and deployment scripts
└── tsconfig            # TypeScript configuration
```

### Technologies

- **Frontend Framework**: React 18
- **Styling**: SCSS, Tailwind CSS, Ant Design
- **State Management**: Zustand, React Query
- **Routing**: React Router 6
- **Internationalization**: i18next
- **Build Tool**: Vite 6
- **Package Management**: pnpm workspaces
- **Build Orchestration**: Turborepo

## Architecture

This project follows a three-part architecture pattern:

1. **API Layer** - Handles network requests to backend services (in api-packages/fp-api)
2. **UI Component Layer** - React components for rendering the user interface (in runtime-packages/account-portal/src/components)
3. **Mapper Layer** - Transforms data between API and UI formats (in runtime-packages/account-portal/src/mappers)

## Styling

The project uses SCSS modules combined with Tailwind CSS for styling:
- Component-specific styles in `styles.scss` files
- Global styles and variables in the themes directory
- Utility classes from Tailwind CSS

## Coding Standards

### CSS Naming Convention
```
- Class names use lowercase letters and hyphens (kebab-case)
```

### Directory Naming Convention
```
- Use lowercase letters and hyphens (kebab-case)
```

### TSX/JSX File Naming Convention
```
- Use PascalCase (uppercase first letter + camelCase)
```

### Other File Naming Convention
```
- Use camelCase (lowercase first letter + camelCase)
```

## Deployment

The application uses Docker for containerization and can be deployed to different environments:
- Development
- SIT (System Integration Testing)
- Production
