.DS_Store
**/node_modules
**/dist
**/.pnp
**/.pnp.js

# testing
**/coverage

# next.js
**/.next/
**/out/

# production
**/build

# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# vercel
**/.vercel

# typescript
*.tsbuildinfo
**/next-env.d.ts

# Editor directories and files
.idea
.vscode
.cursor
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.log
*.js.map
*.ts.map

/*-packages/*/lib

/temp
/*.log

# turbo
.turbo

dist.zip
