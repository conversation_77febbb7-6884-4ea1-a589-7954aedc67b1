{"name": "@fundpark/account-portal-mono", "version": "1.0.0", "description": "FP Account Portal", "scripts": {"dev": "turbo run dev --filter=@fundpark/account-portal-ui", "dev:staging": "turbo run dev:staging --filter=@fundpark/account-portal-ui", "dev:production": "turbo run dev:production --filter=@fundpark/account-portal-ui", "build:development": "turbo run build:development --filter=@fundpark/account-portal-ui", "build:developmentAlt": "turbo run build:developmentAlt --filter=@fundpark/account-portal-ui", "build:staging": "turbo run build:staging --filter=@fundpark/account-portal-ui", "build:production": "turbo run build:production --filter=@fundpark/account-portal-ui", "build:ci": "sh scripts/build.sh"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "engines": {"node": ">=20", "pnpm": ">=9"}, "packageManager": "pnpm@9.12.0", "devDependencies": {"turbo": "^2.3.3", "typescript": "^5"}}